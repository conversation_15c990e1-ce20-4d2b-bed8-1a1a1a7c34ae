<?php $__env->startSection('main-content'); ?>
<div class="professional-admin-login">
    <div class="login-container">
        <div class="login-card">
            
            <div class="login-header">
                <div class="logo-section">
                    <img src="<?php echo e(asset('assets/images/logoIcon/logo.png')); ?>" alt="<?php echo e($general->site_name); ?>" class="login-logo">
                </div>
                <div class="welcome-section">
                    <h1 class="login-title"><?php echo app('translator')->get('Welcome Back'); ?></h1>
                    <p class="login-subtitle"><?php echo app('translator')->get('Sign in to your'); ?> <?php echo e(__($general->site_name)); ?> <?php echo app('translator')->get('account'); ?></p>
                </div>
            </div>

            
            <div class="login-form-section">
                <form action="<?php echo e(route('user.login')); ?>" method="POST" class="professional-login-form verify-gcaptcha">
                    <?php echo csrf_field(); ?>

                    
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-user me-2"></i><?php echo app('translator')->get('Username or Email'); ?>
                        </label>
                        <div class="input-wrapper">
                            <input type="text" class="form-control" value="<?php echo e(old('username')); ?>"
                                   name="username" required placeholder="<?php echo app('translator')->get('Enter your username or email'); ?>" autocomplete="username">
                            <div class="input-icon">
                                <i class="fas fa-user-shield"></i>
                            </div>
                        </div>
                    </div>

                    
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-lock me-2"></i><?php echo app('translator')->get('Password'); ?>
                        </label>
                        <div class="input-wrapper">
                            <input type="password" class="form-control" name="password" required
                                   placeholder="<?php echo app('translator')->get('Enter your password'); ?>" autocomplete="current-password">
                            <div class="input-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <button type="button" class="password-toggle" onclick="toggleAdminPassword()">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    
                    <div class="form-group">
                        <?php if (isset($component)) { $__componentOriginalff0a9fdc5428085522b49c68070c11d6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalff0a9fdc5428085522b49c68070c11d6 = $attributes; } ?>
<?php $component = App\View\Components\Captcha::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('captcha'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Captcha::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalff0a9fdc5428085522b49c68070c11d6)): ?>
<?php $attributes = $__attributesOriginalff0a9fdc5428085522b49c68070c11d6; ?>
<?php unset($__attributesOriginalff0a9fdc5428085522b49c68070c11d6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalff0a9fdc5428085522b49c68070c11d6)): ?>
<?php $component = $__componentOriginalff0a9fdc5428085522b49c68070c11d6; ?>
<?php unset($__componentOriginalff0a9fdc5428085522b49c68070c11d6); ?>
<?php endif; ?>
                    </div>

                    
                    <div class="form-options">
                        <div class="remember-section">
                            <input class="form-check-input" name="remember" type="checkbox" id="remember">
                            <label class="form-check-label" for="remember">
                                <i class="fas fa-clock me-1"></i><?php echo app('translator')->get('Remember Me'); ?>
                            </label>
                        </div>
                        <a href="<?php echo e(route('user.password.request')); ?>" class="forgot-link">
                            <i class="fas fa-key me-1"></i><?php echo app('translator')->get('Forgot Password?'); ?>
                        </a>
                    </div>

                    
                    <button type="submit" class="login-submit-btn">
                        <i class="fas fa-sign-in-alt me-2"></i><?php echo app('translator')->get('Sign In'); ?>
                    </button>
                </form>

                
                <div class="register-link-section">
                    <p class="register-text"><?php echo app('translator')->get("Don't have an account?"); ?></p>
                    <a href="<?php echo e(route('user.register')); ?>" class="register-link">
                        <i class="fas fa-user-plus me-1"></i><?php echo app('translator')->get('Create Account'); ?>
                    </a>
                </div>
            </div>

            
            <div class="security-notice">
                <div class="security-icon">
                    <i class="fas fa-shield-check"></i>
                </div>
                <div class="security-text">
                    <small><?php echo app('translator')->get('Your account security is our priority. All connections are encrypted and secure.'); ?></small>
                </div>
            </div>

            
            <div class="auth-footer">
                <small class="text-muted"><?php echo app('translator')->get('All rights reserved'); ?> &copy; MBFX <?php echo e(date('Y')); ?></small>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>


<?php $__env->startPush('style'); ?>
<style>
/* Professional Admin Login Page Styling */
.professional-admin-login {
    min-height: 100vh;
    background: linear-gradient(135deg,rgb(255, 255, 255) 0%,rgb(255, 255, 255) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
    position: relative;
}

.professional-admin-login::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: white;
        
    pointer-events: none;
}

.login-container {
    width: 100%;
    max-width: 580px;
    position: relative;
    z-index: 1;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.2);
    overflow: hidden;
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    background: linear-gradient(135deg,rgb(0, 0, 0) 0%,rgb(28, 27, 28) 100%);
    padding: 1.5rem 1rem;
    text-align: center;
    color: white;
    position: relative;
}

.login-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.logo-section {
    position: relative;
    z-index: 2;
    margin-bottom: 1.5rem;
}

.login-logo {
    max-width: 180px;
    height: auto;
    filter: brightness(0) invert(1);
    transition: all 0.3s ease;
}

.welcome-section {
    position: relative;
    z-index: 2;
}

.login-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.3rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    color:white;
}

.login-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0;
    font-weight: 300;
     color:white;
}

.login-form-section {
    padding: 2.5rem 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.input-wrapper {
    position: relative;
}

.form-control {
    width: 100%;
    padding: 1rem 3rem 1rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.form-control:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    background: #fff;
    outline: none;
}

.input-icon {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    pointer-events: none;
    transition: color 0.3s ease;
}

.form-control:focus + .input-icon {
    color: #dc3545;
}

.password-toggle {
    position: absolute;
    right: 3rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.password-toggle:hover {
    color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.remember-section {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-check-input {
    width: 18px;
    height: 18px;
    margin: 0;
    accent-color: #dc3545;
}

.form-check-label {
    font-size: 0.9rem;
    color: #666;
    margin: 0;
    cursor: pointer;
}

.forgot-link {
    color:rgb(0, 0, 0);
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.forgot-link:hover {
    color: #c82333;
    text-decoration: underline;
}

.login-submit-btn {
    width: 100%;
    padding: 1rem 2rem;
    background: linear-gradient(135deg,rgb(0, 0, 0) 0%,rgb(32, 30, 31) 100%);
    border: none;
    border-radius: 12px;
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    position: relative;
    overflow: hidden;
}

.login-submit-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.login-submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

.login-submit-btn:hover::before {
    left: 100%;
}

.login-submit-btn:active {
    transform: translateY(0);
}

.register-link-section {
    text-align: center;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e9ecef;
}

.register-text {
    color: #666;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.register-link {
    color: #dc3545;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.register-link:hover {
    color: #c82333;
    text-decoration: underline;
}

.security-notice {
    background: #f8f9fa;
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-top: 1px solid #e9ecef;
}

.auth-footer {
    background: #f8f9fa;
    padding: 0.75rem 1rem;
    text-align: center;
    border-top: 1px solid #e9ecef;
}

.security-icon {
    color: #28a745;
    font-size: 1.2rem;
}

.security-text {
    flex: 1;
}

.security-text small {
    color: #666;
    font-size: 0.65rem;
    line-height: 1.2;
}

/* Responsive Design */
@media (max-width: 576px) {
    .professional-admin-login {
        padding: 1rem;
    }

    .login-header {
        padding: 2rem 1.5rem;
    }

    .login-title {
        font-size: 1.5rem;
    }

    .login-form-section {
        padding: 2rem 1.5rem;
    }

    .form-options {
        flex-direction: column;
        align-items: flex-start;
    }

    .security-notice {
        padding: 1.5rem;
        flex-direction: column;
        text-align: center;
    }
}

/* Loading state */
.login-submit-btn.loading {
    pointer-events: none;
    opacity: 0.8;
}

.login-submit-btn.loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    top: 50%;
    left: 50%;
    margin-left: -10px;
    margin-top: -10px;
    border: 2px solid transparent;
    border-top-color: #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
<?php $__env->stopPush(); ?>


<?php $__env->startPush('script'); ?>
<script>
function toggleAdminPassword() {
    const passwordField = document.querySelector('input[name="password"]');
    const toggleIcon = document.querySelector('.password-toggle i');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.professional-login-form');
    const submitBtn = document.querySelector('.login-submit-btn');

    // Enhanced form submission - prevent duplicate loaders
    form.addEventListener('submit', function(e) {
        const originalText = submitBtn.innerHTML;

        // Hide global preloader if it exists to prevent duplicate loaders
        const globalPreloader = document.querySelector('.preloader-wrapper');
        if (globalPreloader) {
            globalPreloader.style.display = 'none';
        }

        // Add loading state (single loader only)
        submitBtn.classList.add('loading');
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Authenticating...';
        submitBtn.disabled = true;

        // Re-enable after 10 seconds as fallback
        setTimeout(() => {
            submitBtn.classList.remove('loading');
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 10000);
    });

    // Enhanced input focus effects
    const inputs = document.querySelectorAll('.form-control');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make($activeTemplate.'layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\mbf.mybrokerforex.com-31052025\resources\views/templates/basic/user/auth/login.blade.php ENDPATH**/ ?>