@echo off
REM MT5 Real-time Sync Runner
REM Runs every minute via Windows Task Scheduler
REM Auto-generated by MT5 Real-time Sync Setup

REM Set environment variables for Plesk - Updated for PHP 8.4
set PHP_PATH="C:\Program Files (x86)\Plesk\Additional\PleskPHP84\php.exe"
set PROJECT_PATH="C:\Inetpub\vhosts\mybrokerforex.com\mbf.mybrokerforex.com"
set ARTISAN_PATH="%PROJECT_PATH%\artisan"
set LOG_PATH="%PROJECT_PATH%\storage\logs\mt5_sync.log"

REM Change to project directory
cd /d %PROJECT_PATH%

REM Log sync start
echo [%DATE% %TIME%] Starting MT5 sync... >> %LOG_PATH%

REM Run MT5 sync with enhanced settings
%PHP_PATH% %ARTISAN_PATH% mt5:sync-users --fast --force --limit=1000 >> %LOG_PATH% 2>&1

REM Check exit code and log result
if %ERRORLEVEL% EQU 0 (
    echo [%DATE% %TIME%] MT5 sync completed successfully >> %LOG_PATH%
) else (
    echo [%DATE% %TIME%] MT5 sync failed with error code %ERRORLEVEL% >> %LOG_PATH%
)

REM Log completion
echo [%DATE% %TIME%] MT5 sync runner finished >> %LOG_PATH%
echo. >> %LOG_PATH%
