<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔧 DIRECT DATABASE FIX FOR IB STATUS\n";
echo "====================================\n";

// Use direct database queries to fix the issue
echo "\n🔍 Checking current database state:\n";

$users = \DB::select("SELECT id, email, partner, ib_status FROM users WHERE partner = 1 LIMIT 5");
foreach ($users as $user) {
    echo "   - {$user->email}: partner={$user->partner}, ib_status='{$user->ib_status}'\n";
}

// Direct database update
echo "\n🔧 Performing direct database update:\n";

$affected = \DB::update("
    UPDATE users 
    SET ib_status = 'approved', 
        ib_type = COALESCE(NULLIF(ib_type, ''), 'master'),
        ib_approved_at = COALESCE(ib_approved_at, NOW())
    WHERE partner = 1
");

echo "✅ Updated {$affected} users with partner=1\n";

// Verify the update
echo "\n🔍 Verifying database update:\n";

$users = \DB::select("SELECT id, email, partner, ib_status, ib_type FROM users WHERE partner = 1 LIMIT 5");
foreach ($users as $user) {
    echo "   - {$user->email}: partner={$user->partner}, ib_status='{$user->ib_status}', ib_type='{$user->ib_type}'\n";
}

// Test specific user
echo "\n🔍 Testing specific user: <EMAIL>\n";
$testUser = \DB::select("SELECT * FROM users WHERE email = '<EMAIL>'")[0] ?? null;
if ($testUser) {
    echo "   Partner: {$testUser->partner}\n";
    echo "   IB Status: '{$testUser->ib_status}'\n";
    echo "   IB Type: '{$testUser->ib_type}'\n";
    
    // Test with Eloquent model
    $eloquentUser = \App\Models\User::where('email', '<EMAIL>')->first();
    if ($eloquentUser) {
        echo "   isIb() result: " . ($eloquentUser->isIb() ? 'true' : 'false') . "\n";
    }
}

// Check all approved IBs
echo "\n🔍 All approved IBs:\n";
$approvedIBs = \DB::select("SELECT email, partner, ib_status FROM users WHERE partner = 1 AND ib_status = 'approved'");
echo "Found " . count($approvedIBs) . " approved IBs:\n";

foreach (array_slice($approvedIBs, 0, 10) as $ib) {
    echo "   - {$ib->email}\n";
}

// Test isIb() method for approved users
echo "\n🔍 Testing isIb() method for approved users:\n";
$testUsers = \App\Models\User::where('partner', 1)->where('ib_status', 'approved')->take(5)->get();

foreach ($testUsers as $user) {
    echo "   - {$user->email}: isIb() = " . ($user->isIb() ? 'true' : 'false') . "\n";
}

echo "\n✅ Database IB status fix completed!\n";
