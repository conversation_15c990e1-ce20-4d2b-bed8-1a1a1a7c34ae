@extends($activeTemplate.'layouts.master')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-12">
            <!-- Network Statistics -->
            <div class="row mb-4">
                <div class="col-xl-3 col-lg-6 col-sm-6 mb-30">
                    <div class="widget-two style--two box--shadow2 b-radius--5 bg--primary">
                        <div class="widget-two__icon b-radius--5 bg--primary">
                            <i class="las la-users"></i>
                        </div>
                        <div class="widget-two__content">
                            <h3 class="text-white">{{ $networkStats['total_referrals'] }}</h3>
                            <p class="text-white">@lang('Total Referrals')</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-lg-6 col-sm-6 mb-30">
                    <div class="widget-two style--two box--shadow2 b-radius--5 bg--success">
                        <div class="widget-two__icon b-radius--5 bg--success">
                            <i class="las la-user-tie"></i>
                        </div>
                        <div class="widget-two__content">
                            <h3 class="text-white">{{ $networkStats['total_ibs'] }}</h3>
                            <p class="text-white">@lang('Active IBs')</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-lg-6 col-sm-6 mb-30">
                    <div class="widget-two style--two box--shadow2 b-radius--5 bg--warning">
                        <div class="widget-two__icon b-radius--5 bg--warning">
                            <i class="las la-dollar-sign"></i>
                        </div>
                        <div class="widget-two__content">
                            <h3 class="text-white">${{ number_format($networkStats['total_commission'], 2) }}</h3>
                            <p class="text-white">@lang('Total Commission')</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-lg-6 col-sm-6 mb-30">
                    <div class="widget-two style--two box--shadow2 b-radius--5 bg--info">
                        <div class="widget-two__icon b-radius--5 bg--info">
                            <i class="las la-chart-line"></i>
                        </div>
                        <div class="widget-two__content">
                            <h3 class="text-white">{{ number_format($networkStats['total_volume'], 2) }}</h3>
                            <p class="text-white">@lang('Total Volume (Lots)')</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Network Tree Visualization -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">@lang('My Network Tree')</h5>
                    <small class="text-muted">@lang('Hierarchical view of your referral network')</small>
                </div>
                <div class="card-body">
                    <div class="network-tree-container">
                        <!-- Root User (You) -->
                        <div class="network-node root-node">
                            <div class="user-card">
                                <div class="user-avatar">
                                    <i class="las la-crown text-warning"></i>
                                </div>
                                <div class="user-info">
                                    @php
                                        $ibIndicator = '';
                                        if ($user->ib_status == 1) {
                                            if ($user->ib_type === 'master') {
                                                $ibIndicator = ' (M)';
                                            } elseif ($user->ib_type === 'sub') {
                                                $ibIndicator = ' (S)';
                                            }
                                        } else {
                                            $ibIndicator = ' (C)';
                                        }
                                    @endphp
                                    <h6 class="mb-1">{{ $user->fullname }}{{ $ibIndicator }} (@lang('You'))</h6>
                                    <small class="text-muted">{{ $user->username }}</small>
                                    @if($user->mt5_login)
                                        <br><small class="text-info">MT5: {{ $user->mt5_login }}</small>
                                    @endif
                                </div>
                                <div class="user-stats">
                                    <span class="badge badge--primary">{{ $user->ib_type ? ucfirst($user->ib_type) : 'IB' }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Network Tree -->
                        @if(count($networkTree) > 0)
                            <div class="network-children">
                                @include('templates.basic.user.ib.partials.network-tree-node', ['nodes' => $networkTree, 'level' => 1])
                            </div>
                        @else
                            <div class="text-center py-5">
                                <i class="las la-users text-muted" style="font-size: 4rem;"></i>
                                <h5 class="text-muted mt-3">@lang('No referrals yet')</h5>
                                <p class="text-muted">@lang('Share your referral link to start building your network')</p>
                                <a href="{{ route('user.ib.referral_link') }}" class="btn btn--primary">
                                    <i class="las la-link"></i> @lang('Get Referral Link')
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Level Breakdown -->
            @if(count($networkStats['levels']) > 0)
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title">@lang('Network Breakdown by Level')</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($networkStats['levels'] as $level => $data)
                        <div class="col-md-3 mb-3">
                            <div class="level-card">
                                <div class="level-header">
                                    <span class="level-badge">@lang('Level') {{ $level }}</span>
                                </div>
                                <div class="level-stats">
                                    <div class="stat-item">
                                        <span class="stat-value">{{ $data['count'] }}</span>
                                        <span class="stat-label">@lang('Total Users')</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-value">{{ $data['ibs'] }}</span>
                                        <span class="stat-label">@lang('IBs')</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif

            <!-- Recent Network Activity -->
            @if(count($recentActivity) > 0)
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title">@lang('Recent Network Activity')</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>@lang('User')</th>
                                    <th>@lang('Symbol')</th>
                                    <th>@lang('Volume')</th>
                                    <th>@lang('Commission')</th>
                                    <th>@lang('Date')</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentActivity as $activity)
                                <tr>
                                    <td>
                                        <div class="user-info">
                                            <strong>{{ $activity->fromUser->fullname ?? 'N/A' }}</strong>
                                            <br><small class="text-muted">{{ $activity->fromUser->username ?? 'N/A' }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge--primary">{{ $activity->symbol }}</span>
                                    </td>
                                    <td>{{ number_format($activity->volume, 2) }} lots</td>
                                    <td>
                                        <span class="text-success">${{ number_format($activity->commission_amount, 2) }}</span>
                                    </td>
                                    <td>
                                        {{ $activity->deal_time ? $activity->deal_time->format('M d, Y H:i') : 'N/A' }}
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('style')
<style>
.network-tree-container {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
}

.network-node {
    margin: 10px 0;
}

.user-card {
    display: flex;
    align-items: center;
    padding: 15px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 10px;
}

.root-node .user-card {
    border: 2px solid #dc3545;
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
}

.user-avatar {
    margin-right: 15px;
    font-size: 1.5rem;
}

.user-info {
    flex-grow: 1;
}

.user-stats {
    text-align: right;
}

.network-children {
    margin-left: 30px;
    border-left: 2px dashed #dee2e6;
    padding-left: 20px;
}

.level-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
}

.level-badge {
    background: #dc3545;
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: bold;
}

.level-stats {
    margin-top: 15px;
}

.stat-item {
    display: block;
    margin-bottom: 10px;
}

.stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: #dc3545;
}

.stat-label {
    font-size: 12px;
    color: #6c757d;
}
</style>
@endpush
