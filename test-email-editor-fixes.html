<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Editor Test - Popup Functions</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://maxst.icons8.com/vue-static/landings/line-awesome/line-awesome/1.3.0/css/line-awesome.min.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        .editor-container {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .visual-editor {
            min-height: 300px;
            border: 1px solid #ccc;
            padding: 15px;
            background: #fff;
        }
        .html-editor {
            min-height: 300px;
            font-family: monospace;
        }
        .test-email-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .alert {
            margin: 10px 0;
        }
        .btn-group {
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Email Editor Test - Popup Functions Fixed</h1>
        <p class="text-muted">Testing the fixed popup functionality for send email and preview template</p>
        
        <!-- Test Email Section -->
        <div class="test-email-section">
            <h4>Test Email Function</h4>
            <div class="row">
                <div class="col-md-8">
                    <input type="email" id="test-email-address" class="form-control" 
                           placeholder="Enter email address for testing" value="<EMAIL>">
                </div>
                <div class="col-md-4">
                    <button type="button" id="send-test-email" class="btn btn--success">
                        <i class="las la-paper-plane"></i> Send Test Email
                    </button>
                </div>
            </div>
            <div id="test-email-result" class="mt-2" style="display: none;"></div>
        </div>

        <!-- Email Editor Section -->
        <div class="editor-container">
            <h4>Email Template Editor</h4>
            
            <!-- Subject Field -->
            <div class="mb-3">
                <label for="subject" class="form-label">Email Subject</label>
                <input type="text" name="subject" id="subject" class="form-control" 
                       value="Test Email Template" required>
            </div>

            <!-- Editor Tabs -->
            <div class="btn-group mb-3" role="group">
                <button type="button" id="visual-tab" class="btn btn-outline-primary active">
                    <i class="las la-eye"></i> Visual Editor
                </button>
                <button type="button" id="html-tab" class="btn btn-outline-primary">
                    <i class="las la-code"></i> HTML Editor
                </button>
                <button type="button" id="preview-email" class="btn btn--success btn-sm">
                    <i class="las la-external-link-alt"></i> Preview
                </button>
            </div>

            <!-- Visual Editor -->
            <div id="visual-editor-panel" class="editor-panel">
                <div id="visual-editor" class="visual-editor" contenteditable="true">
                    <table width="100%" cellpadding="0" cellspacing="0" style="font-family: Arial, sans-serif;">
                        <tr>
                            <td style="padding: 20px; background-color: #f8f9fa;">
                                <h2 style="color: #333; margin-bottom: 20px;">Dear {{fullname}},</h2>
                                <p style="color: #666; line-height: 1.6; margin-bottom: 15px;">
                                    This is a test email template with proper styling and formatting.
                                </p>
                                <p style="color: #666; line-height: 1.6; margin-bottom: 15px;">
                                    Your account balance is: <strong style="color: #28a745;">{{amount}} {{currency}}</strong>
                                </p>
                                <div style="background: #007bff; color: white; padding: 15px; border-radius: 5px; text-align: center; margin: 20px 0;">
                                    <h3 style="margin: 0; color: white;">Important Notice</h3>
                                    <p style="margin: 10px 0 0 0; color: white;">Transaction ID: {{transaction_id}}</p>
                                </div>
                                <p style="color: #666; line-height: 1.6;">
                                    Best regards,<br>
                                    <strong>{{site_name}} Team</strong>
                                </p>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- HTML Editor -->
            <div id="html-editor-panel" class="editor-panel" style="display: none;">
                <textarea id="html-editor-textarea" class="form-control html-editor"></textarea>
            </div>

            <!-- Hidden Form Fields -->
            <input type="hidden" id="email_body" name="email_body">
            <input type="hidden" id="email_body_final" name="email_body_final">

            <!-- Save Button -->
            <div class="mt-3">
                <button type="button" id="save-template" class="btn btn-primary">
                    <i class="las la-save"></i> Save Template
                </button>
            </div>
        </div>

        <!-- Test Results -->
        <div id="test-results" class="mt-4">
            <h4>Test Results</h4>
            <div id="console-output" class="alert alert-info" style="font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto;"></div>
        </div>
    </div>

    <!-- Include the fixed JavaScript -->
    <script>
        // Mock window.templateData for testing
        window.templateData = {
            content: '',
            id: 1,
            testEmailRoute: '/admin/notification/test-email/1',
            previewRoute: '/admin/notification/preview/1',
            csrfToken: 'test-token',
            debug: true
        };

        // Mock Laravel notification function
        function showLaravelNotification(message, type) {
            const alertClass = type === 'success' ? 'alert-success' : 
                              type === 'error' ? 'alert-danger' : 
                              type === 'warning' ? 'alert-warning' : 'alert-info';
            
            const alert = document.createElement('div');
            alert.className = `alert ${alertClass} alert-dismissible fade show`;
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.insertBefore(alert, document.body.firstChild);
            
            setTimeout(() => {
                alert.remove();
            }, 5000);
        }

        // Console output for testing
        function log(message, type = 'info') {
            const output = document.getElementById('console-output');
            const timestamp = new Date().toLocaleTimeString();
            const icon = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : '✅';
            output.textContent += `[${timestamp}] ${icon} ${message}\n`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }
    </script>
    
    <!-- Include the fixed simple-email-editor.js -->
    <script src="assets/admin/js/simple-email-editor.js"></script>
    
    <script>
        // Initialize the editor when page loads
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Initializing Email Editor Test');
            
            // Initialize all functionality
            if (typeof initializeEmailEditor === 'function') {
                initializeEmailEditor();
                log('✅ Email Editor initialized successfully');
            } else {
                log('❌ Email Editor initialization function not found', 'error');
            }
            
            // Test save button
            document.getElementById('save-template').addEventListener('click', function() {
                log('💾 Save button clicked - testing form sync');
                if (typeof ensureFormFieldsSync === 'function') {
                    ensureFormFieldsSync();
                    showLaravelNotification('Template saved successfully (test mode)', 'success');
                } else {
                    log('❌ ensureFormFieldsSync function not found', 'error');
                }
            });
            
            log('🎯 Test page ready - try the Send Test Email and Preview buttons');
        });
    </script>
</body>
</html>