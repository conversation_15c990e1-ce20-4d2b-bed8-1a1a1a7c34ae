{"__meta": {"id": "01JZ2N5TK6MRN7B26P2WZ7H7P6", "datetime": "2025-07-01 09:30:34", "utime": **********.983655, "method": "POST", "uri": "/mbf.mybrokerforex.com-********/admin/notification/template/update/44", "ip": "::1"}, "messages": {"count": 40, "messages": [{"message": "[09:30:34] LOG.info: === TEMPLATE UPDATE DEBUG START ===", "message_html": null, "is_string": false, "label": "info", "time": **********.89917, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: Template ID: 44", "message_html": null, "is_string": false, "label": "info", "time": **********.899942, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: Request Method: POST", "message_html": null, "is_string": false, "label": "info", "time": **********.900488, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: Request URL: https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/update/44", "message_html": null, "is_string": false, "label": "info", "time": **********.901191, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "message_html": null, "is_string": false, "label": "info", "time": **********.901649, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: Server Environment: WINNT - PHP 8.2.12", "message_html": null, "is_string": false, "label": "info", "time": **********.902002, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: Content Type: multipart/form-data; boundary=----WebKitFormBoundaryDvxlAJrepXOGHdj7", "message_html": null, "is_string": false, "label": "info", "time": **********.902347, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: Content Length: 25505", "message_html": null, "is_string": false, "label": "info", "time": **********.902675, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: Raw POST data keys: _token, template_id, subject, email_status, sms_status, sms_body, email_body_encoded, email_body, email_body_final", "message_html": null, "is_string": false, "label": "info", "time": **********.903031, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: Email body field exists: YES", "message_html": null, "is_string": false, "label": "info", "time": **********.903392, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: Email body final field exists: YES", "message_html": null, "is_string": false, "label": "info", "time": **********.903716, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: ✅ Validation passed", "message_html": null, "is_string": false, "label": "info", "time": **********.929023, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: ✅ Template found - Current subject: Account Verification Required - Action Needed", "message_html": null, "is_string": false, "label": "info", "time": **********.936934, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: ✅ Template found - Current email_body length: 7355", "message_html": null, "is_string": false, "label": "info", "time": **********.937386, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: ✅ Subject updated to: Account Verification Required - Action Needed", "message_html": null, "is_string": false, "label": "info", "time": **********.93788, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: === ENHANCED EMAIL BODY PROCESSING WITH BASE64 SUPPORT ===", "message_html": null, "is_string": false, "label": "info", "time": **********.938254, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: 📦 Base64 encoded content detected", "message_html": null, "is_string": false, "label": "info", "time": **********.938628, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: ✅ Base64 content decoded successfully, length: 7260", "message_html": null, "is_string": false, "label": "info", "time": **********.938999, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: 📝 Decoded content preview: <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Account Verification Required</title>\n\n\n\n\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewpo", "message_html": null, "is_string": false, "label": "info", "time": **********.939345, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: Email body source: email_body_final", "message_html": null, "is_string": false, "label": "info", "time": **********.939703, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: Email body length: 7260", "message_html": null, "is_string": false, "label": "info", "time": **********.940027, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: Email body preview (first 200 chars): <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Account Verification Required</title>\n\n\n\n\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewpo", "message_html": null, "is_string": false, "label": "info", "time": **********.940344, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: After minimal Windows cleanup - Email body length: 7260", "message_html": null, "is_string": false, "label": "info", "time": **********.940708, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: Template 44: Using content directly from editor with minimal cleanup", "message_html": null, "is_string": false, "label": "info", "time": **********.941043, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: === FINAL CONTENT READY FOR SAVE ===", "message_html": null, "is_string": false, "label": "info", "time": **********.941381, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: Final email body length: 7260", "message_html": null, "is_string": false, "label": "info", "time": **********.941703, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: Final email body preview (first 300 chars): <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Account Verification Required</title>\n\n\n\n\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Account Verification Required</title", "message_html": null, "is_string": false, "label": "info", "time": **********.942022, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: Template 44: Skipping all corruption detection and complex processing to prevent issues", "message_html": null, "is_string": false, "label": "info", "time": **********.942338, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: === DATABASE SAVE OPERATION DEBUG ===", "message_html": null, "is_string": false, "label": "info", "time": **********.942659, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: Before save - Template email_body length: 7355", "message_html": null, "is_string": false, "label": "info", "time": **********.94302, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: Before save - New email_body length: 7260", "message_html": null, "is_string": false, "label": "info", "time": **********.943346, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: Before save - Template dirty: []", "message_html": null, "is_string": false, "label": "info", "time": **********.943699, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: After setting fields - Template dirty: {\"email_body\":\"<meta charset=\\\"UTF-8\\\">\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\">\\n    <title>Account Verification Required<\\/title>\\n\\n\\n\\n\\n    <meta charset=\\\"UTF-8\\\">\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\">\\n    <title>Account Verification Required<\\/title>\\n\\n\\n    <meta charset=\\\"UTF-8\\\">\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\">\\n    <title>Account Verification Required<\\/title>\\n\\n\\n    <meta charset=\\\"UTF-8\\\">\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\">\\n    <title>Account Verification Required<\\/title>\\n\\n\\n    <!-- Full Width Container -->\\n    <table width=\\\"100%\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" border=\\\"0\\\" style=\\\"background-color: rgb(244, 244, 244); width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\\\">\\n        <tbody><tr>\\n            <td align=\\\"center\\\" style=\\\"font-family: Arial, sans-serif; vertical-align: top; padding: 10px;\\\">\\n                <!-- Email Container -->\\n                <table width=\\\"600\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" border=\\\"0\\\" style=\\\"background-color: rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 10px; width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\\\">\\n\\n                    <!-- Header Banner - Full Width -->\\n                    <tbody><tr>\\n                        <td width=\\\"100%\\\" style=\\\"background: linear-gradient(135deg, rgb(220, 53, 69) 0%, rgb(200, 35, 51) 100%); padding: 20px 0px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\\\">\\n                            <h1 style=\\\"margin: 20px 0px 10px; font-size: 24px; font-weight: bold; color: rgb(51, 51, 51); font-family: Arial, sans-serif;\\\">Account Verification<\\/h1>\\n                        <\\/td>\\n                    <\\/tr>\\n\\n                    <!-- Logo Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: rgb(255, 255, 255); text-align: center; padding: 20px; border-bottom: 2px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\\\">\\n                            <img src=\\\"https:\\/\\/mbf.mybrokerforex.com\\/assets\\/images\\/logoIcon\\/logo.png\\\" alt=\\\"MBFX\\\" style=\\\"height: auto; width: auto; display: block; margin: 0px auto; max-width: 100%; border: 0px; outline: none; font-family: Arial, sans-serif;\\\" onerror=\\\"this.style.display='none'\\\">\\n                        <\\/td>\\n                    <\\/tr>\\n\\n                    <!-- Title Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: rgb(255, 255, 255); padding: 30px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\\\">\\n                            <h2 style=\\\"margin: 20px 0px 10px; color: rgb(51, 51, 51); font-size: 28px; font-weight: bold; font-family: Arial, sans-serif;\\\">Account Verification Requireded<\\/h2>\\n                        <\\/td>\\n                    <\\/tr>\\n\\n                    <!-- Description Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: rgb(255, 255, 255); padding: 0px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\\\">\\n                            <p style=\\\"margin: 10px 0px; color: rgb(108, 117, 125); font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\\\">Please verify your account to continue using our services.<\\/p>\\n                        <\\/td>\\n                    <\\/tr>\\n\\n                    <!-- Main Content Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: rgb(255, 255, 255); padding: 20px 40px; color: rgb(51, 51, 51); font-family: Arial, sans-serif; vertical-align: top;\\\">\\n                            <p style=\\\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\\\">Dear {{fullname}},<\\/p><p style=\\\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\\\">Your account requires verification to continue using our services.<\\/p><ul><li><strong>Account Status:<\\/strong> Verification Required<\\/li><li><strong>Required Actions:<\\/strong> Complete KYC verification<\\/li><li><br><\\/li><li><span style=\\\"font-size: 0.75rem; font-weight: 400; font-family: Arial, sans-serif;\\\">Please complete the verification process to maintain full account access.<\\/span><\\/li><\\/ul>\\n                        <\\/td>\\n                    <\\/tr>\\n\\n                    <!-- Regards Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: rgb(255, 255, 255); padding: 30px 40px; border-top: 1px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\\\">\\n                            <p style=\\\"margin: 10px 0px; font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\\\">Best regards,<br>\\n                            <strong>MBFX Team<\\/strong><\\/p>\\n                            <p style=\\\"font-size: 12px; color: rgb(108, 117, 125); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\\\">\\n                                If you have any questions, please contact our support team.\\n                            <\\/p>\\n                        <\\/td>\\n                    <\\/tr>\\n\\n                    <!-- Footer Section - Full Width -->\\n                    <tr>\\n                        <td width=\\\"100%\\\" style=\\\"background: linear-gradient(135deg, rgb(52, 58, 64) 0%, rgb(35, 39, 43) 100%); color: rgb(255, 255, 255); padding: 30px 40px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\\\">\\n                            <p style=\\\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\\\"><strong>MBFX<\\/strong> - Professional Trading Platform<\\/p>\\n                            <p style=\\\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\\\">\\n                                <a href=\\\"{{site_url}}\\/user\\/profile\\/setting\\\" style=\\\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\\\">Account Settings<\\/a> |\\n                                <a href=\\\"{{site_url}}\\/contact\\\" style=\\\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\\\">Contact Support<\\/a> |\\n                                <a href=\\\"{{site_url}}\\/policy\\/privacy-policy\\/99\\\" style=\\\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\\\">Privacy Policy<\\/a>\\n                            <\\/p>\\n                            <p style=\\\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\\\">\\n                                \\u00a9 2025 MBFX. All rights reserved.\\n                            <\\/p>\\n                            <p style=\\\"font-size: 10px; color: rgb(153, 153, 153); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\\\">\\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\\n                                <a href=\\\"{{site_url}}\\/user\\/profile\\/setting\\\" style=\\\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\\\">update your preferences<\\/a>.\\n                            <\\/p>\\n                        <\\/td>\\n                    <\\/tr>\\n\\n                <\\/tbody><\\/table>\\n            <\\/td>\\n        <\\/tr>\\n    <\\/tbody><\\/table>\"}", "message_html": null, "is_string": false, "label": "info", "time": **********.944984, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: After setting fields - Template email_body length: 7260", "message_html": null, "is_string": false, "label": "info", "time": **********.945382, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: Save operation result: SUCCESS", "message_html": null, "is_string": false, "label": "info", "time": **********.959336, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: After refresh - Template email_body length: 7260", "message_html": null, "is_string": false, "label": "info", "time": **********.966671, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: After refresh - Content matches: YES", "message_html": null, "is_string": false, "label": "info", "time": **********.967055, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: ✅ Template 44: Database operation completed", "message_html": null, "is_string": false, "label": "info", "time": **********.967414, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: === TEMPLATE UPDATE DEBUG END ===", "message_html": null, "is_string": false, "label": "info", "time": **********.967764, "xdebug_link": null, "collector": "log"}, {"message": "[09:30:34] LOG.info: 📤 Returning AJAX response", "message_html": null, "is_string": false, "label": "info", "time": **********.968116, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.168248, "end": **********.983774, "duration": 0.815526008605957, "duration_str": "816ms", "measures": [{"label": "Booting", "start": **********.168248, "relative_start": 0, "end": **********.727751, "relative_end": **********.727751, "duration": 0.****************, "duration_str": "560ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.727773, "relative_start": 0.****************, "end": **********.983777, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "256ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.78353, "relative_start": 0.****************, "end": **********.794494, "relative_end": **********.794494, "duration": 0.010963916778564453, "duration_str": "10.96ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.977415, "relative_start": 0.****************, "end": **********.978244, "relative_end": **********.978244, "duration": 0.0008289813995361328, "duration_str": "829μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.12", "Environment": "production", "Debug Mode": "Enabled", "URL": "localhost/mbf.mybrokerforex.com-********", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 5, "nb_statements": 4, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.042050000000000004, "accumulated_duration_str": "42.05ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "vendor/beyondcode/laravel-query-detector/src/QueryDetector.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetector.php", "line": 25}, {"index": 10, "namespace": null, "name": "vendor/beyondcode/laravel-query-detector/src/QueryDetectorMiddleware.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetectorMiddleware.php", "line": 30}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21}], "start": **********.781992, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "QueryDetector.php:25", "source": {"index": 9, "namespace": null, "name": "vendor/beyondcode/laravel-query-detector/src/QueryDetector.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetector.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fvendor%2Fbeyondcode%2Flaravel-query-detector%2Fsrc%2FQueryDetector.php&line=25", "ajax": false, "filename": "QueryDetector.php", "line": "25"}, "connection": "mbf-db", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Middleware\\RedirectIfNotAdmin.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.851779, "duration": 0.03377, "duration_str": "33.77ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mbf-db", "explain": null, "start_percent": 0, "width_percent": 80.309}, {"sql": "select * from `notification_templates` where `notification_templates`.`id` = '44' limit 1", "type": "query", "params": [], "bindings": ["44"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/NotificationController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\NotificationController.php", "line": 122}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.930311, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "NotificationController.php:122", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/NotificationController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\NotificationController.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FNotificationController.php&line=122", "ajax": false, "filename": "NotificationController.php", "line": "122"}, "connection": "mbf-db", "explain": null, "start_percent": 80.309, "width_percent": 2.378}, {"sql": "update `notification_templates` set `email_body` = '<meta charset=\\\"UTF-8\\\">\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\">\\n    <title>Account Verification Required</title>\\n\\n\\n\\n\\n    <meta charset=\\\"UTF-8\\\">\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\">\\n    <title>Account Verification Required</title>\\n\\n\\n    <meta charset=\\\"UTF-8\\\">\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\">\\n    <title>Account Verification Required</title>\\n\\n\\n    <meta charset=\\\"UTF-8\\\">\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\">\\n    <title>Account Verification Required</title>\\n\\n\\n    <!-- Full Width Container -->\\n    <table width=\\\"100%\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" border=\\\"0\\\" style=\\\"background-color: rgb(244, 244, 244); width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\\\">\\n        <tbody><tr>\\n            <td align=\\\"center\\\" style=\\\"font-family: Arial, sans-serif; vertical-align: top; padding: 10px;\\\">\\n                <!-- Email Container -->\\n                <table width=\\\"600\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" border=\\\"0\\\" style=\\\"background-color: rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 10px; width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\\\">\\n\\n                    <!-- Header Banner - Full Width -->\\n                    <tbody><tr>\\n                        <td width=\\\"100%\\\" style=\\\"background: linear-gradient(135deg, rgb(220, 53, 69) 0%, rgb(200, 35, 51) 100%); padding: 20px 0px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\\\">\\n                            <h1 style=\\\"margin: 20px 0px 10px; font-size: 24px; font-weight: bold; color: rgb(51, 51, 51); font-family: Arial, sans-serif;\\\">Account Verification</h1>\\n                        </td>\\n                    </tr>\\n\\n                    <!-- Logo Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: rgb(255, 255, 255); text-align: center; padding: 20px; border-bottom: 2px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\\\">\\n                            <img src=\\\"https://mbf.mybrokerforex.com/assets/images/logoIcon/logo.png\\\" alt=\\\"MBFX\\\" style=\\\"height: auto; width: auto; display: block; margin: 0px auto; max-width: 100%; border: 0px; outline: none; font-family: Arial, sans-serif;\\\" onerror=\\\"this.style.display=\\'none\\'\\\">\\n                        </td>\\n                    </tr>\\n\\n                    <!-- Title Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: rgb(255, 255, 255); padding: 30px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\\\">\\n                            <h2 style=\\\"margin: 20px 0px 10px; color: rgb(51, 51, 51); font-size: 28px; font-weight: bold; font-family: Arial, sans-serif;\\\">Account Verification Requireded</h2>\\n                        </td>\\n                    </tr>\\n\\n                    <!-- Description Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: rgb(255, 255, 255); padding: 0px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\\\">\\n                            <p style=\\\"margin: 10px 0px; color: rgb(108, 117, 125); font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\\\">Please verify your account to continue using our services.</p>\\n                        </td>\\n                    </tr>\\n\\n                    <!-- Main Content Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: rgb(255, 255, 255); padding: 20px 40px; color: rgb(51, 51, 51); font-family: Arial, sans-serif; vertical-align: top;\\\">\\n                            <p style=\\\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\\\">Dear {{fullname}},</p><p style=\\\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\\\">Your account requires verification to continue using our services.</p><ul><li><strong>Account Status:</strong> Verification Required</li><li><strong>Required Actions:</strong> Complete KYC verification</li><li><br></li><li><span style=\\\"font-size: 0.75rem; font-weight: 400; font-family: Arial, sans-serif;\\\">Please complete the verification process to maintain full account access.</span></li></ul>\\n                        </td>\\n                    </tr>\\n\\n                    <!-- Regards Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: rgb(255, 255, 255); padding: 30px 40px; border-top: 1px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\\\">\\n                            <p style=\\\"margin: 10px 0px; font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\\\">Best regards,<br>\\n                            <strong>MBFX Team</strong></p>\\n                            <p style=\\\"font-size: 12px; color: rgb(108, 117, 125); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\\\">\\n                                If you have any questions, please contact our support team.\\n                            </p>\\n                        </td>\\n                    </tr>\\n\\n                    <!-- Footer Section - Full Width -->\\n                    <tr>\\n                        <td width=\\\"100%\\\" style=\\\"background: linear-gradient(135deg, rgb(52, 58, 64) 0%, rgb(35, 39, 43) 100%); color: rgb(255, 255, 255); padding: 30px 40px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\\\">\\n                            <p style=\\\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\\\"><strong>MBFX</strong> - Professional Trading Platform</p>\\n                            <p style=\\\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\\\">\\n                                <a href=\\\"{{site_url}}/user/profile/setting\\\" style=\\\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\\\">Account Settings</a> |\\n                                <a href=\\\"{{site_url}}/contact\\\" style=\\\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\\\">Contact Support</a> |\\n                                <a href=\\\"{{site_url}}/policy/privacy-policy/99\\\" style=\\\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\\\">Privacy Policy</a>\\n                            </p>\\n                            <p style=\\\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\\\">\\n                                © 2025 MBFX. All rights reserved.\\n                            </p>\\n                            <p style=\\\"font-size: 10px; color: rgb(153, 153, 153); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\\\">\\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\\n                                <a href=\\\"{{site_url}}/user/profile/setting\\\" style=\\\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\\\">update your preferences</a>.\\n                            </p>\\n                        </td>\\n                    </tr>\\n\\n                </tbody></table>\\n            </td>\\n        </tr>\\n    </tbody></table>', `notification_templates`.`updated_at` = '2025-07-01 09:30:34' where `id` = 44", "type": "query", "params": [], "bindings": ["<meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Account Verification Required</title>\n\n\n\n\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Account Verification Required</title>\n\n\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Account Verification Required</title>\n\n\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Account Verification Required</title>\n\n\n    <!-- Full Width Container -->\n    <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: rgb(244, 244, 244); width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\">\n        <tbody><tr>\n            <td align=\"center\" style=\"font-family: Arial, sans-serif; vertical-align: top; padding: 10px;\">\n                <!-- Email Container -->\n                <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 10px; width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;\">\n\n                    <!-- Header Banner - Full Width -->\n                    <tbody><tr>\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, rgb(220, 53, 69) 0%, rgb(200, 35, 51) 100%); padding: 20px 0px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\n                            <h1 style=\"margin: 20px 0px 10px; font-size: 24px; font-weight: bold; color: rgb(51, 51, 51); font-family: Arial, sans-serif;\">Account Verification</h1>\n                        </td>\n                    </tr>\n\n                    <!-- Logo Section -->\n                    <tr>\n                        <td style=\"background-color: rgb(255, 255, 255); text-align: center; padding: 20px; border-bottom: 2px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\">\n                            <img src=\"https://mbf.mybrokerforex.com/assets/images/logoIcon/logo.png\" alt=\"MBFX\" style=\"height: auto; width: auto; display: block; margin: 0px auto; max-width: 100%; border: 0px; outline: none; font-family: Arial, sans-serif;\" onerror=\"this.style.display='none'\">\n                        </td>\n                    </tr>\n\n                    <!-- Title Section -->\n                    <tr>\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 30px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\n                            <h2 style=\"margin: 20px 0px 10px; color: rgb(51, 51, 51); font-size: 28px; font-weight: bold; font-family: Arial, sans-serif;\">Account Verification Requireded</h2>\n                        </td>\n                    </tr>\n\n                    <!-- Description Section -->\n                    <tr>\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 0px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\n                            <p style=\"margin: 10px 0px; color: rgb(108, 117, 125); font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\">Please verify your account to continue using our services.</p>\n                        </td>\n                    </tr>\n\n                    <!-- Main Content Section -->\n                    <tr>\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 20px 40px; color: rgb(51, 51, 51); font-family: Arial, sans-serif; vertical-align: top;\">\n                            <p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">Dear {{fullname}},</p><p style=\"font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;\">Your account requires verification to continue using our services.</p><ul><li><strong>Account Status:</strong> Verification Required</li><li><strong>Required Actions:</strong> Complete KYC verification</li><li><br></li><li><span style=\"font-size: 0.75rem; font-weight: 400; font-family: Arial, sans-serif;\">Please complete the verification process to maintain full account access.</span></li></ul>\n                        </td>\n                    </tr>\n\n                    <!-- Regards Section -->\n                    <tr>\n                        <td style=\"background-color: rgb(255, 255, 255); padding: 30px 40px; border-top: 1px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;\">\n                            <p style=\"margin: 10px 0px; font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;\">Best regards,<br>\n                            <strong>MBFX Team</strong></p>\n                            <p style=\"font-size: 12px; color: rgb(108, 117, 125); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\">\n                                If you have any questions, please contact our support team.\n                            </p>\n                        </td>\n                    </tr>\n\n                    <!-- Footer Section - Full Width -->\n                    <tr>\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, rgb(52, 58, 64) 0%, rgb(35, 39, 43) 100%); color: rgb(255, 255, 255); padding: 30px 40px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;\">\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\"><strong>MBFX</strong> - Professional Trading Platform</p>\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\">\n                                <a href=\"{{site_url}}/user/profile/setting\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Account Settings</a> |\n                                <a href=\"{{site_url}}/contact\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Contact Support</a> |\n                                <a href=\"{{site_url}}/policy/privacy-policy/99\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">Privacy Policy</a>\n                            </p>\n                            <p style=\"margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;\">\n                                © 2025 MBFX. All rights reserved.\n                            </p>\n                            <p style=\"font-size: 10px; color: rgb(153, 153, 153); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;\">\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\n                                <a href=\"{{site_url}}/user/profile/setting\" style=\"color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;\">update your preferences</a>.\n                            </p>\n                        </td>\n                    </tr>\n\n                </tbody></table>\n            </td>\n        </tr>\n    </tbody></table>", "2025-07-01 09:30:34", 44], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/NotificationController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\NotificationController.php", "line": 215}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.946342, "duration": 0.00621, "duration_str": "6.21ms", "memory": 0, "memory_str": null, "filename": "NotificationController.php:215", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/NotificationController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\NotificationController.php", "line": 215}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FNotificationController.php&line=215", "ajax": false, "filename": "NotificationController.php", "line": "215"}, "connection": "mbf-db", "explain": null, "start_percent": 82.687, "width_percent": 14.768}, {"sql": "select * from `notification_templates` where `id` = 44 limit 1", "type": "query", "params": [], "bindings": [44], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/NotificationController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\NotificationController.php", "line": 219}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.959832, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "NotificationController.php:219", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/NotificationController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\NotificationController.php", "line": 219}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FNotificationController.php&line=219", "ajax": false, "filename": "NotificationController.php", "line": "219"}, "connection": "mbf-db", "explain": null, "start_percent": 97.455, "width_percent": 2.545}]}, "models": {"data": {"App\\Models\\NotificationTemplate": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FModels%2FNotificationTemplate.php&line=1", "ajax": false, "filename": "NotificationTemplate.php", "line": "?"}}, "App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/update/44", "action_name": "admin.setting.notification.template.update", "controller_action": "App\\Http\\Controllers\\Admin\\NotificationController@templateUpdate", "uri": "POST admin/notification/template/update/{id}", "controller": "App\\Http\\Controllers\\Admin\\NotificationController@templateUpdate<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FNotificationController.php&line=98\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin/notification", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FNotificationController.php&line=98\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/NotificationController.php:98-259</a>", "middleware": "web, admin", "duration": "812ms", "peak_memory": "30MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1737486950 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1737486950\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6wydTXTQETk9uM050OalbDmHEZPTqQskjidNxWTA</span>\"\n  \"<span class=sf-dump-key>template_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">44</span>\"\n  \"<span class=sf-dump-key>subject</span>\" => \"<span class=sf-dump-str title=\"45 characters\">Account Verification Required - Action Needed</span>\"\n  \"<span class=sf-dump-key>email_status</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>sms_status</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>sms_body</span>\" => \"<span class=sf-dump-str title=\"97 characters\">Account verification required. Please upload your documents in the KYC section of your dashboard.</span>\"\n  \"<span class=sf-dump-key>email_body_encoded</span>\" => \"<span class=sf-dump-str title=\"9680 characters\">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</span>\"\n  \"<span class=sf-dump-key>email_body</span>\" => \"\"\"\n    <span class=sf-dump-str title=\"7356 characters\">&lt;meta charset=&quot;UTF-8&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">    &lt;title&gt;Account Verification Required&lt;/title&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">    &lt;meta charset=&quot;UTF-8&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">    &lt;title&gt;Account Verification Required&lt;/title&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">    &lt;meta charset=&quot;UTF-8&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">    &lt;title&gt;Account Verification Required&lt;/title&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">    &lt;meta charset=&quot;UTF-8&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">    &lt;title&gt;Account Verification Required&lt;/title&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">    &lt;!-- Full Width Container --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">    &lt;table width=&quot;100%&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; style=&quot;background-color: rgb(244, 244, 244); width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">        &lt;tbody&gt;&lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">            &lt;td align=&quot;center&quot; style=&quot;font-family: Arial, sans-serif; vertical-align: top; padding: 10px;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                &lt;!-- Email Container --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; style=&quot;background-color: rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 10px; width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;!-- Header Banner - Full Width --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;tbody&gt;&lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                        &lt;td width=&quot;100%&quot; style=&quot;background: linear-gradient(135deg, rgb(220, 53, 69) 0%, rgb(200, 35, 51) 100%); padding: 20px 0px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;h1 style=&quot;margin: 20px 0px 10px; font-size: 24px; font-weight: bold; color: rgb(51, 51, 51); font-family: Arial, sans-serif;&quot;&gt;Account Verification&lt;/h1&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;!-- Logo Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                        &lt;td style=&quot;background-color: rgb(255, 255, 255); text-align: center; padding: 20px; border-bottom: 2px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;img src=&quot;https://mbf.mybrokerforex.com/assets/images/logoIcon/logo.png&quot; alt=&quot;MBFX&quot; style=&quot;height: auto; width: auto; display: block; margin: 0px auto; max-width: 100%; border: 0px; outline: none; font-family: Arial, sans-serif;&quot; onerror=&quot;this.style.display=&#039;none&#039;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;!-- Title Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                        &lt;td style=&quot;background-color: rgb(255, 255, 255); padding: 30px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;h2 style=&quot;margin: 20px 0px 10px; color: rgb(51, 51, 51); font-size: 28px; font-weight: bold; font-family: Arial, sans-serif;&quot;&gt;Account Verification Requireded&lt;/h2&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;!-- Description Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                        &lt;td style=&quot;background-color: rgb(255, 255, 255); padding: 0px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;p style=&quot;margin: 10px 0px; color: rgb(108, 117, 125); font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;&quot;&gt;Please verify your account to continue using our services.&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;!-- Main Content Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                        &lt;td style=&quot;background-color: rgb(255, 255, 255); padding: 20px 40px; color: rgb(51, 51, 51); font-family: Arial, sans-serif; vertical-align: top;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;p style=&quot;font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;&quot;&gt;Dear {{fullname}},&lt;/p&gt;&lt;p style=&quot;font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;&quot;&gt;Your account requires verification to continue using our services.&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Account Status:&lt;/strong&gt; Verification Required&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Required Actions:&lt;/strong&gt; Complete KYC verification&lt;/li&gt;&lt;li&gt;&lt;br&gt;&lt;/li&gt;&lt;li&gt;&lt;span style=&quot;font-size: 0.75rem; font-weight: 400; font-family: Arial, sans-serif;&quot;&gt;Please complete the verification process to maintain full account access.&lt;/span&gt;&lt;/li&gt;&lt;/ul&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;!-- Regards Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                        &lt;td style=&quot;background-color: rgb(255, 255, 255); padding: 30px 40px; border-top: 1px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;p style=&quot;margin: 10px 0px; font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;&quot;&gt;Best regards,&lt;br&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;strong&gt;MBFX Team&lt;/strong&gt;&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;p style=&quot;font-size: 12px; color: rgb(108, 117, 125); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                                If you have any questions, please contact our support team.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;!-- Footer Section - Full Width --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                        &lt;td width=&quot;100%&quot; style=&quot;background: linear-gradient(135deg, rgb(52, 58, 64) 0%, rgb(35, 39, 43) 100%); color: rgb(255, 255, 255); padding: 30px 40px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;p style=&quot;margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;&quot;&gt;&lt;strong&gt;MBFX&lt;/strong&gt; - Professional Trading Platform&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;p style=&quot;margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                                &lt;a href=&quot;{{site_url}}/user/profile/setting&quot; style=&quot;color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;&quot;&gt;Account Settings&lt;/a&gt; |<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                                &lt;a href=&quot;{{site_url}}/contact&quot; style=&quot;color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;&quot;&gt;Contact Support&lt;/a&gt; |<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                                &lt;a href=&quot;{{site_url}}/policy/privacy-policy/99&quot; style=&quot;color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;&quot;&gt;Privacy Policy&lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;p style=&quot;margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                                &#169; 2025 MBFX. All rights reserved.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;p style=&quot;font-size: 10px; color: rgb(153, 153, 153); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                                This email was sent to {{email}}. If you no longer wish to receive these emails,<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                                &lt;a href=&quot;{{site_url}}/user/profile/setting&quot; style=&quot;color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;&quot;&gt;update your preferences&lt;/a&gt;.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                &lt;/tbody&gt;&lt;/table&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">            &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">        &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">    &lt;/tbody&gt;&lt;/table&gt;</span>\n    \"\"\"\n  \"<span class=sf-dump-key>email_body_final</span>\" => \"\"\"\n    <span class=sf-dump-str title=\"7356 characters\">&lt;meta charset=&quot;UTF-8&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">    &lt;title&gt;Account Verification Required&lt;/title&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">    &lt;meta charset=&quot;UTF-8&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">    &lt;title&gt;Account Verification Required&lt;/title&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">    &lt;meta charset=&quot;UTF-8&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">    &lt;title&gt;Account Verification Required&lt;/title&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">    &lt;meta charset=&quot;UTF-8&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">    &lt;title&gt;Account Verification Required&lt;/title&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">    &lt;!-- Full Width Container --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">    &lt;table width=&quot;100%&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; style=&quot;background-color: rgb(244, 244, 244); width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">        &lt;tbody&gt;&lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">            &lt;td align=&quot;center&quot; style=&quot;font-family: Arial, sans-serif; vertical-align: top; padding: 10px;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                &lt;!-- Email Container --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; style=&quot;background-color: rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 10px; width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; margin: 0px; padding: 0px;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;!-- Header Banner - Full Width --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;tbody&gt;&lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                        &lt;td width=&quot;100%&quot; style=&quot;background: linear-gradient(135deg, rgb(220, 53, 69) 0%, rgb(200, 35, 51) 100%); padding: 20px 0px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;h1 style=&quot;margin: 20px 0px 10px; font-size: 24px; font-weight: bold; color: rgb(51, 51, 51); font-family: Arial, sans-serif;&quot;&gt;Account Verification&lt;/h1&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;!-- Logo Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                        &lt;td style=&quot;background-color: rgb(255, 255, 255); text-align: center; padding: 20px; border-bottom: 2px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;img src=&quot;https://mbf.mybrokerforex.com/assets/images/logoIcon/logo.png&quot; alt=&quot;MBFX&quot; style=&quot;height: auto; width: auto; display: block; margin: 0px auto; max-width: 100%; border: 0px; outline: none; font-family: Arial, sans-serif;&quot; onerror=&quot;this.style.display=&#039;none&#039;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;!-- Title Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                        &lt;td style=&quot;background-color: rgb(255, 255, 255); padding: 30px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;h2 style=&quot;margin: 20px 0px 10px; color: rgb(51, 51, 51); font-size: 28px; font-weight: bold; font-family: Arial, sans-serif;&quot;&gt;Account Verification Requireded&lt;/h2&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;!-- Description Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                        &lt;td style=&quot;background-color: rgb(255, 255, 255); padding: 0px 40px 20px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;p style=&quot;margin: 10px 0px; color: rgb(108, 117, 125); font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;&quot;&gt;Please verify your account to continue using our services.&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;!-- Main Content Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                        &lt;td style=&quot;background-color: rgb(255, 255, 255); padding: 20px 40px; color: rgb(51, 51, 51); font-family: Arial, sans-serif; vertical-align: top;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;p style=&quot;font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;&quot;&gt;Dear {{fullname}},&lt;/p&gt;&lt;p style=&quot;font-family: Arial, sans-serif; margin: 10px 0px; line-height: 1.6;&quot;&gt;Your account requires verification to continue using our services.&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Account Status:&lt;/strong&gt; Verification Required&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Required Actions:&lt;/strong&gt; Complete KYC verification&lt;/li&gt;&lt;li&gt;&lt;br&gt;&lt;/li&gt;&lt;li&gt;&lt;span style=&quot;font-size: 0.75rem; font-weight: 400; font-family: Arial, sans-serif;&quot;&gt;Please complete the verification process to maintain full account access.&lt;/span&gt;&lt;/li&gt;&lt;/ul&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;!-- Regards Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                        &lt;td style=&quot;background-color: rgb(255, 255, 255); padding: 30px 40px; border-top: 1px solid rgb(233, 236, 239); font-family: Arial, sans-serif; vertical-align: top;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;p style=&quot;margin: 10px 0px; font-size: 16px; font-family: Arial, sans-serif; line-height: 1.6;&quot;&gt;Best regards,&lt;br&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;strong&gt;MBFX Team&lt;/strong&gt;&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;p style=&quot;font-size: 12px; color: rgb(108, 117, 125); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                                If you have any questions, please contact our support team.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;!-- Footer Section - Full Width --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                        &lt;td width=&quot;100%&quot; style=&quot;background: linear-gradient(135deg, rgb(52, 58, 64) 0%, rgb(35, 39, 43) 100%); color: rgb(255, 255, 255); padding: 30px 40px; text-align: center; font-family: Arial, sans-serif; vertical-align: top;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;p style=&quot;margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;&quot;&gt;&lt;strong&gt;MBFX&lt;/strong&gt; - Professional Trading Platform&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;p style=&quot;margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                                &lt;a href=&quot;{{site_url}}/user/profile/setting&quot; style=&quot;color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;&quot;&gt;Account Settings&lt;/a&gt; |<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                                &lt;a href=&quot;{{site_url}}/contact&quot; style=&quot;color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;&quot;&gt;Contact Support&lt;/a&gt; |<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                                &lt;a href=&quot;{{site_url}}/policy/privacy-policy/99&quot; style=&quot;color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;&quot;&gt;Privacy Policy&lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;p style=&quot;margin: 10px 0px; font-size: 14px; font-family: Arial, sans-serif; line-height: 1.6;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                                &#169; 2025 MBFX. All rights reserved.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;p style=&quot;font-size: 10px; color: rgb(153, 153, 153); margin: 10px 0px; font-family: Arial, sans-serif; line-height: 1.6;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                                This email was sent to {{email}}. If you no longer wish to receive these emails,<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                                &lt;a href=&quot;{{site_url}}/user/profile/setting&quot; style=&quot;color: rgb(220, 53, 69); text-decoration: none; font-family: Arial, sans-serif;&quot;&gt;update your preferences&lt;/a&gt;.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">                &lt;/tbody&gt;&lt;/table&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">            &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">        &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"7356 characters\">    &lt;/tbody&gt;&lt;/table&gt;</span>\n    \"\"\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-568421671 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">25505</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryDvxlAJrepXOGHdj7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">https://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"84 characters\">https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/edit/44</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">en-US,en;q=0.9,ms-MY;q=0.8,ms;q=0.7,zh-CN;q=0.6,zh;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InVmS0NJWEZJNUMrbmFyd00rVDJ1cHc9PSIsInZhbHVlIjoiU0w2TndIcldKZS8zaVNpaVBlbTQ4QmRGUGhQZmhqait1aTFvVTYrSThjSU9jbm4rMzQ2OUdoRE1VMnMrRDFYblNqWVBoZ2F4Ym4wV25BaDV6cGpVd3paclF5cUNxZ0FXZjdQSTlRVXRTZFY5eUxoMkdyc09TZDJIeHMvcDFrd04iLCJtYWMiOiJjMjE5Nzg5MzVlM2E0MWJhZmY3YWM4NThkZWMyN2VmOTZjZDFmMzBkMjk1MzIxZDRkMjY5MzVjODJjYzRkNzVhIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Imp6ZjJtakVBOVcwRVhRdlhwaDlDR0E9PSIsInZhbHVlIjoiVk5RUytXdVpYUXlOZlIvelEwVU1IQk1BQ3ZLM0pjNStRODkxSUZXVllLVmxUaTZLK3dKeVVVTnlKZnNUblNQdFBWVm9sanZKK0k4aVcrdldJTlVsQnlRTGpvM2NsOHZoMENUSFZvbnhZejBGYVVhZFE0Vy9jd1liZzVYdldoVHYiLCJtYWMiOiI5ZmY0ZjcxZjFkOTlhYzJhZDg4ZGEzM2E4MzBiZTJhNmZmMjk3MGEwNzQ2YjYwZTQyZGRlNjU1YmZiOWFhYzI1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-568421671\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1722934925 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6wydTXTQETk9uM050OalbDmHEZPTqQskjidNxWTA</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pKwSOwBxhh7a0b12K8rA1re0Q1SyO815L9Vl6qQN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1722934925\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-138631091 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 09:30:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-138631091\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1207732909 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6wydTXTQETk9uM050OalbDmHEZPTqQskjidNxWTA</span>\"\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"84 characters\">https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/edit/44</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>flasher::envelopes</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>53630</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1207732909\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/update/44", "action_name": "admin.setting.notification.template.update", "controller_action": "App\\Http\\Controllers\\Admin\\NotificationController@templateUpdate"}, "badge": null}}