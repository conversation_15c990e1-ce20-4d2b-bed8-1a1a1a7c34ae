<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 COMPREHENSIVE IB DATABASE ANALYSIS\n";
echo "=====================================\n";

// Get all tables
$allTables = DB::select('SHOW TABLES');
$ibRelatedTables = [];

foreach ($allTables as $table) {
    $tableName = array_values((array)$table)[0];
    if (stripos($tableName, 'ib') !== false || 
        stripos($tableName, 'partner') !== false || 
        stripos($tableName, 'commission') !== false || 
        stripos($tableName, 'referral') !== false ||
        stripos($tableName, 'form') !== false) {
        $ibRelatedTables[] = $tableName;
    }
}

echo "📊 IB-Related Tables Found:\n";
foreach ($ibRelatedTables as $table) {
    echo "  - {$table}\n";
}

echo "\n🔍 DETAILED TABLE ANALYSIS:\n";
echo "=====================================\n";

foreach ($ibRelatedTables as $tableName) {
    echo "\n📋 TABLE: {$tableName}\n";
    echo str_repeat("-", 50) . "\n";
    
    try {
        // Get table structure
        $columns = DB::select("DESCRIBE {$tableName}");
        echo "📊 Columns:\n";
        foreach ($columns as $column) {
            echo "  - {$column->Field} ({$column->Type}) " . 
                 ($column->Null === 'YES' ? 'NULL' : 'NOT NULL') . 
                 ($column->Key ? " [{$column->Key}]" : '') . "\n";
        }
        
        // Get record count
        $count = DB::table($tableName)->count();
        echo "📈 Records: {$count}\n";
        
        // Show sample data if records exist
        if ($count > 0 && $count < 1000) {
            $sample = DB::table($tableName)->limit(3)->get();
            echo "📄 Sample Data:\n";
            foreach ($sample as $record) {
                $recordArray = (array)$record;
                $displayData = array_slice($recordArray, 0, 5, true); // First 5 columns
                foreach ($displayData as $key => $value) {
                    echo "    {$key}: " . (is_null($value) ? 'NULL' : substr($value, 0, 50)) . "\n";
                }
                echo "    ---\n";
                break; // Show only first record
            }
        }
        
    } catch (Exception $e) {
        echo "❌ Error analyzing table: " . $e->getMessage() . "\n";
    }
}

echo "\n🔗 TABLE RELATIONSHIPS ANALYSIS:\n";
echo "=====================================\n";

// Analyze users table IB fields
echo "📊 USERS TABLE IB FIELDS:\n";
try {
    $userColumns = DB::select("DESCRIBE users");
    $ibFields = [];
    foreach ($userColumns as $column) {
        if (stripos($column->Field, 'ib') !== false || 
            stripos($column->Field, 'partner') !== false ||
            stripos($column->Field, 'ref') !== false ||
            stripos($column->Field, 'commission') !== false) {
            $ibFields[] = $column->Field;
        }
    }
    
    foreach ($ibFields as $field) {
        echo "  - {$field}\n";
    }
    
    // Get IB statistics from users table
    echo "\n📈 IB STATISTICS:\n";
    $stats = [
        'total_users' => DB::table('users')->count(),
        'approved_ibs' => DB::table('users')->where('partner', 1)->count(),
        'pending_ibs' => DB::table('users')->where('partner', 2)->count(),
        'rejected_ibs' => DB::table('users')->where('partner', 3)->count(),
        'users_with_referrer' => DB::table('users')->whereNotNull('ref_by')->count(),
        'users_with_mt5' => DB::table('users')->whereNotNull('mt5_login')->count()
    ];
    
    foreach ($stats as $key => $value) {
        echo "  - " . ucwords(str_replace('_', ' ', $key)) . ": {$value}\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error analyzing users table: " . $e->getMessage() . "\n";
}

echo "\n🎯 TABLE USAGE RECOMMENDATIONS:\n";
echo "=====================================\n";

// Analyze each table's purpose and usage
$tableRecommendations = [
    'ib_commissions' => 'KEEP - Core commission tracking system',
    'ib_groups' => 'KEEP - IB group management',
    'ib_levels' => 'KEEP - Multi-level IB system',
    'formsIb' => 'ANALYZE - IB application forms (may be redundant)',
    'users' => 'KEEP - Core user table with IB fields'
];

foreach ($ibRelatedTables as $table) {
    if (isset($tableRecommendations[$table])) {
        echo "✅ {$table}: {$tableRecommendations[$table]}\n";
    } else {
        // Analyze table to determine if it's needed
        try {
            $count = DB::table($table)->count();
            $columns = DB::select("DESCRIBE {$table}");
            
            if ($count === 0) {
                echo "⚠️ {$table}: CONSIDER REMOVING - Empty table ({$count} records)\n";
            } elseif ($count < 10 && !in_array($table, ['ib_groups', 'ib_levels'])) {
                echo "⚠️ {$table}: REVIEW - Low usage ({$count} records)\n";
            } else {
                echo "✅ {$table}: KEEP - Active table ({$count} records)\n";
            }
        } catch (Exception $e) {
            echo "❌ {$table}: ERROR - Cannot analyze\n";
        }
    }
}

echo "\n🔍 FOREIGN KEY RELATIONSHIPS:\n";
echo "=====================================\n";

// Check for foreign key relationships
foreach ($ibRelatedTables as $table) {
    try {
        $foreignKeys = DB::select("
            SELECT 
                COLUMN_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
            WHERE TABLE_NAME = '{$table}' 
            AND REFERENCED_TABLE_NAME IS NOT NULL
        ");
        
        if (!empty($foreignKeys)) {
            echo "📋 {$table}:\n";
            foreach ($foreignKeys as $fk) {
                echo "  - {$fk->COLUMN_NAME} → {$fk->REFERENCED_TABLE_NAME}.{$fk->REFERENCED_COLUMN_NAME}\n";
            }
        }
    } catch (Exception $e) {
        // Ignore errors for foreign key analysis
    }
}

echo "\n✅ IB DATABASE ANALYSIS COMPLETE!\n";
