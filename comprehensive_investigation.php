<?php
/**
 * COMPREHENSIVE ROOT CAUSE ANALYSIS
 * This script performs a systematic investigation to identify the real issue
 */

// Include Laravel bootstrap
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🔍 COMPREHENSIVE ROOT CAUSE ANALYSIS\n";
echo "=====================================\n\n";

// 1. DATABASE INVESTIGATION
echo "1️⃣ DATABASE INVESTIGATION\n";
echo "==========================\n";

try {
    // Check database connection
    $connection = \DB::connection();
    echo "✅ Database Connection: " . $connection->getDriverName() . "\n";
    echo "✅ Database Name: " . $connection->getDatabaseName() . "\n";
    
    // Check notification_templates table structure
    $tableExists = \Schema::hasTable('notification_templates');
    echo "✅ notification_templates table exists: " . ($tableExists ? 'YES' : 'NO') . "\n";
    
    if ($tableExists) {
        // Get table schema
        $columns = \Schema::getColumnListing('notification_templates');
        echo "✅ Table columns: " . implode(', ', $columns) . "\n";
        
        // Check specific column details
        $emailBodyColumn = \DB::select("SHOW COLUMNS FROM notification_templates LIKE 'email_body'");
        if (!empty($emailBodyColumn)) {
            $column = $emailBodyColumn[0];
            echo "✅ email_body column type: " . $column->Type . "\n";
            echo "✅ email_body column null: " . $column->Null . "\n";
            echo "✅ email_body column default: " . ($column->Default ?? 'NULL') . "\n";
        }
        
        // Check for any constraints or triggers
        $constraints = \DB::select("
            SELECT CONSTRAINT_NAME, CONSTRAINT_TYPE 
            FROM information_schema.TABLE_CONSTRAINTS 
            WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'notification_templates'
        ", [$connection->getDatabaseName()]);
        
        echo "✅ Table constraints: " . (empty($constraints) ? 'NONE' : count($constraints)) . "\n";
        foreach ($constraints as $constraint) {
            echo "   - {$constraint->CONSTRAINT_NAME}: {$constraint->CONSTRAINT_TYPE}\n";
        }
        
        // Check current template data
        $template = \App\Models\NotificationTemplate::find(1);
        if ($template) {
            echo "✅ Template ID 1 found\n";
            echo "   - Subject: {$template->subj}\n";
            echo "   - Email body length: " . strlen($template->email_body) . "\n";
            echo "   - Updated at: {$template->updated_at}\n";
            echo "   - Has DOCTYPE: " . (str_contains($template->email_body, '<!DOCTYPE') ? 'YES' : 'NO') . "\n";
            echo "   - Has table structure: " . (str_contains($template->email_body, '<table') ? 'YES' : 'NO') . "\n";
        } else {
            echo "❌ Template ID 1 not found\n";
        }
    }
    
} catch (\Exception $e) {
    echo "❌ Database Error: " . $e->getMessage() . "\n";
}

echo "\n";

// 2. QUERY EXECUTION TEST
echo "2️⃣ QUERY EXECUTION TEST\n";
echo "========================\n";

try {
    // Enable query logging
    \DB::enableQueryLog();
    
    // Test direct update
    $template = \App\Models\NotificationTemplate::find(1);
    if ($template) {
        $originalContent = $template->email_body;
        $originalUpdatedAt = $template->updated_at;
        
        echo "📊 Before Update:\n";
        echo "   - Content length: " . strlen($originalContent) . "\n";
        echo "   - Updated at: {$originalUpdatedAt}\n";
        
        // Perform a test update
        $testContent = $originalContent . "\n<!-- Test update at " . date('Y-m-d H:i:s') . " -->";
        $template->email_body = $testContent;
        $template->subj = $template->subj . " [TEST]";
        
        // Check if model detects changes
        $dirtyFields = $template->getDirty();
        echo "📊 Dirty fields: " . json_encode(array_keys($dirtyFields)) . "\n";
        
        // Attempt save
        $saveResult = $template->save();
        echo "📊 Save result: " . ($saveResult ? 'SUCCESS' : 'FAILED') . "\n";
        
        // Get executed queries
        $queries = \DB::getQueryLog();
        echo "📊 Executed queries: " . count($queries) . "\n";
        foreach ($queries as $query) {
            echo "   - SQL: " . substr($query['query'], 0, 100) . "...\n";
            echo "   - Time: {$query['time']}ms\n";
        }
        
        // Verify changes
        $template->refresh();
        echo "📊 After Update:\n";
        echo "   - Content length: " . strlen($template->email_body) . "\n";
        echo "   - Updated at: {$template->updated_at}\n";
        echo "   - Content changed: " . ($template->email_body !== $originalContent ? 'YES' : 'NO') . "\n";
        echo "   - Timestamp changed: " . ($template->updated_at != $originalUpdatedAt ? 'YES' : 'NO') . "\n";
        
        // Restore original content
        $template->email_body = $originalContent;
        $template->subj = str_replace(' [TEST]', '', $template->subj);
        $template->save();
        echo "📊 Original content restored\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Query Execution Error: " . $e->getMessage() . "\n";
    echo "❌ File: " . $e->getFile() . "\n";
    echo "❌ Line: " . $e->getLine() . "\n";
}

echo "\n";

// 3. MODEL INVESTIGATION
echo "3️⃣ MODEL INVESTIGATION\n";
echo "=======================\n";

try {
    // Check model configuration
    $model = new \App\Models\NotificationTemplate();
    
    echo "✅ Model class: " . get_class($model) . "\n";
    echo "✅ Table name: " . $model->getTable() . "\n";
    echo "✅ Primary key: " . $model->getKeyName() . "\n";
    echo "✅ Timestamps: " . ($model->timestamps ? 'ENABLED' : 'DISABLED') . "\n";
    
    // Check fillable/guarded
    $fillable = $model->getFillable();
    $guarded = $model->getGuarded();
    
    echo "✅ Fillable fields: " . (empty($fillable) ? 'ALL (empty array)' : implode(', ', $fillable)) . "\n";
    echo "✅ Guarded fields: " . (empty($guarded) ? 'NONE' : implode(', ', $guarded)) . "\n";
    
    // Check if email_body is fillable
    $isEmailBodyFillable = $model->isFillable('email_body');
    echo "✅ email_body is fillable: " . ($isEmailBodyFillable ? 'YES' : 'NO') . "\n";
    
    // Check for any mutators/accessors
    $methods = get_class_methods($model);
    $mutators = array_filter($methods, function($method) {
        return str_starts_with($method, 'set') && str_ends_with($method, 'Attribute');
    });
    $accessors = array_filter($methods, function($method) {
        return str_starts_with($method, 'get') && str_ends_with($method, 'Attribute');
    });
    
    echo "✅ Mutators: " . (empty($mutators) ? 'NONE' : implode(', ', $mutators)) . "\n";
    echo "✅ Accessors: " . (empty($accessors) ? 'NONE' : implode(', ', $accessors)) . "\n";
    
} catch (\Exception $e) {
    echo "❌ Model Investigation Error: " . $e->getMessage() . "\n";
}

echo "\n";

// 4. FILE SYSTEM INVESTIGATION
echo "4️⃣ FILE SYSTEM INVESTIGATION\n";
echo "=============================\n";

$filesToCheck = [
    'app/Models/NotificationTemplate.php',
    'app/Http/Controllers/Admin/NotificationController.php',
    'resources/views/admin/notification/edit.blade.php',
    'assets/admin/js/visual-builder-email-editor.js',
    'storage/logs/laravel.log',
    'storage/framework/cache',
    'storage/framework/sessions',
    'storage/framework/views'
];

foreach ($filesToCheck as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        $size = is_file($file) ? filesize($file) : 'DIR';
        $modified = date('Y-m-d H:i:s', filemtime($file));
        echo "✅ {$file}: " . substr(sprintf('%o', $perms), -4) . " | {$size} bytes | {$modified}\n";
    } else {
        echo "❌ {$file}: NOT FOUND\n";
    }
}

echo "\n";

// 5. ENVIRONMENT INVESTIGATION
echo "5️⃣ ENVIRONMENT INVESTIGATION\n";
echo "=============================\n";

echo "✅ PHP Version: " . PHP_VERSION . "\n";
echo "✅ Laravel Version: " . app()->version() . "\n";
echo "✅ Environment: " . app()->environment() . "\n";
echo "✅ Debug Mode: " . (config('app.debug') ? 'ENABLED' : 'DISABLED') . "\n";
echo "✅ Database Driver: " . config('database.default') . "\n";
echo "✅ Cache Driver: " . config('cache.default') . "\n";
echo "✅ Session Driver: " . config('session.driver') . "\n";

// Check for any middleware that might interfere
echo "✅ Global Middleware: " . count(app('router')->getMiddleware()) . " registered\n";

echo "\n";

echo "📋 INVESTIGATION COMPLETE\n";
echo "=========================\n";
echo "Review the results above to identify:\n";
echo "1. Database connectivity and schema issues\n";
echo "2. Query execution problems\n";
echo "3. Model configuration issues\n";
echo "4. File permission problems\n";
echo "5. Environment configuration issues\n\n";

echo "🔍 Next step: Run this script and analyze the output to identify the root cause.\n";

?>
