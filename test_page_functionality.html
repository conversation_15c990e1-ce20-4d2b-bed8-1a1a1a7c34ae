<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Template System - Functionality Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .test-container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .test-section h3 { color: #dc3545; margin-top: 0; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .pass { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .fail { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .test-button { background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .test-button:hover { background: #c82333; }
        .test-log { background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 10px 0; max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        iframe { width: 100%; height: 400px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Email Template System - Functionality Test</h1>
        <p><strong>Testing Date:</strong> <span id="testDate"></span></p>
        
        <!-- Test Section 1: Page Loading Test -->
        <div class="test-section">
            <h3>1. Page Loading & JavaScript Execution Test</h3>
            <p>Testing if pages load correctly and JavaScript executes without errors:</p>
            
            <button class="test-button" onclick="testPageLoad('global')">Test Global Template</button>
            <button class="test-button" onclick="testPageLoad('edit1')">Test Edit Template #1</button>
            <button class="test-button" onclick="testPageLoad('edit42')">Test Edit Template #42</button>
            <button class="test-button" onclick="testPageLoad('templates')">Test Templates List</button>
            
            <div id="pageLoadResults" class="test-log"></div>
        </div>

        <!-- Test Section 2: JavaScript Function Test -->
        <div class="test-section">
            <h3>2. JavaScript Function Availability Test</h3>
            <p>Testing if key JavaScript functions are available in loaded pages:</p>
            
            <button class="test-button" onclick="testJavaScriptFunctions()">Test JavaScript Functions</button>
            
            <div id="jsFunctionResults" class="test-log"></div>
        </div>

        <!-- Test Section 3: Form Submission Test -->
        <div class="test-section">
            <h3>3. Form Submission Test</h3>
            <p>Testing form submission functionality:</p>
            
            <button class="test-button" onclick="testFormSubmission()">Test Form Submission</button>
            
            <div id="formSubmissionResults" class="test-log"></div>
        </div>

        <!-- Test Section 4: Live Page Test -->
        <div class="test-section">
            <h3>4. Live Page Test</h3>
            <p>Load actual pages in iframe to test real functionality:</p>
            
            <button class="test-button" onclick="loadPageInIframe('global')">Load Global Template</button>
            <button class="test-button" onclick="loadPageInIframe('edit1')">Load Edit Template #1</button>
            <button class="test-button" onclick="loadPageInIframe('templates')">Load Templates List</button>
            
            <div id="iframeContainer">
                <iframe id="testIframe" src="about:blank"></iframe>
            </div>
            
            <div id="iframeResults" class="test-log"></div>
        </div>
    </div>

    <script>
        // Set test date
        document.getElementById('testDate').textContent = new Date().toLocaleString();

        // Test URLs
        const testUrls = {
            'global': 'https://localhost/mbf.mybrokerforex.com-31052025/admin/notification/global',
            'edit1': 'https://localhost/mbf.mybrokerforex.com-31052025/admin/notification/template/edit/1',
            'edit42': 'https://localhost/mbf.mybrokerforex.com-31052025/admin/notification/template/edit/42',
            'templates': 'https://localhost/mbf.mybrokerforex.com-31052025/admin/notification/templates'
        };

        // Test page loading
        function testPageLoad(pageType) {
            const resultsDiv = document.getElementById('pageLoadResults');
            const url = testUrls[pageType];
            
            resultsDiv.innerHTML += `<div>Testing ${pageType}: ${url}</div>`;
            
            fetch(url, { 
                method: 'GET',
                mode: 'no-cors' // To avoid CORS issues
            })
            .then(response => {
                resultsDiv.innerHTML += `<div class="pass">✅ ${pageType}: Page accessible</div>`;
            })
            .catch(error => {
                resultsDiv.innerHTML += `<div class="fail">❌ ${pageType}: Error - ${error.message}</div>`;
            });
        }

        // Test JavaScript functions
        function testJavaScriptFunctions() {
            const resultsDiv = document.getElementById('jsFunctionResults');
            resultsDiv.innerHTML = '<div>Testing JavaScript function availability...</div>';
            
            // Test if jQuery is available
            if (typeof $ !== 'undefined') {
                resultsDiv.innerHTML += '<div class="pass">✅ jQuery is available</div>';
            } else {
                resultsDiv.innerHTML += '<div class="fail">❌ jQuery is not available</div>';
            }
            
            // Test basic JavaScript functionality
            try {
                const testFunction = function() { return 'test'; };
                if (testFunction() === 'test') {
                    resultsDiv.innerHTML += '<div class="pass">✅ Basic JavaScript execution works</div>';
                }
            } catch (error) {
                resultsDiv.innerHTML += '<div class="fail">❌ JavaScript execution error: ' + error.message + '</div>';
            }
        }

        // Test form submission
        function testFormSubmission() {
            const resultsDiv = document.getElementById('formSubmissionResults');
            resultsDiv.innerHTML = '<div>Testing form submission functionality...</div>';
            
            // Create a test form
            const testForm = document.createElement('form');
            testForm.innerHTML = '<input type="hidden" name="test" value="test">';
            
            // Test form creation
            if (testForm) {
                resultsDiv.innerHTML += '<div class="pass">✅ Form creation works</div>';
            }
            
            // Test form event handling
            try {
                $(testForm).on('submit', function(e) {
                    e.preventDefault();
                    resultsDiv.innerHTML += '<div class="pass">✅ Form event handling works</div>';
                });
                
                // Trigger the event
                $(testForm).trigger('submit');
            } catch (error) {
                resultsDiv.innerHTML += '<div class="fail">❌ Form event error: ' + error.message + '</div>';
            }
        }

        // Load page in iframe
        function loadPageInIframe(pageType) {
            const iframe = document.getElementById('testIframe');
            const resultsDiv = document.getElementById('iframeResults');
            const url = testUrls[pageType];
            
            resultsDiv.innerHTML += `<div>Loading ${pageType} in iframe...</div>`;
            
            iframe.onload = function() {
                try {
                    // Try to access iframe content
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    if (iframeDoc) {
                        resultsDiv.innerHTML += `<div class="pass">✅ ${pageType}: Page loaded successfully in iframe</div>`;
                        
                        // Check for JavaScript errors in the iframe
                        const scripts = iframeDoc.getElementsByTagName('script');
                        resultsDiv.innerHTML += `<div>Found ${scripts.length} script tags in ${pageType}</div>`;
                    }
                } catch (error) {
                    resultsDiv.innerHTML += `<div class="fail">❌ ${pageType}: Cannot access iframe content - ${error.message}</div>`;
                }
            };
            
            iframe.onerror = function() {
                resultsDiv.innerHTML += `<div class="fail">❌ ${pageType}: Failed to load in iframe</div>`;
            };
            
            iframe.src = url;
        }

        // Auto-run basic tests
        setTimeout(function() {
            testJavaScriptFunctions();
        }, 1000);
    </script>
</body>
</html>
