{{-- User Registration Page - Copied from Admin Login Template --}}
@extends($activeTemplate.'layouts.app')
@section('main-content')
<div class="professional-admin-login">
    <div class="login-container">
        <div class="login-card">
            {{-- Header Section --}}
            <div class="login-header">
                <div class="logo-section">
                    <img src="{{ asset('assets/images/logoIcon/logo.png') }}" alt="{{ $general->site_name }}" class="login-logo">
                </div>
                <div class="welcome-section">
                    <h1 class="login-title">@lang('Create Account')</h1>
                    <p class="login-subtitle">@lang('Join') {{ __($general->site_name) }} @lang('and start trading today')</p>
                </div>
            </div>

            {{-- Registration Form Section --}}
            <div class="login-form-section">
                <form action="{{ route('user.register') }}" method="POST" class="professional-login-form verify-gcaptcha">
                    @csrf

                    {{-- First Name and Last Name in Same Row --}}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-user me-2"></i>@lang('First Name') <span class="text-danger">*</span>
                                </label>
                                <div class="input-wrapper">
                                    <input type="text" class="form-control" value="{{ old('firstname') }}"
                                           name="firstname" required placeholder="@lang('Enter your first name')" autocomplete="given-name">
                                    <div class="input-icon">
                                        <i class="fas fa-user"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-user me-2"></i>@lang('Last Name') <span class="text-danger">*</span>
                                </label>
                                <div class="input-wrapper">
                                    <input type="text" class="form-control" value="{{ old('lastname') }}"
                                           name="lastname" required placeholder="@lang('Enter your last name')" autocomplete="family-name">
                                    <div class="input-icon">
                                        <i class="fas fa-user"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- Email and Referral Code in Same Row --}}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-envelope me-2"></i>@lang('Email Address') <span class="text-danger">*</span>
                                </label>
                                <div class="input-wrapper">
                                    <input type="email" class="form-control" value="{{ old('email') }}"
                                           name="email" required placeholder="@lang('Enter your email address')" autocomplete="email">
                                    <div class="input-icon">
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-users me-2"></i>@lang('Referral Code') <span class="text-muted">(@lang('Optional'))</span>
                                </label>
                                <div class="input-wrapper">
                                    <input type="text" class="form-control" value="{{ old('referBy', request()->get('ref')) }}"
                                           name="referBy" placeholder="@lang('Enter referral code if you have one')" autocomplete="off">
                                    <div class="input-icon">
                                        <i class="fas fa-users"></i>
                                    </div>
                                </div>
                                <small class="text-muted">@lang('If someone referred you, enter their referral code here')</small>
                            </div>
                        </div>
                    </div>

                    {{-- Username with Availability Check --}}
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-user-shield me-2"></i>@lang('Username') <span class="text-danger">*</span>
                                </label>
                                <div class="input-wrapper">
                                    <input type="text" class="form-control" value="{{ old('username') }}"
                                           name="username" required placeholder="@lang('Choose a unique username')" autocomplete="username" id="username-input">
                                    <div class="input-icon">
                                        <i class="fas fa-user-shield"></i>
                                    </div>
                                    <div class="username-status" id="username-status"></div>
                                </div>
                                <small class="text-muted">@lang('Choose a unique username for your account. Leave blank to auto-generate from your first name.')</small>
                            </div>
                        </div>
                    </div>

                    {{-- Country and Mobile in Same Row --}}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-globe me-2"></i>@lang('Country') <span class="text-danger">*</span>
                                </label>
                                <div class="input-wrapper">
                                    <select name="country" class="form-control" required>
                                        <option value="">@lang('Select Country')</option>
                                        @foreach($countries as $key => $country)
                                            <option data-mobile_code="{{ $country->dial_code }}" value="{{ $country->country }}" data-code="{{ $key }}" {{ old('country') == $country->country ? 'selected' : '' }}>{{ __($country->country) }}</option>
                                        @endforeach
                                    </select>
                                    <div class="input-icon">
                                        <i class="fas fa-globe"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-phone me-2"></i>@lang('Mobile Number') <span class="text-danger">*</span>
                                </label>
                                <div class="input-wrapper">
                                    <div class="input-group">
                                        <span class="input-group-text mobile-code"></span>
                                        <input type="hidden" name="mobile_code">
                                        <input type="hidden" name="country_code">
                                        <input type="number" name="mobile" value="{{ old('mobile') }}" class="form-control" placeholder="@lang('Your Phone Number')" required>
                                    </div>
                                    <div class="input-icon">
                                        <i class="fas fa-phone"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- Password and Confirm Password in Same Row --}}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-lock me-2"></i>@lang('Password') <span class="text-danger">*</span>
                                </label>
                                <div class="input-wrapper">
                                    <input type="password" class="form-control" name="password" required
                                           placeholder="@lang('Create a strong password')" autocomplete="new-password">
                                    <div class="input-icon">
                                        <i class="fas fa-shield-alt"></i>
                                    </div>
                                    <button type="button" class="password-toggle" onclick="toggleAdminPassword()">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-lock me-2"></i>@lang('Confirm Password') <span class="text-danger">*</span>
                                </label>
                                <div class="input-wrapper">
                                    <input type="password" class="form-control" name="password_confirmation" required
                                           placeholder="@lang('Confirm your password')" autocomplete="new-password">
                                    <div class="input-icon">
                                        <i class="fas fa-shield-alt"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- Captcha --}}
                    <div class="form-group">
                        <x-captcha />
                    </div>

                    @if($general->agree)
                    {{-- Terms & Conditions --}}
                    <div class="form-options">
                        <div class="remember-section">
                            <input class="form-check-input" name="agree" type="checkbox" id="agree" required>
                            <label class="form-check-label" for="agree">
                                @lang('I agree to the') <a href="#" class="forgot-link">@lang('Terms & Conditions')</a>
                            </label>
                        </div>
                    </div>
                    @endif

                    {{-- Submit Button --}}
                    <button type="submit" class="login-submit-btn">
                        <i class="fas fa-user-plus me-2"></i>@lang('Create Account')
                    </button>
                </form>

                {{-- Login Link --}}
                <div class="register-link-section">
                    <p class="register-text">@lang('Already have an account?')</p>
                    <a href="{{ route('user.login') }}" class="register-link">
                        <i class="fas fa-sign-in-alt me-1"></i>@lang('Sign In')
                    </a>
                </div>
            </div>

            {{-- Security Notice --}}
            <div class="security-notice">
                <div class="security-icon">
                    <i class="fas fa-shield-check"></i>
                </div>
                <div class="security-text">
                    <small>@lang('Your personal information is secure and protected. We use industry-standard encryption.')</small>
                </div>
            </div>

            {{-- Footer Copyright --}}
            <div class="auth-footer">
                <small class="text-muted">@lang('All rights reserved') &copy; MBFX {{ date('Y') }}</small>
            </div>
        </div>
    </div>
</div>
@endsection

{{-- Professional Admin Login Styling --}}
@push('style')
<style>
/* Professional Admin Login Page Styling */
.professional-admin-login {
    min-height: 100vh;
    background: linear-gradient(135deg,rgb(255, 255, 255) 0%,rgb(255, 255, 255) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
    position: relative;
}

.professional-admin-login::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: white;
        
    pointer-events: none;
}

.login-container {
    width: 100%;
    max-width: 850px;
    position: relative;
    z-index: 1;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.2);
    overflow: hidden;
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    background: linear-gradient(135deg,rgb(0, 0, 0) 0%,rgb(28, 27, 28) 100%);
    padding: 1.5rem 1rem;
    text-align: center;
    color: white;
    position: relative;
}

.login-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.logo-section {
    position: relative;
    z-index: 2;
    margin-bottom: 1.5rem;
}

.login-logo {
    max-width: 180px;
    height: auto;
    filter: brightness(0) invert(1);
    transition: all 0.3s ease;
}

.welcome-section {
    position: relative;
    z-index: 2;
}

.login-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.3rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    color:white;
}

.login-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0;
    font-weight: 300;
     color:white;
}

.login-form-section {
    padding: 1.5rem 2rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.input-wrapper {
    position: relative;
}

.form-control {
    width: 100%;
    padding: 1rem 3rem 1rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.form-control:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    background: #fff;
    outline: none;
}

.input-icon {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    pointer-events: none;
    transition: color 0.3s ease;
}

.form-control:focus + .input-icon {
    color: #dc3545;
}

.password-toggle {
    position: absolute;
    right: 3rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.password-toggle:hover {
    color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.remember-section {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-check-input {
    width: 18px;
    height: 18px;
    margin: 0;
    accent-color: #dc3545;
}

.form-check-label {
    font-size: 0.9rem;
    color: #666;
    margin: 0;
    cursor: pointer;
}

.forgot-link {
    color:rgb(0, 0, 0);
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.forgot-link:hover {
    color: #c82333;
    text-decoration: underline;
}

.login-submit-btn {
    width: 100%;
    padding: 1rem 2rem;
    background: linear-gradient(135deg,rgb(0, 0, 0) 0%,rgb(32, 30, 31) 100%);
    border: none;
    border-radius: 12px;
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    position: relative;
    overflow: hidden;
}

.login-submit-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.login-submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

.login-submit-btn:hover::before {
    left: 100%;
}

.login-submit-btn:active {
    transform: translateY(0);
}

.register-link-section {
    text-align: center;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e9ecef;
}

.register-text {
    color: #666;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.register-link {
    color: #dc3545;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.register-link:hover {
    color: #c82333;
    text-decoration: underline;
}

.security-notice {
    background: #f8f9fa;
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-top: 1px solid #e9ecef;
}

.auth-footer {
    background: #f8f9fa;
    padding: 0.75rem 1rem;
    text-align: center;
    border-top: 1px solid #e9ecef;
}

.security-icon {
    color: #28a745;
    font-size: 1.2rem;
}

.security-text {
    flex: 1;
}

.security-text small {
    color: #666;
    font-size: 0.65rem;
    line-height: 1.2;
}

/* Input Group Styling */
.input-group {
    display: flex;
    width: 100%;
}

.input-group-text {
    background: #e9ecef;
    border: 2px solid #e9ecef;
    border-right: none;
    border-radius: 12px 0 0 12px;
    padding: 1rem;
    font-size: 0.9rem;
    color: #6c757d;
    min-width: 80px;
    text-align: center;
}

.input-group .form-control {
    border-radius: 0 12px 12px 0;
    border-left: none;
}

.input-group .form-control:focus {
    border-left: none;
}

/* Responsive Design */
@media (max-width: 576px) {
    .professional-admin-login {
        padding: 1rem;
    }

    .login-header {
        padding: 2rem 1.5rem;
    }

    .login-title {
        font-size: 1.5rem;
    }

    .login-form-section {
        padding: 2rem 1.5rem;
    }

    .form-options {
        flex-direction: column;
        align-items: flex-start;
    }

    .security-notice {
        padding: 1.5rem;
        flex-direction: column;
        text-align: center;
    }
}

/* Loading state */
.login-submit-btn.loading {
    pointer-events: none;
    opacity: 0.8;
}

.login-submit-btn.loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    top: 50%;
    left: 50%;
    margin-left: -10px;
    margin-top: -10px;
    border: 2px solid transparent;
    border-top-color: #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Username Status Styling */
.username-status {
    position: absolute;
    right: 3rem;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
}

.username-status small {
    font-size: 0.75rem;
    font-weight: 500;
}
</style>
@endpush

{{-- Enhanced Admin Login JavaScript --}}
@push('script')
<script>
function toggleAdminPassword() {
    const passwordField = document.querySelector('input[name="password"]');
    const toggleIcon = document.querySelector('.password-toggle i');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.professional-login-form');
    const submitBtn = document.querySelector('.login-submit-btn');

    // Enhanced form submission - prevent duplicate loaders
    if (form && submitBtn) {
        form.addEventListener('submit', function(e) {
            const originalText = submitBtn.innerHTML;

            // Hide global preloader if it exists to prevent duplicate loaders
            const globalPreloader = document.querySelector('.preloader-wrapper');
            if (globalPreloader) {
                globalPreloader.style.display = 'none';
            }

            // Add loading state (single loader only)
            submitBtn.classList.add('loading');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating Account...';
            submitBtn.disabled = true;

            // Re-enable after 10 seconds as fallback
            setTimeout(() => {
                submitBtn.classList.remove('loading');
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 10000);
        });
    }

    // Enhanced input focus effects
    const inputs = document.querySelectorAll('.form-control');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });

    // Auto-detect country based on IP geolocation
    function autoDetectCountry() {
        fetch('https://ipapi.co/json/')
            .then(response => response.json())
            .then(data => {
                if (data.country_name) {
                    // Find and select the country in the dropdown
                    $('select[name=country] option').each(function() {
                        if ($(this).val().toLowerCase() === data.country_name.toLowerCase()) {
                            $(this).prop('selected', true);
                            $('select[name=country]').trigger('change');
                            return false; // Break the loop
                        }
                    });
                }
            })
            .catch(error => {
                console.log('Auto-country detection failed:', error);
                // Fallback to default behavior
                initializeCountryDefaults();
            });
    }

    // Initialize country defaults
    function initializeCountryDefaults() {
        $('input[name=mobile_code]').val($('select[name=country] :selected').data('mobile_code'));
        $('input[name=country_code]').val($('select[name=country] :selected').data('code'));
        $('.mobile-code').text('+'+$('select[name=country] :selected').data('mobile_code'));
    }

    // Country and mobile code handling
    $('select[name=country]').change(function(){
        $('input[name=mobile_code]').val($('select[name=country] :selected').data('mobile_code'));
        $('input[name=country_code]').val($('select[name=country] :selected').data('code'));
        $('.mobile-code').text('+'+$('select[name=country] :selected').data('mobile_code'));
    });

    // Initialize auto-detection
    autoDetectCountry();

    // Username availability checking
    let usernameTimeout;
    const usernameInput = document.getElementById('username-input');
    const usernameStatus = document.getElementById('username-status');

    if (usernameInput && usernameStatus) {
        usernameInput.addEventListener('input', function() {
            clearTimeout(usernameTimeout);
            const username = this.value.trim();

            if (username.length < 3) {
                usernameStatus.innerHTML = '';
                return;
            }

            usernameStatus.innerHTML = '<small class="text-info"><i class="fas fa-spinner fa-spin"></i> Checking availability...</small>';

            usernameTimeout = setTimeout(() => {
                fetch('{{ route("user.check.username") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({ username: username })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.available) {
                        usernameStatus.innerHTML = '<small class="text-success"><i class="fas fa-check"></i> Username available</small>';
                    } else {
                        usernameStatus.innerHTML = '<small class="text-danger"><i class="fas fa-times"></i> Username already taken</small>';
                    }
                })
                .catch(error => {
                    usernameStatus.innerHTML = '<small class="text-muted">Unable to check availability</small>';
                });
            }, 500);
        });
    }
});
</script>
@endpush
