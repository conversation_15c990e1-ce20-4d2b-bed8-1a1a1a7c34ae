<?php

require 'vendor/autoload.php';
$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 CHECKING USER LIST ORDERING\n";
echo "==============================\n\n";

// Check current top users
$users = \App\Models\User::orderBy('created_at', 'desc')->limit(10)->get(['id', 'email', 'firstname', 'lastname', 'created_at', 'mt5_login']);

echo "Current top 10 users (by created_at DESC):\n";
foreach($users as $user) {
    $isTestUser = (strpos($user->email, '@exness.') !== false || 
                   strpos($user->email, 'test') !== false ||
                   strpos($user->email, 'dummy') !== false);
    $marker = $isTestUser ? ' ⚠️ TEST/DUMMY' : '';
    echo $user->id . ' - ' . $user->email . ' - ' . $user->created_at . ' - MT5: ' . $user->mt5_login . $marker . "\n";
}

echo "\n";

// Check MT5 database for latest real users
echo "Checking MT5 database for latest real users:\n";
try {
    $mt5Users = \DB::connection('mt5')->table('mt5_users')
        ->where('Email', 'not like', '%@exness.%')
        ->where('Email', 'not like', '%test%')
        ->where('Email', 'not like', '%dummy%')
        ->orderBy('RegDate', 'desc')
        ->limit(10)
        ->get(['Login', 'Email', 'FirstName', 'LastName', 'RegDate']);
    
    foreach($mt5Users as $mt5User) {
        echo $mt5User->Login . ' - ' . $mt5User->Email . ' - ' . $mt5User->RegDate . "\n";
    }
} catch (Exception $e) {
    echo "Error connecting to MT5 database: " . $e->getMessage() . "\n";
}

echo "\n";

// Check for test/dummy users in local database
$testUsers = \App\Models\User::where(function($query) {
    $query->where('email', 'like', '%@exness.%')
          ->orWhere('email', 'like', '%test%')
          ->orWhere('email', 'like', '%dummy%');
})->count();

echo "Test/dummy users in local database: $testUsers\n";

// Check latest real users
$realUsers = \App\Models\User::where('email', 'not like', '%@exness.%')
    ->where('email', 'not like', '%test%')
    ->where('email', 'not like', '%dummy%')
    ->orderBy('created_at', 'desc')
    ->limit(10)
    ->get(['id', 'email', 'firstname', 'lastname', 'created_at', 'mt5_login']);

echo "\nLatest 10 REAL users (excluding test/dummy):\n";
foreach($realUsers as $user) {
    echo $user->id . ' - ' . $user->email . ' - ' . $user->created_at . ' - MT5: ' . $user->mt5_login . "\n";
}
