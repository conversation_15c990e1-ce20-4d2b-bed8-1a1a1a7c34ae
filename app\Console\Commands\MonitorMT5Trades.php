<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\MT5CommissionService;
use App\Services\IbCommissionService;

class MonitorMT5Trades extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mt5:monitor-trades {--interval=60 : Monitoring interval in seconds}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Monitor MT5 trades and process IB commissions';

    protected $mt5CommissionService;
    protected $ibCommissionService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->mt5CommissionService = new MT5CommissionService();
        $this->ibCommissionService = new IbCommissionService();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $interval = $this->option('interval');
        
        $this->info("Starting MT5 trade monitoring (interval: {$interval}s)");
        $this->info("Press Ctrl+C to stop monitoring");

        while (true) {
            try {
                $this->info("[" . now()->format('Y-m-d H:i:s') . "] Checking for new trades...");
                
                // Monitor MT5 trades
                $this->mt5CommissionService->monitorMT5Trades();
                
                // Process any pending commissions
                $processed = $this->ibCommissionService->processPendingCommissions();
                
                if ($processed > 0) {
                    $this->info("Processed {$processed} pending commissions");
                }
                
                // Wait for the specified interval
                sleep($interval);
                
            } catch (\Exception $e) {
                $this->error("Error during monitoring: " . $e->getMessage());
                sleep($interval); // Continue monitoring even after errors
            }
        }

        return 0;
    }
}
