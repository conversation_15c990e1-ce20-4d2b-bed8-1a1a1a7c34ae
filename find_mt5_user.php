<?php

/**
 * Find a user with MT5 accounts for testing
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Finding User with MT5 Accounts ===\n\n";

// Get some MT5 users from the database
$mt5Users = DB::connection('mbf-dbmt5')->table('mt5_users')
    ->select('Email', 'Login', 'Group', 'Balance')
    ->whereNotNull('Email')
    ->where('Email', '!=', '')
    ->take(10)
    ->get();

echo "Sample MT5 users:\n";
foreach ($mt5Users as $mt5User) {
    echo "Email: {$mt5User->Email} | Login: {$mt5User->Login} | Group: {$mt5User->Group} | Balance: \${$mt5User->Balance}\n";
    
    // Check if this email exists in our users table
    $user = \App\Models\User::where('email', $mt5User->Email)->first();
    if ($user) {
        echo "  ✅ Found matching user in users table: ID {$user->id}\n";
        
        // Check if user has wallets
        $walletCount = $user->wallets()->count();
        echo "  Wallets: {$walletCount}\n";
        
        if ($walletCount > 0) {
            echo "  🎯 This user is suitable for testing!\n";
            break;
        }
    } else {
        echo "  ❌ No matching user in users table\n";
    }
    echo "\n";
}

echo "\n=== Search Complete ===\n";
