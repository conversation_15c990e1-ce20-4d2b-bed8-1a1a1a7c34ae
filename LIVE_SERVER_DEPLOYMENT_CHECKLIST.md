# 🚀 LIVE SERVER DEPLOYMENT CHECKLIST
## Windows 2022 Server Email Template Fix

---

## ⚡ **IMMEDIATE ACTIONS REQUIRED**

### **STEP 1: BACKUP CURRENT web.config**
```cmd
# On live server, backup existing web.config
copy web.config web.config.backup.$(date +%Y%m%d)
```

### **STEP 2: REPLACE web.config (CRITICAL)**
- **File**: `web.config.fixed` (created in this project)
- **Action**: Replace the entire `web.config` file on live server
- **Location**: Root directory of your Laravel application

### **STEP 3: RESTART IIS**
```cmd
# Run as Administrator
iisreset
```

### **STEP 4: IMMEDIATE TESTING**
1. **Test Asset Loading**:
   ```
   https://yourdomain.com/assets/admin/css/simple-email-editor.css
   ```
   ✅ **Expected**: Returns CSS content (not HTML)

2. **Test Email Template Editor**:
   ```
   https://yourdomain.com/admin/notification/template/edit/1
   ```
   ✅ **Expected**: Page displays with proper styling and functionality

---

## 🔧 **ROOT CAUSE ANALYSIS**

**Problem**: The current web.config has this broken rewrite rule:
```xml
<action type="Rewrite" url="/" />
```

**Impact**: 
- ALL requests (including CSS/JS/Python) redirect to homepage
- Assets return HTML instead of actual CSS/JS content
- Python API calls may fail due to incorrect routing
- Email template editor appears broken/unstyled

**Solution**: Fixed web.config with proper static file and Python handling:
```xml
<!-- Allow direct access to Python files -->
<rule name="Python Scripts" stopProcessing="true">
  <match url="^.*\.py$" />
  <action type="None" />
</rule>

<!-- Allow direct access to assets -->
<rule name="Static Files" stopProcessing="true">
  <match url="^(assets|css|js|images|fonts)/.*" />
  <action type="None" />
</rule>
```

---

## 📋 **VERIFICATION STEPS**

### **1. Asset Loading Test**
```bash
# These should return actual file content, not HTML:
curl -I https://yourdomain.com/assets/admin/css/simple-email-editor.css
curl -I https://yourdomain.com/assets/admin/js/simple-email-editor.js
```

### **2. Browser Console Check**
- Open DevTools (F12)
- Navigate to email template editor
- Console should show NO 404 errors
- Console should show NO JavaScript errors

### **3. Visual Verification**
- Email template editor should have proper styling
- Shortcode buttons should be visible and clickable
- Visual/HTML editor tabs should work
- Content should be editable

---

## 🚨 **IF ISSUES PERSIST**

### **Check IIS Logs**
```
Location: C:\inetpub\logs\LogFiles\W3SVC1\
Look for: 404 errors on asset files
```

### **Verify File Permissions**
```
Assets folder: Read permissions for IIS_IUSRS
PHP files: Read/Execute permissions
```

### **Clear All Caches**
```cmd
# Laravel caches
php artisan cache:clear
php artisan config:clear
php artisan view:clear

# Browser cache (hard refresh)
Ctrl + F5
```

---

## ✅ **SUCCESS INDICATORS**

After deployment, you should see:

1. ✅ **CSS Loading**: `simple-email-editor.css` returns actual CSS content
2. ✅ **JS Loading**: `simple-email-editor.js` returns actual JavaScript content  
3. ✅ **Proper Styling**: Email template editor displays correctly
4. ✅ **Full Functionality**: All editor features work (tabs, shortcodes, saving)
5. ✅ **No Console Errors**: Browser console shows no 404 or JavaScript errors

---

## 📞 **DEPLOYMENT PRIORITY**

**🔴 CRITICAL**: Replace web.config immediately
**🟡 IMPORTANT**: Restart IIS
**🟢 VERIFY**: Test asset loading and editor functionality

This fix will restore full email template editor functionality on Windows 2022 Server.