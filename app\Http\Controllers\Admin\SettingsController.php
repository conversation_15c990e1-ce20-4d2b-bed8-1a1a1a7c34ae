<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class SettingsController extends Controller
{
    public function index()
    {
        $pageTitle = 'Settings Dashboard';
        
        // Settings categories with their routes and icons - REDESIGNED
        $settingsCategories = [
            'general' => [
                'title' => 'General Settings',
                'icon' => 'las la-cog',
                'color' => 'primary',
                'items' => [
                    [
                        'title' => 'General Setting',
                        'description' => 'Basic site configuration and settings',
                        'route' => 'admin.setting.index',
                        'icon' => 'las la-life-ring'
                    ],
                    [
                        'title' => 'System Configuration',
                        'description' => 'System level configuration settings',
                        'route' => 'admin.setting.system.configuration',
                        'icon' => 'las la-cog'
                    ],
                    [
                        'title' => 'Logo & Favicon',
                        'description' => 'Upload and manage site logos and favicon',
                        'route' => 'admin.setting.logo.icon',
                        'icon' => 'las la-image'
                    ],
                    [
                        'title' => 'Notification Settings',
                        'description' => 'Email and SMS notification configuration',
                        'route' => 'admin.setting.notification.global',
                        'icon' => 'las la-bell'
                    ]
                ]
            ],
            'system_advanced' => [
                'title' => 'System & Advanced',
                'icon' => 'las la-server',
                'color' => 'dark',
                'items' => [
                    [
                        'title' => 'Language',
                        'description' => 'Multi-language configuration and management',
                        'route' => 'admin.language.manage',
                        'icon' => 'las la-language'
                    ],
                    [
                        'title' => 'Blacklist Countries',
                        'description' => 'Manage restricted countries and regions',
                        'route' => 'admin.blacklist.index',
                        'icon' => 'las la-ban'
                    ],
                    [
                        'title' => 'Application Info',
                        'description' => 'System information and diagnostics',
                        'route' => 'admin.system.info',
                        'icon' => 'las la-info-circle'
                    ],
                    [
                        'title' => 'Server Info',
                        'description' => 'Server configuration and status',
                        'route' => 'admin.system.server.info',
                        'icon' => 'las la-server'
                    ],
                    [
                        'title' => 'Cache Management',
                        'description' => 'Clear and optimize system cache',
                        'route' => 'admin.system.optimize',
                        'icon' => 'las la-rocket'
                    ],
                    [
                        'title' => 'System Update',
                        'description' => 'Update system to latest version',
                        'route' => 'admin.system.update',
                        'icon' => 'las la-download'
                    ]
                ]
            ],
            'frontend_manager' => [
                'title' => 'Frontend Manager',
                'icon' => 'las la-puzzle-piece',
                'color' => 'info',
                'items' => [
                    [
                        'title' => 'Manage Pages',
                        'description' => 'Create and edit website pages',
                        'route' => 'admin.frontend.manage.pages',
                        'icon' => 'las la-file-alt'
                    ],
                    [
                        'title' => 'Manage Sections',
                        'description' => 'Configure website sections and content',
                        'route' => 'admin.frontend.manage.pages',
                        'icon' => 'las la-th-large'
                    ],
                    [
                        'title' => 'SEO Manager',
                        'description' => 'Search engine optimization settings',
                        'route' => 'admin.seo',
                        'icon' => 'las la-search'
                    ]
                ]
            ],
            'maintenance_security' => [
                'title' => 'Maintenance & Security',
                'icon' => 'las la-shield-alt',
                'color' => 'warning',
                'items' => [
                    [
                        'title' => 'Maintenance Mode',
                        'description' => 'Enable/disable site maintenance mode',
                        'route' => 'admin.maintenance.mode',
                        'icon' => 'las la-tools'
                    ],
                    [
                        'title' => 'GDPR Cookie',
                        'description' => 'Cookie consent and GDPR compliance',
                        'route' => 'admin.setting.cookie',
                        'icon' => 'las la-cookie-bite'
                    ],
                    [
                        'title' => 'Custom CSS',
                        'description' => 'Add custom styling to your site',
                        'route' => 'admin.setting.custom.css',
                        'icon' => 'las la-code'
                    ]
                ]
            ],
            'integrations' => [
                'title' => 'Integrations & APIs',
                'icon' => 'las la-plug',
                'color' => 'success',
                'items' => [
                    [
                        'title' => 'Pusher Configuration',
                        'description' => 'Real-time communication settings',
                        'route' => 'admin.setting.pusher.configuration',
                        'icon' => 'las la-cogs'
                    ],
                    [
                        'title' => 'Social Credentials',
                        'description' => 'Social media login configuration',
                        'route' => 'admin.setting.socialite.credentials',
                        'icon' => 'las la-users-cog'
                    ],
                    [
                        'title' => 'Email Configuration',
                        'description' => 'SMTP and email settings',
                        'route' => 'admin.setting.notification.email',
                        'icon' => 'las la-envelope'
                    ]
                ]
            ],
            'trading' => [
                'title' => 'Trading Settings',
                'icon' => 'las la-chart-line',
                'color' => 'info',
                'items' => [
                    [
                        'title' => 'Chart Setting',
                        'description' => 'Trading chart configuration',
                        'route' => 'admin.setting.chart',
                        'icon' => 'las la-tools'
                    ],
                    [
                        'title' => 'Charge Setting',
                        'description' => 'Trading fees and charges',
                        'route' => 'admin.setting.charge',
                        'icon' => 'las la-money-check'
                    ],
                    [
                        'title' => 'Wallet Setting',
                        'description' => 'Wallet and balance configuration',
                        'route' => 'admin.wallet.setting',
                        'icon' => 'las la-wallet'
                    ]
                ]
            ],
            'user_management' => [
                'title' => 'User Management',
                'icon' => 'las la-users',
                'color' => 'warning',
                'items' => [
                    [
                        'title' => 'KYC Setting',
                        'description' => 'Know Your Customer verification settings',
                        'route' => 'admin.kyc.setting',
                        'icon' => 'las la-user-check'
                    ],
                    [
                        'title' => 'User Profile Settings',
                        'description' => 'User profile form configuration',
                        'route' => 'admin.user.profile.settings',
                        'icon' => 'las la-user-cog'
                    ]
                ]
            ],
            'notifications' => [
                'title' => 'Notification Settings',
                'icon' => 'las la-bell',
                'color' => 'danger',
                'items' => [
                    [
                        'title' => 'Global Template',
                        'description' => 'Global notification templates',
                        'route' => 'admin.setting.notification.global',
                        'icon' => 'las la-globe'
                    ],
                    [
                        'title' => 'Email Setting',
                        'description' => 'Email configuration and SMTP settings',
                        'route' => 'admin.setting.notification.email',
                        'icon' => 'las la-envelope'
                    ],
                    [
                        'title' => 'SMS Setting',
                        'description' => 'SMS gateway configuration',
                        'route' => 'admin.setting.notification.sms',
                        'icon' => 'las la-sms'
                    ],
                    [
                        'title' => 'Notification Templates',
                        'description' => 'Manage notification templates',
                        'route' => 'admin.setting.notification.templates',
                        'icon' => 'las la-file-alt'
                    ]
                ]
            ],
            'system' => [
                'title' => 'System & Advanced',
                'icon' => 'las la-server',
                'color' => 'dark',
                'items' => [
                    [
                        'title' => 'Cron Job Setting',
                        'description' => 'Scheduled tasks configuration',
                        'route' => 'admin.setting.index',
                        'icon' => 'las la-clock'
                    ],
                    [
                        'title' => 'Extensions',
                        'description' => 'Manage system extensions',
                        'route' => 'admin.setting.index',
                        'icon' => 'las la-puzzle-piece'
                    ],
                    [
                        'title' => 'Language',
                        'description' => 'Multi-language configuration',
                        'route' => 'admin.setting.index',
                        'icon' => 'las la-language'
                    ],
                    [
                        'title' => 'SEO Manager',
                        'description' => 'Search engine optimization settings',
                        'route' => 'admin.seo',
                        'icon' => 'las la-search'
                    ]
                ]
            ]
        ];
        
        return view('admin.settings.index', compact('pageTitle', 'settingsCategories'));
    }
}
