@echo off
REM PHP Version Detection for Plesk Server
REM Automatically detects the correct PHP version and path

echo ========================================
echo PHP VERSION DETECTION FOR PLESK
echo ========================================
echo.

REM Check available PHP versions in Plesk
echo Checking available PHP versions in Plesk...
echo.

set PHP_FOUND=0
set PHP_PATH=""

REM Check PHP 8.4 (preferred)
if exist "C:\Program Files (x86)\Plesk\Additional\PleskPHP84\php.exe" (
    echo ✅ PHP 8.4 found: C:\Program Files (x86)\Plesk\Additional\PleskPHP84\php.exe
    set PHP_PATH="C:\Program Files (x86)\Plesk\Additional\PleskPHP84\php.exe"
    set PHP_VERSION=8.4
    set PHP_FOUND=1
    
    REM Test PHP 8.4
    %PHP_PATH% --version
    echo.
)

REM Check PHP 8.3 (fallback)
if %PHP_FOUND%==0 (
    if exist "C:\Program Files (x86)\Plesk\Additional\PleskPHP83\php.exe" (
        echo ✅ PHP 8.3 found: C:\Program Files (x86)\Plesk\Additional\PleskPHP83\php.exe
        set PHP_PATH="C:\Program Files (x86)\Plesk\Additional\PleskPHP83\php.exe"
        set PHP_VERSION=8.3
        set PHP_FOUND=1
        
        REM Test PHP 8.3
        %PHP_PATH% --version
        echo.
    )
)

REM Check PHP 8.2 (fallback)
if %PHP_FOUND%==0 (
    if exist "C:\Program Files (x86)\Plesk\Additional\PleskPHP82\php.exe" (
        echo ✅ PHP 8.2 found: C:\Program Files (x86)\Plesk\Additional\PleskPHP82\php.exe
        set PHP_PATH="C:\Program Files (x86)\Plesk\Additional\PleskPHP82\php.exe"
        set PHP_VERSION=8.2
        set PHP_FOUND=1
        
        REM Test PHP 8.2
        %PHP_PATH% --version
        echo.
    )
)

REM Check PHP 8.1 (minimum required)
if %PHP_FOUND%==0 (
    if exist "C:\Program Files (x86)\Plesk\Additional\PleskPHP81\php.exe" (
        echo ⚠️  PHP 8.1 found: C:\Program Files (x86)\Plesk\Additional\PleskPHP81\php.exe
        echo ⚠️  WARNING: Your Composer dependencies require PHP >= 8.2.0
        set PHP_PATH="C:\Program Files (x86)\Plesk\Additional\PleskPHP81\php.exe"
        set PHP_VERSION=8.1
        set PHP_FOUND=1
        
        REM Test PHP 8.1
        %PHP_PATH% --version
        echo.
    )
)

if %PHP_FOUND%==0 (
    echo ❌ No compatible PHP version found in Plesk!
    echo.
    echo Please install PHP 8.2 or higher in Plesk:
    echo 1. Open Plesk Panel
    echo 2. Go to Tools & Settings
    echo 3. Go to Updates and Upgrades
    echo 4. Install PHP 8.4 or PHP 8.3
    echo.
    pause
    exit /b 1
)

echo ========================================
echo DETECTED CONFIGURATION
echo ========================================
echo.
echo PHP Version: %PHP_VERSION%
echo PHP Path: %PHP_PATH%
echo.

REM Test Laravel compatibility
echo Testing Laravel compatibility...
set PROJECT_PATH="C:\Inetpub\vhosts\mybrokerforex.com\mbf.mybrokerforex.com"
cd /d %PROJECT_PATH%

%PHP_PATH% artisan --version
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Laravel compatibility test failed
    echo.
    echo Possible issues:
    echo 1. Composer dependencies not updated for PHP %PHP_VERSION%
    echo 2. Missing PHP extensions
    echo 3. Incorrect project path
    echo.
    echo Try running: composer update --no-dev
    pause
    exit /b 1
)

echo ✅ Laravel compatibility test passed
echo.

REM Test MT5 sync command
echo Testing MT5 sync command...
%PHP_PATH% artisan mt5:sync-users --help > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ MT5 sync command not found
    echo.
    echo Please ensure the enhanced sync command is deployed
    pause
    exit /b 1
)

echo ✅ MT5 sync command found
echo.

echo ========================================
echo RECOMMENDED SETUP COMMANDS
echo ========================================
echo.
echo Use these commands with the detected PHP version:
echo.
echo Manual sync test:
echo %PHP_PATH% artisan mt5:sync-users --fast --force --limit=1000
echo.
echo Dry run test:
echo %PHP_PATH% artisan mt5:sync-users --dry-run --limit=100
echo.
echo Update Composer dependencies (if needed):
echo %PHP_PATH% -d memory_limit=512M composer.phar update --no-dev --optimize-autoloader
echo.

echo ========================================
echo NEXT STEPS
echo ========================================
echo.
echo 1. Update setup scripts with detected PHP path
echo 2. Run setup_mt5_realtime_sync.bat
echo 3. Import task in Task Scheduler
echo 4. Test automated sync
echo.

echo ✅ PHP detection completed successfully!
pause
