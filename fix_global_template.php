<?php

use Illuminate\Support\Facades\Artisan;

// Run this via artisan command
Artisan::call('tinker', [], [
    'commands' => [
        '$general = gs();',
        'echo "Current global template length: " . strlen($general->email_template);',
        'echo "\nFirst 200 chars: " . substr($general->email_template, 0, 200);',
        'echo "\nContains DOCTYPE: " . (strpos($general->email_template, "<!DOCTYPE") !== false ? "YES" : "NO");',
        
        // Fix the global template
        '$newGlobalTemplate = "<!DOCTYPE html>
<html lang=\"en\">
<head>
    <meta charset=\"UTF-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>{{subject}}</title>
</head>
<body style=\"margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4;\">
    {{message}}
</body>
</html>";',
        
        '$general->email_template = $newGlobalTemplate;',
        '$general->save();',
        'echo "\nGlobal template updated successfully!";',
        'echo "\nNew length: " . strlen($general->email_template);'
    ]
]);
