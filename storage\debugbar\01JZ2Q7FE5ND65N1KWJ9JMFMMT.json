{"__meta": {"id": "01JZ2Q7FE5ND65N1KWJ9JMFMMT", "datetime": "2025-07-01 10:06:26", "utime": **********.246919, "method": "POST", "uri": "/mbf.mybrokerforex.com-********/admin/notification/template/update/1", "ip": "::1"}, "messages": {"count": 40, "messages": [{"message": "[10:06:26] LOG.info: === TEMPLATE UPDATE DEBUG START ===", "message_html": null, "is_string": false, "label": "info", "time": **********.158371, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: Template ID: 1", "message_html": null, "is_string": false, "label": "info", "time": **********.158806, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: Request Method: POST", "message_html": null, "is_string": false, "label": "info", "time": **********.159168, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: Request URL: https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/update/1", "message_html": null, "is_string": false, "label": "info", "time": **********.15965, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "message_html": null, "is_string": false, "label": "info", "time": **********.160013, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: Server Environment: WINNT - PHP 8.2.12", "message_html": null, "is_string": false, "label": "info", "time": **********.160354, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: Content Type: multipart/form-data; boundary=----WebKitFormBoundaryEiGFPUBlJk0TXD7u", "message_html": null, "is_string": false, "label": "info", "time": **********.160681, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: Content Length: 18315", "message_html": null, "is_string": false, "label": "info", "time": **********.160999, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: Raw POST data keys: _token, template_id, subject, email_status, sms_status, sms_body, email_body_encoded, email_body, email_body_final", "message_html": null, "is_string": false, "label": "info", "time": **********.161355, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: Email body field exists: YES", "message_html": null, "is_string": false, "label": "info", "time": **********.161709, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: Email body final field exists: YES", "message_html": null, "is_string": false, "label": "info", "time": **********.162045, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: ✅ Validation passed", "message_html": null, "is_string": false, "label": "info", "time": **********.183798, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: ✅ Template found - Current subject: Your Account has been Credited", "message_html": null, "is_string": false, "label": "info", "time": **********.192066, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: ✅ Template found - Current email_body length: 5107", "message_html": null, "is_string": false, "label": "info", "time": **********.192507, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: ✅ Subject updated to: Your Account has been Credited", "message_html": null, "is_string": false, "label": "info", "time": **********.192953, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: === ENHANCED EMAIL BODY PROCESSING WITH BASE64 SUPPORT ===", "message_html": null, "is_string": false, "label": "info", "time": **********.193298, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: 📦 Base64 encoded content detected", "message_html": null, "is_string": false, "label": "info", "time": **********.193692, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: ✅ Base64 content decoded successfully, length: 5097", "message_html": null, "is_string": false, "label": "info", "time": **********.194054, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: 📝 Decoded content preview: <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Balance Added Successfully</title>\n\n\n    <!-- Full Width Container -->\n    <table width=\"10", "message_html": null, "is_string": false, "label": "info", "time": **********.194386, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: Email body source: email_body_final", "message_html": null, "is_string": false, "label": "info", "time": **********.194744, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: Email body length: 5097", "message_html": null, "is_string": false, "label": "info", "time": **********.195083, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: Email body preview (first 200 chars): <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Balance Added Successfully</title>\n\n\n    <!-- Full Width Container -->\n    <table width=\"10", "message_html": null, "is_string": false, "label": "info", "time": **********.195414, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: After minimal Windows cleanup - Email body length: 5097", "message_html": null, "is_string": false, "label": "info", "time": **********.195755, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: Template 1: Using content directly from editor with minimal cleanup", "message_html": null, "is_string": false, "label": "info", "time": **********.196103, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: === FINAL CONTENT READY FOR SAVE ===", "message_html": null, "is_string": false, "label": "info", "time": **********.196387, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: Final email body length: 5097", "message_html": null, "is_string": false, "label": "info", "time": **********.196714, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: Final email body preview (first 300 chars): <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Balance Added Successfully</title>\n\n\n    <!-- Full Width Container -->\n    <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: #f4f4f4;\">\n        <tbody><t", "message_html": null, "is_string": false, "label": "info", "time": **********.197028, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: Template 1: Skipping all corruption detection and complex processing to prevent issues", "message_html": null, "is_string": false, "label": "info", "time": **********.197351, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: === DATABASE SAVE OPERATION DEBUG ===", "message_html": null, "is_string": false, "label": "info", "time": **********.19768, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: Before save - Template email_body length: 5107", "message_html": null, "is_string": false, "label": "info", "time": **********.198029, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: Before save - New email_body length: 5097", "message_html": null, "is_string": false, "label": "info", "time": **********.19836, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: Before save - Template dirty: []", "message_html": null, "is_string": false, "label": "info", "time": **********.19872, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: After setting fields - Template dirty: {\"email_body\":\"<meta charset=\\\"UTF-8\\\">\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\">\\n    <title>Balance Added Successfully<\\/title>\\n\\n\\n    <!-- Full Width Container -->\\n    <table width=\\\"100%\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" border=\\\"0\\\" style=\\\"background-color: #f4f4f4;\\\">\\n        <tbody><tr>\\n            <td align=\\\"center\\\">\\n                <!-- Email Container -->\\n                <table width=\\\"600\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" border=\\\"0\\\" style=\\\"background-color: #ffffff; box-shadow: 0 0 10px rgba(0,0,0,0.1);\\\">\\n\\n                    <!-- Header Banner - Full Width -->\\n                    <tbody><tr>\\n                        <td width=\\\"100%\\\" style=\\\"background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); padding: 20px 0; text-align: center;\\\">\\n                            <h1 style=\\\"margin: 0; font-size: 24px; font-weight: bold; color: #ffffff;\\\">Balance Notification<\\/h1>\\n                        <\\/td>\\n                    <\\/tr>\\n\\n                    <!-- Logo Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: #ffffff; text-align: center; padding: 20px; border-bottom: 2px solid #e9ecef;\\\">\\n                            <img src=\\\"https:\\/\\/mbf.mybrokerforex.com\\/assets\\/images\\/logoIcon\\/logo.png\\\" alt=\\\"MBFX\\\" style=\\\"height: 60px; width: auto; display: block; margin: 0 auto;\\\" onerror=\\\"this.style.display='none'\\\">\\n                        <\\/td>\\n                    <\\/tr>\\n\\n                    <!-- Title Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: #ffffff; padding: 30px 40px 20px; text-align: center;\\\">\\n                            <h2 style=\\\"margin: 0; color: #dc3545; font-size: 28px; font-weight: bold;\\\">Balance Added Successfully<\\/h2>\\n                        <\\/td>\\n                    <\\/tr>\\n\\n                    <!-- Description Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: #ffffff; padding: 0 40px 20px; text-align: center;\\\">\\n                            <p style=\\\"margin: 0; color: #6c757d; font-size: 16px;\\\">Your account balance has been updated with a new deposit.<\\/p>\\n                        <\\/td>\\n                    <\\/tr>\\n\\n                    <!-- Main Content Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: #ffffff; padding: 20px 40px; color: #333333;\\\">\\n                            <p>Dear {{fullname}},<\\/p><p>We are pleased to inform you that {{amount}} {{currency}} has been added to your account.<\\/p><ul><li><strong>Amount:<\\/strong> {{amount}} {{currency}}<\\/li><li><strong>New Balance:<\\/strong> {{new_balance}} {{currency}}<\\/li><li><strong>Transaction ID:<\\/strong> {{transaction_id}}<\\/li><li><strong>Date:<\\/strong> {{transaction_date}}<\\/li><\\/ul><p>The funds are now available in your account.<\\/p>\\n                        <\\/td>\\n                    <\\/tr>\\n\\n                    <!-- Regards Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: #ffffff; padding: 30px 40px; border-top: 1px solid #e9ecef;\\\">\\n                            <p style=\\\"margin: 0 0 10px 0; font-size: 16px;\\\">Best regards,<br>\\n                            <strong>MBFX Team<\\/strong><\\/p>\\n                            <p style=\\\"font-size: 12px; color: #6c757d; margin: 15px 0 0 0;\\\">\\n                                If you have any questions, please contact our support team.\\n                            <\\/p>\\n                        <\\/td>\\n                    <\\/tr>\\n\\n                    <!-- Footer Section - Full Width -->\\n                    <tr>\\n                        <td width=\\\"100%\\\" style=\\\"background: linear-gradient(135deg, #343a40 0%, #23272b 100%); color: #ffffff; padding: 30px 40px; text-align: center;\\\">\\n                            <p style=\\\"margin: 0 0 10px 0; font-size: 14px;\\\"><strong>MBFX<\\/strong> - Professional Trading Platform<\\/p>\\n                            <p style=\\\"margin: 0 0 10px 0; font-size: 14px;\\\">\\n                                <a href=\\\"{{site_url}}\\/user\\/profile\\/setting\\\" style=\\\"color: #ffffff; text-decoration: none;\\\">Account Settings<\\/a> |\\n                                <a href=\\\"{{site_url}}\\/contact\\\" style=\\\"color: #ffffff; text-decoration: none;\\\">Contact Support<\\/a> |\\n                                <a href=\\\"{{site_url}}\\/policy\\/privacy-policy\\/99\\\" style=\\\"color: #ffffff; text-decoration: none;\\\">Privacy Policy<\\/a>\\n                            <\\/p>\\n                            <p style=\\\"margin: 15px 0 0 0; font-size: 14px;\\\">\\n                                \\u00a9 2025 MBFX. All rights reserved.\\n                            <\\/p>\\n                            <p style=\\\"font-size: 10px; color: #999999; margin: 10px 0 0 0;\\\">\\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\\n                                <a href=\\\"{{site_url}}\\/user\\/profile\\/setting\\\" style=\\\"color: #999999; text-decoration: none;\\\">update your preferences<\\/a>.\\n                            <\\/p>\\n                        <\\/td>\\n                    <\\/tr>\\n\\n                <\\/tbody><\\/table>\\n            <\\/td>\\n        <\\/tr>\\n    <\\/tbody><\\/table>\"}", "message_html": null, "is_string": false, "label": "info", "time": **********.199955, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: After setting fields - Template email_body length: 5097", "message_html": null, "is_string": false, "label": "info", "time": **********.200353, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: Save operation result: SUCCESS", "message_html": null, "is_string": false, "label": "info", "time": **********.222779, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: After refresh - Template email_body length: 5097", "message_html": null, "is_string": false, "label": "info", "time": **********.230392, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: After refresh - Content matches: YES", "message_html": null, "is_string": false, "label": "info", "time": **********.230905, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: ✅ Template 1: Database operation completed", "message_html": null, "is_string": false, "label": "info", "time": **********.23125, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: === TEMPLATE UPDATE DEBUG END ===", "message_html": null, "is_string": false, "label": "info", "time": **********.231558, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:26] LOG.info: 📤 Returning AJAX response", "message_html": null, "is_string": false, "label": "info", "time": **********.231888, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751364385.509723, "end": **********.247072, "duration": 0.7373490333557129, "duration_str": "737ms", "measures": [{"label": "Booting", "start": 1751364385.509723, "relative_start": 0, "end": **********.017127, "relative_end": **********.017127, "duration": 0.***************, "duration_str": "507ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.01715, "relative_start": 0.****************, "end": **********.247076, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "230ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.072079, "relative_start": 0.****************, "end": **********.082473, "relative_end": **********.082473, "duration": 0.010394096374511719, "duration_str": "10.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.23992, "relative_start": 0.****************, "end": **********.24091, "relative_end": **********.24091, "duration": 0.000990152359008789, "duration_str": "990μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.12", "Environment": "production", "Debug Mode": "Enabled", "URL": "localhost/mbf.mybrokerforex.com-********", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 5, "nb_statements": 4, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.023229999999999997, "accumulated_duration_str": "23.23ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "vendor/beyondcode/laravel-query-detector/src/QueryDetector.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetector.php", "line": 25}, {"index": 10, "namespace": null, "name": "vendor/beyondcode/laravel-query-detector/src/QueryDetectorMiddleware.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetectorMiddleware.php", "line": 30}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21}], "start": **********.070568, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "QueryDetector.php:25", "source": {"index": 9, "namespace": null, "name": "vendor/beyondcode/laravel-query-detector/src/QueryDetector.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetector.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fvendor%2Fbeyondcode%2Flaravel-query-detector%2Fsrc%2FQueryDetector.php&line=25", "ajax": false, "filename": "QueryDetector.php", "line": "25"}, "connection": "mbf-db", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Middleware\\RedirectIfNotAdmin.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.14009, "duration": 0.0062699999999999995, "duration_str": "6.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mbf-db", "explain": null, "start_percent": 0, "width_percent": 26.991}, {"sql": "select * from `notification_templates` where `notification_templates`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/NotificationController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\NotificationController.php", "line": 122}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.185067, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "NotificationController.php:122", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/NotificationController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\NotificationController.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FNotificationController.php&line=122", "ajax": false, "filename": "NotificationController.php", "line": "122"}, "connection": "mbf-db", "explain": null, "start_percent": 26.991, "width_percent": 4.391}, {"sql": "update `notification_templates` set `email_body` = '<meta charset=\\\"UTF-8\\\">\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\">\\n    <title>Balance Added Successfully</title>\\n\\n\\n    <!-- Full Width Container -->\\n    <table width=\\\"100%\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" border=\\\"0\\\" style=\\\"background-color: #f4f4f4;\\\">\\n        <tbody><tr>\\n            <td align=\\\"center\\\">\\n                <!-- Email Container -->\\n                <table width=\\\"600\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" border=\\\"0\\\" style=\\\"background-color: #ffffff; box-shadow: 0 0 10px rgba(0,0,0,0.1);\\\">\\n\\n                    <!-- Header Banner - Full Width -->\\n                    <tbody><tr>\\n                        <td width=\\\"100%\\\" style=\\\"background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); padding: 20px 0; text-align: center;\\\">\\n                            <h1 style=\\\"margin: 0; font-size: 24px; font-weight: bold; color: #ffffff;\\\">Balance Notification</h1>\\n                        </td>\\n                    </tr>\\n\\n                    <!-- Logo Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: #ffffff; text-align: center; padding: 20px; border-bottom: 2px solid #e9ecef;\\\">\\n                            <img src=\\\"https://mbf.mybrokerforex.com/assets/images/logoIcon/logo.png\\\" alt=\\\"MBFX\\\" style=\\\"height: 60px; width: auto; display: block; margin: 0 auto;\\\" onerror=\\\"this.style.display=\\'none\\'\\\">\\n                        </td>\\n                    </tr>\\n\\n                    <!-- Title Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: #ffffff; padding: 30px 40px 20px; text-align: center;\\\">\\n                            <h2 style=\\\"margin: 0; color: #dc3545; font-size: 28px; font-weight: bold;\\\">Balance Added Successfully</h2>\\n                        </td>\\n                    </tr>\\n\\n                    <!-- Description Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: #ffffff; padding: 0 40px 20px; text-align: center;\\\">\\n                            <p style=\\\"margin: 0; color: #6c757d; font-size: 16px;\\\">Your account balance has been updated with a new deposit.</p>\\n                        </td>\\n                    </tr>\\n\\n                    <!-- Main Content Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: #ffffff; padding: 20px 40px; color: #333333;\\\">\\n                            <p>Dear {{fullname}},</p><p>We are pleased to inform you that {{amount}} {{currency}} has been added to your account.</p><ul><li><strong>Amount:</strong> {{amount}} {{currency}}</li><li><strong>New Balance:</strong> {{new_balance}} {{currency}}</li><li><strong>Transaction ID:</strong> {{transaction_id}}</li><li><strong>Date:</strong> {{transaction_date}}</li></ul><p>The funds are now available in your account.</p>\\n                        </td>\\n                    </tr>\\n\\n                    <!-- Regards Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: #ffffff; padding: 30px 40px; border-top: 1px solid #e9ecef;\\\">\\n                            <p style=\\\"margin: 0 0 10px 0; font-size: 16px;\\\">Best regards,<br>\\n                            <strong>MBFX Team</strong></p>\\n                            <p style=\\\"font-size: 12px; color: #6c757d; margin: 15px 0 0 0;\\\">\\n                                If you have any questions, please contact our support team.\\n                            </p>\\n                        </td>\\n                    </tr>\\n\\n                    <!-- Footer Section - Full Width -->\\n                    <tr>\\n                        <td width=\\\"100%\\\" style=\\\"background: linear-gradient(135deg, #343a40 0%, #23272b 100%); color: #ffffff; padding: 30px 40px; text-align: center;\\\">\\n                            <p style=\\\"margin: 0 0 10px 0; font-size: 14px;\\\"><strong>MBFX</strong> - Professional Trading Platform</p>\\n                            <p style=\\\"margin: 0 0 10px 0; font-size: 14px;\\\">\\n                                <a href=\\\"{{site_url}}/user/profile/setting\\\" style=\\\"color: #ffffff; text-decoration: none;\\\">Account Settings</a> |\\n                                <a href=\\\"{{site_url}}/contact\\\" style=\\\"color: #ffffff; text-decoration: none;\\\">Contact Support</a> |\\n                                <a href=\\\"{{site_url}}/policy/privacy-policy/99\\\" style=\\\"color: #ffffff; text-decoration: none;\\\">Privacy Policy</a>\\n                            </p>\\n                            <p style=\\\"margin: 15px 0 0 0; font-size: 14px;\\\">\\n                                © 2025 MBFX. All rights reserved.\\n                            </p>\\n                            <p style=\\\"font-size: 10px; color: #999999; margin: 10px 0 0 0;\\\">\\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\\n                                <a href=\\\"{{site_url}}/user/profile/setting\\\" style=\\\"color: #999999; text-decoration: none;\\\">update your preferences</a>.\\n                            </p>\\n                        </td>\\n                    </tr>\\n\\n                </tbody></table>\\n            </td>\\n        </tr>\\n    </tbody></table>', `notification_templates`.`updated_at` = '2025-07-01 10:06:26' where `id` = 1", "type": "query", "params": [], "bindings": ["<meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Balance Added Successfully</title>\n\n\n    <!-- Full Width Container -->\n    <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: #f4f4f4;\">\n        <tbody><tr>\n            <td align=\"center\">\n                <!-- Email Container -->\n                <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: #ffffff; box-shadow: 0 0 10px rgba(0,0,0,0.1);\">\n\n                    <!-- Header Banner - Full Width -->\n                    <tbody><tr>\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); padding: 20px 0; text-align: center;\">\n                            <h1 style=\"margin: 0; font-size: 24px; font-weight: bold; color: #ffffff;\">Balance Notification</h1>\n                        </td>\n                    </tr>\n\n                    <!-- Logo Section -->\n                    <tr>\n                        <td style=\"background-color: #ffffff; text-align: center; padding: 20px; border-bottom: 2px solid #e9ecef;\">\n                            <img src=\"https://mbf.mybrokerforex.com/assets/images/logoIcon/logo.png\" alt=\"MBFX\" style=\"height: 60px; width: auto; display: block; margin: 0 auto;\" onerror=\"this.style.display='none'\">\n                        </td>\n                    </tr>\n\n                    <!-- Title Section -->\n                    <tr>\n                        <td style=\"background-color: #ffffff; padding: 30px 40px 20px; text-align: center;\">\n                            <h2 style=\"margin: 0; color: #dc3545; font-size: 28px; font-weight: bold;\">Balance Added Successfully</h2>\n                        </td>\n                    </tr>\n\n                    <!-- Description Section -->\n                    <tr>\n                        <td style=\"background-color: #ffffff; padding: 0 40px 20px; text-align: center;\">\n                            <p style=\"margin: 0; color: #6c757d; font-size: 16px;\">Your account balance has been updated with a new deposit.</p>\n                        </td>\n                    </tr>\n\n                    <!-- Main Content Section -->\n                    <tr>\n                        <td style=\"background-color: #ffffff; padding: 20px 40px; color: #333333;\">\n                            <p>Dear {{fullname}},</p><p>We are pleased to inform you that {{amount}} {{currency}} has been added to your account.</p><ul><li><strong>Amount:</strong> {{amount}} {{currency}}</li><li><strong>New Balance:</strong> {{new_balance}} {{currency}}</li><li><strong>Transaction ID:</strong> {{transaction_id}}</li><li><strong>Date:</strong> {{transaction_date}}</li></ul><p>The funds are now available in your account.</p>\n                        </td>\n                    </tr>\n\n                    <!-- Regards Section -->\n                    <tr>\n                        <td style=\"background-color: #ffffff; padding: 30px 40px; border-top: 1px solid #e9ecef;\">\n                            <p style=\"margin: 0 0 10px 0; font-size: 16px;\">Best regards,<br>\n                            <strong>MBFX Team</strong></p>\n                            <p style=\"font-size: 12px; color: #6c757d; margin: 15px 0 0 0;\">\n                                If you have any questions, please contact our support team.\n                            </p>\n                        </td>\n                    </tr>\n\n                    <!-- Footer Section - Full Width -->\n                    <tr>\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, #343a40 0%, #23272b 100%); color: #ffffff; padding: 30px 40px; text-align: center;\">\n                            <p style=\"margin: 0 0 10px 0; font-size: 14px;\"><strong>MBFX</strong> - Professional Trading Platform</p>\n                            <p style=\"margin: 0 0 10px 0; font-size: 14px;\">\n                                <a href=\"{{site_url}}/user/profile/setting\" style=\"color: #ffffff; text-decoration: none;\">Account Settings</a> |\n                                <a href=\"{{site_url}}/contact\" style=\"color: #ffffff; text-decoration: none;\">Contact Support</a> |\n                                <a href=\"{{site_url}}/policy/privacy-policy/99\" style=\"color: #ffffff; text-decoration: none;\">Privacy Policy</a>\n                            </p>\n                            <p style=\"margin: 15px 0 0 0; font-size: 14px;\">\n                                © 2025 MBFX. All rights reserved.\n                            </p>\n                            <p style=\"font-size: 10px; color: #999999; margin: 10px 0 0 0;\">\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\n                                <a href=\"{{site_url}}/user/profile/setting\" style=\"color: #999999; text-decoration: none;\">update your preferences</a>.\n                            </p>\n                        </td>\n                    </tr>\n\n                </tbody></table>\n            </td>\n        </tr>\n    </tbody></table>", "2025-07-01 10:06:26", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/NotificationController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\NotificationController.php", "line": 215}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2013268, "duration": 0.015, "duration_str": "15ms", "memory": 0, "memory_str": null, "filename": "NotificationController.php:215", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/NotificationController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\NotificationController.php", "line": 215}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FNotificationController.php&line=215", "ajax": false, "filename": "NotificationController.php", "line": "215"}, "connection": "mbf-db", "explain": null, "start_percent": 31.382, "width_percent": 64.572}, {"sql": "select * from `notification_templates` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/NotificationController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\NotificationController.php", "line": 219}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.223255, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "NotificationController.php:219", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/NotificationController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\NotificationController.php", "line": 219}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FNotificationController.php&line=219", "ajax": false, "filename": "NotificationController.php", "line": "219"}, "connection": "mbf-db", "explain": null, "start_percent": 95.954, "width_percent": 4.046}]}, "models": {"data": {"App\\Models\\NotificationTemplate": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FModels%2FNotificationTemplate.php&line=1", "ajax": false, "filename": "NotificationTemplate.php", "line": "?"}}, "App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/update/1", "action_name": "admin.setting.notification.template.update", "controller_action": "App\\Http\\Controllers\\Admin\\NotificationController@templateUpdate", "uri": "POST admin/notification/template/update/{id}", "controller": "App\\Http\\Controllers\\Admin\\NotificationController@templateUpdate<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FNotificationController.php&line=98\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin/notification", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FNotificationController.php&line=98\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/NotificationController.php:98-259</a>", "middleware": "web, admin", "duration": "729ms", "peak_memory": "30MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1556865074 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1556865074\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6wydTXTQETk9uM050OalbDmHEZPTqQskjidNxWTA</span>\"\n  \"<span class=sf-dump-key>template_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>subject</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Your Account has been Credited</span>\"\n  \"<span class=sf-dump-key>email_status</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>sms_status</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>sms_body</span>\" => \"<span class=sf-dump-str title=\"167 characters\">{{amount}} {{wallet_currency}}  credited in your account. Your Current Balance {{post_balance}} {{wallet_currency}} . Transaction: #{{trx}}. Admin note is &quot;{{remark}}&quot;</span>\"\n  \"<span class=sf-dump-key>email_body_encoded</span>\" => \"<span class=sf-dump-str title=\"6796 characters\">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</span>\"\n  \"<span class=sf-dump-key>email_body</span>\" => \"\"\"\n    <span class=sf-dump-str title=\"5176 characters\">&lt;meta charset=&quot;UTF-8&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">    &lt;title&gt;Balance Added Successfully&lt;/title&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">    &lt;!-- Full Width Container --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">    &lt;table width=&quot;100%&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; style=&quot;background-color: #f4f4f4;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">        &lt;tbody&gt;&lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">            &lt;td align=&quot;center&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                &lt;!-- Email Container --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; style=&quot;background-color: #ffffff; box-shadow: 0 0 10px rgba(0,0,0,0.1);&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;!-- Header Banner - Full Width --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;tbody&gt;&lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                        &lt;td width=&quot;100%&quot; style=&quot;background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); padding: 20px 0; text-align: center;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;h1 style=&quot;margin: 0; font-size: 24px; font-weight: bold; color: #ffffff;&quot;&gt;Balance Notification&lt;/h1&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;!-- Logo Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                        &lt;td style=&quot;background-color: #ffffff; text-align: center; padding: 20px; border-bottom: 2px solid #e9ecef;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;img src=&quot;https://mbf.mybrokerforex.com/assets/images/logoIcon/logo.png&quot; alt=&quot;MBFX&quot; style=&quot;height: 60px; width: auto; display: block; margin: 0 auto;&quot; onerror=&quot;this.style.display=&#039;none&#039;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;!-- Title Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                        &lt;td style=&quot;background-color: #ffffff; padding: 30px 40px 20px; text-align: center;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;h2 style=&quot;margin: 0; color: #dc3545; font-size: 28px; font-weight: bold;&quot;&gt;Balance Added Successfully&lt;/h2&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;!-- Description Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                        &lt;td style=&quot;background-color: #ffffff; padding: 0 40px 20px; text-align: center;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;p style=&quot;margin: 0; color: #6c757d; font-size: 16px;&quot;&gt;Your account balance has been updated with a new deposit.&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;!-- Main Content Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                        &lt;td style=&quot;background-color: #ffffff; padding: 20px 40px; color: #333333;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;p&gt;Dear {{fullname}},&lt;/p&gt;&lt;p&gt;We are pleased to inform you that {{amount}} {{currency}} has been added to your account.&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Amount:&lt;/strong&gt; {{amount}} {{currency}}&lt;/li&gt;&lt;li&gt;&lt;strong&gt;New Balance:&lt;/strong&gt; {{new_balance}} {{currency}}&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Transaction ID:&lt;/strong&gt; {{transaction_id}}&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Date:&lt;/strong&gt; {{transaction_date}}&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;The funds are now available in your account.&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;!-- Regards Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                        &lt;td style=&quot;background-color: #ffffff; padding: 30px 40px; border-top: 1px solid #e9ecef;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;p style=&quot;margin: 0 0 10px 0; font-size: 16px;&quot;&gt;Best regards,&lt;br&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;strong&gt;MBFX Team&lt;/strong&gt;&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;p style=&quot;font-size: 12px; color: #6c757d; margin: 15px 0 0 0;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                                If you have any questions, please contact our support team.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;!-- Footer Section - Full Width --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                        &lt;td width=&quot;100%&quot; style=&quot;background: linear-gradient(135deg, #343a40 0%, #23272b 100%); color: #ffffff; padding: 30px 40px; text-align: center;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;p style=&quot;margin: 0 0 10px 0; font-size: 14px;&quot;&gt;&lt;strong&gt;MBFX&lt;/strong&gt; - Professional Trading Platform&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;p style=&quot;margin: 0 0 10px 0; font-size: 14px;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                                &lt;a href=&quot;{{site_url}}/user/profile/setting&quot; style=&quot;color: #ffffff; text-decoration: none;&quot;&gt;Account Settings&lt;/a&gt; |<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                                &lt;a href=&quot;{{site_url}}/contact&quot; style=&quot;color: #ffffff; text-decoration: none;&quot;&gt;Contact Support&lt;/a&gt; |<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                                &lt;a href=&quot;{{site_url}}/policy/privacy-policy/99&quot; style=&quot;color: #ffffff; text-decoration: none;&quot;&gt;Privacy Policy&lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;p style=&quot;margin: 15px 0 0 0; font-size: 14px;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                                &#169; 2025 MBFX. All rights reserved.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;p style=&quot;font-size: 10px; color: #999999; margin: 10px 0 0 0;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                                This email was sent to {{email}}. If you no longer wish to receive these emails,<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                                &lt;a href=&quot;{{site_url}}/user/profile/setting&quot; style=&quot;color: #999999; text-decoration: none;&quot;&gt;update your preferences&lt;/a&gt;.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                &lt;/tbody&gt;&lt;/table&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">            &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">        &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">    &lt;/tbody&gt;&lt;/table&gt;</span>\n    \"\"\"\n  \"<span class=sf-dump-key>email_body_final</span>\" => \"\"\"\n    <span class=sf-dump-str title=\"5176 characters\">&lt;meta charset=&quot;UTF-8&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">    &lt;title&gt;Balance Added Successfully&lt;/title&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">    &lt;!-- Full Width Container --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">    &lt;table width=&quot;100%&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; style=&quot;background-color: #f4f4f4;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">        &lt;tbody&gt;&lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">            &lt;td align=&quot;center&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                &lt;!-- Email Container --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; style=&quot;background-color: #ffffff; box-shadow: 0 0 10px rgba(0,0,0,0.1);&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;!-- Header Banner - Full Width --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;tbody&gt;&lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                        &lt;td width=&quot;100%&quot; style=&quot;background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); padding: 20px 0; text-align: center;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;h1 style=&quot;margin: 0; font-size: 24px; font-weight: bold; color: #ffffff;&quot;&gt;Balance Notification&lt;/h1&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;!-- Logo Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                        &lt;td style=&quot;background-color: #ffffff; text-align: center; padding: 20px; border-bottom: 2px solid #e9ecef;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;img src=&quot;https://mbf.mybrokerforex.com/assets/images/logoIcon/logo.png&quot; alt=&quot;MBFX&quot; style=&quot;height: 60px; width: auto; display: block; margin: 0 auto;&quot; onerror=&quot;this.style.display=&#039;none&#039;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;!-- Title Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                        &lt;td style=&quot;background-color: #ffffff; padding: 30px 40px 20px; text-align: center;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;h2 style=&quot;margin: 0; color: #dc3545; font-size: 28px; font-weight: bold;&quot;&gt;Balance Added Successfully&lt;/h2&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;!-- Description Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                        &lt;td style=&quot;background-color: #ffffff; padding: 0 40px 20px; text-align: center;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;p style=&quot;margin: 0; color: #6c757d; font-size: 16px;&quot;&gt;Your account balance has been updated with a new deposit.&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;!-- Main Content Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                        &lt;td style=&quot;background-color: #ffffff; padding: 20px 40px; color: #333333;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;p&gt;Dear {{fullname}},&lt;/p&gt;&lt;p&gt;We are pleased to inform you that {{amount}} {{currency}} has been added to your account.&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Amount:&lt;/strong&gt; {{amount}} {{currency}}&lt;/li&gt;&lt;li&gt;&lt;strong&gt;New Balance:&lt;/strong&gt; {{new_balance}} {{currency}}&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Transaction ID:&lt;/strong&gt; {{transaction_id}}&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Date:&lt;/strong&gt; {{transaction_date}}&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;The funds are now available in your account.&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;!-- Regards Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                        &lt;td style=&quot;background-color: #ffffff; padding: 30px 40px; border-top: 1px solid #e9ecef;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;p style=&quot;margin: 0 0 10px 0; font-size: 16px;&quot;&gt;Best regards,&lt;br&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;strong&gt;MBFX Team&lt;/strong&gt;&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;p style=&quot;font-size: 12px; color: #6c757d; margin: 15px 0 0 0;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                                If you have any questions, please contact our support team.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;!-- Footer Section - Full Width --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                        &lt;td width=&quot;100%&quot; style=&quot;background: linear-gradient(135deg, #343a40 0%, #23272b 100%); color: #ffffff; padding: 30px 40px; text-align: center;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;p style=&quot;margin: 0 0 10px 0; font-size: 14px;&quot;&gt;&lt;strong&gt;MBFX&lt;/strong&gt; - Professional Trading Platform&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;p style=&quot;margin: 0 0 10px 0; font-size: 14px;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                                &lt;a href=&quot;{{site_url}}/user/profile/setting&quot; style=&quot;color: #ffffff; text-decoration: none;&quot;&gt;Account Settings&lt;/a&gt; |<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                                &lt;a href=&quot;{{site_url}}/contact&quot; style=&quot;color: #ffffff; text-decoration: none;&quot;&gt;Contact Support&lt;/a&gt; |<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                                &lt;a href=&quot;{{site_url}}/policy/privacy-policy/99&quot; style=&quot;color: #ffffff; text-decoration: none;&quot;&gt;Privacy Policy&lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;p style=&quot;margin: 15px 0 0 0; font-size: 14px;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                                &#169; 2025 MBFX. All rights reserved.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;p style=&quot;font-size: 10px; color: #999999; margin: 10px 0 0 0;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                                This email was sent to {{email}}. If you no longer wish to receive these emails,<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                                &lt;a href=&quot;{{site_url}}/user/profile/setting&quot; style=&quot;color: #999999; text-decoration: none;&quot;&gt;update your preferences&lt;/a&gt;.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">                &lt;/tbody&gt;&lt;/table&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">            &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">        &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5176 characters\">    &lt;/tbody&gt;&lt;/table&gt;</span>\n    \"\"\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1668039565 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">18315</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryEiGFPUBlJk0TXD7u</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">https://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"83 characters\">https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/edit/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">en-US,en;q=0.9,ms-MY;q=0.8,ms;q=0.7,zh-CN;q=0.6,zh;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ilo0clBVOFFWaXhwNG52ZXU0MU5rYmc9PSIsInZhbHVlIjoiNEJHcFJUd1BvYjB4WnpaR3UvNENOMTQ3Wk9Mb0VZWXg1ZHZjSDd2Wk4vOFhRZE53NGxHWkpzd3k1QVZqQXJZRnlJWEhHUGd1eXZUU1Axb3d3U1BBQ3JDQ3JMb2l5Q1FZVkM1dkkyVzRnWkR5YTYyM3pPc1ZmR2dkOGUzRDI5TWoiLCJtYWMiOiIyYjFkYTY1NGU2ODRmNDk1ZTg4NjE4ODYzNzJlNjkwMGNmMmI4ZTZjYWM4MzVlNDBmNGMxMjljODVkMjRkMjUwIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Imp6ZXZKb1lmK2ROU2h0aTJKQkw1WWc9PSIsInZhbHVlIjoiZHR2SDFFa2NaWFZ6eXhZWlI5Y3BIdTNxaXFjbmhlV1ZTdmF6SnZOQ2VMbE5LeW84T3pLeWFVWjU0d1hjWG5qZ20yVmFwTzMvN3o3Sk96T0FnMmhEWjhwV1RWTVcxVFpWd3N1WHpGaWdFa2xPWVhwTlJUUWJXOEViU3RaOHJLY1UiLCJtYWMiOiJjNGMyMmJhNWRhYWM5NTRhODQ0ODQ0N2Y3ZjhlYmUwZDU1NmIxNDExNWU0MWU0YzU5ZTZkNjRmMjgzMjU3MWQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1668039565\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1497480183 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6wydTXTQETk9uM050OalbDmHEZPTqQskjidNxWTA</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pKwSOwBxhh7a0b12K8rA1re0Q1SyO815L9Vl6qQN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1497480183\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-514981754 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 10:06:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-514981754\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-587548040 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6wydTXTQETk9uM050OalbDmHEZPTqQskjidNxWTA</span>\"\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"83 characters\">https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/edit/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>flasher::envelopes</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>53630</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-587548040\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/update/1", "action_name": "admin.setting.notification.template.update", "controller_action": "App\\Http\\Controllers\\Admin\\NotificationController@templateUpdate"}, "badge": null}}