<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rebate_rules', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('ib_group_id');
            $table->unsignedBigInteger('symbol_group_id')->nullable();
            $table->string('symbol')->nullable()->comment('Specific symbol or null for group-wide rule');
            $table->decimal('rebate_per_lot', 10, 2)->default(0)->comment('Base rebate amount per lot');
            $table->decimal('min_volume', 10, 2)->default(0)->comment('Minimum volume for rebate');
            $table->decimal('max_volume', 10, 2)->nullable()->comment('Maximum volume for rebate');
            $table->boolean('status')->default(true)->comment('Active/Inactive');
            $table->timestamps();
            
            // Foreign keys
            $table->foreign('ib_group_id')->references('id')->on('ib_groups')->onDelete('cascade');
            $table->foreign('symbol_group_id')->references('id')->on('symbol_groups')->onDelete('set null');
            
            // Indexes
            $table->index(['ib_group_id', 'symbol']);
            $table->index(['ib_group_id', 'symbol_group_id']);
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rebate_rules');
    }
};
