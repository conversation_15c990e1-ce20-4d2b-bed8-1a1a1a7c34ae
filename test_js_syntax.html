<!DOCTYPE html>
<html>
<head>
    <title>JavaScript Syntax Test</title>
</head>
<body>
    <h1>JavaScript Syntax Test</h1>
    <div id="result"></div>
    
    <script>
        console.log('Testing JavaScript syntax...');
        
        try {
            // Test the problematic line from the error
            const CONFIG = {
                DEBUG: window.location.hostname === 'localhost' || window.location.search.includes('debug=1'),
                INIT_DELAY: 500,
                RETRY_ATTEMPTS: 3,
                enhanced: true,
                version: '3.0',
                features: {
                    preview: true,
                    realtime: true,
                    validation: true
                }
            };
            
            console.log('✅ CONFIG object created successfully');
            document.getElementById('result').innerHTML = '<p style="color: green;">✅ JavaScript syntax is valid</p>';
            
        } catch (error) {
            console.error('❌ JavaScript syntax error:', error);
            document.getElementById('result').innerHTML = '<p style="color: red;">❌ JavaScript syntax error: ' + error.message + '</p>';
        }
    </script>
    
    <!-- Test loading the actual file -->
    <script>
        console.log('Testing external JavaScript file...');
        
        // Create a script element to test the external file
        const script = document.createElement('script');
        script.src = 'assets/admin/js/simple-email-editor.js';
        script.onload = function() {
            console.log('✅ External JavaScript file loaded successfully');
            document.getElementById('result').innerHTML += '<p style="color: green;">✅ External JavaScript file loaded successfully</p>';
        };
        script.onerror = function() {
            console.error('❌ External JavaScript file failed to load');
            document.getElementById('result').innerHTML += '<p style="color: red;">❌ External JavaScript file failed to load</p>';
        };
        
        // Don't actually load it to avoid conflicts, just test the syntax above
        // document.head.appendChild(script);
    </script>
</body>
</html>
