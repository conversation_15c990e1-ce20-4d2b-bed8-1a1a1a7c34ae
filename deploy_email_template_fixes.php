<?php
/**
 * Deploy Email Template Fixes
 * Comprehensive script to apply all JavaScript and link fixes
 * 
 * Usage: php deploy_email_template_fixes.php
 */

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🚀 DEPLOYING EMAIL TEMPLATE FIXES\n";
echo "==================================\n\n";

$startTime = microtime(true);
$errors = [];
$successes = [];

try {
    // Step 1: Clear all caches
    echo "🗄️  Step 1: Clearing all caches...\n";
    
    $cacheCommands = [
        'cache:clear' => 'Application Cache',
        'config:clear' => 'Configuration Cache',
        'view:clear' => 'View Cache',
        'route:clear' => 'Route Cache',
        'optimize:clear' => 'All Optimizations'
    ];
    
    foreach ($cacheCommands as $command => $description) {
        try {
            Artisan::call($command);
            echo "   ✅ {$description} cleared\n";
            $successes[] = "Cache cleared: {$description}";
        } catch (Exception $e) {
            echo "   ❌ Failed to clear {$description}: " . $e->getMessage() . "\n";
            $errors[] = "Cache clear failed: {$description} - " . $e->getMessage();
        }
    }
    
    echo "\n";

    // Step 2: Verify shortcode service enhancements
    echo "🔗 Step 2: Verifying shortcode service enhancements...\n";
    
    $shortcodeService = new App\Services\ShortcodeService();
    $testContext = [
        'fullname' => 'Test User',
        'email' => '<EMAIL>',
        'username' => 'testuser'
    ];
    
    $testShortcodes = [
        'site_url',
        'admin_url',
        'login_url',
        'dashboard_url',
        'footer_contact_support',
        'footer_login_account',
        'btn_login_account',
        'btn_verify_email',
        'mt5_download_link'
    ];
    
    $processedContent = $shortcodeService->processShortcodes('Test {{site_url}} {{login_url}}', $testContext);
    
    foreach ($testShortcodes as $shortcode) {
        $testContent = "{{" . $shortcode . "}}";
        $processed = $shortcodeService->processShortcodes($testContent, $testContext);
        
        if ($processed !== $testContent) {
            echo "   ✅ Shortcode working: {$shortcode}\n";
            $successes[] = "Shortcode verified: {$shortcode}";
        } else {
            echo "   ❌ Shortcode not working: {$shortcode}\n";
            $errors[] = "Shortcode failed: {$shortcode}";
        }
    }
    
    echo "\n";

    // Step 3: Run enhanced email template command
    echo "📧 Step 3: Applying enhanced email templates...\n";
    
    try {
        // First, check current templates
        $templates = App\Models\NotificationTemplate::all();
        echo "   📋 Found {$templates->count()} templates to enhance\n";
        
        // Run the enhancement command
        Artisan::call('email:enhance-templates');
        $output = Artisan::output();
        
        echo "   📝 Enhancement command output:\n";
        $lines = explode("\n", trim($output));
        foreach ($lines as $line) {
            if (!empty(trim($line))) {
                echo "      " . trim($line) . "\n";
            }
        }
        
        $successes[] = "Email templates enhanced successfully";
        
    } catch (Exception $e) {
        echo "   ❌ Failed to enhance templates: " . $e->getMessage() . "\n";
        $errors[] = "Template enhancement failed: " . $e->getMessage();
    }
    
    echo "\n";

    // Step 4: Verify template links
    echo "🔍 Step 4: Verifying template links...\n";
    
    $templatesAfter = App\Models\NotificationTemplate::all();
    $totalLinks = 0;
    $workingLinks = 0;
    
    foreach ($templatesAfter as $template) {
        preg_match_all('/href=["\']([^"\']*)["\']/', $template->email_body, $matches);
        $links = $matches[1];
        
        foreach ($links as $link) {
            $totalLinks++;
            
            // Check if link is properly formatted
            if (strpos($link, 'http') === 0 || strpos($link, '/') === 0 || strpos($link, 'mailto:') === 0) {
                $workingLinks++;
            }
        }
    }
    
    echo "   📊 Link Analysis:\n";
    echo "      Total links found: {$totalLinks}\n";
    echo "      Properly formatted links: {$workingLinks}\n";
    
    if ($totalLinks > 0) {
        $linkPercentage = round(($workingLinks / $totalLinks) * 100);
        echo "      Link quality: {$linkPercentage}%\n";
        
        if ($linkPercentage >= 90) {
            echo "   ✅ Link quality is excellent\n";
            $successes[] = "Link quality verified: {$linkPercentage}%";
        } else {
            echo "   ⚠️  Link quality needs improvement: {$linkPercentage}%\n";
            $errors[] = "Link quality below 90%: {$linkPercentage}%";
        }
    }
    
    echo "\n";

    // Step 5: Test notification system
    echo "🔔 Step 5: Testing notification system...\n";
    
    // Check if notification functions exist
    $notificationFiles = [
        'resources/views/admin/layouts/app.blade.php' => 'Admin Layout',
        'resources/views/admin/notification/edit.blade.php' => 'Template Editor'
    ];
    
    foreach ($notificationFiles as $file => $description) {
        if (file_exists($file)) {
            $content = file_get_contents($file);
            
            if (strpos($content, 'notify(') !== false) {
                echo "   ✅ Notification function found in {$description}\n";
                $successes[] = "Notification function verified: {$description}";
            } else {
                echo "   ❌ Notification function missing in {$description}\n";
                $errors[] = "Notification function missing: {$description}";
            }
        } else {
            echo "   ⚠️  File not found: {$file}\n";
            $errors[] = "File not found: {$file}";
        }
    }
    
    echo "\n";

    // Step 6: Generate test email
    echo "📬 Step 6: Generating test email preview...\n";
    
    try {
        $testTemplate = $templatesAfter->first();
        if ($testTemplate) {
            $shortcodeService = new App\Services\ShortcodeService();
            $testContext = [
                'fullname' => 'John Doe',
                'email' => '<EMAIL>',
                'username' => 'johndoe',
                'site_name' => 'MBFX',
                'code' => '123456'
            ];
            
            $processedContent = $shortcodeService->processShortcodes($testTemplate->email_body, $testContext);
            
            // Count processed shortcodes
            preg_match_all('/\{\{[^}]+\}\}/', $testTemplate->email_body, $originalShortcodes);
            preg_match_all('/\{\{[^}]+\}\}/', $processedContent, $remainingShortcodes);
            
            $processedCount = count($originalShortcodes[0]) - count($remainingShortcodes[0]);
            
            echo "   📧 Test template: {$testTemplate->name}\n";
            echo "   🔄 Shortcodes processed: {$processedCount}/" . count($originalShortcodes[0]) . "\n";
            
            if ($processedCount > 0) {
                echo "   ✅ Shortcode processing is working\n";
                $successes[] = "Shortcode processing verified";
            } else {
                echo "   ❌ Shortcode processing may have issues\n";
                $errors[] = "Shortcode processing not working properly";
            }
            
            // Save test email preview
            file_put_contents('test_email_preview.html', $processedContent);
            echo "   💾 Test email preview saved to: test_email_preview.html\n";
            
        } else {
            echo "   ⚠️  No templates found for testing\n";
            $errors[] = "No templates available for testing";
        }
        
    } catch (Exception $e) {
        echo "   ❌ Failed to generate test email: " . $e->getMessage() . "\n";
        $errors[] = "Test email generation failed: " . $e->getMessage();
    }
    
    echo "\n";

} catch (Exception $e) {
    echo "❌ Critical error during deployment: " . $e->getMessage() . "\n";
    $errors[] = "Critical deployment error: " . $e->getMessage();
}

// Final summary
$endTime = microtime(true);
$duration = round($endTime - $startTime, 2);

echo "📊 DEPLOYMENT SUMMARY\n";
echo "====================\n";
echo "⏱️  Duration: {$duration} seconds\n";
echo "✅ Successes: " . count($successes) . "\n";
echo "❌ Errors: " . count($errors) . "\n\n";

if (!empty($successes)) {
    echo "✅ SUCCESSFUL OPERATIONS:\n";
    foreach ($successes as $success) {
        echo "   - {$success}\n";
    }
    echo "\n";
}

if (!empty($errors)) {
    echo "❌ ERRORS ENCOUNTERED:\n";
    foreach ($errors as $error) {
        echo "   - {$error}\n";
    }
    echo "\n";
}

// Recommendations
echo "🎯 NEXT STEPS:\n";
echo "==============\n";

if (count($errors) === 0) {
    echo "🎉 All fixes deployed successfully!\n";
    echo "1. Test the template editor in admin panel\n";
    echo "2. Verify JavaScript console shows no errors\n";
    echo "3. Check that HTML editor tab works properly\n";
    echo "4. Test email sending with enhanced templates\n";
    echo "5. Verify all links work in sent emails\n";
} else {
    echo "⚠️  Some issues need attention:\n";
    echo "1. Review the errors listed above\n";
    echo "2. Check file permissions and paths\n";
    echo "3. Verify database connectivity\n";
    echo "4. Test individual components manually\n";
    echo "5. Check server logs for additional details\n";
}

echo "\n🔗 TEST URLS:\n";
echo "=============\n";
echo "- Template Editor: " . url('/admin/notification/template/edit/1') . "\n";
echo "- Admin Dashboard: " . url('/admin') . "\n";
echo "- Test Email Preview: test_email_preview.html\n";

echo "\n🎯 Deployment completed!\n";

?>
