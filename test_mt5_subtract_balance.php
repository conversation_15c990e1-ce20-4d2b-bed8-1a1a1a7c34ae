<?php

require_once 'vendor/autoload.php';

echo "🧪 Testing MT5 Subtract Balance Python Integration\n";
echo "================================================\n\n";

// Test the exact command that would be executed for subtract balance
$pythonExe = env('PYTHON_EXE', 'python');
$pythonScript = base_path('python/mt5manager.py');

// Test parameters (using safe test values)
$testLogin = 123456; // Replace with actual test MT5 login
$testAmount = -10.00; // Negative amount for subtract
$testComment = "Test subtract balance operation";

echo "🔍 Testing Subtract Balance Command Construction:\n";
echo "================================================\n";
echo "Python Executable: {$pythonExe}\n";
echo "Python Script: {$pythonScript}\n";
echo "Test Login: {$testLogin}\n";
echo "Test Amount: {$testAmount}\n";
echo "Test Comment: {$testComment}\n\n";

// Build the exact command that the controller uses
$command = sprintf(
    '%s "%s" add_balance --login %d --amount %.2f --comment "%s" 2>&1',
    $pythonExe,
    $pythonScript,
    $testLogin,
    $testAmount,
    escapeshellarg($testComment)
);

echo "🔧 Generated Command:\n";
echo "====================\n";
echo $command . "\n\n";

echo "📋 Command Analysis:\n";
echo "===================\n";
echo "✅ Uses 'add_balance' command (correct for both add and subtract)\n";
echo "✅ Negative amount (-10.00) for subtract operation\n";
echo "✅ Proper escaping of comment parameter\n";
echo "✅ 2>&1 redirection for error capture\n\n";

echo "🔍 Environment Variables Check:\n";
echo "==============================\n";
echo "PYTHON_EXE: " . (env('PYTHON_EXE') ?: 'Not set (using default: python)') . "\n";
echo "PYTHON_SCRIPT: " . (env('PYTHON_SCRIPT') ?: 'Not set (using default path)') . "\n\n";

echo "📁 File Existence Check:\n";
echo "========================\n";
echo "Python Script Exists: " . (file_exists($pythonScript) ? "✅ YES" : "❌ NO") . "\n";
echo "Python Script Path: {$pythonScript}\n\n";

if (file_exists($pythonScript)) {
    echo "🔍 Python Script Content Check:\n";
    echo "===============================\n";
    
    // Check if the add_balance function exists in the script
    $scriptContent = file_get_contents($pythonScript);
    
    if (strpos($scriptContent, 'def add_balance') !== false) {
        echo "✅ add_balance function found in script\n";
    } else {
        echo "❌ add_balance function NOT found in script\n";
    }
    
    if (strpos($scriptContent, 'DealerBalance') !== false) {
        echo "✅ DealerBalance method found in script\n";
    } else {
        echo "❌ DealerBalance method NOT found in script\n";
    }
    
    if (strpos($scriptContent, 'amount < 0') !== false || strpos($scriptContent, 'negative') !== false) {
        echo "✅ Negative amount handling found in script\n";
    } else {
        echo "⚠️ Negative amount handling not explicitly found\n";
    }
}

echo "\n🧪 DEBUGGING STEPS FOR SUBTRACT BALANCE ISSUE:\n";
echo "==============================================\n\n";

echo "**Step 1: Check Laravel Logs**\n";
echo "- Location: storage/logs/laravel.log\n";
echo "- Look for: 'MT5 Balance Operation Debug' entries\n";
echo "- Check: original_amount vs processed_amount for subtract operations\n\n";

echo "**Step 2: Verify Python Script Execution**\n";
echo "- Test command manually in terminal:\n";
echo "  {$command}\n";
echo "- Expected output: JSON with status 'success' or 'error'\n\n";

echo "**Step 3: Check MT5 Server Connection**\n";
echo "- Verify MT5 server is accessible\n";
echo "- Check manager credentials are correct\n";
echo "- Test with a small positive amount first (add balance)\n\n";

echo "**Step 4: Compare Add vs Subtract Operations**\n";
echo "- Add Balance: amount = abs(\$request->amount)\n";
echo "- Subtract Balance: amount = -abs(\$request->amount)\n";
echo "- Both use same 'add_balance' Python command\n\n";

echo "**Step 5: Test with Actual MT5 Account**\n";
echo "- Use a real MT5 login from admin user detail page\n";
echo "- Start with small amounts (\$1-\$5)\n";
echo "- Check MT5 terminal for balance changes\n\n";

echo "🔧 POTENTIAL FIXES:\n";
echo "==================\n\n";

echo "**Fix 1: Enhanced Error Handling**\n";
echo "- Add more detailed logging in updateMT5Balance method\n";
echo "- Log the exact Python output and parsing results\n";
echo "- Check for specific error messages from MT5 server\n\n";

echo "**Fix 2: Python Script Validation**\n";
echo "- Ensure DealerBalance method handles negative amounts correctly\n";
echo "- Verify MT5Manager connection is stable\n";
echo "- Add timeout handling for long-running operations\n\n";

echo "**Fix 3: Account Balance Validation**\n";
echo "- Check if account has sufficient balance before subtract\n";
echo "- Verify account is not in read-only mode\n";
echo "- Ensure account group allows balance modifications\n\n";

echo "**Fix 4: Command Execution Environment**\n";
echo "- Test if Python executable path is correct\n";
echo "- Verify all required Python modules are installed\n";
echo "- Check Windows/Linux path differences\n\n";

echo "🎯 TESTING CHECKLIST:\n";
echo "====================\n";
echo "□ Test Add Balance operation (should work)\n";
echo "□ Check Laravel logs for MT5 operation details\n";
echo "□ Test Python script manually with negative amount\n";
echo "□ Verify MT5 account has sufficient balance\n";
echo "□ Check MT5 server connection status\n";
echo "□ Test with different MT5 accounts\n";
echo "□ Verify environment variables are set correctly\n";
echo "□ Check file permissions on Python script\n\n";

echo "📊 EXPECTED BEHAVIOR:\n";
echo "====================\n";
echo "✅ Add Balance: Positive amount → Increases MT5 balance\n";
echo "✅ Subtract Balance: Negative amount → Decreases MT5 balance\n";
echo "✅ Both operations: Use same Python 'add_balance' command\n";
echo "✅ Success response: JSON with status 'success' and new_balance\n";
echo "✅ Error response: JSON with status 'error' and error message\n\n";

echo "🚨 COMMON ISSUES:\n";
echo "================\n";
echo "❌ Insufficient balance in MT5 account\n";
echo "❌ MT5 server connection timeout\n";
echo "❌ Account in read-only mode\n";
echo "❌ Invalid MT5 login credentials\n";
echo "❌ Python script execution permissions\n";
echo "❌ Missing Python dependencies\n\n";

echo "Ready for systematic debugging! 🔍\n";
