@extends('admin.layouts.app')

@section('panel')
<div class="row">
    <div class="col-lg-8">
        <div class="card b-radius--10">
            <div class="card-header">
                <h5 class="card-title mb-0">@lang('IB Application Details - ') {{ $formData->username ?? 'N/A' }}</h5>
                <div class="status-message mt-2">
                    @if ($user->ib_status == 'approved')
                        <span class="badge badge--success">@lang('Approved IB')</span>
                        <span class="badge badge--info">{{ ucfirst($user->ib_type) }} IB</span>
                        @if($user->ibGroup)
                            <span class="badge badge--primary">{{ $user->ibGroup->name }}</span>
                        @endif
                    @elseif ($user->ib_status == 'rejected')
                        <span class="badge badge--danger">@lang('Rejected')</span>
                    @elseif ($user->ib_status == 'pending')
                        <span class="badge badge--warning">@lang('Pending Approval')</span>
                    @else
                        <span class="badge badge--secondary">@lang('No Status')</span>
                    @endif
                </div>
            </div>
            <div class="card-body">
                <!-- Application Information -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>@lang('Selected Country')</label>
                            <input class="form-control" type="text" value="{{ $formData->country }}" readonly>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>@lang('Market Services')</label>
                            <input class="form-control" type="text" value="{{ $formData->services ?? 'N/A' }}" readonly>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            @php
                                $selectedOptions = json_decode($formData->selectable_options, true) ?? [];
                                $formattedOptions = implode(' & ', $selectedOptions);
                            @endphp
                            <label>@lang('How Do You Attract Your Clients')</label>
                            <input type="text" class="form-control" value="{{ $formattedOptions ?? 'N/A' }}" readonly>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            @php
                                $backgroundOptions = json_decode($formData->background_options, true) ?? [];
                                $formattedBackground = implode(' & ', $backgroundOptions);
                            @endphp
                            <label>@lang('Brokers Background')</label>
                            <input type="text" class="form-control" value="{{ $formattedBackground ?: 'N/A' }}" readonly>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>@lang('Trading Volume (3 Months)')</label>
                            <input class="form-control" type="text" value="{{ $formData->trading_volume ?? 'N/A' }}" readonly>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>@lang('Expected Clients (3 Months)')</label>
                            <input class="form-control" type="text" value="{{ $formData->expected_clients ?? 'N/A' }}" readonly>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>@lang('Active Clients')</label>
                            <input class="form-control" type="text" value="{{ $formData->active_clients ?? 'N/A' }}" readonly>
                        </div>
                    </div>
                </div>

                @if($user->ib_status === 'approved')
                <!-- Current IB Information -->
                <div class="border-top pt-4 mt-4">
                    <h6 class="mb-3">@lang('Current IB Information')</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('IB Type')</label>
                                <input class="form-control" type="text" value="{{ ucfirst($user->ib_type) }} IB" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Referral Code')</label>
                                <input class="form-control" type="text" value="{{ $user->referral_code }}" readonly>
                            </div>
                        </div>
                    </div>
                    @if($user->ibParent)
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Parent IB')</label>
                                <input class="form-control" type="text" value="{{ $user->ibParent->fullname }} ({{ $user->ibParent->username }})" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Hierarchy Level')</label>
                                <input class="form-control" type="text" value="Level {{ $user->getIbLevelInHierarchy() }}" readonly>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
                @endif
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        {{-- TASK 5: Professional Admin IB Application Review Interface --}}
        @if($user->ib_status === 'pending')
        <!-- Professional Approval Form -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom" style="padding: 1.5rem;">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <div class="bg-warning rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                            <i class="fas fa-clock text-white"></i>
                        </div>
                    </div>
                    <div>
                        <h5 class="mb-1" style="color: #333; font-weight: 600; font-size: 16px;">Application Review</h5>
                        <p class="mb-0 text-muted" style="font-size: 12px;">Pending approval since {{ $user->created_at->format('M d, Y') }}</p>
                    </div>
                </div>
            </div>

            <div class="card-body" style="padding: 1.5rem;">
                {{-- TASK 5: Critical Fix - Proper State Management --}}
                <form method="POST" action="{{ route('admin.ib.approve', $user->id) }}" id="approval-form">
                    @csrf
                    <div class="mb-4">
                        <label class="form-label" style="font-weight: 500; color: #333; font-size: 14px; margin-bottom: 8px;">
                            @lang('IB Type') <span class="text-danger">*</span>
                        </label>
                        <select name="ib_type" class="form-select" required
                          style="border: 1px solid #ddd; border-radius: 6px; padding: 12px; font-size: 14px; background-color: #fff;">
                            <option value="">@lang('Select IB Type')</option>
                            <option value="master">@lang('Master IB (50% Commission)')</option>
                            <option value="sub">@lang('Sub IB (30% Commission)')</option>
                        </select>
                        <small class="text-muted" style="font-size: 12px;">Choose the appropriate IB level for this applicant</small>
                    </div>

                    <div class="mb-4">
                        <label class="form-label" style="font-weight: 500; color: #333; font-size: 14px; margin-bottom: 8px;">
                            @lang('IB Group')
                        </label>
                        <select name="ib_group_id" class="form-select"
                          style="border: 1px solid #ddd; border-radius: 6px; padding: 12px; font-size: 14px; background-color: #fff;">
                            <option value="">@lang('Default Group')</option>
                            @foreach($ibGroups as $group)
                                <option value="{{ $group->id }}">{{ $group->name }}</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="mb-4 parent-ib-group" style="display: none;">
                        <label class="form-label" style="font-weight: 500; color: #333; font-size: 14px; margin-bottom: 8px;">
                            @lang('Parent IB') <span class="text-danger">*</span>
                        </label>
                        <select name="ib_parent_id" class="form-select"
                          style="border: 1px solid #ddd; border-radius: 6px; padding: 12px; font-size: 14px; background-color: #fff;">
                            <option value="">@lang('Select Parent IB')</option>
                            @foreach($availableParents as $parent)
                                <option value="{{ $parent->id }}">{{ $parent->fullname }} ({{ ucfirst($parent->ib_type) }} IB)</option>
                            @endforeach
                        </select>
                        <small class="text-muted" style="font-size: 12px;">Required for Sub-IB applications</small>
                    </div>

                    <div class="mb-4">
                        <button type="submit" class="btn w-100 py-3" id="approve-btn"
                          style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); border: none; border-radius: 6px; color: white; font-weight: 600; font-size: 14px; box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3); transition: all 0.3s ease;">
                            <i class="fas fa-check me-2"></i> @lang('Approve Application')
                        </button>
                    </div>
                </form>

                {{-- Rejection Form --}}
                <div class="border-top pt-4">
                    <form method="POST" action="{{ route('admin.ib.reject', $user->id) }}" id="rejection-form">
                        @csrf
                        <div class="mb-3">
                            <label class="form-label" style="font-weight: 500; color: #333; font-size: 14px; margin-bottom: 8px;">
                                @lang('Rejection Reason')
                            </label>
                            <textarea name="reason" class="form-control" rows="3"
                              style="border: 1px solid #ddd; border-radius: 6px; padding: 12px; font-size: 14px; resize: vertical;"
                              placeholder="Provide a reason for rejection (optional)"></textarea>
                        </div>
                        <div class="mb-0">
                            <button type="submit" class="btn w-100 py-3" id="reject-btn"
                              style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); border: none; border-radius: 6px; color: white; font-weight: 600; font-size: 14px; box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3); transition: all 0.3s ease;">
                                <i class="fas fa-times me-2"></i> @lang('Reject Application')
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        @endif

        @if($user->ib_status === 'approved')
        {{-- TASK 5: Already Approved State Management --}}
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom" style="padding: 1.5rem;">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <div class="bg-success rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                            <i class="fas fa-check text-white"></i>
                        </div>
                    </div>
                    <div>
                        <h5 class="mb-1" style="color: #333; font-weight: 600; font-size: 16px;">Application Approved</h5>
                        <p class="mb-0 text-muted" style="font-size: 12px;">Approved on {{ $user->updated_at->format('M d, Y H:i') }}</p>
                    </div>
                </div>
            </div>

            <div class="card-body" style="padding: 1.5rem;">
                <div class="alert alert-success border-0 mb-4" style="background: #d4edda; border-radius: 6px;">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-info-circle me-2"></i>
                        <div>
                            <strong>Status:</strong> This application has been approved<br>
                            <small class="text-muted">IB Type: {{ ucfirst($user->ib_type) }} IB</small>
                        </div>
                    </div>
                </div>

                {{-- IB Actions --}}
                <div class="mb-4">
                    <h6 class="mb-3" style="color: #333; font-weight: 600; font-size: 14px;">IB Management Actions</h6>
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.ib.hierarchy', $user->id) }}" class="btn btn-outline-primary py-2">
                            <i class="fas fa-sitemap me-2"></i> @lang('View Network Hierarchy')
                        </a>
                        <a href="{{ route('admin.become_ib.createAccount', $user->id) }}" class="btn btn-outline-info py-2">
                            <i class="fas fa-user-plus me-2"></i> @lang('Create MT5 Account')
                        </a>
                    </div>
                </div>

                {{-- Reversal Option --}}
                <div class="border-top pt-4">
                    <h6 class="mb-3" style="color: #333; font-weight: 600; font-size: 14px;">Administrative Actions</h6>
                    <form method="POST" action="{{ route('admin.ib.reject', $user->id) }}" id="reversal-form">
                        @csrf
                        <div class="mb-3">
                            <label class="form-label" style="font-weight: 500; color: #333; font-size: 14px; margin-bottom: 8px;">
                                @lang('Reversal Reason')
                            </label>
                            <textarea name="reason" class="form-control" rows="3"
                              style="border: 1px solid #ddd; border-radius: 6px; padding: 12px; font-size: 14px; resize: vertical;"
                              placeholder="Reason for reversing approval (required)" required></textarea>
                        </div>
                        <div class="mb-0">
                            <button type="submit" class="btn w-100 py-2" id="reverse-btn"
                              style="background: #6c757d; border: none; border-radius: 6px; color: white; font-weight: 500; font-size: 14px; transition: all 0.3s ease;"
                              onclick="return confirm('Are you sure you want to reverse this approval? This action cannot be undone.')">
                                <i class="fas fa-undo me-2"></i> @lang('Reverse Approval')
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- IB Statistics -->
        <div class="card border-0 shadow-sm mt-3">
            <div class="card-header bg-white border-bottom" style="padding: 1rem 1.5rem;">
                <h6 class="mb-0" style="color: #333; font-weight: 600; font-size: 14px;">
                    <i class="fas fa-chart-bar me-2 text-primary"></i>IB Performance Statistics
                </h6>
            </div>
            <div class="card-body" style="padding: 1.5rem;">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="p-3" style="background: #f8f9fa; border-radius: 6px;">
                            <h4 class="text-primary mb-1" style="font-size: 24px; font-weight: 600;">{{ $user->getTotalReferredClients() }}</h4>
                            <span class="text-muted" style="font-size: 12px;">@lang('Total Clients')</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="p-3" style="background: #f8f9fa; border-radius: 6px;">
                            <h4 class="text-success mb-1" style="font-size: 24px; font-weight: 600;">{{ $user->getActiveReferredClients() }}</h4>
                            <span class="text-muted" style="font-size: 12px;">@lang('Active Clients')</span>
                        </div>
                    </div>
                </div>
                @if($user->isMasterIb())
                <div class="row text-center mt-3">
                    <div class="col-12">
                        <div class="p-3" style="background: #f8f9fa; border-radius: 6px;">
                            <h4 class="text-info mb-1" style="font-size: 24px; font-weight: 600;">{{ $user->ibChildren->count() }}</h4>
                            <span class="text-muted" style="font-size: 12px;">@lang('Sub IBs')</span>
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
        @elseif($user->ib_status === 'rejected')
        {{-- TASK 5: Rejected State Management --}}
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom" style="padding: 1.5rem;">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <div class="bg-danger rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                            <i class="fas fa-times text-white"></i>
                        </div>
                    </div>
                    <div>
                        <h5 class="mb-1" style="color: #333; font-weight: 600; font-size: 16px;">Application Rejected</h5>
                        <p class="mb-0 text-muted" style="font-size: 12px;">Rejected on {{ $user->updated_at->format('M d, Y H:i') }}</p>
                    </div>
                </div>
            </div>

            <div class="card-body" style="padding: 1.5rem;">
                <div class="alert alert-danger border-0 mb-4" style="background: #f8d7da; border-radius: 6px;">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <div>
                            <strong>Status:</strong> This application has been rejected<br>
                            @if($user->ib_rejection_reason)
                                <small class="text-muted">Reason: {{ $user->ib_rejection_reason }}</small>
                            @endif
                        </div>
                    </div>
                </div>

                {{-- Re-approval Option --}}
                <div class="border-top pt-4">
                    <h6 class="mb-3" style="color: #333; font-weight: 600; font-size: 14px;">Re-approval Options</h6>
                    <form method="POST" action="{{ route('admin.ib.approve', $user->id) }}" id="reapproval-form">
                        @csrf
                        <div class="mb-3">
                            <label class="form-label" style="font-weight: 500; color: #333; font-size: 14px; margin-bottom: 8px;">
                                @lang('IB Type') <span class="text-danger">*</span>
                            </label>
                            <select name="ib_type" class="form-select" required
                              style="border: 1px solid #ddd; border-radius: 6px; padding: 12px; font-size: 14px; background-color: #fff;">
                                <option value="">@lang('Select IB Type')</option>
                                <option value="master">@lang('Master IB (50% Commission)')</option>
                                <option value="sub">@lang('Sub IB (30% Commission)')</option>
                            </select>
                        </div>
                        <div class="mb-0">
                            <button type="submit" class="btn w-100 py-2"
                              style="background: #28a745; border: none; border-radius: 6px; color: white; font-weight: 500; font-size: 14px; transition: all 0.3s ease;"
                              onclick="return confirm('Are you sure you want to approve this previously rejected application?')">
                                <i class="fas fa-redo me-2"></i> @lang('Re-approve Application')
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        @endif
    </div>
</div>
@endsection

@push('breadcrumb-plugins')
<div class="d-inline">
    <a href="{{ route('admin.form_ib') }}" class="btn btn--dark">
        <i class="las la-arrow-left"></i> @lang('Back to Applications')
    </a>
</div>
@endpush

{{-- TASK 5: Professional styling and enhanced JavaScript --}}
@push('style')
<style>
/* Professional Admin IB Review Interface Styling */
.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15) !important;
}

.form-select:focus, .form-control:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.alert {
  border: none !important;
}

.card {
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
}

/* Button loading states */
.btn-loading {
  position: relative;
  pointer-events: none;
}

.btn-loading::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  top: 50%;
  left: 50%;
  margin-left: -8px;
  margin-top: -8px;
  border: 2px solid transparent;
  border-top-color: #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .card-body {
    padding: 1rem !important;
  }

  .card-header {
    padding: 1rem !important;
  }
}
</style>
@endpush

@push('script')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // TASK 5: Enhanced form handling with proper state management

    // Show/hide parent IB selection based on IB type
    const ibTypeSelects = document.querySelectorAll('select[name="ib_type"]');
    ibTypeSelects.forEach(select => {
        select.addEventListener('change', function() {
            const ibType = this.value;
            const parentGroups = document.querySelectorAll('.parent-ib-group');
            const parentSelects = document.querySelectorAll('select[name="ib_parent_id"]');

            if (ibType === 'sub') {
                parentGroups.forEach(group => group.style.display = 'block');
                parentSelects.forEach(select => select.required = true);
            } else {
                parentGroups.forEach(group => group.style.display = 'none');
                parentSelects.forEach(select => select.required = false);
            }
        });
    });

    // TASK 5: Critical Fix - Prevent duplicate submissions
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');

            // Prevent double submission
            if (submitBtn.classList.contains('btn-loading')) {
                e.preventDefault();
                return false;
            }

            // Add loading state
            submitBtn.classList.add('btn-loading');
            submitBtn.disabled = true;

            // Store original text
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';

            // Re-enable after 5 seconds as fallback
            setTimeout(() => {
                submitBtn.classList.remove('btn-loading');
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }, 5000);
        });
    });

    // Load available parents based on group selection
    const groupSelects = document.querySelectorAll('select[name="ib_group_id"]');
    groupSelects.forEach(select => {
        select.addEventListener('change', function() {
            const groupId = this.value;
            const userId = {{ $user->id }};

            if (groupId) {
                fetch('{{ route("admin.ib.available_parents") }}?' + new URLSearchParams({
                    group_id: groupId,
                    user_id: userId
                }))
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const parentSelects = document.querySelectorAll('select[name="ib_parent_id"]');
                        parentSelects.forEach(parentSelect => {
                            let options = '<option value="">@lang("Select Parent IB")</option>';
                            data.parents.forEach(parent => {
                                options += `<option value="${parent.id}">${parent.name} - ${parent.group}</option>`;
                            });
                            parentSelect.innerHTML = options;
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading parent IBs:', error);
                });
            }
        });
    });

    // Enhanced confirmation dialogs
    const confirmButtons = document.querySelectorAll('[onclick*="confirm"]');
    confirmButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const action = this.textContent.trim();
            const message = this.getAttribute('onclick').match(/confirm\('([^']+)'\)/)[1];

            if (confirm(message)) {
                this.closest('form').submit();
            }
        });
    });
});
</script>
@endpush
