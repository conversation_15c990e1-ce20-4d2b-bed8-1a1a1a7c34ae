<?php

/**
 * Test script to verify all three critical issues are fixed:
 * 1. Search functionality for all fields
 * 2. MT5 sync and duplicate consolidation
 * 3. Username display format
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Console\Commands\SyncMT5UsersToLocal;

echo "🧪 TESTING ADMIN USER LIST FIXES\n";
echo "================================\n\n";

// Test 1: Search Functionality
echo "📋 TEST 1: Search Functionality\n";
echo "-------------------------------\n";

try {
    // Test search by name
    $nameSearch = User::searchable(['users.firstname', 'users.lastname'])
        ->where('firstname', 'like', '%john%')
        ->orWhere('lastname', 'like', '%doe%')
        ->count();
    echo "✅ Name search: Found {$nameSearch} users\n";

    // Test search by email
    $emailSearch = User::searchable(['users.email'])
        ->where('email', 'like', '%test%')
        ->count();
    echo "✅ Email search: Found {$emailSearch} users\n";

    // Test search by MT5 login
    $mt5Search = User::searchable(['users.mt5_login'])
        ->whereNotNull('mt5_login')
        ->count();
    echo "✅ MT5 login search: Found {$mt5Search} users with MT5 accounts\n";

    // Test search by phone
    $phoneSearch = User::searchable(['users.mobile'])
        ->whereNotNull('mobile')
        ->count();
    echo "✅ Phone search: Found {$phoneSearch} users with phone numbers\n";

} catch (Exception $e) {
    echo "❌ Search test failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 2: Duplicate Email Detection
echo "📋 TEST 2: Duplicate Email Detection\n";
echo "------------------------------------\n";

try {
    // Find duplicate emails
    $duplicateEmails = DB::table('users')
        ->select('email', DB::raw('COUNT(*) as count'))
        ->whereNotNull('email')
        ->where('email', '!=', '')
        ->groupBy('email')
        ->having('count', '>', 1)
        ->get();

    echo "📊 Found " . count($duplicateEmails) . " duplicate email groups:\n";
    
    foreach ($duplicateEmails->take(5) as $duplicate) {
        echo "   - {$duplicate->email}: {$duplicate->count} accounts\n";
        
        // Show details for this email
        $users = User::where('email', $duplicate->email)
            ->select('id', 'username', 'created_at', 'mt5_login')
            ->orderBy('created_at', 'desc')
            ->get();
            
        foreach ($users as $user) {
            $mt5Status = $user->mt5_login ? "MT5: {$user->mt5_login}" : "No MT5";
            echo "     * User ID {$user->id}: {$user->username} ({$mt5Status}) - {$user->created_at}\n";
        }
    }

} catch (Exception $e) {
    echo "❌ Duplicate detection failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Username Format Analysis
echo "📋 TEST 3: Username Format Analysis\n";
echo "-----------------------------------\n";

try {
    // Find users with problematic usernames
    $problematicUsernames = User::where('username', 'like', '%_%')
        ->where(function($query) {
            $query->where('username', 'regexp', '_[a-f0-9]{4,8}$')
                  ->orWhere('username', 'regexp', '_[0-9]{4,}$');
        })
        ->select('id', 'username', 'firstname', 'lastname', 'email', 'mt5_login', 'mt5_synced_at')
        ->limit(10)
        ->get();

    echo "🔍 Found " . count($problematicUsernames) . " users with problematic usernames:\n";
    
    foreach ($problematicUsernames as $user) {
        $syncStatus = $user->mt5_synced_at ? "Synced" : "Not synced";
        echo "   - ID {$user->id}: '{$user->username}' ({$user->firstname} {$user->lastname}) - {$syncStatus}\n";
        
        // Show what the cleaned username would be
        $cleanUsername = $user->username;
        $cleanUsername = preg_replace('/_[a-f0-9]{4,8}$/', '', $cleanUsername);
        $cleanUsername = preg_replace('/_\d{4,}$/', '', $cleanUsername);
        
        if ($user->mt5_synced_at || $user->mt5_login) {
            if (!str_starts_with($cleanUsername, '@')) {
                $cleanUsername = '@' . $cleanUsername;
            }
        }
        
        echo "     → Cleaned: '{$cleanUsername}'\n";
    }

} catch (Exception $e) {
    echo "❌ Username analysis failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 4: Performance Test
echo "📋 TEST 4: Performance Test\n";
echo "---------------------------\n";

try {
    $startTime = microtime(true);
    
    // Simulate the admin user list query with all optimizations
    $users = User::select([
            'users.*',
            DB::raw('CASE WHEN mt5_login IS NOT NULL THEN 1 ELSE 0 END as has_mt5'),
            DB::raw('CASE WHEN mt5_synced_at IS NOT NULL THEN 1 ELSE 0 END as is_synced')
        ])
        ->with([
            'wallets:id,user_id,currency_id,balance',
            'wallets.currency:id,symbol,sign'
        ])
        ->orderBy('users.created_at', 'desc')
        ->limit(50)
        ->get();
    
    $endTime = microtime(true);
    $duration = round(($endTime - $startTime) * 1000, 2);
    
    echo "⚡ Query performance: {$duration}ms for " . count($users) . " users\n";
    
    if ($duration < 3000) {
        echo "✅ Performance: GOOD (under 3 seconds)\n";
    } else {
        echo "⚠️ Performance: SLOW (over 3 seconds)\n";
    }

} catch (Exception $e) {
    echo "❌ Performance test failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 5: MT5 Sync Status
echo "📋 TEST 5: MT5 Sync Status\n";
echo "--------------------------\n";

try {
    $totalUsers = User::count();
    $mt5Users = User::whereNotNull('mt5_login')->count();
    $syncedUsers = User::whereNotNull('mt5_synced_at')->count();
    $recentSync = User::whereNotNull('mt5_synced_at')
        ->where('mt5_synced_at', '>', now()->subHours(24))
        ->count();

    echo "📊 User Statistics:\n";
    echo "   - Total users: {$totalUsers}\n";
    echo "   - Users with MT5 accounts: {$mt5Users}\n";
    echo "   - Users synced from MT5: {$syncedUsers}\n";
    echo "   - Recently synced (24h): {$recentSync}\n";
    
    $syncPercentage = $totalUsers > 0 ? round(($mt5Users / $totalUsers) * 100, 1) : 0;
    echo "   - MT5 coverage: {$syncPercentage}%\n";

} catch (Exception $e) {
    echo "❌ MT5 sync status check failed: " . $e->getMessage() . "\n";
}

echo "\n";
echo "🎉 TEST COMPLETED\n";
echo "================\n";
echo "Please review the results above to verify all fixes are working correctly.\n";
echo "If any issues are found, please run the appropriate fix commands.\n\n";

// Recommendations
echo "💡 RECOMMENDATIONS:\n";
echo "-------------------\n";
echo "1. Run 'php artisan mt5:sync-users --fast --limit=1000' to sync MT5 data\n";
echo "2. Check admin user list search functionality in browser\n";
echo "3. Verify username display format is clean (no weird suffixes)\n";
echo "4. Confirm duplicate emails are consolidated properly\n";
echo "5. Test page load performance (should be under 3 seconds)\n";
