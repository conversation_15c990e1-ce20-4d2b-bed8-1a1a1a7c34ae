<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class MT5UserMigrationService
{
    /**
     * Complete migration of MT5 users to local database
     */
    public function migrateAllMT5Users($deleteExisting = false)
    {
        try {
            Log::info("Starting complete MT5 users migration");

            if ($deleteExisting) {
                $this->clearExistingUsers();
            }

            // Get all MT5 users
            $mt5Users = DB::connection('mbf-dbmt5')
                ->table('mt5_users')
                ->select('*')
                ->orderBy('Login')
                ->get();

            $migrated = 0;
            $updated = 0;
            $errors = 0;

            foreach ($mt5Users as $mt5User) {
                try {
                    $result = $this->migrateSingleUser($mt5User);
                    if ($result === 'created') {
                        $migrated++;
                    } elseif ($result === 'updated') {
                        $updated++;
                    }
                } catch (\Exception $e) {
                    $errors++;
                    Log::error("Failed to migrate MT5 user {$mt5User->Login}", [
                        'error' => $e->getMessage(),
                        'mt5_data' => $mt5User
                    ]);
                }
            }

            Log::info("MT5 users migration completed", [
                'total_processed' => $mt5Users->count(),
                'migrated' => $migrated,
                'updated' => $updated,
                'errors' => $errors
            ]);

            return [
                'success' => true,
                'total_processed' => $mt5Users->count(),
                'migrated' => $migrated,
                'updated' => $updated,
                'errors' => $errors
            ];

        } catch (\Exception $e) {
            Log::error("MT5 users migration failed: " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Clear existing users (optional)
     */
    private function clearExistingUsers()
    {
        Log::info("Clearing existing users table");

        // Disable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        // Clear related tables first
        DB::table('ib_commissions')->truncate();
        DB::table('transactions')->truncate();
        DB::table('deposits')->truncate();
        DB::table('withdrawals')->truncate();

        // Clear users table
        DB::table('users')->truncate();

        // Re-enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        Log::info("Users table and related tables cleared");
    }

    /**
     * Migrate single MT5 user
     */
    private function migrateSingleUser($mt5User)
    {
        // Skip if no email
        if (empty($mt5User->Email)) {
            return 'skipped';
        }

        // Check if user already exists
        $existingUser = User::where('email', $mt5User->Email)->first();

        if ($existingUser) {
            // Update existing user with MT5 data
            $this->updateUserWithMT5Data($existingUser, $mt5User);
            // Add this MT5 account to the user
            $this->addMT5AccountToUser($existingUser, $mt5User);
            return 'updated';
        } else {
            // Create new user from MT5 data
            $newUser = $this->createUserFromMT5Data($mt5User);
            // Add this MT5 account to the user
            $this->addMT5AccountToUser($newUser, $mt5User);
            return 'created';
        }
    }

    /**
     * Add MT5 account information to user
     */
    private function addMT5AccountToUser($user, $mt5User)
    {
        // Check if this MT5 account already exists for this user
        $existingAccount = \App\Models\UserAccounts::where('User_Id', $user->id)
            ->where('Account', $mt5User->Login)
            ->first();

        if (!$existingAccount) {
            // Create new MT5 account record
            $userAccount = new \App\Models\UserAccounts();
            $userAccount->User_Id = $user->id;
            $userAccount->Account = $mt5User->Login;
            $userAccount->Master_Password = 'migrated'; // Default password
            $userAccount->Account_Type = $this->determineMT5AccountType($mt5User->Group);
            $userAccount->created_at = $mt5User->Registration ? \Carbon\Carbon::parse($mt5User->Registration) : now();
            $userAccount->updated_at = now();
            $userAccount->save();

            \Log::info("Added MT5 account {$mt5User->Login} to user {$user->id} ({$user->email})");
        }
    }

    /**
     * Determine MT5 account type from group
     */
    private function determineMT5AccountType($group)
    {
        if (strpos($group, 'demo') !== false || strpos($group, 'Demo') !== false) {
            return 'demo';
        }
        return 'live';
    }

    /**
     * Create new user from MT5 data
     */
    private function createUserFromMT5Data($mt5User)
    {
        // Determine IB status from MT5 group
        $ibStatus = $this->determineIbStatusFromGroup($mt5User->Group);
        
        // Generate username from email or MT5 login
        $username = $this->generateUsername($mt5User->Email, $mt5User->Login);

        // Parse names properly - handle different MT5 name patterns
        $firstName = '';
        $lastName = '';

        // Case 1: Both FirstName and LastName exist and are not empty
        if (!empty(trim($mt5User->FirstName)) && !empty(trim($mt5User->LastName))) {
            $firstName = trim($mt5User->FirstName);
            $lastName = trim($mt5User->LastName);
        }
        // Case 2: Only FirstName exists (might contain full name)
        elseif (!empty(trim($mt5User->FirstName))) {
            $fullName = trim($mt5User->FirstName);
            $nameParts = explode(' ', $fullName, 2);
            $firstName = $nameParts[0];
            $lastName = isset($nameParts[1]) && !empty(trim($nameParts[1])) ? trim($nameParts[1]) : '';
        }
        // Case 3: Use Name field as fallback
        elseif (!empty(trim($mt5User->Name))) {
            $fullName = trim($mt5User->Name);
            $nameParts = explode(' ', $fullName, 2);
            $firstName = $nameParts[0];
            $lastName = isset($nameParts[1]) && !empty(trim($nameParts[1])) ? trim($nameParts[1]) : '';
        }
        // Case 4: Use email prefix as last resort
        else {
            $emailParts = explode('@', $mt5User->Email);
            $firstName = $emailParts[0] ?: 'User';
            $lastName = '';
        }

        // Ensure we have at least a first name
        if (empty($firstName)) {
            $firstName = 'User';
        }

        $userData = [
            // Basic user data with properly parsed names
            'firstname' => $firstName,
            'lastname' => $lastName,
            'username' => $username,
            'email' => $mt5User->Email,
            'country_code' => $mt5User->Country ?: '',
            'mobile' => $mt5User->Phone ?: '',
            'password' => bcrypt('password123'), // Default password
            'address' => [
                'address' => $mt5User->Address ?: '',
                'city' => $mt5User->City ?: '',
                'state' => $mt5User->State ?: '',
                'zip' => $mt5User->ZipCode ?: '',
                'country' => $mt5User->Country ?: ''
            ],

            // Account status
            'status' => 1, // Active
            'kv' => 1, // KYC verified
            'ev' => 1, // Email verified
            'sv' => 1, // SMS verified
            'ts' => 0, // 2FA off
            'tv' => 1, // 2FA verified

            // IB data
            'ib_status' => $ibStatus['status'],
            'ib_type' => $ibStatus['type'],
            'partner' => $ibStatus['partner'],
            'ib_approved_at' => $ibStatus['status'] === User::IB_STATUS_APPROVED ? now() : null,

            // MT5 sync data - ALL FIELDS
            'mt5_login' => $mt5User->Login,
            'mt5_timestamp' => $mt5User->Timestamp,
            'mt5_group' => $mt5User->Group,
            'mt5_cert_serial_number' => $mt5User->CertSerialNumber,
            'mt5_rights' => $mt5User->Rights,
            'mt5_leverage' => $mt5User->Leverage,
            'mt5_agent' => $mt5User->Agent,
            'mt5_trade_accounts' => $mt5User->TradeAccounts,
            'mt5_limit_positions' => $mt5User->LimitPositions,
            'mt5_limit_orders' => $mt5User->LimitOrders,
            'mt5_lead_campaign' => $mt5User->LeadCampaign,
            'mt5_lead_source' => $mt5User->LeadSource,
            'mt5_timestamp_trade' => $mt5User->TimestampTrade,
            'mt5_balance' => $mt5User->Balance ?: 0,
            'mt5_credit' => $mt5User->Credit ?: 0,
            'mt5_equity' => $mt5User->Balance ?: 0, // Use balance as equity if not available
            'mt5_interest_rate' => $mt5User->InterestRate ?: 0,
            'mt5_commission_daily' => $mt5User->CommissionDaily ?: 0,
            'mt5_commission_monthly' => $mt5User->CommissionMonthly ?: 0,
            'mt5_balance_prev_day' => $mt5User->BalancePrevDay ?: 0,
            'mt5_balance_prev_month' => $mt5User->BalancePrevMonth ?: 0,
            'mt5_equity_prev_day' => $mt5User->EquityPrevDay ?: 0,
            'mt5_equity_prev_month' => $mt5User->EquityPrevMonth ?: 0,
            'mt5_name' => $mt5User->Name ?: $mt5User->Email,
            'mt5_currency' => 'USD', // Default currency
            'mt5_registration' => $mt5User->Registration,
            'mt5_last_access' => $mt5User->LastAccess,
            'mt5_last_pass_change' => $mt5User->LastPassChange,
            // Note: firstname/lastname are stored in main user fields, not duplicated here
            'mt5_middle_name' => $mt5User->MiddleName,
            'mt5_country' => $mt5User->Country,
            'mt5_language' => $mt5User->Language,
            'mt5_client_id' => $mt5User->ClientID,
            'mt5_city' => $mt5User->City,
            'mt5_state' => $mt5User->State,
            'mt5_zip_code' => $mt5User->ZipCode,
            'mt5_address' => $mt5User->Address,
            'mt5_phone' => $mt5User->Phone,
            'mt5_email' => $mt5User->Email,
            'mt5_id' => $mt5User->ID,
            'mt5_status' => $mt5User->Status,
            'mt5_company' => $mt5User->Company,
            'mt5_account' => $mt5User->Account,
            'mt5_comment' => $mt5User->Comment,
            'mt5_color' => $mt5User->Color,
            'mt5_phone_password' => $mt5User->PhonePassword,
            'mt5_mqid' => $mt5User->MQID,
            'mt5_last_ip' => $mt5User->LastIP,
            'mt5_api_data' => $mt5User->ApiData,
            'mt5_synced_at' => now(),

            'created_at' => now(),
            'updated_at' => now()
        ];

        return User::create($userData);
    }

    /**
     * Update existing user with MT5 data - prioritize better data
     */
    private function updateUserWithMT5Data($user, $mt5User)
    {
        $ibStatus = $this->determineIbStatusFromGroup($mt5User->Group);

        // Parse names properly for updates too
        $firstName = '';
        $lastName = '';

        if (!empty(trim($mt5User->FirstName)) && !empty(trim($mt5User->LastName))) {
            $firstName = trim($mt5User->FirstName);
            $lastName = trim($mt5User->LastName);
        } elseif (!empty(trim($mt5User->FirstName))) {
            $fullName = trim($mt5User->FirstName);
            $nameParts = explode(' ', $fullName, 2);
            $firstName = $nameParts[0];
            $lastName = isset($nameParts[1]) && !empty(trim($nameParts[1])) ? trim($nameParts[1]) : '';
        } elseif (!empty(trim($mt5User->Name))) {
            $fullName = trim($mt5User->Name);
            $nameParts = explode(' ', $fullName, 2);
            $firstName = $nameParts[0];
            $lastName = isset($nameParts[1]) && !empty(trim($nameParts[1])) ? trim($nameParts[1]) : '';
        }

        // Determine if this MT5 record has better data than current user data
        $shouldUpdateContactInfo = $this->shouldUpdateContactInfo($user, $mt5User);
        $shouldUpdateIBStatus = $this->shouldUpdateIBStatus($user, $ibStatus);

        $updateData = [];

        // Update names if we have better data
        if (!empty($firstName) && (empty($user->firstname) || $shouldUpdateContactInfo)) {
            $updateData['firstname'] = $firstName;
        }
        if (!empty($lastName) && (empty($user->lastname) || $shouldUpdateContactInfo)) {
            $updateData['lastname'] = $lastName;
        }

        // Update contact info if we have better data
        if ($shouldUpdateContactInfo) {
            if (!empty($mt5User->Country)) {
                $updateData['country_code'] = $mt5User->Country;
            }
            if (!empty($mt5User->Phone)) {
                $updateData['mobile'] = $mt5User->Phone;
            }
        }

        // Always update IB status if this record has IB status
        if ($shouldUpdateIBStatus) {
            $updateData['ib_status'] = $ibStatus['status'];
            $updateData['ib_type'] = $ibStatus['type'];
            $updateData['partner'] = $ibStatus['partner'];
            $updateData['ib_approved_at'] = $ibStatus['status'] === User::IB_STATUS_APPROVED ? now() : null;
        }

        // Always update MT5 fields with latest data
        $updateData['mt5_login'] = $mt5User->Login;
        $updateData['mt5_group'] = $mt5User->Group;
        $updateData['mt5_balance'] = $mt5User->Balance ?: 0;
        $updateData['mt5_agent'] = $mt5User->Agent;
        $updateData['mt5_synced_at'] = now();

        if (!empty($updateData)) {
            $user->update($updateData);
        }
        return $user;
    }

    /**
     * Determine if we should update contact info based on data quality
     */
    private function shouldUpdateContactInfo($user, $mt5User)
    {
        // If user has no contact info, always update
        if (empty($user->mobile) && empty($user->country_code)) {
            return true;
        }

        // If MT5 record has more complete data, update
        $mt5HasPhone = !empty($mt5User->Phone) && strlen($mt5User->Phone) > 4;
        $mt5HasCountry = !empty($mt5User->Country) && $mt5User->Country !== 'Aland Islands';
        $userHasPhone = !empty($user->mobile) && strlen($user->mobile) > 4;
        $userHasCountry = !empty($user->country_code) && $user->country_code !== 'Aland Islands';

        // Update if MT5 has better data
        return ($mt5HasPhone && !$userHasPhone) || ($mt5HasCountry && !$userHasCountry);
    }

    /**
     * Determine if we should update IB status
     */
    private function shouldUpdateIBStatus($user, $ibStatus)
    {
        // Always update if current user is not an IB but MT5 record shows IB status
        return $user->ib_status == 0 && $ibStatus['status'] == User::IB_STATUS_APPROVED;
    }

    /**
     * Determine IB status from MT5 group
     */
    private function determineIbStatusFromGroup($group)
    {
        $ibGroups = [
            'real\\Affiliates' => ['status' => User::IB_STATUS_APPROVED, 'type' => 'master', 'partner' => 1],
            'real\\IB\\IB MAIN' => ['status' => User::IB_STATUS_APPROVED, 'type' => 'master', 'partner' => 1],
            'real\\IB\\IB SUB' => ['status' => User::IB_STATUS_APPROVED, 'type' => 'sub', 'partner' => 1],
            'real\\Multi-IB\\Default' => ['status' => User::IB_STATUS_APPROVED, 'type' => 'master', 'partner' => 1],
            'real\\Multi-IB\\Level1' => ['status' => User::IB_STATUS_APPROVED, 'type' => 'sub', 'partner' => 1],
            'real\\Multi-IB\\Level2' => ['status' => User::IB_STATUS_APPROVED, 'type' => 'sub', 'partner' => 1],
            'real\\Multi-IB\\Level3' => ['status' => User::IB_STATUS_APPROVED, 'type' => 'sub', 'partner' => 1],
            'real\\Multi-IB\\Level4' => ['status' => User::IB_STATUS_APPROVED, 'type' => 'sub', 'partner' => 1],
            'real\\Multi-IB\\Level5' => ['status' => User::IB_STATUS_APPROVED, 'type' => 'sub', 'partner' => 1],
        ];

        // Return default values for non-IB users (status = 0 means not an IB)
        return $ibGroups[$group] ?? ['status' => 0, 'type' => null, 'partner' => 0];
    }

    /**
     * Generate unique username
     */
    private function generateUsername($email, $mt5Login)
    {
        $baseUsername = str_replace(['@', '.', '+'], '', $email);
        $baseUsername = substr($baseUsername, 0, 15);
        
        // Check if username exists
        $counter = 1;
        $username = $baseUsername;
        
        while (User::where('username', $username)->exists()) {
            $username = $baseUsername . $counter;
            $counter++;
        }
        
        return $username;
    }

    /**
     * Set up referral relationships based on MT5 Agent field
     */
    public function setupReferralRelationships()
    {
        Log::info("Setting up referral relationships");
        
        $users = User::whereNotNull('mt5_agent')
            ->where('mt5_agent', '!=', 0)
            ->whereNull('ref_by')
            ->get();

        $updated = 0;
        
        foreach ($users as $user) {
            $parentUser = User::where('mt5_login', $user->mt5_agent)->first();
            
            if ($parentUser) {
                $user->update(['ref_by' => $parentUser->id]);
                $updated++;
            }
        }

        Log::info("Referral relationships setup completed", ['updated' => $updated]);
        return $updated;
    }
}
