<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Deposit;
use App\Models\Withdrawal;
use App\Models\SupportTicket;
use App\Models\FormIb;
use App\Models\AdminNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DashboardActivityController extends Controller
{
    /**
     * Get dashboard activity data for all tabs
     */
    public function getActivityData(Request $request)
    {
        try {
            $tab = $request->get('tab', 'transactions');
            $limit = $request->get('limit', 10);

            \Log::info('Dashboard activity request', ['tab' => $tab, 'limit' => $limit]);

            switch ($tab) {
                case 'transactions':
                    return $this->getTransactionActivities($limit);
                case 'accounts':
                    return $this->getAccountActivities($limit);
                case 'mt5':
                    return $this->getMT5Activities($limit);
                case 'tickets':
                    return $this->getTicketActivities($limit);
                case 'kyc':
                    return $this->getKYCActivities($limit);
                case 'partnership':
                    return $this->getPartnershipActivities($limit);
                default:
                    return response()->json(['success' => false, 'error' => 'Invalid tab'], 400);
            }
        } catch (\Exception $e) {
            \Log::error('Dashboard activity error', [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Internal server error',
                'message' => config('app.debug') ? $e->getMessage() : 'An error occurred while loading activities'
            ], 500);
        }
    }

    /**
     * Get recent transaction activities
     */
    private function getTransactionActivities($limit)
    {
        try {
            $deposits = Deposit::with('user:id,username,firstname,lastname')
                ->select('id', 'user_id', 'amount', 'charge', 'method_code', 'status', 'created_at', 'updated_at')
                ->latest()
                ->limit($limit)
                ->get()
                ->map(function ($deposit) {
                    // Safe fullname handling for PHP 8.4 compatibility
                    $userFullname = 'N/A';
                    if ($deposit->user) {
                        try {
                            $userFullname = $deposit->user->fullname ?? 'N/A';
                        } catch (\Exception $e) {
                            $userFullname = trim(($deposit->user->firstname ?? '') . ' ' . ($deposit->user->lastname ?? '')) ?: 'N/A';
                        }
                    }

                    return [
                        'id' => $deposit->id,
                        'type' => 'deposit',
                        'user' => $deposit->user->username ?? 'N/A',
                        'user_name' => $userFullname,
                        'amount' => showAmount($deposit->amount),
                        'method' => $deposit->method_code,
                        'status' => $deposit->status,
                        'status_class' => $this->getStatusClass($deposit->status),
                        'created_at' => $deposit->created_at,
                        'updated_at' => $deposit->updated_at,
                        'url' => route('admin.deposit.details', $deposit->id)
                    ];
                });
        } catch (\Exception $e) {
            \Log::error('Error loading deposits', ['error' => $e->getMessage()]);
            $deposits = collect();
        }

        try {
            $withdrawals = Withdrawal::with('user:id,username,firstname,lastname')
                ->select('id', 'user_id', 'amount', 'charge', 'method_code', 'status', 'created_at', 'updated_at')
                ->latest()
                ->limit($limit)
                ->get()
                ->map(function ($withdrawal) {
                    return [
                        'id' => $withdrawal->id,
                        'type' => 'withdrawal',
                        'user' => $withdrawal->user->username ?? 'N/A',
                        'user_name' => $withdrawal->user->fullname ?? 'N/A',
                        'amount' => showAmount($withdrawal->amount),
                        'method' => $withdrawal->method_code,
                        'status' => $withdrawal->status,
                        'status_class' => $this->getStatusClass($withdrawal->status),
                        'created_at' => $withdrawal->created_at,
                        'updated_at' => $withdrawal->updated_at,
                        'url' => route('admin.withdraw.details', $withdrawal->id)
                    ];
                });
        } catch (\Exception $e) {
            \Log::error('Error loading withdrawals', ['error' => $e->getMessage()]);
            $withdrawals = collect();
        }

        $activities = $deposits->concat($withdrawals)
            ->sortByDesc('created_at')
            ->take($limit)
            ->values();

        return response()->json([
            'success' => true,
            'data' => $activities,
            'total' => $activities->count()
        ]);
    }

    /**
     * Get recent account activities
     */
    private function getAccountActivities($limit)
    {
        $users = User::select('id', 'username', 'firstname', 'lastname', 'email', 'ev', 'sv', 'kv', 'created_at')
            ->latest()
            ->limit($limit)
            ->get()
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'type' => 'registration',
                    'user' => $user->username,
                    'user_name' => $user->fullname,
                    'email' => $user->email,
                    'verification_status' => [
                        'email' => $user->ev ? 'verified' : 'pending',
                        'sms' => $user->sv ? 'verified' : 'pending',
                        'kyc' => $user->kv == 1 ? 'verified' : ($user->kv == 2 ? 'pending' : 'unverified')
                    ],
                    'created_at' => $user->created_at,
                    'url' => route('admin.users.detail', $user->id)
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $users,
            'total' => $users->count()
        ]);
    }

    /**
     * Get recent MT5 activities
     */
    private function getMT5Activities($limit)
    {
        $activities = collect();

        // Get recent MT5 account creations from admin notifications
        $mt5Notifications = AdminNotification::where(function($query) {
                $query->where('title', 'like', '%MT5%')
                      ->orWhere('title', 'like', '%account%')
                      ->orWhere('title', 'like', '%balance%')
                      ->orWhere('title', 'like', '%leverage%');
            })
            ->with('user:id,username,firstname,lastname')
            ->select('id', 'user_id', 'title', 'created_at', 'click_url')
            ->latest()
            ->limit($limit)
            ->get()
            ->map(function ($notification) {
                return [
                    'id' => $notification->id,
                    'type' => 'mt5_notification',
                    'user' => $notification->user->username ?? 'System',
                    'user_name' => $notification->user->fullname ?? 'System Activity',
                    'activity' => $notification->title,
                    'created_at' => $notification->created_at,
                    'url' => $notification->click_url ?? '#'
                ];
            });

        $activities = $activities->concat($mt5Notifications);

        // Get recent users with MT5 accounts (from user registrations)
        try {
            $recentMT5Users = User::where(function($query) {
                    $query->whereNotNull('mt5_login')
                          ->orWhereNotNull('all_mt5_accounts')
                          ->orWhere('all_mt5_accounts', '!=', '')
                          ->orWhere('all_mt5_accounts', '!=', '[]');
                })
                ->select('id', 'username', 'firstname', 'lastname', 'mt5_login', 'all_mt5_accounts', 'mt5_balance', 'created_at', 'updated_at')
                ->latest('updated_at')
                ->limit($limit)
                ->get()
                ->map(function ($user) {
                    $mt5Accounts = $user->all_mt5_accounts;
                    $accountCount = 1; // Default to 1 if mt5_login exists

                    if (!empty($mt5Accounts) && $mt5Accounts !== '[]') {
                        if (is_string($mt5Accounts)) {
                            $decoded = json_decode($mt5Accounts, true);
                            $accountCount = is_array($decoded) ? count($decoded) : 1;
                        } elseif (is_array($mt5Accounts)) {
                            $accountCount = count($mt5Accounts);
                        }
                    }

                    $balance = $user->mt5_balance ? number_format($user->mt5_balance, 2) : '0.00';

                    return [
                        'id' => $user->id,
                        'type' => 'mt5_account_update',
                        'user' => $user->username,
                        'user_name' => $user->fullname,
                        'activity' => "MT5 Account Activity - {$accountCount} account(s) - Balance: \${$balance}",
                        'mt5_login' => $user->mt5_login,
                        'mt5_balance' => $balance,
                        'account_count' => $accountCount,
                        'created_at' => $user->updated_at,
                        'url' => route('admin.users.detail', $user->id)
                    ];
                });
        } catch (\Exception $e) {
            \Log::error('Error loading MT5 users', ['error' => $e->getMessage()]);
            $recentMT5Users = collect();
        }

        $activities = $activities->concat($recentMT5Users);

        // Sort by created_at and limit
        $finalActivities = $activities->sortByDesc('created_at')->take($limit)->values();

        return response()->json([
            'success' => true,
            'data' => $finalActivities,
            'total' => $finalActivities->count()
        ]);
    }

    /**
     * Get recent ticket activities
     */
    private function getTicketActivities($limit)
    {
        // Check if SupportTicket model exists
        if (!class_exists('App\Models\SupportTicket')) {
            return response()->json([
                'success' => true,
                'data' => [],
                'total' => 0,
                'message' => 'Support ticket system not available'
            ]);
        }

        try {
            $tickets = SupportTicket::with('user:id,username,firstname,lastname')
                ->select('id', 'user_id', 'ticket', 'subject', 'status', 'priority', 'created_at', 'updated_at')
                ->latest()
                ->limit($limit)
                ->get()
                ->map(function ($ticket) {
                    return [
                        'id' => $ticket->id,
                        'type' => 'support_ticket',
                        'user' => $ticket->user->username ?? 'N/A',
                        'user_name' => $ticket->user->fullname ?? 'N/A',
                        'ticket_number' => $ticket->ticket,
                        'subject' => $ticket->subject,
                        'status' => $ticket->status,
                        'priority' => $ticket->priority,
                        'status_class' => $this->getTicketStatusClass($ticket->status),
                        'priority_class' => $this->getPriorityClass($ticket->priority),
                        'created_at' => $ticket->created_at,
                        'updated_at' => $ticket->updated_at,
                        'url' => route('admin.ticket.view', $ticket->id)
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $tickets,
                'total' => $tickets->count()
            ]);
        } catch (\Exception $e) {
            // Fallback to admin notifications for ticket-related activities
            $ticketNotifications = AdminNotification::where('title', 'like', '%ticket%')
                ->orWhere('title', 'like', '%support%')
                ->with('user:id,username,firstname,lastname')
                ->select('id', 'user_id', 'title', 'created_at', 'click_url')
                ->latest()
                ->limit($limit)
                ->get()
                ->map(function ($notification) {
                    return [
                        'id' => $notification->id,
                        'type' => 'ticket_notification',
                        'user' => $notification->user->username ?? 'System',
                        'user_name' => $notification->user->fullname ?? 'System',
                        'activity' => $notification->title,
                        'status' => 'pending',
                        'status_class' => 'badge--warning',
                        'created_at' => $notification->created_at,
                        'url' => $notification->click_url ?? '#'
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $ticketNotifications,
                'total' => $ticketNotifications->count()
            ]);
        }
    }

    /**
     * Get recent KYC activities
     */
    private function getKYCActivities($limit)
    {
        try {
            \Log::info('KYC Activities: Starting data retrieval', ['limit' => $limit]);

            $kycUsers = User::whereIn('kv', [1, 2]) // Verified or Pending
                ->select('id', 'username', 'firstname', 'lastname', 'kv', 'kyc_data', 'created_at', 'updated_at')
                ->latest('updated_at')
                ->limit($limit)
                ->get();

            \Log::info('KYC Activities: Query executed successfully', ['count' => $kycUsers->count()]);

            $mappedData = $kycUsers->map(function ($user) {
                try {
                    // Safe handling of kyc_data
                    $kycData = [];
                    if ($user->kyc_data) {
                        if (is_string($user->kyc_data)) {
                            $kycData = json_decode($user->kyc_data, true) ?: [];
                        } elseif (is_array($user->kyc_data) || is_object($user->kyc_data)) {
                            $kycData = (array) $user->kyc_data;
                        }
                    }

                    $documentTypes = [];
                    if (is_array($kycData)) {
                        foreach ($kycData as $key => $value) {
                            if (!empty($value) && is_string($key) && strpos($key, 'file') !== false) {
                                $documentTypes[] = ucfirst(str_replace(['_file', '_'], [' ', ' '], $key));
                            }
                        }
                    }

                    // Safe fullname handling for PHP 8.4 compatibility
                    $userFullname = 'N/A';
                    try {
                        $userFullname = $user->fullname ?? 'N/A';
                    } catch (\Exception $e) {
                        $userFullname = trim(($user->firstname ?? '') . ' ' . ($user->lastname ?? '')) ?: 'N/A';
                        \Log::warning('KYC Activities: Fullname accessor failed', [
                            'user_id' => $user->id,
                            'error' => $e->getMessage()
                        ]);
                    }

                    return [
                        'id' => $user->id,
                        'type' => 'kyc_submission',
                        'user' => $user->username ?? 'N/A',
                        'user_name' => $userFullname,
                        'status' => $user->kv == 1 ? 'verified' : 'pending',
                        'status_class' => $user->kv == 1 ? 'badge--success' : 'badge--warning',
                        'document_types' => implode(', ', $documentTypes) ?: 'Documents Submitted',
                        'document_count' => count($documentTypes),
                        'created_at' => $user->created_at,
                        'updated_at' => $user->updated_at,
                        'url' => route('admin.users.detail', $user->id)
                    ];
                } catch (\Exception $e) {
                    \Log::error('KYC Activities: Error processing user data', [
                        'user_id' => $user->id ?? 'unknown',
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);

                    // Return safe fallback data
                    return [
                        'id' => $user->id ?? 0,
                        'type' => 'kyc_submission',
                        'user' => $user->username ?? 'N/A',
                        'user_name' => 'N/A',
                        'status' => 'pending',
                        'status_class' => 'badge--warning',
                        'document_types' => 'Documents Submitted',
                        'document_count' => 0,
                        'created_at' => $user->created_at ?? now(),
                        'updated_at' => $user->updated_at ?? now(),
                        'url' => route('admin.users.detail', $user->id ?? 1)
                    ];
                }
            });

            \Log::info('KYC Activities: Data mapping completed successfully', ['mapped_count' => $mappedData->count()]);

            return response()->json([
                'success' => true,
                'data' => $mappedData,
                'total' => $mappedData->count()
            ]);
        } catch (\Exception $e) {
            \Log::error('KYC Activities: Critical error occurred', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => true,
                'data' => [],
                'total' => 0,
                'message' => 'KYC data temporarily unavailable'
            ]);
        }
    }

    /**
     * Get recent partnership/IB activities
     */
    private function getPartnershipActivities($limit)
    {
        $activities = collect();

        // Get IB applications
        try {
            $ibApplications = FormIb::with('user:id,username,firstname,lastname')
                ->select('id', 'user_id', 'ib_status', 'created_at', 'updated_at')
                ->latest()
                ->limit($limit)
                ->get()
                ->map(function ($application) {
                    return [
                        'id' => $application->id,
                        'type' => 'ib_application',
                        'user' => $application->user->username ?? 'N/A',
                        'user_name' => $application->user->fullname ?? 'N/A',
                        'activity' => 'IB Application ' . ucfirst($application->ib_status ?? 'pending'),
                        'status' => $application->ib_status ?? 'pending',
                        'status_class' => $this->getIbStatusClass($application->ib_status),
                        'created_at' => $application->created_at,
                        'updated_at' => $application->updated_at,
                        'url' => route('admin.form_ib')
                    ];
                });

            $activities = $activities->concat($ibApplications);
        } catch (\Exception $e) {
            // FormIb table might not exist, continue with other activities
        }

        // Get recent IB users (approved partners)
        $ibUsers = User::where('ib_status', 'approved')
            ->orWhere('partner', 1)
            ->with('referrals:id,username,firstname,lastname,ref_by')
            ->select('id', 'username', 'firstname', 'lastname', 'ib_status', 'partner', 'created_at', 'updated_at')
            ->latest('updated_at')
            ->limit($limit)
            ->get()
            ->map(function ($user) {
                $referralCount = $user->referrals ? $user->referrals->count() : 0;

                return [
                    'id' => $user->id,
                    'type' => 'ib_activity',
                    'user' => $user->username,
                    'user_name' => $user->fullname,
                    'activity' => "IB Partner - {$referralCount} referral(s)",
                    'status' => 'approved',
                    'status_class' => 'badge--success',
                    'created_at' => $user->updated_at,
                    'url' => route('admin.users.detail', $user->id)
                ];
            });

        $activities = $activities->concat($ibUsers);

        // Get IB-related admin notifications
        $ibNotifications = AdminNotification::where(function($query) {
                $query->where('title', 'like', '%IB%')
                      ->orWhere('title', 'like', '%partner%')
                      ->orWhere('title', 'like', '%referral%')
                      ->orWhere('title', 'like', '%commission%');
            })
            ->with('user:id,username,firstname,lastname')
            ->select('id', 'user_id', 'title', 'created_at', 'click_url')
            ->latest()
            ->limit($limit)
            ->get()
            ->map(function ($notification) {
                return [
                    'id' => $notification->id,
                    'type' => 'ib_notification',
                    'user' => $notification->user->username ?? 'System',
                    'user_name' => $notification->user->fullname ?? 'System',
                    'activity' => $notification->title,
                    'status' => 'active',
                    'status_class' => 'badge--primary',
                    'created_at' => $notification->created_at,
                    'url' => $notification->click_url ?? '#'
                ];
            });

        $activities = $activities->concat($ibNotifications);

        // Sort by created_at and limit
        $finalActivities = $activities->sortByDesc('created_at')->take($limit)->values();

        return response()->json([
            'success' => true,
            'data' => $finalActivities,
            'total' => $finalActivities->count()
        ]);
    }

    /**
     * Get status CSS class
     */
    private function getStatusClass($status)
    {
        return match($status) {
            1, 'approved', 'completed' => 'badge--success',
            2, 'pending' => 'badge--warning',
            3, 'rejected', 'cancelled' => 'badge--danger',
            default => 'badge--dark'
        };
    }

    /**
     * Get ticket status CSS class
     */
    private function getTicketStatusClass($status)
    {
        return match($status) {
            0 => 'badge--success', // Open
            1 => 'badge--primary', // Answered
            2 => 'badge--warning', // Customer Reply
            3 => 'badge--dark', // Closed
            default => 'badge--dark'
        };
    }

    /**
     * Get priority CSS class
     */
    private function getPriorityClass($priority)
    {
        return match($priority) {
            1 => 'badge--danger', // High
            2 => 'badge--warning', // Medium
            3 => 'badge--info', // Low
            default => 'badge--dark'
        };
    }

    /**
     * Get IB status CSS class
     */
    private function getIbStatusClass($status)
    {
        return match($status) {
            'approved' => 'badge--success',
            'pending' => 'badge--warning',
            'rejected' => 'badge--danger',
            default => 'badge--dark'
        };
    }
}
