@extends('admin.layouts.app')

@section('panel')
    <div class="row">
        <div class="col-lg-12">
            <div class="card b-radius--10">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="card-title mb-0">{{ __($pageTitle) }}</h6>
                    <div class="d-flex gap-2">
                        <a href="{{ route('admin.users.profile.requests.export', request()->query()) }}" 
                           class="btn btn--primary btn--sm">
                            <i class="las la-download"></i> Export CSV
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Summary Statistics -->
                    <div class="row mb-4">
                        <div class="col-xl-3 col-lg-6 col-sm-6 mb-30">
                            <div class="widget-two style--two box--shadow2 b-radius--5 bg--primary">
                                <div class="widget-two__icon">
                                    <i class="las la-file-alt"></i>
                                </div>
                                <div class="widget-two__content">
                                    <h3 class="text-white">{{ $stats['total_requests'] }}</h3>
                                    <p class="text-white">Total Requests</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-lg-6 col-sm-6 mb-30">
                            <div class="widget-two style--two box--shadow2 b-radius--5 bg--warning">
                                <div class="widget-two__icon">
                                    <i class="las la-clock"></i>
                                </div>
                                <div class="widget-two__content">
                                    <h3 class="text-white">{{ $stats['pending_requests'] }}</h3>
                                    <p class="text-white">Pending Requests</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-lg-6 col-sm-6 mb-30">
                            <div class="widget-two style--two box--shadow2 b-radius--5 bg--success">
                                <div class="widget-two__icon">
                                    <i class="las la-check-circle"></i>
                                </div>
                                <div class="widget-two__content">
                                    <h3 class="text-white">{{ $stats['approved_requests'] }}</h3>
                                    <p class="text-white">Approved ({{ $stats['approval_rate'] }}%)</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-lg-6 col-sm-6 mb-30">
                            <div class="widget-two style--two box--shadow2 b-radius--5 bg--danger">
                                <div class="widget-two__icon">
                                    <i class="las la-times-circle"></i>
                                </div>
                                <div class="widget-two__content">
                                    <h3 class="text-white">{{ $stats['rejected_requests'] }}</h3>
                                    <p class="text-white">Rejected ({{ $stats['rejection_rate'] }}%)</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-lg-12">
                            <form method="GET" action="{{ route('admin.users.profile.requests.reports') }}">
                                <div class="row">
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="form-label">Status</label>
                                            <select name="status" class="form-control">
                                                <option value="">All Status</option>
                                                <option value="pending" {{ $status == 'pending' ? 'selected' : '' }}>Pending</option>
                                                <option value="approved" {{ $status == 'approved' ? 'selected' : '' }}>Approved</option>
                                                <option value="rejected" {{ $status == 'rejected' ? 'selected' : '' }}>Rejected</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="form-label">From Date</label>
                                            <input type="date" name="date_from" class="form-control" value="{{ $dateFrom }}">
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="form-label">To Date</label>
                                            <input type="date" name="date_to" class="form-control" value="{{ $dateTo }}">
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="form-label">User</label>
                                            <select name="user_id" class="form-control">
                                                <option value="">All Users</option>
                                                @foreach($users as $user)
                                                    <option value="{{ $user->id }}" {{ $userId == $user->id ? 'selected' : '' }}>
                                                        {{ $user->firstname }} {{ $user->lastname }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="form-label">Search</label>
                                            <input type="text" name="search" class="form-control" placeholder="Name, Email..." value="{{ $search }}">
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="form-label">&nbsp;</label>
                                            <div class="d-flex gap-2">
                                                <button type="submit" class="btn btn--primary btn--sm">
                                                    <i class="las la-search"></i> Filter
                                                </button>
                                                <a href="{{ route('admin.users.profile.requests.reports') }}" class="btn btn--secondary btn--sm">
                                                    <i class="las la-undo"></i> Reset
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Reports Table -->
                    <div class="table-responsive--md table-responsive">
                        <table class="table table--light style--two">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Request Summary</th>
                                    <th>Status</th>
                                    <th>Submitted</th>
                                    <th>Processed By</th>
                                    <th>Processed At</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($reports as $report)
                                    <tr>
                                        <td>
                                            <div>
                                                <span class="fw-bold">{{ $report->user ? $report->user->firstname . ' ' . $report->user->lastname : 'N/A' }}</span>
                                                <br>
                                                <small class="text-muted">{{ $report->user ? $report->user->email : 'N/A' }}</small>
                                                <br>
                                                <small class="text-muted">{{ $report->user ? '@' . $report->user->username : 'N/A' }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="text-wrap" style="max-width: 300px;">
                                                {{ Str::limit($report->formatted_summary, 100) }}
                                            </div>
                                        </td>
                                        <td>{!! $report->status_badge !!}</td>
                                        <td>
                                            <div>
                                                {{ showDateTime($report->submitted_at) }}
                                                <br>
                                                <small class="text-muted">{{ $report->submitted_at->diffForHumans() }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            {{ $report->processedBy ? $report->processedBy->name : 'N/A' }}
                                        </td>
                                        <td>
                                            @if($report->processed_at)
                                                <div>
                                                    {{ showDateTime($report->processed_at) }}
                                                    <br>
                                                    <small class="text-muted">{{ $report->processed_at->diffForHumans() }}</small>
                                                </div>
                                            @else
                                                <span class="text-muted">N/A</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="d-flex gap-1">
                                                <button type="button"
                                                        class="btn btn--info btn--shadow btn--sm show-request-btn"
                                                        data-request-id="{{ $report->id }}"
                                                        data-bs-toggle="tooltip"
                                                        title="Show Request Details">
                                                    <i class="las la-eye"></i> Show Request
                                                </button>
                                                @if($report->user)
                                                    <a href="{{ route('admin.users.detail', $report->user->id) }}"
                                                       class="btn btn--primary btn--shadow btn--sm"
                                                       data-bs-toggle="tooltip"
                                                       title="View User Profile">
                                                        <i class="las la-desktop"></i> View User
                                                    </a>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                    @if($report->rejection_reason)
                                        <tr class="bg-light">
                                            <td colspan="7">
                                                <small class="text-danger">
                                                    <strong>Rejection Reason:</strong> {{ $report->rejection_reason }}
                                                </small>
                                            </td>
                                        </tr>
                                    @endif
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center">
                                            <div class="py-4">
                                                <i class="las la-file-alt" style="font-size: 3rem; color: #ccc;"></i>
                                                <br>
                                                <span class="text-muted">No profile request reports found</span>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($reports->hasPages())
                        <div class="card-footer py-4">
                            {{ paginateLinks($reports) }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

<!-- Profile Request Details Modal -->
<div class="modal fade" id="requestDetailsModal" tabindex="-1" aria-labelledby="requestDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header bg--primary">
                <h5 class="modal-title text-white" id="requestDetailsModalLabel">
                    <i class="las la-file-alt"></i> Profile Change Request Details
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="requestDetailsContent">
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading request details...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn--secondary" data-bs-dismiss="modal">
                    <i class="las la-times"></i> Close
                </button>
            </div>
        </div>
    </div>
</div>

@push('breadcrumb-plugins')
    <div class="d-flex align-items-center gap-2">
        <span class="text-muted">Recent Requests (30 days): {{ $stats['recent_requests'] }}</span>
    </div>
@endpush

@push('script')
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Handle Show Request button click
    $('.show-request-btn').on('click', function() {
        const requestId = $(this).data('request-id');
        const modal = $('#requestDetailsModal');
        const content = $('#requestDetailsContent');

        // Show modal
        modal.modal('show');

        // Reset content to loading state
        content.html(`
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 text-muted">Loading request details...</p>
            </div>
        `);

        // Fetch request details
        $.ajax({
            url: `{{ route('admin.users.profile.requests.details', '') }}/${requestId}`,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    displayRequestDetails(response.data);
                } else {
                    showError('Failed to load request details');
                }
            },
            error: function(xhr) {
                showError('Error loading request details: ' + (xhr.responseJSON?.message || 'Unknown error'));
            }
        });
    });

    function displayRequestDetails(data) {
        const content = $('#requestDetailsContent');

        let html = `
            <div class="row">
                <div class="col-md-6">
                    <div class="card border-0 bg-light mb-3">
                        <div class="card-header bg-transparent border-0">
                            <h6 class="mb-0"><i class="las la-user"></i> User Information</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless table-sm">
                                <tr>
                                    <td class="fw-bold">Name:</td>
                                    <td>${data.user.name}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Email:</td>
                                    <td>${data.user.email}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Username:</td>
                                    <td>@${data.user.username}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-0 bg-light mb-3">
                        <div class="card-header bg-transparent border-0">
                            <h6 class="mb-0"><i class="las la-clock"></i> Request Timeline</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless table-sm">
                                <tr>
                                    <td class="fw-bold">Status:</td>
                                    <td>${data.status_badge}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Submitted:</td>
                                    <td>${data.submitted_at}<br><small class="text-muted">${data.submitted_at_human}</small></td>
                                </tr>
                                ${data.processed_at ? `
                                <tr>
                                    <td class="fw-bold">Processed:</td>
                                    <td>${data.processed_at}<br><small class="text-muted">${data.processed_at_human}</small></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Processed By:</td>
                                    <td>${data.processed_by || 'N/A'}</td>
                                </tr>
                                ` : ''}
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Request Details Section
        if (data.request_details && data.request_details.length > 0) {
            html += `
                <div class="card border-0 bg-light mb-3">
                    <div class="card-header bg-transparent border-0">
                        <h6 class="mb-0"><i class="las la-edit"></i> Requested Changes</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
            `;

            data.request_details.forEach((detail, index) => {
                html += `
                    <div class="col-md-6 mb-3">
                        <div class="border rounded p-3 h-100">
                            <h6 class="text-primary mb-2">${detail.name}</h6>
                `;

                if (detail.is_file) {
                    if (detail.is_image) {
                        html += `
                            <div class="mb-2">
                                <img src="{{ asset(getFilePath('verify')) }}/${detail.value}"
                                     alt="Document Preview"
                                     class="img-fluid rounded"
                                     style="max-width: 200px; max-height: 200px; object-fit: contain; cursor: pointer;"
                                     onclick="window.open(this.src, '_blank')" />
                            </div>
                            <a href="{{ asset(getFilePath('verify')) }}/${detail.value}"
                               target="_blank" class="btn btn--sm btn--primary">
                                <i class="las la-download"></i> Download Image
                            </a>
                        `;
                    } else if (detail.is_pdf) {
                        html += `
                            <div class="mb-2">
                                <i class="las la-file-pdf text-danger" style="font-size: 3rem;"></i>
                            </div>
                            <a href="{{ asset(getFilePath('verify')) }}/${detail.value}"
                               target="_blank" class="btn btn--sm btn--primary">
                                <i class="las la-file-pdf"></i> View PDF
                            </a>
                        `;
                    } else {
                        html += `
                            <div class="mb-2">
                                <i class="las la-file text-info" style="font-size: 3rem;"></i>
                            </div>
                            <a href="{{ asset(getFilePath('verify')) }}/${detail.value}"
                               target="_blank" class="btn btn--sm btn--primary">
                                <i class="las la-download"></i> Download File
                            </a>
                        `;
                    }
                } else {
                    html += `<p class="mb-0 text-dark">${detail.value}</p>`;
                }

                html += `</div></div>`;
            });

            html += `</div></div></div>`;
        }

        // Additional Information
        html += `<div class="row">`;

        // Rejection Reason
        if (data.rejection_reason) {
            html += `
                <div class="col-md-6">
                    <div class="card border-danger mb-3">
                        <div class="card-header bg-danger text-white">
                            <h6 class="mb-0"><i class="las la-times-circle"></i> Rejection Reason</h6>
                        </div>
                        <div class="card-body">
                            <p class="mb-0">${data.rejection_reason}</p>
                        </div>
                    </div>
                </div>
            `;
        }

        // Technical Information
        html += `
            <div class="col-md-6">
                <div class="card border-0 bg-light mb-3">
                    <div class="card-header bg-transparent border-0">
                        <h6 class="mb-0"><i class="las la-info-circle"></i> Technical Information</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td class="fw-bold">IP Address:</td>
                                <td>${data.ip_address || 'N/A'}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">User Agent:</td>
                                <td><small>${data.user_agent || 'N/A'}</small></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        `;

        html += `</div>`;

        content.html(html);
    }

    function showError(message) {
        const content = $('#requestDetailsContent');
        content.html(`
            <div class="text-center py-4">
                <i class="las la-exclamation-triangle text-danger" style="font-size: 3rem;"></i>
                <h5 class="text-danger mt-2">Error</h5>
                <p class="text-muted">${message}</p>
            </div>
        `);
    }

    // Close modal with ESC key
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape') {
            $('#requestDetailsModal').modal('hide');
        }
    });
});
</script>
@endpush
