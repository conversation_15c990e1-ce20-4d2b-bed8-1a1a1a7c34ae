<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use Carbon\Carbon;

class MigrateMT5UsersComplete extends Command
{
    protected $signature = 'mt5:migrate-complete {--limit=5000} {--dry-run} {--force}';
    protected $description = 'Complete migration of ALL MT5 users with Solution 1: One email per display with all MT5 accounts in hover';

    public function handle()
    {
        $this->info('🚀 STARTING COMPLETE MT5 MIGRATION - SOLUTION 1');
        $this->info('================================================');
        
        $limit = $this->option('limit');
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');
        
        if ($dryRun) {
            $this->warn('🧪 DRY RUN MODE - No changes will be made');
        }
        
        try {
            // Step 1: Get all MT5 users from Ireland database
            $this->info('📊 Step 1: Fetching ALL MT5 users from mbf-dbmt5...');
            
            $mt5Users = DB::connection('mbf-dbmt5')->table('mt5_users')
                ->whereNotNull('Email')
                ->where('Email', '!=', '')
                ->orderBy('Registration', 'desc')
                ->limit($limit)
                ->get();
            
            $this->info("Found {$mt5Users->count()} MT5 users to process");
            
            if ($mt5Users->isEmpty()) {
                $this->error('No MT5 users found!');
                return 1;
            }
            
            // Step 2: Group by email to implement Solution 1
            $this->info('📋 Step 2: Grouping users by email (Solution 1)...');
            
            $groupedUsers = $mt5Users->groupBy('Email');
            $this->info("Found {$groupedUsers->count()} unique emails");
            
            $processed = 0;
            $created = 0;
            $updated = 0;
            $errors = 0;
            
            // Step 3: Process each email group
            foreach ($groupedUsers as $email => $userGroup) {
                try {
                    if (!$dryRun) {
                        $result = $this->processEmailGroup($email, $userGroup);
                        if ($result['created']) $created++;
                        if ($result['updated']) $updated++;
                    }
                    $processed++;
                    
                    if ($processed % 100 == 0) {
                        $this->info("Processed {$processed}/{$groupedUsers->count()} emails...");
                    }
                    
                } catch (\Exception $e) {
                    $errors++;
                    $this->error("Error processing {$email}: " . $e->getMessage());
                }
            }
            
            // Step 4: Results
            $this->info('✅ MIGRATION COMPLETED!');
            $this->info("📊 Results:");
            $this->info("   Emails processed: {$processed}");
            $this->info("   Users created: {$created}");
            $this->info("   Users updated: {$updated}");
            $this->info("   Errors: {$errors}");
            
            return 0;
            
        } catch (\Exception $e) {
            $this->error('Migration failed: ' . $e->getMessage());
            return 1;
        }
    }
    
    private function processEmailGroup($email, $userGroup)
    {
        // Get the most recent MT5 user for this email (primary account)
        $primaryMT5User = $userGroup->sortByDesc('Registration')->first();
        
        // Collect all MT5 accounts for this email
        $allMT5Accounts = $userGroup->pluck('Login')->toArray();
        $allMT5AccountsString = implode(',', $allMT5Accounts);
        
        // Create detailed MT5 accounts info for hover tooltips
        $detailedAccounts = [];
        foreach ($userGroup as $mt5User) {
            $detailedAccounts[] = [
                'login' => $mt5User->Login,
                'group' => $mt5User->Group ?? 'Unknown',
                'balance' => $mt5User->Balance ?? 0,
                'equity' => $mt5User->Equity ?? 0,
                'leverage' => $mt5User->Leverage ?? 100,
                'reg_date' => $mt5User->Registration
            ];
        }
        
        // Check if user already exists in local database
        $existingUser = User::where('email', $email)->first();
        
        $userData = [
            'firstname' => $primaryMT5User->FirstName ?? 'Unknown',
            'lastname' => $primaryMT5User->LastName ?? '',
            'email' => $email,
            'username' => $this->generateCleanUsername($primaryMT5User->FirstName, $primaryMT5User->LastName, $email),
            'country_code' => $primaryMT5User->Country ?? '',
            'mobile' => $primaryMT5User->Phone ?? '',
            'status' => 1,
            'ev' => 1,
            'sv' => 1,
            'profile_complete' => 1,
            // Primary MT5 account data
            'mt5_login' => $primaryMT5User->Login,
            'mt5_group' => $primaryMT5User->Group ?? 'real',
            'mt5_balance' => $primaryMT5User->Balance ?? 0,
            'mt5_equity' => $primaryMT5User->Equity ?? 0,
            'mt5_credit' => $primaryMT5User->Credit ?? 0,
            'mt5_leverage' => $primaryMT5User->Leverage ?? 100,
            'mt5_currency' => $primaryMT5User->Currency ?? 'USD',
            'mt5_synced_at' => now(),
            // ALL MT5 accounts for this email (Solution 1)
            'all_mt5_accounts' => $allMT5AccountsString,
            'all_mt5_accounts_detailed' => json_encode($detailedAccounts),
            'created_at' => Carbon::parse($primaryMT5User->Registration),
            'updated_at' => now()
        ];
        
        $created = false;
        $updated = false;
        
        if ($existingUser) {
            // Update existing user with consolidated MT5 data
            $existingUser->update($userData);
            $updated = true;
        } else {
            // Create new user
            User::create($userData);
            $created = true;
        }
        
        return ['created' => $created, 'updated' => $updated];
    }
    
    private function generateCleanUsername($firstName, $lastName, $email)
    {
        $baseUsername = strtolower($firstName);
        if ($lastName) {
            $baseUsername .= strtolower($lastName);
        }
        
        // Remove special characters
        $baseUsername = preg_replace('/[^a-z0-9]/', '', $baseUsername);
        
        // Ensure uniqueness
        $username = $baseUsername;
        $counter = 1;
        while (User::where('username', $username)->exists()) {
            $username = $baseUsername . $counter;
            $counter++;
        }
        
        return $username;
    }
}
