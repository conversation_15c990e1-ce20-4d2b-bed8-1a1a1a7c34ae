<?php
/**
 * DEBUG MT5 PATH RESOLUTION
 * This script will show exactly what paths <PERSON><PERSON> is using
 */

echo "<h1>MT5 Path Resolution Debug</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .code{background:#f5f5f5;padding:10px;border:1px solid #ddd;margin:10px 0;}</style>";

// Load Laravel environment
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "<h2>1. Environment Variables from Laravel</h2>";

// Get the actual values <PERSON><PERSON> is reading
$pythonExe = env('PYTHON_EXE', 'python');
$pythonScript = env('PYTHON_SCRIPT', base_path('python/mt5manager.py'));

echo "<div class='code'>";
echo "<strong>PYTHON_EXE from env():</strong> " . htmlspecialchars($pythonExe) . "<br>";
echo "<strong>PYTHON_SCRIPT from env():</strong> " . htmlspecialchars($pythonScript) . "<br>";
echo "<strong>base_path():</strong> " . htmlspecialchars(base_path()) . "<br>";
echo "<strong>base_path('python/mt5manager.py'):</strong> " . htmlspecialchars(base_path('python/mt5manager.py')) . "<br>";
echo "</div>";

echo "<h2>2. File Existence Check</h2>";

// Check if files exist
if (file_exists($pythonExe)) {
    echo "<span class='success'>✓ Python executable found: " . htmlspecialchars($pythonExe) . "</span><br>";
} else {
    echo "<span class='error'>✗ Python executable NOT found: " . htmlspecialchars($pythonExe) . "</span><br>";
}

if (file_exists($pythonScript)) {
    echo "<span class='success'>✓ Python script found: " . htmlspecialchars($pythonScript) . "</span><br>";
    echo "<strong>Script size:</strong> " . filesize($pythonScript) . " bytes<br>";
} else {
    echo "<span class='error'>✗ Python script NOT found: " . htmlspecialchars($pythonScript) . "</span><br>";
    
    // Check alternative locations
    $alternatives = [
        'C:\\Inetpub\\vhosts\\mybrokerforex.com\\mbf.mybrokerforex.com\\python\\mt5manager.py',
        base_path() . '\\python\\mt5manager.py',
        base_path() . '/python/mt5manager.py',
        'python/mt5manager.py',
        'python\\mt5manager.py'
    ];
    
    echo "<br><strong>Checking alternative locations:</strong><br>";
    foreach ($alternatives as $alt) {
        if (file_exists($alt)) {
            echo "<span class='success'>✓ FOUND: " . htmlspecialchars($alt) . "</span><br>";
        } else {
            echo "<span class='error'>✗ Not found: " . htmlspecialchars($alt) . "</span><br>";
        }
    }
}

echo "<h2>3. MT5Service Simulation</h2>";

// Simulate what MT5Service is doing
echo "<strong>Simulating MT5Service constructor...</strong><br>";

$mt5PythonExe = env('PYTHON_EXE', 'python');
$mt5PythonScript = env('PYTHON_SCRIPT', base_path('python/mt5manager.py'));

echo "<div class='code'>";
echo "<strong>MT5Service \$this->pythonExe:</strong> " . htmlspecialchars($mt5PythonExe) . "<br>";
echo "<strong>MT5Service \$this->pythonScript:</strong> " . htmlspecialchars($mt5PythonScript) . "<br>";
echo "</div>";

// Simulate command building
$accountLogin = 12345; // dummy login
$newLeverage = 300;

$command = escapeshellarg($mt5PythonExe) . " " . escapeshellarg($mt5PythonScript) . " change_leverage --login {$accountLogin} --leverage {$newLeverage}";

echo "<strong>Generated command:</strong><br>";
echo "<div class='code'>" . htmlspecialchars($command) . "</div>";

echo "<h2>4. Command Execution Test</h2>";

if (file_exists($mt5PythonExe) && file_exists($mt5PythonScript)) {
    echo "<strong>Testing the exact command that MT5Service would run...</strong><br>";
    
    // Test with --help first
    $helpCommand = escapeshellarg($mt5PythonExe) . " " . escapeshellarg($mt5PythonScript) . " --help";
    echo "<strong>Help command:</strong> " . htmlspecialchars($helpCommand) . "<br>";
    
    $helpOutput = shell_exec($helpCommand . ' 2>&1');
    if ($helpOutput) {
        echo "<span class='success'>✓ Help command executed successfully</span><br>";
        echo "<div class='code'>" . htmlspecialchars($helpOutput) . "</div>";
    } else {
        echo "<span class='error'>✗ Help command failed</span><br>";
    }
    
    // Test actual leverage command with a test login
    echo "<br><strong>Testing leverage command (will fail with invalid login, but should show proper error):</strong><br>";
    $testCommand = escapeshellarg($mt5PythonExe) . " " . escapeshellarg($mt5PythonScript) . " change_leverage --login 999999 --leverage 300";
    echo "<strong>Test command:</strong> " . htmlspecialchars($testCommand) . "<br>";
    
    $testOutput = shell_exec($testCommand . ' 2>&1');
    if ($testOutput) {
        echo "<span class='success'>✓ Command executed (check output for errors)</span><br>";
        echo "<div class='code'>" . htmlspecialchars($testOutput) . "</div>";
    } else {
        echo "<span class='error'>✗ Command execution failed</span><br>";
    }
    
} else {
    echo "<span class='error'>✗ Cannot test - files not found</span><br>";
}

echo "<h2>5. .env File Content Check</h2>";

if (file_exists('.env')) {
    $envContent = file_get_contents('.env');
    $envLines = explode("\n", $envContent);
    
    echo "<strong>Python-related lines in .env:</strong><br>";
    echo "<div class='code'>";
    foreach ($envLines as $line) {
        if (stripos($line, 'PYTHON') !== false) {
            echo htmlspecialchars($line) . "<br>";
        }
    }
    echo "</div>";
} else {
    echo "<span class='error'>✗ .env file not found</span><br>";
}

echo "<h2>6. PHP Configuration</h2>";
echo "<strong>PHP Version:</strong> " . PHP_VERSION . "<br>";
echo "<strong>exec() enabled:</strong> " . (function_exists('exec') ? 'Yes' : 'No') . "<br>";
echo "<strong>shell_exec() enabled:</strong> " . (function_exists('shell_exec') ? 'Yes' : 'No') . "<br>";
echo "<strong>Current working directory:</strong> " . getcwd() . "<br>";

echo "<h2>7. Recommended Fix</h2>";

if (!file_exists($pythonScript)) {
    echo "<div style='background:#ffe6e6;padding:15px;border:1px solid red;'>";
    echo "<h4>❌ ISSUE FOUND: Python script path is wrong</h4>";
    echo "<p>Laravel is looking for the script at:</p>";
    echo "<code>" . htmlspecialchars($pythonScript) . "</code>";
    echo "<p>But this file doesn't exist. Update your .env file with the correct path.</p>";
    echo "</div>";
} else {
    echo "<div style='background:#e6ffe6;padding:15px;border:1px solid green;'>";
    echo "<h4>✅ Paths look correct</h4>";
    echo "<p>The issue might be in the MT5Service code or command execution.</p>";
    echo "</div>";
}

echo "<p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><em>Delete this file after debugging.</em></p>";
?>
