<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\AccountLevel;

class AccountLevelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $accountLevels = [
            [
                'name' => 'No Deposit Bonus',
                'platform_group_default' => 'real\MBFX\B\Sw\PrmGLOBAL_USD',
                'trading_server_live' => 'MBFXGlobal-Server',
                'enable_separate_swap_free' => false,
                'platform_group_swap_free' => null,
                'leverage_options' => [100, 200, 300, 400, 500],
                'country_restrictions' => ['India', 'Pakistan'],
                'tags' => ['bonus', 'promotional'],
                'status' => false // Deactivated as shown in image
            ],
            [
                'name' => 'Premium',
                'platform_group_default' => 'real\MBFX\B\Sw\PrmGLOBAL_USD',
                'trading_server_live' => 'MBFXGlobal-Server',
                'enable_separate_swap_free' => true,
                'platform_group_swap_free' => 'real\MBFX\B\ShPrmGLOBAL_USD',
                'leverage_options' => [100, 200, 300, 400, 500],
                'country_restrictions' => [],
                'tags' => ['premium', 'standard'],
                'status' => true
            ],
            [
                'name' => 'Copy Trading',
                'platform_group_default' => 'real\MBFX\B\Sw\PrmGLOBAL_USD',
                'trading_server_live' => 'MBFXGlobal-Server',
                'enable_separate_swap_free' => false,
                'platform_group_swap_free' => null,
                'leverage_options' => [100, 200, 300, 400, 500],
                'country_restrictions' => [],
                'tags' => ['copy', 'social'],
                'status' => true
            ],
            [
                'name' => 'VIP',
                'platform_group_default' => 'real\MBFX\B\Sw\PrmGLOBAL_USD',
                'trading_server_live' => 'MBFXGlobal-Server',
                'enable_separate_swap_free' => false,
                'platform_group_swap_free' => null,
                'leverage_options' => [100, 200, 300, 400, 500],
                'country_restrictions' => [],
                'tags' => ['vip', 'exclusive'],
                'status' => true
            ],
            [
                'name' => 'Test Premium B',
                'platform_group_default' => 'real\MBFX\B\Sw\PrmGLOBAL_USD',
                'trading_server_live' => 'MBFXGlobal-Server',
                'enable_separate_swap_free' => false,
                'platform_group_swap_free' => null,
                'leverage_options' => [100, 200, 300, 400, 500],
                'country_restrictions' => [],
                'tags' => ['test', 'beta'],
                'status' => false // Deactivated as shown in image
            ]
        ];

        foreach ($accountLevels as $level) {
            AccountLevel::updateOrCreate(
                ['name' => $level['name']],
                $level
            );
        }

        $this->command->info('Account levels seeded successfully!');
    }
}
