<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\NotificationTemplate;

class NotificationTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create missing notification templates
        $templates = [
            // MT5 Internal Balance Transfer Templates
            [
                'act' => 'MT5_INTERNAL_TRANSFER_SENT',
                'name' => 'MT5 Internal Transfer - Sent',
                'subj' => 'Internal Transfer Completed - {{amount}} {{currency}} Sent',
                'email_body' => '<div style="padding: 20px; font-family: Arial, sans-serif;"><h3>Internal Transfer Completed</h3><p>Dear {{fullname}},</p><p>Your internal transfer has been completed successfully:</p><ul><li><strong>Amount:</strong> {{amount}} {{currency}}</li><li><strong>From Account:</strong> {{from_account}} ({{from_group}})</li><li><strong>To Account:</strong> {{to_account}} ({{to_group}})</li><li><strong>Transaction ID:</strong> {{transaction_id}}</li><li><strong>Date:</strong> {{transfer_date}}</li></ul><p>Your funds have been transferred between your MT5 accounts.</p><p>Best regards,<br>{{site_name}} Team</p></div>',
                'sms_body' => 'Internal transfer completed: {{amount}} {{currency}} from {{from_account}} to {{to_account}}. Transaction ID: {{transaction_id}}',
                'shortcodes' => json_encode([
                    'fullname' => 'Full name of the user',
                    'username' => 'Username of the user',
                    'amount' => 'Transfer amount',
                    'currency' => 'Currency symbol',
                    'from_account' => 'Source MT5 account login',
                    'from_group' => 'Source MT5 account group',
                    'to_account' => 'Destination MT5 account login',
                    'to_group' => 'Destination MT5 account group',
                    'transaction_id' => 'Transaction reference ID',
                    'transfer_date' => 'Date and time of transfer',
                    'site_name' => 'Name of the site'
                ]),
                'email_status' => 1,
                'sms_status' => 0
            ],
            [
                'act' => 'MT5_INTERNAL_TRANSFER_RECEIVED',
                'name' => 'MT5 Internal Transfer - Received',
                'subj' => 'Internal Transfer Received - {{amount}} {{currency}} Added to Your Account',
                'email_body' => '<div style="padding: 20px; font-family: Arial, sans-serif;"><h3>Internal Transfer Received</h3><p>Dear {{fullname}},</p><p>You have received an internal transfer:</p><ul><li><strong>Amount:</strong> {{amount}} {{currency}}</li><li><strong>From Account:</strong> {{from_account}} ({{from_group}})</li><li><strong>To Account:</strong> {{to_account}} ({{to_group}})</li><li><strong>New Balance:</strong> {{new_balance}} {{currency}}</li><li><strong>Transaction ID:</strong> {{transaction_id}}</li><li><strong>Date:</strong> {{transfer_date}}</li></ul><p>The funds are now available in your MT5 account.</p><p>Best regards,<br>{{site_name}} Team</p></div>',
                'sms_body' => 'Internal transfer received: {{amount}} {{currency}} added to {{to_account}}. New balance: {{new_balance}} {{currency}}',
                'shortcodes' => json_encode([
                    'fullname' => 'Full name of the user',
                    'username' => 'Username of the user',
                    'amount' => 'Transfer amount',
                    'currency' => 'Currency symbol',
                    'from_account' => 'Source MT5 account login',
                    'from_group' => 'Source MT5 account group',
                    'to_account' => 'Destination MT5 account login',
                    'to_group' => 'Destination MT5 account group',
                    'new_balance' => 'New account balance after transfer',
                    'transaction_id' => 'Transaction reference ID',
                    'transfer_date' => 'Date and time of transfer',
                    'site_name' => 'Name of the site'
                ]),
                'email_status' => 1,
                'sms_status' => 0
            ],
            [
                'act' => 'MT5_ACCOUNT_CREATED',
                'name' => 'MT5 Account Created',
                'subj' => 'New MT5 Trading Account Created - {{mt5_login}}',
                'email_body' => '<div style="padding: 20px; font-family: Arial, sans-serif;"><h3>MT5 Trading Account Created</h3><p>Dear {{fullname}},</p><p>Your new MT5 trading account has been created successfully:</p><ul><li><strong>Login:</strong> {{mt5_login}}</li><li><strong>Group:</strong> {{mt5_group}}</li><li><strong>Leverage:</strong> 1:{{leverage}}</li><li><strong>Currency:</strong> {{currency}}</li><li><strong>Server:</strong> {{server_name}}</li></ul><p><strong>Important:</strong> Your trading password has been sent separately for security reasons.</p><p>You can now start trading with your new MT5 account.</p><p>Best regards,<br>{{site_name}} Team</p></div>',
                'sms_body' => 'New MT5 account created: {{mt5_login}} with leverage 1:{{leverage}}. Check your email for details.',
                'shortcodes' => json_encode([
                    'fullname' => 'Full name of the user',
                    'username' => 'Username of the user',
                    'mt5_login' => 'MT5 account login number',
                    'mt5_group' => 'MT5 account group',
                    'leverage' => 'Account leverage',
                    'currency' => 'Account currency',
                    'server_name' => 'MT5 server name',
                    'site_name' => 'Name of the site'
                ]),
                'email_status' => 1,
                'sms_status' => 0
            ],
            [
                'act' => 'MT5_PASSWORD_CHANGED',
                'name' => 'MT5 Password Changed',
                'subj' => 'MT5 Account Password Changed - {{mt5_login}}',
                'email_body' => '<div style="padding: 20px; font-family: Arial, sans-serif;"><h3>MT5 Password Changed</h3><p>Dear {{fullname}},</p><p>The password for your MT5 trading account has been changed successfully:</p><ul><li><strong>Account:</strong> {{mt5_login}} ({{mt5_group}})</li><li><strong>Changed on:</strong> {{change_date}}</li><li><strong>IP Address:</strong> {{ip_address}}</li></ul><p>If you did not make this change, please contact our support team immediately.</p><p>Best regards,<br>{{site_name}} Team</p></div>',
                'sms_body' => 'MT5 password changed for account {{mt5_login}}. If this was not you, contact support immediately.',
                'shortcodes' => json_encode([
                    'fullname' => 'Full name of the user',
                    'username' => 'Username of the user',
                    'mt5_login' => 'MT5 account login number',
                    'mt5_group' => 'MT5 account group',
                    'change_date' => 'Date and time of password change',
                    'ip_address' => 'IP address from which change was made',
                    'site_name' => 'Name of the site'
                ]),
                'email_status' => 1,
                'sms_status' => 0
            ],
            [
                'act' => 'MT5_LEVERAGE_CHANGED',
                'name' => 'MT5 Leverage Changed',
                'subj' => 'MT5 Account Leverage Updated - {{mt5_login}}',
                'email_body' => '<div style="padding: 20px; font-family: Arial, sans-serif;"><h3>MT5 Leverage Updated</h3><p>Dear {{fullname}},</p><p>The leverage for your MT5 trading account has been updated:</p><ul><li><strong>Account:</strong> {{mt5_login}} ({{mt5_group}})</li><li><strong>Previous Leverage:</strong> 1:{{old_leverage}}</li><li><strong>New Leverage:</strong> 1:{{new_leverage}}</li><li><strong>Updated on:</strong> {{update_date}}</li></ul><p>The new leverage is now active on your trading account.</p><p>Best regards,<br>{{site_name}} Team</p></div>',
                'sms_body' => 'MT5 leverage updated for {{mt5_login}}: 1:{{old_leverage}} → 1:{{new_leverage}}',
                'shortcodes' => json_encode([
                    'fullname' => 'Full name of the user',
                    'username' => 'Username of the user',
                    'mt5_login' => 'MT5 account login number',
                    'mt5_group' => 'MT5 account group',
                    'old_leverage' => 'Previous leverage value',
                    'new_leverage' => 'New leverage value',
                    'update_date' => 'Date and time of leverage change',
                    'site_name' => 'Name of the site'
                ]),
                'email_status' => 1,
                'sms_status' => 0
            ],
            [
                'act' => 'USER_REGISTRATION',
                'name' => 'User Registration Welcome',
                'subj' => 'Welcome to {{site_name}} - Account Created Successfully',
                'email_body' => 'Welcome to {{site_name}}! Your account has been created successfully. You can now access all our trading features and services. Your username is {{username}}. Thank you for joining us!',
                'sms_body' => 'Welcome to {{site_name}}! Your account {{username}} has been created successfully.',
                'shortcodes' => json_encode([
                    'username' => 'Username of the user',
                    'fullname' => 'Full name of the user',
                    'site_name' => 'Name of the site'
                ]),
                'email_status' => 1,
                'sms_status' => 0
            ],
            [
                'act' => 'IB_APPLICATION_ADMIN',
                'name' => 'IB Application - Admin Notification',
                'subj' => 'New IB Application Submitted - {{username}}',
                'email_body' => 'A new IB application has been submitted by {{username}} ({{email}}). Please review the application in the admin panel. Application details: Expected clients: {{expected_clients}}, Trading volume: {{trading_volume}}.',
                'sms_body' => 'New IB application from {{username}}. Please review in admin panel.',
                'shortcodes' => json_encode([
                    'username' => 'Username of the applicant',
                    'email' => 'Email of the applicant',
                    'fullname' => 'Full name of the applicant',
                    'expected_clients' => 'Expected number of clients',
                    'trading_volume' => 'Expected trading volume'
                ]),
                'email_status' => 1,
                'sms_status' => 0
            ],
            [
                'act' => 'IB_APPLICATION_USER',
                'name' => 'IB Application - User Confirmation',
                'subj' => 'IB Application Submitted Successfully',
                'email_body' => 'Your IB application has been submitted successfully. Our team will review your application and contact you within 24-48 hours. Thank you for your interest in becoming our partner.',
                'sms_body' => 'Your IB application has been submitted successfully. We will review and contact you soon.',
                'shortcodes' => json_encode([
                    'username' => 'Username of the applicant',
                    'fullname' => 'Full name of the applicant'
                ]),
                'email_status' => 1,
                'sms_status' => 0
            ],
            [
                'act' => 'KYC_SUBMISSION_ADMIN',
                'name' => 'KYC Submission - Admin Notification',
                'subj' => 'New KYC Document Submitted - {{username}}',
                'email_body' => 'User {{username}} ({{email}}) has submitted KYC documents for verification. Please review the documents in the admin panel.',
                'sms_body' => 'New KYC submission from {{username}}. Please review in admin panel.',
                'shortcodes' => json_encode([
                    'username' => 'Username of the user',
                    'email' => 'Email of the user',
                    'fullname' => 'Full name of the user'
                ]),
                'email_status' => 1,
                'sms_status' => 0
            ],
            [
                'act' => 'KYC_SUBMISSION_USER',
                'name' => 'KYC Submission - User Confirmation',
                'subj' => 'KYC Documents Submitted Successfully',
                'email_body' => 'Your KYC documents have been submitted successfully. Our team will review your documents and update your account status within 24-48 hours.',
                'sms_body' => 'Your KYC documents have been submitted successfully. We will review and update your status soon.',
                'shortcodes' => json_encode([
                    'username' => 'Username of the user',
                    'fullname' => 'Full name of the user'
                ]),
                'email_status' => 1,
                'sms_status' => 0
            ],
            [
                'act' => 'IB_APPLICATION_APPROVED',
                'name' => 'IB Application - Approved',
                'subj' => 'Congratulations! Your IB Application has been Approved',
                'email_body' => 'Congratulations {{username}}! Your IB application has been approved. You are now a {{ib_type}} IB with referral code: {{referral_code}}. You can start earning commissions from your referrals.',
                'sms_body' => 'Congratulations! Your IB application has been approved. Referral code: {{referral_code}}',
                'shortcodes' => json_encode([
                    'username' => 'Username of the IB',
                    'ib_type' => 'Type of IB (Master/Sub)',
                    'referral_code' => 'IB referral code'
                ]),
                'email_status' => 1,
                'sms_status' => 0
            ],
            [
                'act' => 'IB_APPLICATION_REJECTED',
                'name' => 'IB Application - Rejected',
                'subj' => 'IB Application Status Update',
                'email_body' => 'Dear {{username}}, we regret to inform you that your IB application has been rejected. Reason: {{reason}}. You may reapply after addressing the mentioned concerns.',
                'sms_body' => 'Your IB application has been rejected. Reason: {{reason}}',
                'shortcodes' => json_encode([
                    'username' => 'Username of the applicant',
                    'reason' => 'Reason for rejection'
                ]),
                'email_status' => 1,
                'sms_status' => 0
            ],
            // Additional MT5 and Trading Templates
            [
                'act' => 'MT5_BALANCE_ADDED',
                'name' => 'MT5 Balance Added',
                'subj' => 'Balance Added to Your MT5 Account - {{amount}} {{currency}}',
                'email_body' => '<div style="padding: 20px; font-family: Arial, sans-serif;"><h3>Balance Added</h3><p>Dear {{fullname}},</p><p>Your MT5 account balance has been updated:</p><ul><li><strong>Account:</strong> {{mt5_login}} ({{mt5_group}})</li><li><strong>Amount Added:</strong> {{amount}} {{currency}}</li><li><strong>New Balance:</strong> {{new_balance}} {{currency}}</li><li><strong>Transaction ID:</strong> {{transaction_id}}</li><li><strong>Date:</strong> {{transaction_date}}</li></ul><p>The funds are now available for trading.</p><p>Best regards,<br>{{site_name}} Team</p></div>',
                'sms_body' => 'Balance added: {{amount}} {{currency}} to MT5 account {{mt5_login}}. New balance: {{new_balance}} {{currency}}',
                'shortcodes' => json_encode([
                    'fullname' => 'Full name of the user',
                    'username' => 'Username of the user',
                    'mt5_login' => 'MT5 account login number',
                    'mt5_group' => 'MT5 account group',
                    'amount' => 'Amount added',
                    'currency' => 'Currency symbol',
                    'new_balance' => 'New account balance',
                    'transaction_id' => 'Transaction reference ID',
                    'transaction_date' => 'Date and time of transaction',
                    'site_name' => 'Name of the site'
                ]),
                'email_status' => 1,
                'sms_status' => 0
            ],
            [
                'act' => 'MT5_BALANCE_DEDUCTED',
                'name' => 'MT5 Balance Deducted',
                'subj' => 'Balance Deducted from Your MT5 Account - {{amount}} {{currency}}',
                'email_body' => '<div style="padding: 20px; font-family: Arial, sans-serif;"><h3>Balance Deducted</h3><p>Dear {{fullname}},</p><p>Your MT5 account balance has been updated:</p><ul><li><strong>Account:</strong> {{mt5_login}} ({{mt5_group}})</li><li><strong>Amount Deducted:</strong> {{amount}} {{currency}}</li><li><strong>Remaining Balance:</strong> {{new_balance}} {{currency}}</li><li><strong>Reason:</strong> {{reason}}</li><li><strong>Transaction ID:</strong> {{transaction_id}}</li><li><strong>Date:</strong> {{transaction_date}}</li></ul><p>If you have any questions, please contact our support team.</p><p>Best regards,<br>{{site_name}} Team</p></div>',
                'sms_body' => 'Balance deducted: {{amount}} {{currency}} from MT5 account {{mt5_login}}. Remaining: {{new_balance}} {{currency}}',
                'shortcodes' => json_encode([
                    'fullname' => 'Full name of the user',
                    'username' => 'Username of the user',
                    'mt5_login' => 'MT5 account login number',
                    'mt5_group' => 'MT5 account group',
                    'amount' => 'Amount deducted',
                    'currency' => 'Currency symbol',
                    'new_balance' => 'Remaining account balance',
                    'reason' => 'Reason for deduction',
                    'transaction_id' => 'Transaction reference ID',
                    'transaction_date' => 'Date and time of transaction',
                    'site_name' => 'Name of the site'
                ]),
                'email_status' => 1,
                'sms_status' => 0
            ],
            [
                'act' => 'ACCOUNT_VERIFICATION_REQUIRED',
                'name' => 'Account Verification Required',
                'subj' => 'Account Verification Required - Action Needed',
                'email_body' => '<div style="padding: 20px; font-family: Arial, sans-serif;"><h3>Account Verification Required</h3><p>Dear {{fullname}},</p><p>To continue using our services, please verify your account by providing the required documents:</p><ul><li>Government-issued ID (passport, driver\'s license, or national ID)</li><li>Proof of address (utility bill or bank statement, not older than 3 months)</li></ul><p>Please upload these documents in your account dashboard under the KYC section.</p><p><strong>Note:</strong> Some features may be limited until verification is complete.</p><p>Best regards,<br>{{site_name}} Team</p></div>',
                'sms_body' => 'Account verification required. Please upload your documents in the KYC section of your dashboard.',
                'shortcodes' => json_encode([
                    'fullname' => 'Full name of the user',
                    'username' => 'Username of the user',
                    'site_name' => 'Name of the site'
                ]),
                'email_status' => 1,
                'sms_status' => 0
            ],
            [
                'act' => 'SECURITY_ALERT',
                'name' => 'Security Alert',
                'subj' => 'Security Alert - Unusual Activity Detected',
                'email_body' => '<div style="padding: 20px; font-family: Arial, sans-serif;"><h3>Security Alert</h3><p>Dear {{fullname}},</p><p>We detected unusual activity on your account:</p><ul><li><strong>Activity:</strong> {{activity_type}}</li><li><strong>Date/Time:</strong> {{activity_date}}</li><li><strong>IP Address:</strong> {{ip_address}}</li><li><strong>Location:</strong> {{location}}</li></ul><p>If this was you, no action is needed. If you don\'t recognize this activity, please:</p><ol><li>Change your password immediately</li><li>Enable two-factor authentication</li><li>Contact our support team</li></ol><p>Best regards,<br>{{site_name}} Security Team</p></div>',
                'sms_body' => 'Security alert: {{activity_type}} detected on your account. If this wasn\'t you, change your password immediately.',
                'shortcodes' => json_encode([
                    'fullname' => 'Full name of the user',
                    'username' => 'Username of the user',
                    'activity_type' => 'Type of suspicious activity',
                    'activity_date' => 'Date and time of activity',
                    'ip_address' => 'IP address of the activity',
                    'location' => 'Geographic location',
                    'site_name' => 'Name of the site'
                ]),
                'email_status' => 1,
                'sms_status' => 0
            ]
        ];

        foreach ($templates as $template) {
            // Check if template already exists
            $existing = NotificationTemplate::where('act', $template['act'])->first();
            if (!$existing) {
                NotificationTemplate::create($template);
            }
        }
    }
}
