<?php
// Direct test of MT5 trading data without Laravel authentication
require_once 'vendor/autoload.php';

// Set up basic Laravel environment
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

// Test the MT5Service directly
use App\Services\MT5Service;
use Illuminate\Support\Facades\Log;

echo "🔧 Direct MT5 Service Test\n";
echo "========================\n\n";

try {
    $mt5Service = new MT5Service();
    
    echo "📡 Testing MT5Service->getOpenPositions(873475)...\n";
    $result = $mt5Service->getOpenPositions(873475);
    
    echo "📊 Result:\n";
    echo json_encode($result, JSON_PRETTY_PRINT) . "\n\n";
    
    if ($result['success']) {
        echo "✅ SUCCESS: Found " . count($result['data']) . " positions\n";
        
        foreach ($result['data'] as $position) {
            echo "  - Symbol: " . ($position['Symbol'] ?? 'N/A') . "\n";
            echo "  - Profit: $" . number_format($position['Profit'] ?? 0, 2) . "\n";
            echo "  - Volume: " . ($position['Volume'] ?? 0) . "\n";
            echo "  - Login: " . ($position['Login'] ?? 'N/A') . "\n";
            echo "  ---\n";
        }
    } else {
        echo "❌ FAILED: " . ($result['message'] ?? 'Unknown error') . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ EXCEPTION: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n🔧 Testing Python script directly...\n";
echo "=====================================\n";

$pythonCommand = 'python python/mt5managertrades.py get_positions --login 873475';
echo "Executing: $pythonCommand\n\n";

$output = [];
$returnCode = 0;
exec($pythonCommand . ' 2>&1', $output, $returnCode);

echo "Return Code: $returnCode\n";
echo "Output:\n" . implode("\n", $output) . "\n";

if ($returnCode === 0) {
    echo "\n✅ Python script executed successfully\n";
} else {
    echo "\n❌ Python script failed\n";
}
?>
