<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🧹 IB DATABASE CLEANUP RECOMMENDATIONS\n";
echo "=====================================\n";

// Get all IB-related tables
$allTables = DB::select('SHOW TABLES');
$ibRelatedTables = [];

foreach ($allTables as $table) {
    $tableName = array_values((array)$table)[0];
    if (stripos($tableName, 'ib') !== false || 
        stripos($tableName, 'partner') !== false || 
        stripos($tableName, 'commission') !== false || 
        stripos($tableName, 'referral') !== false ||
        stripos($tableName, 'form') !== false ||
        stripos($tableName, 'subscriber') !== false) {
        $ibRelatedTables[] = $tableName;
    }
}

echo "📋 CLEANUP ANALYSIS RESULTS:\n";
echo "=====================================\n";

$keepTables = [];
$removeTables = [];
$reviewTables = [];

foreach ($ibRelatedTables as $tableName) {
    try {
        $count = DB::table($tableName)->count();
        $columns = DB::select("DESCRIBE {$tableName}");
        
        // Analyze table importance
        $analysis = analyzeTable($tableName, $count, $columns);
        
        echo "\n📊 TABLE: {$tableName}\n";
        echo "   Records: {$count}\n";
        echo "   Status: {$analysis['status']}\n";
        echo "   Reason: {$analysis['reason']}\n";
        
        if ($analysis['action'] === 'keep') {
            $keepTables[] = $tableName;
        } elseif ($analysis['action'] === 'remove') {
            $removeTables[] = $tableName;
        } else {
            $reviewTables[] = $tableName;
        }
        
    } catch (Exception $e) {
        echo "\n❌ TABLE: {$tableName} - Error: " . $e->getMessage() . "\n";
    }
}

echo "\n\n🎯 CLEANUP RECOMMENDATIONS:\n";
echo "=====================================\n";

echo "\n✅ KEEP THESE TABLES ({count} tables):\n";
foreach ($keepTables as $table) {
    echo "   - {$table}\n";
}

echo "\n❌ SAFE TO REMOVE ({count} tables):\n";
foreach ($removeTables as $table) {
    echo "   - {$table}\n";
}

echo "\n⚠️ REVIEW THESE TABLES ({count} tables):\n";
foreach ($reviewTables as $table) {
    echo "   - {$table}\n";
}

// Generate SQL commands for removal
if (!empty($removeTables)) {
    echo "\n🔧 SQL COMMANDS TO REMOVE UNUSED TABLES:\n";
    echo "=====================================\n";
    echo "-- BACKUP FIRST: mysqldump -u root -p mbf-db > backup_before_cleanup.sql\n\n";
    
    foreach ($removeTables as $table) {
        echo "DROP TABLE IF EXISTS `{$table}`;\n";
    }
    
    echo "\n-- After running these commands, verify your system still works properly\n";
}

// Generate optimization recommendations
echo "\n🚀 OPTIMIZATION RECOMMENDATIONS:\n";
echo "=====================================\n";

$recommendations = [
    'ib_commissions' => 'Add indexes on (user_id, status, created_at) for better performance',
    'users' => 'Consider adding composite index on (partner, ib_status) for IB queries',
    'ib_groups' => 'Ensure status field is indexed for active group queries',
    'ib_levels' => 'Add index on (level, status) for level-based queries'
];

foreach ($recommendations as $table => $recommendation) {
    if (in_array($table, $keepTables)) {
        echo "📈 {$table}: {$recommendation}\n";
    }
}

echo "\n✅ DATABASE CLEANUP ANALYSIS COMPLETE!\n";

/**
 * Analyze table to determine if it should be kept, removed, or reviewed
 */
function analyzeTable($tableName, $recordCount, $columns)
{
    // Essential IB system tables - always keep
    $essentialTables = [
        'users' => 'Core user table with IB functionality',
        'ib_commissions' => 'Commission tracking system',
        'ib_groups' => 'IB group management',
        'ib_levels' => 'Multi-level IB configuration'
    ];
    
    if (isset($essentialTables[$tableName])) {
        return [
            'action' => 'keep',
            'status' => '✅ ESSENTIAL',
            'reason' => $essentialTables[$tableName]
        ];
    }
    
    // Tables with data - analyze usage
    if ($recordCount > 0) {
        if ($recordCount >= 10) {
            return [
                'action' => 'keep',
                'status' => '✅ ACTIVE',
                'reason' => "Has {$recordCount} records - actively used"
            ];
        } else {
            return [
                'action' => 'review',
                'status' => '⚠️ LOW USAGE',
                'reason' => "Only {$recordCount} records - review if still needed"
            ];
        }
    }
    
    // Empty tables - check if they're needed for functionality
    $functionalTables = [
        'ib_resources' => 'IB resource management - may be needed for future features',
        'forms' => 'General form system - may be used by other modules'
    ];
    
    if (isset($functionalTables[$tableName])) {
        return [
            'action' => 'review',
            'status' => '⚠️ EMPTY BUT FUNCTIONAL',
            'reason' => $functionalTables[$tableName]
        ];
    }
    
    // Likely unused tables
    $unusedTables = [
        'subscribers' => 'Newsletter subscription - not IB related',
        'referrals' => 'Legacy referral system - replaced by users.ref_by'
    ];
    
    if (isset($unusedTables[$tableName])) {
        return [
            'action' => 'remove',
            'status' => '❌ UNUSED',
            'reason' => $unusedTables[$tableName]
        ];
    }
    
    // Default for empty unknown tables
    if ($recordCount === 0) {
        return [
            'action' => 'remove',
            'status' => '❌ EMPTY',
            'reason' => 'Empty table with no clear purpose'
        ];
    }
    
    // Default for unknown tables with data
    return [
        'action' => 'review',
        'status' => '⚠️ UNKNOWN',
        'reason' => 'Unknown table purpose - manual review needed'
    ];
}
