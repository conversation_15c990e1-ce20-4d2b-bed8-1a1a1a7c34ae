<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 CHECKING USER IB STATUS\n";
echo "==========================\n";

$userId = 6902;
$user = \App\Models\User::find($userId);

if (!$user) {
    echo "❌ User {$userId} not found\n";
    exit;
}

echo "User Details:\n";
echo "   - ID: {$user->id}\n";
echo "   - Email: {$user->email}\n";
echo "   - Name: {$user->firstname} {$user->lastname}\n";
echo "   - Partner: {$user->partner}\n";
echo "   - IB Status: {$user->ib_status}\n";
echo "   - IB Type: {$user->ib_type}\n";
echo "   - MT5 Login: {$user->mt5_login}\n";

echo "\nIB Status Constants:\n";
echo "   - IB_STATUS_PENDING: " . \App\Models\User::IB_STATUS_PENDING . "\n";
echo "   - IB_STATUS_APPROVED: " . \App\Models\User::IB_STATUS_APPROVED . "\n";
echo "   - IB_STATUS_REJECTED: " . \App\Models\User::IB_STATUS_REJECTED . "\n";

echo "\nIB Detection Results:\n";
echo "   - isIb(): " . ($user->isIb() ? 'true' : 'false') . "\n";
echo "   - isMasterIb(): " . ($user->isMasterIb() ? 'true' : 'false') . "\n";
echo "   - isSubIb(): " . ($user->isSubIb() ? 'true' : 'false') . "\n";

echo "\nCondition Analysis:\n";
echo "   - partner == 1: " . ($user->partner == 1 ? 'true' : 'false') . "\n";
echo "   - ib_status == IB_STATUS_APPROVED: " . ($user->ib_status == \App\Models\User::IB_STATUS_APPROVED ? 'true' : 'false') . "\n";

// Check if user has any IB-related data
$commissions = \App\Models\IbCommission::where('to_ib_user_id', $user->id)->count();
$referrals = \App\Models\User::where('ref_by', $user->id)->count();

echo "\nIB Activity:\n";
echo "   - Commissions earned: {$commissions}\n";
echo "   - Direct referrals: {$referrals}\n";

// Check MT5 group for IB indication
if ($user->mt5_login) {
    $mt5Data = \DB::connection('mbf-dbmt5')->table('mt5_users')->where('Login', $user->mt5_login)->first();
    if ($mt5Data) {
        echo "   - MT5 Group: {$mt5Data->Group}\n";
        echo "   - Is Affiliates Group: " . (stripos($mt5Data->Group, 'Affiliates') !== false ? 'true' : 'false') . "\n";
    }
}

echo "\n✅ USER IB STATUS CHECK COMPLETED!\n";
