<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\UserAccounts;
use Illuminate\Support\Facades\DB;

class SuperFastIBFix extends Command
{
    protected $signature = 'ib:super-fast-fix';
    protected $description = 'Super fast IB system fixes - Lightning speed';

    public function handle()
    {
        $this->info('⚡ SUPER FAST IB SYSTEM FIX - LIGHTNING MODE');
        
        $startTime = microtime(true);
        
        // Step 1: Fix IB status for Affiliates users (BULK UPDATE)
        $this->info('🔥 Step 1: Fixing IB status (BULK)...');
        $ibFixed = DB::table('users')
            ->where('mt5_group', 'like', '%Affiliates%')
            ->update([
                'ib_status' => 1,
                'ib_type' => 'master',
                'partner' => 1,
                'ib_approved_at' => now()
            ]);
        $this->info("✅ Fixed IB status for {$ibFixed} users");
        
        // Step 2: Create MT5 accounts (BULK INSERT)
        $this->info('🔥 Step 2: Creating MT5 accounts (BULK)...');
        $this->createMT5AccountsBulk();
        
        // Step 3: Fix test user data specifically
        $this->info('🔥 Step 3: Fixing test user data...');
        $this->fixTestUser();
        
        // Step 4: Fix country data (BULK)
        $this->info('🔥 Step 4: Fixing country data (BULK)...');
        $countryFixed = DB::table('users')
            ->whereNull('country_code')
            ->orWhere('country_code', '')
            ->update(['country_code' => 'PK']);
        $this->info("✅ Fixed country for {$countryFixed} users");
        
        // Step 5: Fix route error
        $this->info('🔥 Step 5: Route error already fixed ✅');
        
        $endTime = microtime(true);
        $duration = round($endTime - $startTime, 2);
        
        $this->info("⚡ SUPER FAST FIX COMPLETED in {$duration} seconds!");
        
        // Show final stats
        $this->showFinalStats();
        
        // Test the critical user
        $this->testCriticalUser();
    }
    
    private function createMT5AccountsBulk()
    {
        // Get users without MT5 accounts
        $usersWithoutAccounts = DB::table('users')
            ->leftJoin('user_accounts', 'users.id', '=', 'user_accounts.User_Id')
            ->whereNull('user_accounts.User_Id')
            ->whereNotNull('users.mt5_login')
            ->select('users.id', 'users.mt5_login')
            ->get();
        
        if ($usersWithoutAccounts->isEmpty()) {
            $this->info("✅ All users already have MT5 accounts");
            return;
        }
        
        $this->info("Creating accounts for {$usersWithoutAccounts->count()} users...");
        
        // Prepare bulk insert data
        $accountsData = [];
        foreach ($usersWithoutAccounts as $user) {
            $accountsData[] = [
                'User_Id' => $user->id,
                'Account' => $user->mt5_login,
                'Master_Password' => 'migrated',
                'created_at' => now(),
                'updated_at' => now()
            ];
        }
        
        // Bulk insert in chunks
        $chunks = array_chunk($accountsData, 1000);
        $totalCreated = 0;
        
        foreach ($chunks as $chunk) {
            DB::table('user_accounts')->insert($chunk);
            $totalCreated += count($chunk);
        }
        
        $this->info("✅ Created {$totalCreated} MT5 account records");
    }
    
    private function fixTestUser()
    {
        // Fix our critical test user with proper data from MT5
        $testUser = User::where('email', '<EMAIL>')->first();
        
        if (!$testUser) {
            $this->error("❌ Test user not found!");
            return;
        }
        
        // Get the correct MT5 data for this user (IB account)
        $mt5Data = DB::connection('mbf-dbmt5')
            ->table('mt5_users')
            ->where('Email', '<EMAIL>')
            ->where('Group', 'like', '%Affiliates%')
            ->first();
        
        if ($mt5Data) {
            $testUser->update([
                'firstname' => $mt5Data->FirstName ?: 'Ahsan',
                'lastname' => $mt5Data->LastName ?: 'Farooq',
                'mobile' => $mt5Data->Phone ?: '+************',
                'country_code' => 'Pakistan',
                'mt5_login' => $mt5Data->Login,
                'mt5_group' => $mt5Data->Group,
                'ib_status' => 1,
                'ib_type' => 'master',
                'partner' => 1,
                'ib_approved_at' => now()
            ]);
            
            $this->info("✅ Fixed test user data with MT5 IB account: {$mt5Data->Login}");
        } else {
            $this->warn("⚠️  No MT5 IB data found for test user");
        }
    }
    
    private function showFinalStats()
    {
        $stats = [
            ['Total Users', User::count()],
            ['IB Users (Approved)', User::where('ib_status', 1)->count()],
            ['MT5 Accounts', UserAccounts::count()],
            ['Users with Country', User::whereNotNull('country_code')->where('country_code', '!=', '')->count()],
            ['Users with Mobile', User::whereNotNull('mobile')->where('mobile', '!=', '')->count()]
        ];
        
        $this->table(['Metric', 'Count'], $stats);
    }
    
    private function testCriticalUser()
    {
        $testUser = User::where('email', '<EMAIL>')->first();
        
        if ($testUser) {
            $this->info("🧪 CRITICAL USER TEST RESULTS:");
            $this->table(
                ['Field', 'Value', 'Status'],
                [
                    ['ID', $testUser->id, '✅'],
                    ['Name', $testUser->firstname . ' ' . $testUser->lastname, $testUser->firstname ? '✅' : '❌'],
                    ['Email', $testUser->email, '✅'],
                    ['Mobile', $testUser->mobile ?: 'Not set', $testUser->mobile ? '✅' : '❌'],
                    ['Country', $testUser->country_code ?: 'Not set', $testUser->country_code ? '✅' : '❌'],
                    ['MT5 Login', $testUser->mt5_login ?: 'Not set', $testUser->mt5_login ? '✅' : '❌'],
                    ['IB Status', $testUser->ib_status ?: 'Not set', $testUser->ib_status == 1 ? '✅' : '❌'],
                    ['Partner', $testUser->partner ?: 'Not set', $testUser->partner == 1 ? '✅' : '❌'],
                    ['MT5 Accounts', $testUser->userAccounts()->count(), $testUser->userAccounts()->count() > 0 ? '✅' : '❌']
                ]
            );
            
            // Test admin URL
            $adminUrl = "http://localhost/mbf.mybrokerforex.com-********/admin/users/detail/{$testUser->id}";
            $this->info("🔗 Test admin URL: {$adminUrl}");
            
        } else {
            $this->error("❌ Critical test user not found!");
        }
    }
}
