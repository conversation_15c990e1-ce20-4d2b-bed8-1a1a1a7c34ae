<?php
  // Get user's support tickets using existing system
  $userTickets = \App\Models\SupportTicket::where('user_id', $user->id)
    ->orderBy('id', 'desc')
    ->paginate(15);
?>

<div class="row mb--20">
  <div class="col-lg-12">
    <div class="card mt-30">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
          <i class="fas fa-ticket-alt text-primary me-2"></i><?php echo app('translator')->get('Support Tickets'); ?>
        </h5>
        <div class="d-flex gap-2">
          <span class="badge badge--info"><?php echo e($userTickets->total()); ?> Total</span>
          <span class="badge badge--warning"><?php echo e($userTickets->where('status', \App\Constants\Status::TICKET_OPEN)->count()); ?> Open</span>
        </div>
      </div>

      <div class="card-body p-0">
        <div class="table-responsive--sm table-responsive">
          <table class="table table--light style--two">
            <thead>
              <tr>
                <th><?php echo app('translator')->get('Subject'); ?></th>
                <th><?php echo app('translator')->get('Status'); ?></th>
                <th><?php echo app('translator')->get('Priority'); ?></th>
                <th><?php echo app('translator')->get('Last Reply'); ?></th>
                <th><?php echo app('translator')->get('Created'); ?></th>
                <th><?php echo app('translator')->get('Action'); ?></th>
              </tr>
            </thead>
            <tbody>
              <?php $__empty_1 = true; $__currentLoopData = $userTickets; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ticket): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr>
                  <td>
                    <strong>[<?php echo app('translator')->get('Ticket'); ?>#<?php echo e($ticket->ticket); ?>]</strong>
                    <br><?php echo e(strLimit($ticket->subject, 40)); ?>

                  </td>
                  <td>
                    <?php if($ticket->status == \App\Constants\Status::TICKET_OPEN): ?>
                      <span class="badge badge--primary"><?php echo app('translator')->get('Open'); ?></span>
                    <?php elseif($ticket->status == \App\Constants\Status::TICKET_ANSWER): ?>
                      <span class="badge badge--success"><?php echo app('translator')->get('Answered'); ?></span>
                    <?php elseif($ticket->status == \App\Constants\Status::TICKET_REPLY): ?>
                      <span class="badge badge--warning"><?php echo app('translator')->get('Customer Reply'); ?></span>
                    <?php elseif($ticket->status == \App\Constants\Status::TICKET_CLOSE): ?>
                      <span class="badge badge--dark"><?php echo app('translator')->get('Closed'); ?></span>
                    <?php endif; ?>
                  </td>
                  <td>
                    <?php if($ticket->priority == 1): ?>
                      <span class="badge badge--dark"><?php echo app('translator')->get('Low'); ?></span>
                    <?php elseif($ticket->priority == 2): ?>
                      <span class="badge badge--success"><?php echo app('translator')->get('Medium'); ?></span>
                    <?php elseif($ticket->priority == 3): ?>
                      <span class="badge badge--warning"><?php echo app('translator')->get('High'); ?></span>
                    <?php endif; ?>
                  </td>
                  <td><?php echo e(diffForHumans($ticket->last_reply)); ?></td>
                  <td><?php echo e(showDateTime($ticket->created_at)); ?></td>
                  <td>
                    <div class="button--group">
                      <a href="<?php echo e(route('admin.ticket.view', $ticket->id)); ?>" class="btn btn--primary btn--shadow btn--sm">
                        <i class="las la-desktop"></i> <?php echo app('translator')->get('Details'); ?>
                      </a>
                      <?php if($ticket->status != \App\Constants\Status::TICKET_CLOSE): ?>
                        <button type="button" class="btn btn--danger btn--shadow btn--sm confirmationBtn"
                                data-question="<?php echo app('translator')->get('Are you sure to close this ticket?'); ?>"
                                data-action="<?php echo e(route('admin.ticket.close', $ticket->id)); ?>">
                          <i class="las la-times-circle"></i> <?php echo app('translator')->get('Close'); ?>
                        </button>
                      <?php endif; ?>
                    </div>
                  </td>
                </tr>
              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                  <td colspan="6" class="text-center text-muted py-4">
                    <i class="las la-ticket-alt" style="font-size: 2rem;"></i>
                    <br><?php echo app('translator')->get('No support tickets found for this user'); ?>
                  </td>
                </tr>
              <?php endif; ?>
            </tbody>
          </table>
        </div>
      </div>

      <?php if($userTickets->hasPages()): ?>
        <div class="card-footer">
          <?php echo e(paginateLinks($userTickets)); ?>

        </div>
      <?php endif; ?>
    </div>
  </div>
</div>

<?php if (isset($component)) { $__componentOriginalbd5922df145d522b37bf664b524be380 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbd5922df145d522b37bf664b524be380 = $attributes; } ?>
<?php $component = App\View\Components\ConfirmationModal::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('confirmation-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\ConfirmationModal::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbd5922df145d522b37bf664b524be380)): ?>
<?php $attributes = $__attributesOriginalbd5922df145d522b37bf664b524be380; ?>
<?php unset($__attributesOriginalbd5922df145d522b37bf664b524be380); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbd5922df145d522b37bf664b524be380)): ?>
<?php $component = $__componentOriginalbd5922df145d522b37bf664b524be380; ?>
<?php unset($__componentOriginalbd5922df145d522b37bf664b524be380); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\mbf.mybrokerforex.com-31052025\resources\views/components/user-detail/tickets.blade.php ENDPATH**/ ?>