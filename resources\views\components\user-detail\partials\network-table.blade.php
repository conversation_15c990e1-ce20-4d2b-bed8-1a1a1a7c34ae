{{-- Network Table Rows Component --}}
@foreach($users as $networkUser)
<tr>
    <td>
        <div style="padding-left: {{ ($networkUser['level'] - 1) * 20 }}px;">
            <div class="d-flex align-items-center">
                <div class="user-avatar me-2">
                    @if($networkUser['user']->isIb())
                        <i class="las la-user-tie text-{{ $networkUser['user']->ib_type == 'master' ? 'primary' : 'success' }}"></i>
                    @else
                        <i class="las la-user text-muted"></i>
                    @endif
                </div>
                <div>
                    <strong>{{ $networkUser['user']->fullname }}</strong>
                    <br>
                    <small class="text-muted">{{ $networkUser['user']->username }}</small>
                    <br>
                    <small class="text-info">{{ $networkUser['user']->email }}</small>
                </div>
            </div>
        </div>
    </td>
    
    <td>
        <span class="badge badge--info">Level {{ $networkUser['level'] }}</span>
    </td>
    
    <td>
        @if($networkUser['user']->isIb())
            @if($networkUser['user']->ib_status == 'approved')
                <span class="badge badge--success">Approved</span>
            @elseif($networkUser['user']->ib_status == 'pending')
                <span class="badge badge--warning">Pending</span>
            @else
                <span class="badge badge--danger">Rejected</span>
            @endif
        @else
            <span class="badge badge--secondary">Not IB</span>
        @endif
    </td>
    
    <td>
        @if($networkUser['user']->isIb())
            <span class="badge badge--{{ $networkUser['user']->ib_type == 'master' ? 'primary' : 'info' }}">
                {{ ucfirst($networkUser['user']->ib_type) }} IB
            </span>
        @else
            <span class="text-muted">Client</span>
        @endif
    </td>
    
    <td>
        @if($networkUser['user']->ibGroup)
            <span class="badge badge--warning">{{ $networkUser['user']->ibGroup->name }}</span>
            <br>
            <small class="text-muted">{{ $networkUser['user']->ibGroup->commission_multiplier }}x multiplier</small>
        @else
            <span class="text-muted">No Group</span>
        @endif
    </td>
    
    <td>
        <div>
            <strong>{{ $networkUser['user']->created_at->format('M d, Y') }}</strong>
            <br>
            <small class="text-muted">{{ $networkUser['user']->created_at->diffForHumans() }}</small>
            @if($networkUser['user']->isIb() && $networkUser['user']->ib_approved_at)
                <br>
                <small class="text-success">
                    IB since {{ $networkUser['user']->ib_approved_at->format('M d, Y') }}
                </small>
            @endif
        </div>
    </td>
    
    <td>
        @if($networkUser['user']->isIb())
            <div class="commission-info">
                <strong class="text--success">
                    {{ showAmount($networkUser['user']->ibCommissionsEarned->sum('commission_amount')) }}
                </strong>
                <br>
                <small class="text-muted">
                    {{ $networkUser['user']->ibCommissionsEarned->count() }} trades
                </small>
                @php
                    $pendingCommissions = $networkUser['user']->ibCommissionsEarned->where('status', 'pending')->sum('commission_amount');
                @endphp
                @if($pendingCommissions > 0)
                    <br>
                    <small class="text-warning">
                        {{ showAmount($pendingCommissions) }} pending
                    </small>
                @endif
            </div>
        @else
            <span class="text-muted">N/A</span>
        @endif
    </td>
    
    <td>
        <div class="dropdown">
            <button class="btn btn-sm btn-outline--primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                Actions
            </button>
            <ul class="dropdown-menu">
                <li>
                    <a class="dropdown-item" href="{{ route('admin.users.detail', $networkUser['user']->id) }}">
                        <i class="las la-eye"></i> View Details
                    </a>
                </li>
                
                @if($networkUser['user']->isIb())
                    <li>
                        <a class="dropdown-item" href="{{ route('admin.ib.hierarchy', $networkUser['user']->id) }}">
                            <i class="las la-sitemap"></i> View IB Hierarchy
                        </a>
                    </li>
                    
                    <li>
                        <a class="dropdown-item" href="#" onclick="viewCommissions({{ $networkUser['user']->id }})">
                            <i class="las la-dollar-sign"></i> View Commissions
                        </a>
                    </li>
                @endif
                
                <li>
                    <a class="dropdown-item" href="#" onclick="sendMessage({{ $networkUser['user']->id }})">
                        <i class="las la-envelope"></i> Send Message
                    </a>
                </li>
                
                @if(!$networkUser['user']->isIb() && $networkUser['user']->ib_status != 'pending')
                    <li>
                        <a class="dropdown-item" href="#" onclick="promoteToIb({{ $networkUser['user']->id }})">
                            <i class="las la-user-plus"></i> Promote to IB
                        </a>
                    </li>
                @endif
            </ul>
        </div>
    </td>
</tr>
@endforeach

@if(count($users) == 0)
<tr>
    <td colspan="8" class="text-center py-4">
        <i class="las la-users text-muted" style="font-size: 3rem;"></i>
        <br>
        <span class="text-muted">No network members found</span>
    </td>
</tr>
@endif

<script>
function viewCommissions(userId) {
    // Implementation for viewing user commissions
    window.open(`/admin/users/${userId}/commissions`, '_blank');
}

function sendMessage(userId) {
    // Implementation for sending message to user
    alert('Send message functionality - User ID: ' + userId);
}

function promoteToIb(userId) {
    // Implementation for promoting user to IB
    if (confirm('Are you sure you want to promote this user to IB?')) {
        // Add promotion logic here
        alert('Promote to IB functionality - User ID: ' + userId);
    }
}
</script>

<style>
.commission-info {
    min-width: 120px;
}

.user-avatar {
    width: 30px;
    text-align: center;
}

.dropdown-menu {
    min-width: 180px;
}

.dropdown-item {
    padding: 8px 16px;
    font-size: 14px;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

.dropdown-item i {
    width: 16px;
    margin-right: 8px;
}

@media (max-width: 768px) {
    .table-responsive {
        font-size: 12px;
    }
    
    .commission-info {
        min-width: auto;
    }
    
    .dropdown-menu {
        min-width: 150px;
    }
}
</style>
