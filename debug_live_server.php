<?php
/**
 * Live Server Diagnostic Script
 * Place this file in your project root and access via browser
 * URL: https://yourdomain.com/debug_live_server.php
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Live Server Diagnostic Report</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .section{margin:20px 0;padding:15px;border:1px solid #ddd;} .success{color:green;} .error{color:red;} .warning{color:orange;}</style>";

// 1. PHP Version Check
echo "<div class='section'>";
echo "<h2>1. PHP Environment</h2>";
echo "<strong>PHP Version:</strong> " . PHP_VERSION . "<br>";
echo "<strong>PHP SAPI:</strong> " . php_sapi_name() . "<br>";
echo "<strong>Server Software:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "<br>";

// Check if PHP version is compatible
$phpVersion = PHP_VERSION;
if (version_compare($phpVersion, '8.2.0', '>=')) {
    echo "<span class='success'>✓ PHP version is compatible</span><br>";
} else {
    echo "<span class='error'>✗ PHP version too old. Requires 8.2+</span><br>";
}
echo "</div>";

// 2. Laravel Environment Check
echo "<div class='section'>";
echo "<h2>2. Laravel Environment</h2>";

// Check if .env exists
if (file_exists('.env')) {
    echo "<span class='success'>✓ .env file exists</span><br>";
    
    // Load .env and check key settings
    $envContent = file_get_contents('.env');
    $envLines = explode("\n", $envContent);
    $envVars = [];
    
    foreach ($envLines as $line) {
        if (strpos($line, '=') !== false && !str_starts_with(trim($line), '#')) {
            list($key, $value) = explode('=', $line, 2);
            $envVars[trim($key)] = trim($value);
        }
    }
    
    // Check critical environment variables
    $criticalVars = ['APP_KEY', 'APP_URL', 'DB_HOST', 'DB_DATABASE', 'DB_USERNAME'];
    foreach ($criticalVars as $var) {
        if (isset($envVars[$var]) && !empty($envVars[$var])) {
            echo "<span class='success'>✓ {$var} is set</span><br>";
        } else {
            echo "<span class='error'>✗ {$var} is missing or empty</span><br>";
        }
    }
    
    // Show APP_ENV and APP_DEBUG
    echo "<strong>APP_ENV:</strong> " . ($envVars['APP_ENV'] ?? 'not set') . "<br>";
    echo "<strong>APP_DEBUG:</strong> " . ($envVars['APP_DEBUG'] ?? 'not set') . "<br>";
    echo "<strong>APP_URL:</strong> " . ($envVars['APP_URL'] ?? 'not set') . "<br>";
    
} else {
    echo "<span class='error'>✗ .env file not found</span><br>";
}
echo "</div>";

// 3. File Permissions Check
echo "<div class='section'>";
echo "<h2>3. File Permissions</h2>";

$directories = [
    'storage',
    'storage/logs',
    'storage/framework',
    'storage/framework/cache',
    'storage/framework/sessions',
    'storage/framework/views',
    'bootstrap/cache'
];

foreach ($directories as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "<span class='success'>✓ {$dir} is writable</span><br>";
        } else {
            echo "<span class='error'>✗ {$dir} is not writable</span><br>";
        }
    } else {
        echo "<span class='error'>✗ {$dir} directory not found</span><br>";
    }
}
echo "</div>";

// 4. Database Connection Test
echo "<div class='section'>";
echo "<h2>4. Database Connection</h2>";

if (file_exists('.env')) {
    try {
        // Try to connect to database
        $host = $envVars['DB_HOST'] ?? 'localhost';
        $database = $envVars['DB_DATABASE'] ?? '';
        $username = $envVars['DB_USERNAME'] ?? '';
        $password = $envVars['DB_PASSWORD'] ?? '';
        
        if (!empty($database) && !empty($username)) {
            $dsn = "mysql:host={$host};dbname={$database}";
            $pdo = new PDO($dsn, $username, $password);
            echo "<span class='success'>✓ Database connection successful</span><br>";
            
            // Test a simple query
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM users LIMIT 1");
            if ($stmt) {
                $result = $stmt->fetch();
                echo "<span class='success'>✓ Database query test successful</span><br>";
            }
        } else {
            echo "<span class='error'>✗ Database credentials not configured</span><br>";
        }
    } catch (Exception $e) {
        echo "<span class='error'>✗ Database connection failed: " . $e->getMessage() . "</span><br>";
    }
} else {
    echo "<span class='error'>✗ Cannot test database - .env not found</span><br>";
}
echo "</div>";

// 5. PHP Extensions Check
echo "<div class='section'>";
echo "<h2>5. Required PHP Extensions</h2>";

$requiredExtensions = [
    'pdo',
    'pdo_mysql',
    'mbstring',
    'openssl',
    'tokenizer',
    'xml',
    'ctype',
    'json',
    'bcmath',
    'curl',
    'fileinfo',
    'gd',
    'zip'
];

foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<span class='success'>✓ {$ext}</span><br>";
    } else {
        echo "<span class='error'>✗ {$ext} (missing)</span><br>";
    }
}
echo "</div>";

// 6. URL and Route Testing
echo "<div class='section'>";
echo "<h2>6. URL and Route Testing</h2>";

$currentUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
echo "<strong>Current URL:</strong> {$currentUrl}<br>";

// Test if mod_rewrite is working
if (isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], 'index.php') === false) {
    echo "<span class='success'>✓ URL rewriting appears to be working</span><br>";
} else {
    echo "<span class='warning'>⚠ URL rewriting may not be configured properly</span><br>";
}

// Check .htaccess
if (file_exists('.htaccess')) {
    echo "<span class='success'>✓ .htaccess file exists</span><br>";
} else {
    echo "<span class='error'>✗ .htaccess file missing</span><br>";
}
echo "</div>";

// 7. Python and MT5 Integration Check
echo "<div class='section'>";
echo "<h2>7. Python and MT5 Integration</h2>";

// Check if Python is available
$pythonPath = $envVars['PYTHON_EXE'] ?? 'python';
$pythonScript = $envVars['PYTHON_SCRIPT'] ?? '';

echo "<strong>Python Path:</strong> {$pythonPath}<br>";
echo "<strong>Python Script:</strong> {$pythonScript}<br>";

// Test Python availability
$output = shell_exec("{$pythonPath} --version 2>&1");
if ($output && strpos(strtolower($output), 'python') !== false) {
    echo "<span class='success'>✓ Python is available: " . trim($output) . "</span><br>";
} else {
    echo "<span class='error'>✗ Python not found or not accessible</span><br>";
}

// Check if MT5 script exists
if (!empty($pythonScript) && file_exists($pythonScript)) {
    echo "<span class='success'>✓ MT5 Python script exists</span><br>";
} else {
    echo "<span class='error'>✗ MT5 Python script not found</span><br>";
}
echo "</div>";

// 8. API Endpoint Tests
echo "<div class='section'>";
echo "<h2>8. API Endpoint Tests</h2>";

echo "<p>Test these API endpoints manually:</p>";
echo "<ul>";
echo "<li><a href='/api/test' target='_blank'>/api/test</a> (if exists)</li>";
echo "<li><a href='/user/dashboard' target='_blank'>/user/dashboard</a></li>";
echo "<li><a href='/admin/dashboard' target='_blank'>/admin/dashboard</a></li>";
echo "</ul>";

echo "<p><strong>AJAX Test:</strong></p>";
echo "<button onclick='testAjax()'>Test AJAX Call</button>";
echo "<div id='ajax-result'></div>";

echo "<script>
function testAjax() {
    fetch('/user/dashboard', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        }
    })
    .then(response => response.text())
    .then(data => {
        document.getElementById('ajax-result').innerHTML = '<pre>' + data.substring(0, 500) + '...</pre>';
    })
    .catch(error => {
        document.getElementById('ajax-result').innerHTML = '<span class=\"error\">AJAX Error: ' + error + '</span>';
    });
}
</script>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>9. Server Information</h2>";
echo "<strong>Document Root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "<br>";
echo "<strong>Script Filename:</strong> " . ($_SERVER['SCRIPT_FILENAME'] ?? 'Unknown') . "<br>";
echo "<strong>Server Admin:</strong> " . ($_SERVER['SERVER_ADMIN'] ?? 'Unknown') . "<br>";
echo "<strong>Server Port:</strong> " . ($_SERVER['SERVER_PORT'] ?? 'Unknown') . "<br>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>10. Live Server Deployment Checklist</h2>";
echo "<ol>";
echo "<li><strong>Update .env file with live server paths:</strong>";
echo "<pre>";
echo "APP_ENV=production\n";
echo "APP_DEBUG=false\n";
echo "APP_URL=https://yourdomain.com\n\n";
echo "# Python Configuration for Windows Server\n";
echo "PYTHON_EXE=C:\\Python\\python.exe\n";
echo "PYTHON_SCRIPT=C:\\path\\to\\your\\project\\python\\mt5manager.py\n\n";
echo "# MT5 Configuration\n";
echo "MT5_SERVER=**************\n";
echo "MT5_PORT=443\n";
echo "MT5_MANAGER_LOGIN=877966\n";
echo "MT5_MANAGER_PASSWORD=ElVi!tL7\n";
echo "MT5_BUILD=484\n";
echo "MT5_AGENT=WebAPI\n";
echo "</pre></li>";

echo "<li><strong>Set correct file permissions in Plesk:</strong>";
echo "<ul>";
echo "<li>storage/ folder: 755 (recursive)</li>";
echo "<li>bootstrap/cache/ folder: 755</li>";
echo "<li>python/mt5manager.py: 755 (executable)</li>";
echo "</ul></li>";

echo "<li><strong>Enable PHP functions in Plesk:</strong>";
echo "<ul>";
echo "<li>Go to PHP Settings in Plesk</li>";
echo "<li>Remove 'exec', 'shell_exec', 'system' from disabled_functions</li>";
echo "<li>Or contact hosting provider to enable these functions</li>";
echo "</ul></li>";

echo "<li><strong>Install Python on Windows Server:</strong>";
echo "<ul>";
echo "<li>Download Python 3.8+ from python.org</li>";
echo "<li>Install with 'Add to PATH' option checked</li>";
echo "<li>Install required packages: pip install MetaTrader5</li>";
echo "</ul></li>";

echo "<li><strong>Clear all caches:</strong>";
echo "<pre>";
echo "php artisan cache:clear\n";
echo "php artisan config:clear\n";
echo "php artisan route:clear\n";
echo "php artisan view:clear\n";
echo "composer dump-autoload\n";
echo "</pre></li>";

echo "<li><strong>Test the diagnostic scripts:</strong>";
echo "<ul>";
echo "<li>Upload debug_live_server.php to your domain root</li>";
echo "<li>Upload debug_mt5_api.php to your domain root</li>";
echo "<li>Visit https://yourdomain.com/debug_live_server.php</li>";
echo "<li>Visit https://yourdomain.com/debug_mt5_api.php</li>";
echo "</ul></li>";
echo "</ol>";
echo "</div>";
?>
