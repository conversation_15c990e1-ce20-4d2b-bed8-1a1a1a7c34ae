{"__meta": {"id": "01JZ2Q76C27EPF6RZPNCE9KDDE", "datetime": "2025-07-01 10:06:16", "utime": **********.964411, "method": "POST", "uri": "/mbf.mybrokerforex.com-********/admin/notification/template/update/1", "ip": "::1"}, "messages": {"count": 40, "messages": [{"message": "[10:06:16] LOG.info: === TEMPLATE UPDATE DEBUG START ===", "message_html": null, "is_string": false, "label": "info", "time": **********.886719, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: Template ID: 1", "message_html": null, "is_string": false, "label": "info", "time": **********.887137, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: Request Method: POST", "message_html": null, "is_string": false, "label": "info", "time": **********.88749, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: Request URL: https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/update/1", "message_html": null, "is_string": false, "label": "info", "time": **********.887881, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "message_html": null, "is_string": false, "label": "info", "time": **********.888241, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: Server Environment: WINNT - PHP 8.2.12", "message_html": null, "is_string": false, "label": "info", "time": **********.888579, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: Content Type: multipart/form-data; boundary=----WebKitFormBoundaryQ2i2uo0U59RHVDOg", "message_html": null, "is_string": false, "label": "info", "time": **********.888918, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: Content Length: 18361", "message_html": null, "is_string": false, "label": "info", "time": **********.889242, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: Raw POST data keys: _token, template_id, subject, email_status, sms_status, sms_body, email_body_encoded, email_body, email_body_final", "message_html": null, "is_string": false, "label": "info", "time": **********.889609, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: Email body field exists: YES", "message_html": null, "is_string": false, "label": "info", "time": **********.889959, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: Email body final field exists: YES", "message_html": null, "is_string": false, "label": "info", "time": **********.890348, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: ✅ Validation passed", "message_html": null, "is_string": false, "label": "info", "time": **********.912819, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: ✅ Template found - Current subject: Your Account has been Credited", "message_html": null, "is_string": false, "label": "info", "time": **********.920336, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: ✅ Template found - Current email_body length: 5106", "message_html": null, "is_string": false, "label": "info", "time": **********.920767, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: ✅ Subject updated to: Your Account has been Credited", "message_html": null, "is_string": false, "label": "info", "time": **********.9212, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: === ENHANCED EMAIL BODY PROCESSING WITH BASE64 SUPPORT ===", "message_html": null, "is_string": false, "label": "info", "time": **********.921529, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: 📦 Base64 encoded content detected", "message_html": null, "is_string": false, "label": "info", "time": **********.921886, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: ✅ Base64 content decoded successfully, length: 5107", "message_html": null, "is_string": false, "label": "info", "time": **********.922339, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: 📝 Decoded content preview: \n\n\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Balance Added Successfully</title>\n\n\n    <!-- Full Width Container -->\n    <table wi", "message_html": null, "is_string": false, "label": "info", "time": **********.922683, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: Email body source: email_body_final", "message_html": null, "is_string": false, "label": "info", "time": **********.923025, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: Email body length: 5107", "message_html": null, "is_string": false, "label": "info", "time": **********.923342, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: Email body preview (first 200 chars): \n\n\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Balance Added Successfully</title>\n\n\n    <!-- Full Width Container -->\n    <table wi", "message_html": null, "is_string": false, "label": "info", "time": **********.923677, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: After minimal Windows cleanup - Email body length: 5107", "message_html": null, "is_string": false, "label": "info", "time": **********.924039, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: Template 1: Using content directly from editor with minimal cleanup", "message_html": null, "is_string": false, "label": "info", "time": **********.924371, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: === FINAL CONTENT READY FOR SAVE ===", "message_html": null, "is_string": false, "label": "info", "time": **********.924717, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: Final email body length: 5107", "message_html": null, "is_string": false, "label": "info", "time": **********.925062, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: Final email body preview (first 300 chars): \n\n\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Balance Added Successfully</title>\n\n\n    <!-- Full Width Container -->\n    <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: #f4f4f4;\">\n        <t", "message_html": null, "is_string": false, "label": "info", "time": **********.925353, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: Template 1: Skipping all corruption detection and complex processing to prevent issues", "message_html": null, "is_string": false, "label": "info", "time": **********.925666, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: === DATABASE SAVE OPERATION DEBUG ===", "message_html": null, "is_string": false, "label": "info", "time": **********.925975, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: Before save - Template email_body length: 5106", "message_html": null, "is_string": false, "label": "info", "time": **********.926316, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: Before save - New email_body length: 5107", "message_html": null, "is_string": false, "label": "info", "time": **********.92664, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: Before save - Template dirty: []", "message_html": null, "is_string": false, "label": "info", "time": **********.926961, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: After setting fields - Template dirty: {\"email_body\":\"\\n\\n\\n    <meta charset=\\\"UTF-8\\\">\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\">\\n    <title>Balance Added Successfully<\\/title>\\n\\n\\n    <!-- Full Width Container -->\\n    <table width=\\\"100%\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" border=\\\"0\\\" style=\\\"background-color: #f4f4f4;\\\">\\n        <tbody><tr>\\n            <td align=\\\"center\\\">\\n                <!-- Email Container -->\\n                <table width=\\\"600\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" border=\\\"0\\\" style=\\\"background-color: #ffffff; box-shadow: 0 0 10px rgba(0,0,0,0.1);\\\">\\n\\n                    <!-- Header Banner - Full Width -->\\n                    <tbody><tr>\\n                        <td width=\\\"100%\\\" style=\\\"background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); padding: 20px 0; text-align: center;\\\">\\n                            <h1 style=\\\"margin: 0; font-size: 24px; font-weight: bold; color: #ffffff;\\\">Balance Notification<\\/h1>\\n                        <\\/td>\\n                    <\\/tr>\\n\\n                    <!-- Logo Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: #ffffff; text-align: center; padding: 20px; border-bottom: 2px solid #e9ecef;\\\">\\n                            <img src=\\\"https:\\/\\/mbf.mybrokerforex.com\\/assets\\/images\\/logoIcon\\/logo.png\\\" alt=\\\"MBFX\\\" style=\\\"height: 60px; width: auto; display: block; margin: 0 auto;\\\" onerror=\\\"this.style.display='none'\\\">\\n                        <\\/td>\\n                    <\\/tr>\\n\\n                    <!-- Title Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: #ffffff; padding: 30px 40px 20px; text-align: center;\\\">\\n                            <h2 style=\\\"margin: 0; color: #dc3545; font-size: 28px; font-weight: bold;\\\">Balance Added Successfullys<\\/h2>\\n                        <\\/td>\\n                    <\\/tr>\\n\\n                    <!-- Description Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: #ffffff; padding: 0 40px 20px; text-align: center;\\\">\\n                            <p style=\\\"margin: 0; color: #6c757d; font-size: 16px;\\\">Your account balance has been updated with a new deposit.<\\/p>\\n                        <\\/td>\\n                    <\\/tr>\\n\\n                    <!-- Main Content Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: #ffffff; padding: 20px 40px; color: #333333;\\\">\\n                            <p>Dear {{fullname}},<\\/p><p>We are pleased to inform you that {{amount}} {{currency}} has been added to your account.<\\/p><ul><li><strong>Amount:<\\/strong> {{amount}} {{currency}}<\\/li><li><strong>New Balance:<\\/strong> {{new_balance}} {{currency}}<\\/li><li><strong>Transaction ID:<\\/strong> {{transaction_id}}<\\/li><li><strong>Date:<\\/strong> {{transaction_date}}<\\/li><\\/ul><p>The funds are now available in your account.<\\/p>\\n                        <\\/td>\\n                    <\\/tr>\\n\\n                    <!-- Regards Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: #ffffff; padding: 30px 40px; border-top: 1px solid #e9ecef;\\\">\\n                            <p style=\\\"margin: 0 0 10px 0; font-size: 16px;\\\">Best regards,<br>\\n                            <strong>MBFX Team<\\/strong><\\/p>\\n                            <p style=\\\"font-size: 12px; color: #6c757d; margin: 15px 0 0 0;\\\">\\n                                If you have any questions, please contact our support team.\\n                            <\\/p>\\n                        <\\/td>\\n                    <\\/tr>\\n\\n                    <!-- Footer Section - Full Width -->\\n                    <tr>\\n                        <td width=\\\"100%\\\" style=\\\"background: linear-gradient(135deg, #343a40 0%, #23272b 100%); color: #ffffff; padding: 30px 40px; text-align: center;\\\">\\n                            <p style=\\\"margin: 0 0 10px 0; font-size: 14px;\\\"><strong>MBFX<\\/strong> - Professional Trading Platform<\\/p>\\n                            <p style=\\\"margin: 0 0 10px 0; font-size: 14px;\\\">\\n                                <a href=\\\"{{site_url}}\\/user\\/profile\\/setting\\\" style=\\\"color: #ffffff; text-decoration: none;\\\">Account Settings<\\/a> |\\n                                <a href=\\\"{{site_url}}\\/contact\\\" style=\\\"color: #ffffff; text-decoration: none;\\\">Contact Support<\\/a> |\\n                                <a href=\\\"{{site_url}}\\/policy\\/privacy-policy\\/99\\\" style=\\\"color: #ffffff; text-decoration: none;\\\">Privacy Policy<\\/a>\\n                            <\\/p>\\n                            <p style=\\\"margin: 15px 0 0 0; font-size: 14px;\\\">\\n                                \\u00a9 2025 MBFX. All rights reserved.\\n                            <\\/p>\\n                            <p style=\\\"font-size: 10px; color: #999999; margin: 10px 0 0 0;\\\">\\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\\n                                <a href=\\\"{{site_url}}\\/user\\/profile\\/setting\\\" style=\\\"color: #999999; text-decoration: none;\\\">update your preferences<\\/a>.\\n                            <\\/p>\\n                        <\\/td>\\n                    <\\/tr>\\n\\n                <\\/tbody><\\/table>\\n            <\\/td>\\n        <\\/tr>\\n    <\\/tbody><\\/table>\\n\\n\"}", "message_html": null, "is_string": false, "label": "info", "time": **********.928211, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: After setting fields - Template email_body length: 5107", "message_html": null, "is_string": false, "label": "info", "time": **********.928615, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: Save operation result: SUCCESS", "message_html": null, "is_string": false, "label": "info", "time": **********.939732, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: After refresh - Template email_body length: 5107", "message_html": null, "is_string": false, "label": "info", "time": **********.947044, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: After refresh - Content matches: YES", "message_html": null, "is_string": false, "label": "info", "time": **********.947438, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: ✅ Template 1: Database operation completed", "message_html": null, "is_string": false, "label": "info", "time": **********.947819, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: === TEMPLATE UPDATE DEBUG END ===", "message_html": null, "is_string": false, "label": "info", "time": **********.948169, "xdebug_link": null, "collector": "log"}, {"message": "[10:06:16] LOG.info: 📤 Returning AJAX response", "message_html": null, "is_string": false, "label": "info", "time": **********.948514, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.240133, "end": **********.964536, "duration": 0.724402904510498, "duration_str": "724ms", "measures": [{"label": "Booting", "start": **********.240133, "relative_start": 0, "end": **********.726958, "relative_end": **********.726958, "duration": 0.*****************, "duration_str": "487ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.726988, "relative_start": 0.*****************, "end": **********.964539, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "238ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.781842, "relative_start": 0.****************, "end": **********.791615, "relative_end": **********.791615, "duration": 0.009773015975952148, "duration_str": "9.77ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.956677, "relative_start": 0.****************, "end": **********.957644, "relative_end": **********.957644, "duration": 0.0009670257568359375, "duration_str": "967μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.12", "Environment": "production", "Debug Mode": "Enabled", "URL": "localhost/mbf.mybrokerforex.com-********", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 5, "nb_statements": 4, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02933, "accumulated_duration_str": "29.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "vendor/beyondcode/laravel-query-detector/src/QueryDetector.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetector.php", "line": 25}, {"index": 10, "namespace": null, "name": "vendor/beyondcode/laravel-query-detector/src/QueryDetectorMiddleware.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetectorMiddleware.php", "line": 30}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21}], "start": **********.780268, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "QueryDetector.php:25", "source": {"index": 9, "namespace": null, "name": "vendor/beyondcode/laravel-query-detector/src/QueryDetector.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetector.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fvendor%2Fbeyondcode%2Flaravel-query-detector%2Fsrc%2FQueryDetector.php&line=25", "ajax": false, "filename": "QueryDetector.php", "line": "25"}, "connection": "mbf-db", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Middleware\\RedirectIfNotAdmin.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.850612, "duration": 0.02357, "duration_str": "23.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mbf-db", "explain": null, "start_percent": 0, "width_percent": 80.361}, {"sql": "select * from `notification_templates` where `notification_templates`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/NotificationController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\NotificationController.php", "line": 122}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.914102, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "NotificationController.php:122", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/NotificationController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\NotificationController.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FNotificationController.php&line=122", "ajax": false, "filename": "NotificationController.php", "line": "122"}, "connection": "mbf-db", "explain": null, "start_percent": 80.361, "width_percent": 3.307}, {"sql": "update `notification_templates` set `email_body` = '\\n\\n\\n    <meta charset=\\\"UTF-8\\\">\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\">\\n    <title>Balance Added Successfully</title>\\n\\n\\n    <!-- Full Width Container -->\\n    <table width=\\\"100%\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" border=\\\"0\\\" style=\\\"background-color: #f4f4f4;\\\">\\n        <tbody><tr>\\n            <td align=\\\"center\\\">\\n                <!-- Email Container -->\\n                <table width=\\\"600\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" border=\\\"0\\\" style=\\\"background-color: #ffffff; box-shadow: 0 0 10px rgba(0,0,0,0.1);\\\">\\n\\n                    <!-- Header Banner - Full Width -->\\n                    <tbody><tr>\\n                        <td width=\\\"100%\\\" style=\\\"background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); padding: 20px 0; text-align: center;\\\">\\n                            <h1 style=\\\"margin: 0; font-size: 24px; font-weight: bold; color: #ffffff;\\\">Balance Notification</h1>\\n                        </td>\\n                    </tr>\\n\\n                    <!-- Logo Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: #ffffff; text-align: center; padding: 20px; border-bottom: 2px solid #e9ecef;\\\">\\n                            <img src=\\\"https://mbf.mybrokerforex.com/assets/images/logoIcon/logo.png\\\" alt=\\\"MBFX\\\" style=\\\"height: 60px; width: auto; display: block; margin: 0 auto;\\\" onerror=\\\"this.style.display=\\'none\\'\\\">\\n                        </td>\\n                    </tr>\\n\\n                    <!-- Title Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: #ffffff; padding: 30px 40px 20px; text-align: center;\\\">\\n                            <h2 style=\\\"margin: 0; color: #dc3545; font-size: 28px; font-weight: bold;\\\">Balance Added Successfullys</h2>\\n                        </td>\\n                    </tr>\\n\\n                    <!-- Description Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: #ffffff; padding: 0 40px 20px; text-align: center;\\\">\\n                            <p style=\\\"margin: 0; color: #6c757d; font-size: 16px;\\\">Your account balance has been updated with a new deposit.</p>\\n                        </td>\\n                    </tr>\\n\\n                    <!-- Main Content Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: #ffffff; padding: 20px 40px; color: #333333;\\\">\\n                            <p>Dear {{fullname}},</p><p>We are pleased to inform you that {{amount}} {{currency}} has been added to your account.</p><ul><li><strong>Amount:</strong> {{amount}} {{currency}}</li><li><strong>New Balance:</strong> {{new_balance}} {{currency}}</li><li><strong>Transaction ID:</strong> {{transaction_id}}</li><li><strong>Date:</strong> {{transaction_date}}</li></ul><p>The funds are now available in your account.</p>\\n                        </td>\\n                    </tr>\\n\\n                    <!-- Regards Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: #ffffff; padding: 30px 40px; border-top: 1px solid #e9ecef;\\\">\\n                            <p style=\\\"margin: 0 0 10px 0; font-size: 16px;\\\">Best regards,<br>\\n                            <strong>MBFX Team</strong></p>\\n                            <p style=\\\"font-size: 12px; color: #6c757d; margin: 15px 0 0 0;\\\">\\n                                If you have any questions, please contact our support team.\\n                            </p>\\n                        </td>\\n                    </tr>\\n\\n                    <!-- Footer Section - Full Width -->\\n                    <tr>\\n                        <td width=\\\"100%\\\" style=\\\"background: linear-gradient(135deg, #343a40 0%, #23272b 100%); color: #ffffff; padding: 30px 40px; text-align: center;\\\">\\n                            <p style=\\\"margin: 0 0 10px 0; font-size: 14px;\\\"><strong>MBFX</strong> - Professional Trading Platform</p>\\n                            <p style=\\\"margin: 0 0 10px 0; font-size: 14px;\\\">\\n                                <a href=\\\"{{site_url}}/user/profile/setting\\\" style=\\\"color: #ffffff; text-decoration: none;\\\">Account Settings</a> |\\n                                <a href=\\\"{{site_url}}/contact\\\" style=\\\"color: #ffffff; text-decoration: none;\\\">Contact Support</a> |\\n                                <a href=\\\"{{site_url}}/policy/privacy-policy/99\\\" style=\\\"color: #ffffff; text-decoration: none;\\\">Privacy Policy</a>\\n                            </p>\\n                            <p style=\\\"margin: 15px 0 0 0; font-size: 14px;\\\">\\n                                © 2025 MBFX. All rights reserved.\\n                            </p>\\n                            <p style=\\\"font-size: 10px; color: #999999; margin: 10px 0 0 0;\\\">\\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\\n                                <a href=\\\"{{site_url}}/user/profile/setting\\\" style=\\\"color: #999999; text-decoration: none;\\\">update your preferences</a>.\\n                            </p>\\n                        </td>\\n                    </tr>\\n\\n                </tbody></table>\\n            </td>\\n        </tr>\\n    </tbody></table>\\n\\n', `notification_templates`.`updated_at` = '2025-07-01 10:06:16' where `id` = 1", "type": "query", "params": [], "bindings": ["\n\n\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Balance Added Successfully</title>\n\n\n    <!-- Full Width Container -->\n    <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: #f4f4f4;\">\n        <tbody><tr>\n            <td align=\"center\">\n                <!-- Email Container -->\n                <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: #ffffff; box-shadow: 0 0 10px rgba(0,0,0,0.1);\">\n\n                    <!-- Header Banner - Full Width -->\n                    <tbody><tr>\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); padding: 20px 0; text-align: center;\">\n                            <h1 style=\"margin: 0; font-size: 24px; font-weight: bold; color: #ffffff;\">Balance Notification</h1>\n                        </td>\n                    </tr>\n\n                    <!-- Logo Section -->\n                    <tr>\n                        <td style=\"background-color: #ffffff; text-align: center; padding: 20px; border-bottom: 2px solid #e9ecef;\">\n                            <img src=\"https://mbf.mybrokerforex.com/assets/images/logoIcon/logo.png\" alt=\"MBFX\" style=\"height: 60px; width: auto; display: block; margin: 0 auto;\" onerror=\"this.style.display='none'\">\n                        </td>\n                    </tr>\n\n                    <!-- Title Section -->\n                    <tr>\n                        <td style=\"background-color: #ffffff; padding: 30px 40px 20px; text-align: center;\">\n                            <h2 style=\"margin: 0; color: #dc3545; font-size: 28px; font-weight: bold;\">Balance Added Successfullys</h2>\n                        </td>\n                    </tr>\n\n                    <!-- Description Section -->\n                    <tr>\n                        <td style=\"background-color: #ffffff; padding: 0 40px 20px; text-align: center;\">\n                            <p style=\"margin: 0; color: #6c757d; font-size: 16px;\">Your account balance has been updated with a new deposit.</p>\n                        </td>\n                    </tr>\n\n                    <!-- Main Content Section -->\n                    <tr>\n                        <td style=\"background-color: #ffffff; padding: 20px 40px; color: #333333;\">\n                            <p>Dear {{fullname}},</p><p>We are pleased to inform you that {{amount}} {{currency}} has been added to your account.</p><ul><li><strong>Amount:</strong> {{amount}} {{currency}}</li><li><strong>New Balance:</strong> {{new_balance}} {{currency}}</li><li><strong>Transaction ID:</strong> {{transaction_id}}</li><li><strong>Date:</strong> {{transaction_date}}</li></ul><p>The funds are now available in your account.</p>\n                        </td>\n                    </tr>\n\n                    <!-- Regards Section -->\n                    <tr>\n                        <td style=\"background-color: #ffffff; padding: 30px 40px; border-top: 1px solid #e9ecef;\">\n                            <p style=\"margin: 0 0 10px 0; font-size: 16px;\">Best regards,<br>\n                            <strong>MBFX Team</strong></p>\n                            <p style=\"font-size: 12px; color: #6c757d; margin: 15px 0 0 0;\">\n                                If you have any questions, please contact our support team.\n                            </p>\n                        </td>\n                    </tr>\n\n                    <!-- Footer Section - Full Width -->\n                    <tr>\n                        <td width=\"100%\" style=\"background: linear-gradient(135deg, #343a40 0%, #23272b 100%); color: #ffffff; padding: 30px 40px; text-align: center;\">\n                            <p style=\"margin: 0 0 10px 0; font-size: 14px;\"><strong>MBFX</strong> - Professional Trading Platform</p>\n                            <p style=\"margin: 0 0 10px 0; font-size: 14px;\">\n                                <a href=\"{{site_url}}/user/profile/setting\" style=\"color: #ffffff; text-decoration: none;\">Account Settings</a> |\n                                <a href=\"{{site_url}}/contact\" style=\"color: #ffffff; text-decoration: none;\">Contact Support</a> |\n                                <a href=\"{{site_url}}/policy/privacy-policy/99\" style=\"color: #ffffff; text-decoration: none;\">Privacy Policy</a>\n                            </p>\n                            <p style=\"margin: 15px 0 0 0; font-size: 14px;\">\n                                © 2025 MBFX. All rights reserved.\n                            </p>\n                            <p style=\"font-size: 10px; color: #999999; margin: 10px 0 0 0;\">\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\n                                <a href=\"{{site_url}}/user/profile/setting\" style=\"color: #999999; text-decoration: none;\">update your preferences</a>.\n                            </p>\n                        </td>\n                    </tr>\n\n                </tbody></table>\n            </td>\n        </tr>\n    </tbody></table>\n\n", "2025-07-01 10:06:16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/NotificationController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\NotificationController.php", "line": 215}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.929559, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "NotificationController.php:215", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/NotificationController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\NotificationController.php", "line": 215}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FNotificationController.php&line=215", "ajax": false, "filename": "NotificationController.php", "line": "215"}, "connection": "mbf-db", "explain": null, "start_percent": 83.669, "width_percent": 13.126}, {"sql": "select * from `notification_templates` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/NotificationController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\NotificationController.php", "line": 219}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.940235, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "NotificationController.php:219", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/NotificationController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\NotificationController.php", "line": 219}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FNotificationController.php&line=219", "ajax": false, "filename": "NotificationController.php", "line": "219"}, "connection": "mbf-db", "explain": null, "start_percent": 96.795, "width_percent": 3.205}]}, "models": {"data": {"App\\Models\\NotificationTemplate": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FModels%2FNotificationTemplate.php&line=1", "ajax": false, "filename": "NotificationTemplate.php", "line": "?"}}, "App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/update/1", "action_name": "admin.setting.notification.template.update", "controller_action": "App\\Http\\Controllers\\Admin\\NotificationController@templateUpdate", "uri": "POST admin/notification/template/update/{id}", "controller": "App\\Http\\Controllers\\Admin\\NotificationController@templateUpdate<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FNotificationController.php&line=98\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin/notification", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FNotificationController.php&line=98\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/NotificationController.php:98-259</a>", "middleware": "web, admin", "duration": "725ms", "peak_memory": "30MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1458961044 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1458961044\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6wydTXTQETk9uM050OalbDmHEZPTqQskjidNxWTA</span>\"\n  \"<span class=sf-dump-key>template_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>subject</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Your Account has been Credited</span>\"\n  \"<span class=sf-dump-key>email_status</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>sms_status</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>sms_body</span>\" => \"<span class=sf-dump-str title=\"167 characters\">{{amount}} {{wallet_currency}}  credited in your account. Your Current Balance {{post_balance}} {{wallet_currency}} . Transaction: #{{trx}}. Admin note is &quot;{{remark}}&quot;</span>\"\n  \"<span class=sf-dump-key>email_body_encoded</span>\" => \"<span class=sf-dump-str title=\"6812 characters\">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</span>\"\n  \"<span class=sf-dump-key>email_body</span>\" => \"\"\"\n    <span class=sf-dump-str title=\"5177 characters\">&lt;meta charset=&quot;UTF-8&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">    &lt;title&gt;Balance Added Successfully&lt;/title&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">    &lt;!-- Full Width Container --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">    &lt;table width=&quot;100%&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; style=&quot;background-color: #f4f4f4;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">        &lt;tbody&gt;&lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">            &lt;td align=&quot;center&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                &lt;!-- Email Container --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; style=&quot;background-color: #ffffff; box-shadow: 0 0 10px rgba(0,0,0,0.1);&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;!-- Header Banner - Full Width --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;tbody&gt;&lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                        &lt;td width=&quot;100%&quot; style=&quot;background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); padding: 20px 0; text-align: center;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;h1 style=&quot;margin: 0; font-size: 24px; font-weight: bold; color: #ffffff;&quot;&gt;Balance Notification&lt;/h1&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;!-- Logo Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                        &lt;td style=&quot;background-color: #ffffff; text-align: center; padding: 20px; border-bottom: 2px solid #e9ecef;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;img src=&quot;https://mbf.mybrokerforex.com/assets/images/logoIcon/logo.png&quot; alt=&quot;MBFX&quot; style=&quot;height: 60px; width: auto; display: block; margin: 0 auto;&quot; onerror=&quot;this.style.display=&#039;none&#039;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;!-- Title Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                        &lt;td style=&quot;background-color: #ffffff; padding: 30px 40px 20px; text-align: center;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;h2 style=&quot;margin: 0; color: #dc3545; font-size: 28px; font-weight: bold;&quot;&gt;Balance Added Successfullys&lt;/h2&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;!-- Description Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                        &lt;td style=&quot;background-color: #ffffff; padding: 0 40px 20px; text-align: center;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;p style=&quot;margin: 0; color: #6c757d; font-size: 16px;&quot;&gt;Your account balance has been updated with a new deposit.&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;!-- Main Content Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                        &lt;td style=&quot;background-color: #ffffff; padding: 20px 40px; color: #333333;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;p&gt;Dear {{fullname}},&lt;/p&gt;&lt;p&gt;We are pleased to inform you that {{amount}} {{currency}} has been added to your account.&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Amount:&lt;/strong&gt; {{amount}} {{currency}}&lt;/li&gt;&lt;li&gt;&lt;strong&gt;New Balance:&lt;/strong&gt; {{new_balance}} {{currency}}&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Transaction ID:&lt;/strong&gt; {{transaction_id}}&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Date:&lt;/strong&gt; {{transaction_date}}&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;The funds are now available in your account.&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;!-- Regards Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                        &lt;td style=&quot;background-color: #ffffff; padding: 30px 40px; border-top: 1px solid #e9ecef;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;p style=&quot;margin: 0 0 10px 0; font-size: 16px;&quot;&gt;Best regards,&lt;br&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;strong&gt;MBFX Team&lt;/strong&gt;&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;p style=&quot;font-size: 12px; color: #6c757d; margin: 15px 0 0 0;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                                If you have any questions, please contact our support team.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;!-- Footer Section - Full Width --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                        &lt;td width=&quot;100%&quot; style=&quot;background: linear-gradient(135deg, #343a40 0%, #23272b 100%); color: #ffffff; padding: 30px 40px; text-align: center;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;p style=&quot;margin: 0 0 10px 0; font-size: 14px;&quot;&gt;&lt;strong&gt;MBFX&lt;/strong&gt; - Professional Trading Platform&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;p style=&quot;margin: 0 0 10px 0; font-size: 14px;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                                &lt;a href=&quot;{{site_url}}/user/profile/setting&quot; style=&quot;color: #ffffff; text-decoration: none;&quot;&gt;Account Settings&lt;/a&gt; |<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                                &lt;a href=&quot;{{site_url}}/contact&quot; style=&quot;color: #ffffff; text-decoration: none;&quot;&gt;Contact Support&lt;/a&gt; |<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                                &lt;a href=&quot;{{site_url}}/policy/privacy-policy/99&quot; style=&quot;color: #ffffff; text-decoration: none;&quot;&gt;Privacy Policy&lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;p style=&quot;margin: 15px 0 0 0; font-size: 14px;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                                &#169; 2025 MBFX. All rights reserved.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;p style=&quot;font-size: 10px; color: #999999; margin: 10px 0 0 0;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                                This email was sent to {{email}}. If you no longer wish to receive these emails,<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                                &lt;a href=&quot;{{site_url}}/user/profile/setting&quot; style=&quot;color: #999999; text-decoration: none;&quot;&gt;update your preferences&lt;/a&gt;.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                &lt;/tbody&gt;&lt;/table&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">            &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">        &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">    &lt;/tbody&gt;&lt;/table&gt;</span>\n    \"\"\"\n  \"<span class=sf-dump-key>email_body_final</span>\" => \"\"\"\n    <span class=sf-dump-str title=\"5177 characters\">&lt;meta charset=&quot;UTF-8&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">    &lt;title&gt;Balance Added Successfully&lt;/title&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">    &lt;!-- Full Width Container --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">    &lt;table width=&quot;100%&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; style=&quot;background-color: #f4f4f4;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">        &lt;tbody&gt;&lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">            &lt;td align=&quot;center&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                &lt;!-- Email Container --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; style=&quot;background-color: #ffffff; box-shadow: 0 0 10px rgba(0,0,0,0.1);&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;!-- Header Banner - Full Width --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;tbody&gt;&lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                        &lt;td width=&quot;100%&quot; style=&quot;background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); padding: 20px 0; text-align: center;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;h1 style=&quot;margin: 0; font-size: 24px; font-weight: bold; color: #ffffff;&quot;&gt;Balance Notification&lt;/h1&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;!-- Logo Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                        &lt;td style=&quot;background-color: #ffffff; text-align: center; padding: 20px; border-bottom: 2px solid #e9ecef;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;img src=&quot;https://mbf.mybrokerforex.com/assets/images/logoIcon/logo.png&quot; alt=&quot;MBFX&quot; style=&quot;height: 60px; width: auto; display: block; margin: 0 auto;&quot; onerror=&quot;this.style.display=&#039;none&#039;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;!-- Title Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                        &lt;td style=&quot;background-color: #ffffff; padding: 30px 40px 20px; text-align: center;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;h2 style=&quot;margin: 0; color: #dc3545; font-size: 28px; font-weight: bold;&quot;&gt;Balance Added Successfullys&lt;/h2&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;!-- Description Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                        &lt;td style=&quot;background-color: #ffffff; padding: 0 40px 20px; text-align: center;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;p style=&quot;margin: 0; color: #6c757d; font-size: 16px;&quot;&gt;Your account balance has been updated with a new deposit.&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;!-- Main Content Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                        &lt;td style=&quot;background-color: #ffffff; padding: 20px 40px; color: #333333;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;p&gt;Dear {{fullname}},&lt;/p&gt;&lt;p&gt;We are pleased to inform you that {{amount}} {{currency}} has been added to your account.&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Amount:&lt;/strong&gt; {{amount}} {{currency}}&lt;/li&gt;&lt;li&gt;&lt;strong&gt;New Balance:&lt;/strong&gt; {{new_balance}} {{currency}}&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Transaction ID:&lt;/strong&gt; {{transaction_id}}&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Date:&lt;/strong&gt; {{transaction_date}}&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;The funds are now available in your account.&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;!-- Regards Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                        &lt;td style=&quot;background-color: #ffffff; padding: 30px 40px; border-top: 1px solid #e9ecef;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;p style=&quot;margin: 0 0 10px 0; font-size: 16px;&quot;&gt;Best regards,&lt;br&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;strong&gt;MBFX Team&lt;/strong&gt;&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;p style=&quot;font-size: 12px; color: #6c757d; margin: 15px 0 0 0;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                                If you have any questions, please contact our support team.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;!-- Footer Section - Full Width --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                        &lt;td width=&quot;100%&quot; style=&quot;background: linear-gradient(135deg, #343a40 0%, #23272b 100%); color: #ffffff; padding: 30px 40px; text-align: center;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;p style=&quot;margin: 0 0 10px 0; font-size: 14px;&quot;&gt;&lt;strong&gt;MBFX&lt;/strong&gt; - Professional Trading Platform&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;p style=&quot;margin: 0 0 10px 0; font-size: 14px;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                                &lt;a href=&quot;{{site_url}}/user/profile/setting&quot; style=&quot;color: #ffffff; text-decoration: none;&quot;&gt;Account Settings&lt;/a&gt; |<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                                &lt;a href=&quot;{{site_url}}/contact&quot; style=&quot;color: #ffffff; text-decoration: none;&quot;&gt;Contact Support&lt;/a&gt; |<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                                &lt;a href=&quot;{{site_url}}/policy/privacy-policy/99&quot; style=&quot;color: #ffffff; text-decoration: none;&quot;&gt;Privacy Policy&lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;p style=&quot;margin: 15px 0 0 0; font-size: 14px;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                                &#169; 2025 MBFX. All rights reserved.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;p style=&quot;font-size: 10px; color: #999999; margin: 10px 0 0 0;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                                This email was sent to {{email}}. If you no longer wish to receive these emails,<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                                &lt;a href=&quot;{{site_url}}/user/profile/setting&quot; style=&quot;color: #999999; text-decoration: none;&quot;&gt;update your preferences&lt;/a&gt;.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">                &lt;/tbody&gt;&lt;/table&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">            &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">        &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"5177 characters\">    &lt;/tbody&gt;&lt;/table&gt;</span>\n    \"\"\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1028746189 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">18361</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryQ2i2uo0U59RHVDOg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">https://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"83 characters\">https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/edit/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">en-US,en;q=0.9,ms-MY;q=0.8,ms;q=0.7,zh-CN;q=0.6,zh;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjA2dThhbGhCR3RHaFBmM3dMNElhaHc9PSIsInZhbHVlIjoiNjl1NUltR0JleWlpYW9KWkt2VWRYSUZ4b2xzT1dNVThSQXdWUG9IMjI0dVE4bHpiQ2lGbTRJTU1DSVJDcjRudXZ4cWJCYkxiMVpFYWd2ZUhhUFJLTFBXektsMGt4bjNQbDE1dUVqRTI2Vzg1RHlsbklneGdtVGJuOHFWcllNKy8iLCJtYWMiOiI3NDM1NGI4MDQzNzJhNDhkNWUzNjgxMGJhZjRkN2RlOTQ0YjU2OGQ3MjdjOWM1ZjI0NjUyZWE3YTlhMmQ4ZWQwIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkI3WHUwUWtpYjN3Tm9uKzk4VVB0enc9PSIsInZhbHVlIjoiT1V3RmJ5b0U2Y2loZ0h2b0FOTnVqRmVUd0FNeEU1b2pGTS9mdGpyMW1ZVnNsamtseGE3QU5KZG1NN2dMNmV6THk2RzBVR2kyWERwY0lIeWo4MjFCNGhYZUdxK1hER01EYW1uOFVUbm0rVUNxNDYwaVBYYkM2alhucnNQRGEzRkciLCJtYWMiOiIxNzE1Mjc3MjYxYjZkNzZhYjkzYzhmMGY1OGI3MzhhN2ZmOWVjZjU3MjdkM2UyNWRjMzcwYmUzMzk2ZGU0NDYyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1028746189\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1656339033 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6wydTXTQETk9uM050OalbDmHEZPTqQskjidNxWTA</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pKwSOwBxhh7a0b12K8rA1re0Q1SyO815L9Vl6qQN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1656339033\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1994911456 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 10:06:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1994911456\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1027262937 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6wydTXTQETk9uM050OalbDmHEZPTqQskjidNxWTA</span>\"\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"83 characters\">https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/edit/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>flasher::envelopes</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>53630</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1027262937\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/update/1", "action_name": "admin.setting.notification.template.update", "controller_action": "App\\Http\\Controllers\\Admin\\NotificationController@templateUpdate"}, "badge": null}}