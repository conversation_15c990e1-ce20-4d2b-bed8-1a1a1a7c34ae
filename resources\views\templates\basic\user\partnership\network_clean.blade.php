@extends($activeTemplate . 'layouts.master')
@section('content')

<!-- Include OrgChart.js CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/orgchart@4.0.1/dist/css/jquery.orgchart.min.css" />

<div class="container-fluid">
    <div class="row justify-content-center mt-4">
        <div class="col-xl-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">@lang('Partnership Network - Clean Test')</h5>
                    <div class="text-muted">Testing basic network tree functionality</div>
                </div>
                <div class="card-body">
                    <!-- Debug Info -->
                    <div class="alert alert-info mb-3">
                        <h6>Debug Information:</h6>
                        <div id="debugInfo">Loading debug info...</div>
                    </div>

                    <!-- Network Container -->
                    <div id="networkContainer" style="min-height: 400px; border: 2px dashed #ddd; padding: 20px;">
                        <div class="text-center py-5">
                            <h4>Network Tree Loading...</h4>
                            <div class="spinner-border text-primary" role="status"></div>
                            <div class="mt-2">
                                <small class="text-muted">If this doesn't change, check console for errors</small>
                            </div>
                        </div>
                    </div>

                    <!-- Test Buttons -->
                    <div class="mt-3">
                        <button class="btn btn-primary" onclick="testOrgChart()">Test OrgChart</button>
                        <button class="btn btn-secondary" onclick="testFallback()">Test Fallback</button>
                        <button class="btn btn-info" onclick="showDebugData()">Show Debug Data</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{{-- Pass data to JavaScript safely --}}
<script>
// Step 1: Pass PHP data to JavaScript
window.phpData = {
    networkData: @json($networkData ?? []),
    user: {
        id: {{ $user->id ?? 0 }},
        name: @json(($user->firstname ?? '') . ' ' . ($user->lastname ?? '')),
        mt5_login: @json($user->mt5_login ?? 'N/A')
    }
};
console.log('PHP Data passed:', window.phpData);
</script>

@push('script')
<script src="https://cdn.jsdelivr.net/npm/orgchart@4.0.1/dist/js/jquery.orgchart.min.js"></script>
<script>
$(document).ready(function() {
    console.log('Document ready - starting network initialization');

    // Step 2: Create BACKEND_DATA from window.phpData
    window.BACKEND_DATA = {
        networkData: window.phpData.networkData || [],
        user: window.phpData.user || {},
        routes: {
            getReferrals: @json(route('user.partnership.get-referrals')),
            network: @json(route('user.partnership.network'))
        }
    };

    console.log('BACKEND_DATA created:', window.BACKEND_DATA);

    // Update debug info
    updateDebugInfo();

    // Step 3: Test OrgChart.js availability
    if (typeof $.fn.orgchart !== 'undefined') {
        console.log('OrgChart.js loaded successfully');
        initializeSimpleTree();
    } else {
        console.log('OrgChart.js not available, showing fallback');
        showSimpleFallback();
    }
});

function updateDebugInfo() {
    const debugHtml = `
        <strong>✅ JavaScript Status:</strong><br>
        • PHP Data: ${typeof window.phpData !== 'undefined' ? '✅ Loaded' : '❌ Missing'}<br>
        • BACKEND_DATA: ${typeof window.BACKEND_DATA !== 'undefined' ? '✅ Created' : '❌ Missing'}<br>
        • jQuery: ${typeof $ !== 'undefined' ? '✅ Available' : '❌ Missing'}<br>
        • OrgChart.js: ${typeof $.fn.orgchart !== 'undefined' ? '✅ Available' : '❌ Missing'}<br>
        • User: ${window.BACKEND_DATA?.user?.name || 'Unknown'}<br>
        • User ID: ${window.BACKEND_DATA?.user?.id || 'Unknown'}
    `;
    $('#debugInfo').html(debugHtml);
}

function initializeSimpleTree() {
    console.log('Initializing simple tree...');

    const treeData = {
        id: window.BACKEND_DATA.user.id || 1,
        name: window.BACKEND_DATA.user.name || 'Test User',
        title: 'Network Root',
        children: [
            {
                id: 2,
                name: 'Test Child 1',
                title: 'Sub IB'
            },
            {
                id: 3,
                name: 'Test Child 2',
                title: 'Client'
            }
        ]
    };

    try {
        // Clear the container first
        $('#networkContainer').empty();

        // Initialize OrgChart
        $('#networkContainer').orgchart({
            data: treeData,
            nodeContent: 'name',
            direction: 't2b',
            visibleLevel: 2
        });

        console.log('Simple tree initialized successfully');

        // Add success message
        setTimeout(() => {
            $('#networkContainer').prepend('<div class="alert alert-success">✅ OrgChart.js tree loaded successfully!</div>');
        }, 500);

    } catch (error) {
        console.error('OrgChart error:', error);
        showSimpleFallback();
    }
}

function showSimpleFallback() {
    console.log('Showing simple fallback tree...');

    const fallbackHtml = `
        <div class="alert alert-warning">⚠️ OrgChart.js fallback mode</div>
        <div class="text-center">
            <h4>Network Tree (Fallback Mode)</h4>
            <div class="row justify-content-center">
                <div class="col-md-4">
                    <div class="card border-primary">
                        <div class="card-body text-center">
                            <h6 class="card-title">${window.BACKEND_DATA.user.name || 'Test User'}</h6>
                            <p class="card-text">
                                <small class="text-muted">MT5: ${window.BACKEND_DATA.user.mt5_login || 'N/A'}</small><br>
                                <small class="text-muted">ID: ${window.BACKEND_DATA.user.id || 'N/A'}</small>
                            </p>
                            <span class="badge bg-primary">Master IB</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row justify-content-center mt-3">
                <div class="col-md-3">
                    <div class="card border-success">
                        <div class="card-body text-center">
                            <h6 class="card-title">Test Child 1</h6>
                            <span class="badge bg-success">Sub IB</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-secondary">
                        <div class="card-body text-center">
                            <h6 class="card-title">Test Child 2</h6>
                            <span class="badge bg-secondary">Client</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    $('#networkContainer').html(fallbackHtml);
    console.log('Simple fallback displayed');
}

// Test functions for buttons
function testOrgChart() {
    console.log('Testing OrgChart manually...');
    initializeSimpleTree();
}

function testFallback() {
    console.log('Testing fallback manually...');
    showSimpleFallback();
}

function showDebugData() {
    console.log('=== DEBUG DATA ===');
    console.log('window.phpData:', window.phpData);
    console.log('window.BACKEND_DATA:', window.BACKEND_DATA);
    console.log('jQuery available:', typeof $ !== 'undefined');
    console.log('OrgChart available:', typeof $.fn.orgchart !== 'undefined');

    alert('Debug data logged to console. Check F12 → Console tab');
}
</script>
@endpush

@endsection
