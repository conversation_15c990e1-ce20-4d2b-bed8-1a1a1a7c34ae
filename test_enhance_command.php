<?php
/**
 * TEST ENHANCE COMMAND FUNCTIONALITY
 * This script tests if the email:enhance-templates command works properly
 */

// Include Laravel bootstrap
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🔧 TESTING EMAIL ENHANCE COMMAND\n";
echo "=================================\n\n";

// 1. CHECK COMMAND REGISTRATION
echo "1️⃣ COMMAND REGISTRATION CHECK\n";
echo "==============================\n";

try {
    // Get all registered commands
    $commands = \Artisan::all();
    
    $enhanceCommandExists = isset($commands['email:enhance-templates']);
    echo "✅ email:enhance-templates command: " . ($enhanceCommandExists ? 'REGISTERED' : 'NOT FOUND') . "\n";
    
    if ($enhanceCommandExists) {
        $command = $commands['email:enhance-templates'];
        echo "✅ Command class: " . get_class($command) . "\n";
        echo "✅ Command description: " . $command->getDescription() . "\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Command registration check error: " . $e->getMessage() . "\n";
}

echo "\n";

// 2. CHECK TEMPLATE COUNT
echo "2️⃣ TEMPLATE COUNT CHECK\n";
echo "=======================\n";

try {
    $templateCount = \App\Models\NotificationTemplate::count();
    echo "✅ Total templates in database: {$templateCount}\n";
    
    if ($templateCount > 0) {
        $sampleTemplate = \App\Models\NotificationTemplate::first();
        echo "✅ Sample template: {$sampleTemplate->name} (ID: {$sampleTemplate->id})\n";
        echo "✅ Sample template type: {$sampleTemplate->act}\n";
        echo "✅ Sample content length: " . strlen($sampleTemplate->email_body) . " characters\n";
    } else {
        echo "⚠️  No templates found in database\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Template count check error: " . $e->getMessage() . "\n";
}

echo "\n";

// 3. TEST COMMAND EXECUTION (PREVIEW MODE)
echo "3️⃣ COMMAND EXECUTION TEST (PREVIEW)\n";
echo "====================================\n";

try {
    echo "📊 Testing command in preview mode...\n";
    
    // Capture output
    ob_start();
    
    // Run the command in preview mode
    $exitCode = \Artisan::call('email:enhance-templates', [
        '--preview' => true,
        '--template' => [1] // Test with first template only
    ]);
    
    $output = ob_get_clean();
    
    echo "✅ Command exit code: {$exitCode}\n";
    echo "✅ Command executed successfully\n";
    
    // Show first 500 characters of output
    $outputPreview = substr($output, 0, 500);
    echo "✅ Command output preview:\n";
    echo "---\n{$outputPreview}...\n---\n";
    
} catch (\Exception $e) {
    ob_end_clean();
    echo "❌ Command execution error: " . $e->getMessage() . "\n";
    echo "❌ Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n";

// 4. CHECK DEPENDENCIES
echo "4️⃣ DEPENDENCY CHECK\n";
echo "===================\n";

try {
    // Check if required classes exist
    $requiredClasses = [
        'App\Models\NotificationTemplate',
        'App\Console\Commands\EnhanceEmailTemplates'
    ];
    
    foreach ($requiredClasses as $class) {
        $exists = class_exists($class);
        echo "✅ {$class}: " . ($exists ? 'EXISTS' : 'MISSING') . "\n";
    }
    
    // Check if command file exists
    $commandFile = 'app/Console/Commands/EnhanceEmailTemplates.php';
    $fileExists = file_exists($commandFile);
    echo "✅ Command file: " . ($fileExists ? 'EXISTS' : 'MISSING') . "\n";
    
    if ($fileExists) {
        $fileSize = filesize($commandFile);
        echo "✅ Command file size: {$fileSize} bytes\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Dependency check error: " . $e->getMessage() . "\n";
}

echo "\n";

// 5. RECOMMENDATIONS
echo "5️⃣ RECOMMENDATIONS\n";
echo "==================\n";

if ($enhanceCommandExists && $templateCount > 0) {
    echo "✅ GOOD: Command is ready to use\n";
    echo "📋 To enhance all templates:\n";
    echo "   php artisan email:enhance-templates\n\n";
    echo "📋 To preview changes first:\n";
    echo "   php artisan email:enhance-templates --preview\n\n";
    echo "📋 To enhance specific template:\n";
    echo "   php artisan email:enhance-templates --template=1\n\n";
} else {
    echo "❌ ISSUES DETECTED:\n";
    if (!$enhanceCommandExists) {
        echo "   - Command not registered properly\n";
    }
    if ($templateCount === 0) {
        echo "   - No templates in database\n";
    }
}

echo "🔧 COMMAND TEST COMPLETED\n";

?>
