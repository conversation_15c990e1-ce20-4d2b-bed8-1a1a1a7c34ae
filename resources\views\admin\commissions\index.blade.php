@extends('admin.layouts.app')

@section('panel')
<div class="row">
    <!-- Statistics Cards -->
    <div class="col-lg-3 col-sm-6 mb-30">
        <div class="widget-two style--two box--shadow2 b-radius--5 bg--info">
            <div class="widget-two__icon b-radius--5 bg--info">
                <i class="las la-dollar-sign"></i>
            </div>
            <div class="widget-two__content">
                <h3 class="text-white">${{ showAmount($stats['total_amount_pending']) }}</h3>
                <p class="text-white">@lang('Pending Amount')</p>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-sm-6 mb-30">
        <div class="widget-two style--two box--shadow2 b-radius--5 bg--success">
            <div class="widget-two__icon b-radius--5 bg--success">
                <i class="las la-check-circle"></i>
            </div>
            <div class="widget-two__content">
                <h3 class="text-white">${{ showAmount($stats['total_amount_approved']) }}</h3>
                <p class="text-white">@lang('Approved Amount')</p>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-sm-6 mb-30">
        <div class="widget-two style--two box--shadow2 b-radius--5 bg--warning">
            <div class="widget-two__icon b-radius--5 bg--warning">
                <i class="las la-clock"></i>
            </div>
            <div class="widget-two__content">
                <h3 class="text-white">{{ $stats['total_pending'] }}</h3>
                <p class="text-white">@lang('Pending Commissions')</p>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-sm-6 mb-30">
        <div class="widget-two style--two box--shadow2 b-radius--5 bg--primary">
            <div class="widget-two__icon b-radius--5 bg--primary">
                <i class="las la-calendar-day"></i>
            </div>
            <div class="widget-two__content">
                <h3 class="text-white">{{ $stats['today_commissions'] }}</h3>
                <p class="text-white">@lang('Today\'s Commissions')</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">
        <div class="card b-radius--10">
            <div class="card-header">
                <div class="row g-3 align-items-center">
                    <div class="col-md-6">
                        <h5 class="card-title">@lang('Commission Management')</h5>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('admin.commissions.pending') }}" class="btn btn--warning btn-sm">
                                <i class="las la-clock"></i> @lang('Pending') ({{ $stats['total_pending'] }})
                            </a>
                            <button type="button" class="btn btn--success btn-sm" data-bs-toggle="modal" data-bs-target="#realTimeModal">
                                <i class="las la-bolt"></i> @lang('Real-Time Process')
                            </button>
                            <button type="button" class="btn btn--info btn-sm" data-bs-toggle="modal" data-bs-target="#batchModal">
                                <i class="las la-layer-group"></i> @lang('Batch Process')
                            </button>
                            <button type="button" class="btn btn--primary btn-sm" data-bs-toggle="modal" data-bs-target="#syncModal">
                                <i class="las la-sync"></i> @lang('Sync from MT5')
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive--sm table-responsive">
                    <table class="table table--light style--two">
                        <thead>
                            <tr>
                                <th>@lang('IB User')</th>
                                <th>@lang('MT5 Login')</th>
                                <th>@lang('Deal ID')</th>
                                <th>@lang('Amount')</th>
                                <th>@lang('Symbol')</th>
                                <th>@lang('Status')</th>
                                <th>@lang('Date')</th>
                                <th>@lang('Action')</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($commissions as $commission)
                            <tr>
                                <td>
                                    <div>
                                        <strong>{{ $commission->firstname }} {{ $commission->lastname }}</strong><br>
                                        <small class="text-muted">{{ $commission->email }}</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="fw-bold">{{ $commission->mt5_login }}</span>
                                </td>
                                <td>
                                    <span class="badge badge--dark">{{ $commission->mt5_deal_id }}</span>
                                </td>
                                <td>
                                    <span class="fw-bold text-success">${{ number_format($commission->commission_amount, 2) }}</span>
                                    <br><small class="text-muted">Level {{ $commission->level }}</small>
                                </td>
                                <td>
                                    <span class="badge badge--info">{{ $commission->symbol ?: 'N/A' }}</span>
                                    <br><small class="text-muted">{{ number_format($commission->volume, 2) }} lots</small>
                                </td>
                                <td>
                                    @if($commission->status == 'pending')
                                        <span class="badge badge--warning">@lang('Pending')</span>
                                    @elseif($commission->status == 'paid')
                                        <span class="badge badge--success">@lang('Paid')</span>
                                    @else
                                        <span class="badge badge--danger">@lang('Cancelled')</span>
                                    @endif
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ showDateTime($commission->deal_time) }}</strong><br>
                                        <small class="text-muted">{{ \Carbon\Carbon::parse($commission->deal_time)->diffForHumans() }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="button--group">
                                        @if($commission->status == 'pending')
                                            <button type="button" class="btn btn-sm btn-outline--success approve-btn" 
                                                    data-id="{{ $commission->id }}">
                                                <i class="las la-check"></i> @lang('Approve')
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline--danger reject-btn" 
                                                    data-id="{{ $commission->id }}">
                                                <i class="las la-times"></i> @lang('Reject')
                                            </button>
                                        @else
                                            <button type="button" class="btn btn-sm btn-outline--info view-btn" 
                                                    data-commission="{{ json_encode($commission) }}">
                                                <i class="las la-eye"></i> @lang('View')
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="8" class="text-center">@lang('No commission records found')</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if($commissions->hasPages())
            <div class="card-footer py-4">
                {{ $commissions->links() }}
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Real-Time Commission Processing Modal -->
<div class="modal fade" id="realTimeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@lang('Real-Time Commission Processing')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="realTimeForm">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="las la-info-circle"></i>
                        @lang('Process commission for a specific MT5 trade with duplicate prevention.')
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Deal ID') <span class="text-danger">*</span></label>
                                <input type="text" name="deal_id" class="form-control" placeholder="e.g., 3125048" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('MT5 Login') <span class="text-danger">*</span></label>
                                <input type="text" name="mt5_login" class="form-control" placeholder="e.g., 878012" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Symbol') <span class="text-danger">*</span></label>
                                <input type="text" name="symbol" class="form-control" placeholder="e.g., GOLDUSD.p" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Volume (Lots)') <span class="text-danger">*</span></label>
                                <input type="number" name="volume" class="form-control" step="0.01" placeholder="e.g., 2.0" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Profit') <span class="text-danger">*</span></label>
                                <input type="number" name="profit" class="form-control" step="0.01" placeholder="e.g., 102.00" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Commission')</label>
                                <input type="number" name="commission" class="form-control" step="0.01" placeholder="0.00">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>@lang('Trade Time') <span class="text-danger">*</span></label>
                        <input type="datetime-local" name="time" class="form-control" required>
                    </div>

                    <div id="realTimeResult" class="mt-3" style="display: none;"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn--dark" data-bs-dismiss="modal">@lang('Cancel')</button>
                    <button type="submit" class="btn btn--success">
                        <i class="las la-bolt"></i> @lang('Process Commission')
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Batch Commission Processing Modal -->
<div class="modal fade" id="batchModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@lang('Batch Commission Processing')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="batchForm">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="las la-exclamation-triangle"></i>
                        @lang('Process commissions for recent profitable MT5 trades automatically.')
                    </div>

                    <div class="form-group">
                        <label>@lang('Time Period (Hours)')</label>
                        <select name="hours" class="form-control">
                            <option value="1">@lang('Last 1 hour')</option>
                            <option value="6">@lang('Last 6 hours')</option>
                            <option value="24" selected>@lang('Last 24 hours')</option>
                            <option value="72">@lang('Last 3 days')</option>
                            <option value="168">@lang('Last 7 days')</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <div class="form-check">
                            <input type="checkbox" name="force_reprocess" value="1" class="form-check-input" id="forceReprocess">
                            <label class="form-check-label" for="forceReprocess">
                                @lang('Force reprocess already processed trades')
                            </label>
                        </div>
                        <small class="text-muted">@lang('Check this to reprocess trades that already have commission records')</small>
                    </div>

                    <div id="batchResult" class="mt-3" style="display: none;"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn--dark" data-bs-dismiss="modal">@lang('Cancel')</button>
                    <button type="submit" class="btn btn--info">
                        <i class="las la-layer-group"></i> @lang('Process Batch')
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Sync Modal -->
<div class="modal fade" id="syncModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@lang('Sync Commissions from MT5')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('admin.commissions.sync') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label>@lang('Sync Period (Days)')</label>
                        <select name="days" class="form-control">
                            <option value="7">@lang('Last 7 days')</option>
                            <option value="30" selected>@lang('Last 30 days')</option>
                            <option value="90">@lang('Last 90 days')</option>
                            <option value="365">@lang('Last 365 days')</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn--dark" data-bs-dismiss="modal">@lang('Cancel')</button>
                    <button type="submit" class="btn btn--primary">@lang('Sync Now')</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('script')
<script>
$(document).ready(function() {
    // Set default time to now
    const now = new Date();
    now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
    $('input[name="time"]').val(now.toISOString().slice(0, 16));

    // Real-time commission processing
    $('#realTimeForm').on('submit', function(e) {
        e.preventDefault();

        const formData = $(this).serialize();
        const submitBtn = $(this).find('button[type="submit"]');
        const resultDiv = $('#realTimeResult');

        submitBtn.prop('disabled', true).html('<i class="las la-spinner la-spin"></i> Processing...');
        resultDiv.hide();

        $.post('{{ route('admin.commissions.process.realtime') }}', formData)
            .done(function(response) {
                if (response.success) {
                    let html = '<div class="alert alert-success">';
                    html += '<h6><i class="las la-check-circle"></i> Commission Processed Successfully!</h6>';
                    html += `<p><strong>Commissions Created:</strong> ${response.commissions_created}</p>`;
                    html += `<p><strong>Total Amount:</strong> $${response.total_amount}</p>`;

                    if (response.commissions && response.commissions.length > 0) {
                        html += '<p><strong>Distribution:</strong></p><ul>';
                        response.commissions.forEach(function(comm) {
                            html += `<li>Level ${comm.level}: ${comm.ib_name} receives $${comm.amount} (${comm.rate}%)</li>`;
                        });
                        html += '</ul>';
                    }

                    html += '</div>';
                    resultDiv.html(html).show();

                    // Reload page after 3 seconds
                    setTimeout(function() {
                        location.reload();
                    }, 3000);
                } else {
                    resultDiv.html(`<div class="alert alert-danger"><i class="las la-exclamation-triangle"></i> ${response.message}</div>`).show();
                }
            })
            .fail(function(xhr) {
                const error = xhr.responseJSON ? xhr.responseJSON.message : 'Processing failed';
                resultDiv.html(`<div class="alert alert-danger"><i class="las la-exclamation-triangle"></i> ${error}</div>`).show();
            })
            .always(function() {
                submitBtn.prop('disabled', false).html('<i class="las la-bolt"></i> Process Commission');
            });
    });

    // Batch commission processing
    $('#batchForm').on('submit', function(e) {
        e.preventDefault();

        const formData = $(this).serialize();
        const submitBtn = $(this).find('button[type="submit"]');
        const resultDiv = $('#batchResult');

        submitBtn.prop('disabled', true).html('<i class="las la-spinner la-spin"></i> Processing...');
        resultDiv.hide();

        $.post('{{ route('admin.commissions.process.batch') }}', formData)
            .done(function(response) {
                if (response.success) {
                    let html = '<div class="alert alert-success">';
                    html += '<h6><i class="las la-check-circle"></i> Batch Processing Completed!</h6>';
                    html += `<p><strong>Total Trades:</strong> ${response.stats.total_trades}</p>`;
                    html += `<p><strong>Processed:</strong> ${response.stats.processed}</p>`;
                    html += `<p><strong>Skipped:</strong> ${response.stats.skipped}</p>`;
                    html += `<p><strong>Failed:</strong> ${response.stats.failed}</p>`;
                    html += '</div>';
                    resultDiv.html(html).show();

                    // Reload page after 3 seconds
                    setTimeout(function() {
                        location.reload();
                    }, 3000);
                } else {
                    resultDiv.html(`<div class="alert alert-danger"><i class="las la-exclamation-triangle"></i> ${response.message}</div>`).show();
                }
            })
            .fail(function(xhr) {
                const error = xhr.responseJSON ? xhr.responseJSON.message : 'Batch processing failed';
                resultDiv.html(`<div class="alert alert-danger"><i class="las la-exclamation-triangle"></i> ${error}</div>`).show();
            })
            .always(function() {
                submitBtn.prop('disabled', false).html('<i class="las la-layer-group"></i> Process Batch');
            });
    });

    // Approve commission
    $('.approve-btn').on('click', function() {
        const id = $(this).data('id');
        if (confirm('Are you sure you want to approve this commission?')) {
            $.post(`{{ route('admin.commissions.approve', '') }}/${id}`, {
                _token: '{{ csrf_token() }}'
            }).done(function(response) {
                location.reload();
            }).fail(function() {
                alert('Failed to approve commission');
            });
        }
    });

    // Reject commission
    $('.reject-btn').on('click', function() {
        const id = $(this).data('id');
        const reason = prompt('Enter rejection reason (optional):');
        if (reason !== null) {
            $.post(`{{ route('admin.commissions.reject', '') }}/${id}`, {
                _token: '{{ csrf_token() }}',
                reason: reason
            }).done(function(response) {
                location.reload();
            }).fail(function() {
                alert('Failed to reject commission');
            });
        }
    });
});
</script>
@endpush
