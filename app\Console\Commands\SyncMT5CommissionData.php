<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\IbCommissionIntegrationService;
use Illuminate\Support\Facades\Log;

class SyncMT5CommissionData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mt5:sync-commissions
                            {--limit=1000 : Number of deals to process per batch}
                            {--dry-run : Run without making changes}
                            {--force : Force sync even if commission exists}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync MT5 commission data from deals table for IB system';

    /**
     * Commission integration service
     */
    protected $commissionService;

    /**
     * Create a new command instance.
     */
    public function __construct(IbCommissionIntegrationService $commissionService)
    {
        parent::__construct();
        $this->commissionService = $commissionService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔄 Starting MT5 Commission Data Synchronization');
        $this->info('================================================');

        $limit = $this->option('limit');
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');

        if ($dryRun) {
            $this->warn('🧪 DRY RUN MODE - No changes will be made');
        }

        try {
            $this->info("📊 Processing up to {$limit} recent deals...");
            
            $startTime = microtime(true);
            
            if ($dryRun) {
                $this->simulateSync($limit);
            } else {
                $result = $this->commissionService->syncCommissionData($limit);
                $this->displayResults($result);
            }

            $endTime = microtime(true);
            $duration = round($endTime - $startTime, 2);
            
            $this->info("⏱️  Sync completed in {$duration} seconds");
            $this->info('✅ MT5 Commission sync finished successfully');

        } catch (\Exception $e) {
            $this->error('❌ Error during commission sync: ' . $e->getMessage());
            Log::error('MT5 Commission sync error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }

        return 0;
    }

    /**
     * Display sync results
     */
    private function displayResults($result)
    {
        if ($result['success']) {
            $this->info("✅ Successfully processed {$result['processed']} deals");
            $this->info("💰 Total commission generated: $" . number_format($result['commission_total'], 2));
            
            if ($result['processed'] > 0) {
                $avgCommission = $result['commission_total'] / $result['processed'];
                $this->info("📈 Average commission per deal: $" . number_format($avgCommission, 2));
            }
        } else {
            $this->error("❌ Sync failed: " . $result['error']);
        }
    }

    /**
     * Simulate sync for dry run mode
     */
    private function simulateSync($limit)
    {
        $this->info('🧪 Simulating commission sync...');
        
        // This would show what would be processed without actually doing it
        $this->info("📊 Would process up to {$limit} recent deals");
        $this->info("🔍 Would check for IB relationships");
        $this->info("💰 Would calculate commissions based on rebate rules");
        $this->info("🏗️  Would create commission records");
        $this->info("🌳 Would process multi-level commissions");
        
        $this->warn('🧪 DRY RUN - No actual changes made');
    }
}
