<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Windows Server Email Editor Diagnostic</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .diagnostic-panel { background: white; padding: 20px; margin: 10px 0; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .test-result { padding: 10px; margin: 5px 0; border-radius: 3px; }
        .test-success { background: #d4edda; border: 1px solid #c3e6cb; }
        .test-error { background: #f8d7da; border: 1px solid #f5c6cb; }
        .test-warning { background: #fff3cd; border: 1px solid #ffeaa7; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 3px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .log-output { max-height: 300px; overflow-y: auto; background: #000; color: #00ff00; padding: 10px; font-family: monospace; }
    </style>
</head>
<body>
    <h1>🔧 Windows Server 2022/Plesk Email Editor Diagnostic</h1>
    
    <div class="diagnostic-panel">
        <h2>📋 System Information</h2>
        <div id="system-info">
            <div class="test-result test-success">
                <strong>User Agent:</strong> <span id="user-agent"></span>
            </div>
            <div class="test-result test-success">
                <strong>Current URL:</strong> <span id="current-url"></span>
            </div>
            <div class="test-result test-success">
                <strong>Timestamp:</strong> <span id="timestamp"></span>
            </div>
        </div>
    </div>

    <div class="diagnostic-panel">
        <h2>🔍 Asset Loading Tests</h2>
        <button onclick="testAssetLoading()">Test Asset Loading</button>
        <div id="asset-tests"></div>
    </div>

    <div class="diagnostic-panel">
        <h2>📜 JavaScript Function Tests</h2>
        <button onclick="testJavaScriptFunctions()">Test JavaScript Functions</button>
        <div id="js-tests"></div>
    </div>

    <div class="diagnostic-panel">
        <h2>🌐 Network Connectivity Tests</h2>
        <button onclick="testNetworkConnectivity()">Test Network</button>
        <div id="network-tests"></div>
    </div>

    <div class="diagnostic-panel">
        <h2>🎯 Template Loading Tests</h2>
        <button onclick="testTemplateLoading()">Test Template Loading</button>
        <div id="template-tests"></div>
    </div>

    <div class="diagnostic-panel">
        <h2>📊 Browser Console Errors</h2>
        <button onclick="captureConsoleErrors()">Capture Console Errors</button>
        <div id="console-errors"></div>
    </div>

    <div class="diagnostic-panel">
        <h2>📝 Diagnostic Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <button onclick="exportLog()">Export Log</button>
        <div id="diagnostic-log" class="log-output"></div>
    </div>

    <script>
        let diagnosticLog = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toISOString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            diagnosticLog.push(logEntry);
            
            const logDiv = document.getElementById('diagnostic-log');
            logDiv.innerHTML += logEntry + '\n';
            logDiv.scrollTop = logDiv.scrollHeight;
            
            console.log(logEntry);
        }
        
        function clearLog() {
            diagnosticLog = [];
            document.getElementById('diagnostic-log').innerHTML = '';
        }
        
        function exportLog() {
            const logContent = diagnosticLog.join('\n');
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'windows_server_diagnostic_' + new Date().toISOString().replace(/[:.]/g, '-') + '.txt';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
        
        function testAssetLoading() {
            log('Starting asset loading tests...', 'info');
            const testsDiv = document.getElementById('asset-tests');
            testsDiv.innerHTML = '';
            
            const assets = [
                '/assets/admin/js/simple-email-editor.js',
                '/assets/admin/css/simple-email-editor.css',
                '/assets/admin/js/app.js',
                '/assets/admin/css/main.css'
            ];
            
            assets.forEach(asset => {
                const script = document.createElement('script');
                const link = document.createElement('link');
                
                if (asset.endsWith('.js')) {
                    script.src = asset;
                    script.onload = () => {
                        testsDiv.innerHTML += `<div class="test-result test-success">✅ ${asset} - Loaded successfully</div>`;
                        log(`Asset loaded: ${asset}`, 'success');
                    };
                    script.onerror = () => {
                        testsDiv.innerHTML += `<div class="test-result test-error">❌ ${asset} - Failed to load</div>`;
                        log(`Asset failed: ${asset}`, 'error');
                    };
                    document.head.appendChild(script);
                } else if (asset.endsWith('.css')) {
                    link.rel = 'stylesheet';
                    link.href = asset;
                    link.onload = () => {
                        testsDiv.innerHTML += `<div class="test-result test-success">✅ ${asset} - Loaded successfully</div>`;
                        log(`Asset loaded: ${asset}`, 'success');
                    };
                    link.onerror = () => {
                        testsDiv.innerHTML += `<div class="test-result test-error">❌ ${asset} - Failed to load</div>`;
                        log(`Asset failed: ${asset}`, 'error');
                    };
                    document.head.appendChild(link);
                }
            });
        }
        
        function testJavaScriptFunctions() {
            log('Testing JavaScript functions...', 'info');
            const testsDiv = document.getElementById('js-tests');
            testsDiv.innerHTML = '';
            
            const functions = [
                'initializeEditorContent',
                'syncEditorContent',
                'cleanHtmlContent',
                'switchEditorMode',
                'updateHiddenFields'
            ];
            
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    testsDiv.innerHTML += `<div class="test-result test-success">✅ ${funcName} - Function exists</div>`;
                    log(`Function exists: ${funcName}`, 'success');
                } else {
                    testsDiv.innerHTML += `<div class="test-result test-error">❌ ${funcName} - Function not found</div>`;
                    log(`Function missing: ${funcName}`, 'error');
                }
            });
        }
        
        function testNetworkConnectivity() {
            log('Testing network connectivity...', 'info');
            const testsDiv = document.getElementById('network-tests');
            testsDiv.innerHTML = '';
            
            // Test AJAX connectivity
            fetch('/admin/dashboard')
                .then(response => {
                    if (response.ok) {
                        testsDiv.innerHTML += `<div class="test-result test-success">✅ Admin dashboard - Accessible</div>`;
                        log('Admin dashboard accessible', 'success');
                    } else {
                        testsDiv.innerHTML += `<div class="test-result test-warning">⚠️ Admin dashboard - Response: ${response.status}</div>`;
                        log(`Admin dashboard response: ${response.status}`, 'warning');
                    }
                })
                .catch(error => {
                    testsDiv.innerHTML += `<div class="test-result test-error">❌ Admin dashboard - Error: ${error.message}</div>`;
                    log(`Admin dashboard error: ${error.message}`, 'error');
                });
        }
        
        function testTemplateLoading() {
            log('Testing template loading...', 'info');
            const testsDiv = document.getElementById('template-tests');
            testsDiv.innerHTML = '';
            
            // Test template endpoint
            fetch('/admin/notification/template/edit/4')
                .then(response => {
                    if (response.ok) {
                        testsDiv.innerHTML += `<div class="test-result test-success">✅ Template edit page - Accessible</div>`;
                        log('Template edit page accessible', 'success');
                        return response.text();
                    } else {
                        testsDiv.innerHTML += `<div class="test-result test-error">❌ Template edit page - Response: ${response.status}</div>`;
                        log(`Template edit page response: ${response.status}`, 'error');
                        throw new Error(`HTTP ${response.status}`);
                    }
                })
                .then(html => {
                    // Check for key elements
                    if (html.includes('visual-editor-content')) {
                        testsDiv.innerHTML += `<div class="test-result test-success">✅ Visual editor element - Found in HTML</div>`;
                        log('Visual editor element found', 'success');
                    } else {
                        testsDiv.innerHTML += `<div class="test-result test-error">❌ Visual editor element - Not found in HTML</div>`;
                        log('Visual editor element missing', 'error');
                    }
                    
                    if (html.includes('simple-email-editor.js')) {
                        testsDiv.innerHTML += `<div class="test-result test-success">✅ JavaScript reference - Found in HTML</div>`;
                        log('JavaScript reference found', 'success');
                    } else {
                        testsDiv.innerHTML += `<div class="test-result test-error">❌ JavaScript reference - Not found in HTML</div>`;
                        log('JavaScript reference missing', 'error');
                    }
                })
                .catch(error => {
                    testsDiv.innerHTML += `<div class="test-result test-error">❌ Template loading error: ${error.message}</div>`;
                    log(`Template loading error: ${error.message}`, 'error');
                });
        }
        
        function captureConsoleErrors() {
            log('Capturing console errors...', 'info');
            const errorsDiv = document.getElementById('console-errors');
            errorsDiv.innerHTML = '';
            
            // Override console.error to capture errors
            const originalError = console.error;
            const errors = [];
            
            console.error = function(...args) {
                errors.push(args.join(' '));
                originalError.apply(console, args);
            };
            
            // Check for existing errors
            if (window.jsErrors && window.jsErrors.length > 0) {
                window.jsErrors.forEach(error => {
                    errorsDiv.innerHTML += `<div class="test-result test-error">❌ ${error}</div>`;
                    log(`Console error: ${error}`, 'error');
                });
            } else {
                errorsDiv.innerHTML += `<div class="test-result test-success">✅ No console errors detected</div>`;
                log('No console errors detected', 'success');
            }
            
            // Restore original console.error after 5 seconds
            setTimeout(() => {
                console.error = originalError;
                if (errors.length > 0) {
                    errors.forEach(error => {
                        errorsDiv.innerHTML += `<div class="test-result test-error">❌ New error: ${error}</div>`;
                        log(`New console error: ${error}`, 'error');
                    });
                }
            }, 5000);
        }
        
        // Initialize diagnostic
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('user-agent').textContent = navigator.userAgent;
            document.getElementById('current-url').textContent = window.location.href;
            document.getElementById('timestamp').textContent = new Date().toISOString();
            
            log('Windows Server Email Editor Diagnostic initialized', 'info');
            log(`User Agent: ${navigator.userAgent}`, 'info');
            log(`Current URL: ${window.location.href}`, 'info');
            
            // Capture JavaScript errors
            window.jsErrors = [];
            window.addEventListener('error', function(e) {
                const error = `${e.message} at ${e.filename}:${e.lineno}:${e.colno}`;
                window.jsErrors.push(error);
                log(`JavaScript error captured: ${error}`, 'error');
            });
        });
    </script>
</body>
</html>
