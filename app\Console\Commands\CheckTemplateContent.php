<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\NotificationTemplate;

class CheckTemplateContent extends Command
{
    protected $signature = 'email:check-content {template_id}';
    protected $description = 'Check email template content for duplicates';

    public function handle()
    {
        $templateId = $this->argument('template_id');
        $template = NotificationTemplate::find($templateId);

        if (!$template) {
            $this->error("Template {$templateId} not found");
            return;
        }

        $this->info("Template: " . $template->name);
        $this->info("Action Code: " . $template->act);
        $this->info("Subject: " . $template->subj);
        $this->info("Content Length: " . strlen($template->email_body) . " characters");
        
        $this->line("First 300 characters:");
        $this->line(substr($template->email_body, 0, 300));
        $this->line("...");

        // Check for hardcoded sample data
        $content = $template->email_body;
        $sampleDataIndicators = [
            '<PERSON> Doe' => 'Should use {{fullname}}',
            '<EMAIL>' => 'Should use {{email}}',
            '<EMAIL>' => 'Should use {{email}}',
            'MBFX Team' => 'Should use {{site_name}} Team',
            'Dear John' => 'Should use Dear {{fullname}}',
            'Hello John' => 'Should use Hello {{fullname}}',
            'Hi John' => 'Should use Hi {{fullname}}',
            '$1,000' => 'Should use {{amount}} {{currency}}',
            '1000 USD' => 'Should use {{amount}} {{currency}}',
            'Test User' => 'Should use {{fullname}}',
            'Sample Company' => 'Should use {{site_name}}'
        ];

        $this->line("\nSample Data Check:");
        $foundSampleData = false;
        foreach ($sampleDataIndicators as $indicator => $replacement) {
            if (strpos($content, $indicator) !== false) {
                $this->line("❌ FOUND: '$indicator' - $replacement");
                $foundSampleData = true;
            }
        }

        if (!$foundSampleData) {
            $this->line("✅ No hardcoded sample data found");
        }

        // Check for proper shortcode usage
        $this->line("\nShortcode Usage Check:");
        $requiredShortcodes = ['{{fullname}}', '{{email}}', '{{site_name}}'];
        foreach ($requiredShortcodes as $shortcode) {
            $found = strpos($content, $shortcode) !== false;
            $status = $found ? '✅ FOUND' : '⚠️ MISSING';
            $this->line("- $shortcode: $status");
        }

        // Check for Laravel references
        $this->line("\nLaravel References Check:");
        $laravelIndicators = ['Laravel Team', 'Laravel', 'laravel.com', 'Laravel Framework'];
        $foundLaravel = false;
        foreach ($laravelIndicators as $indicator) {
            if (stripos($content, $indicator) !== false || stripos($template->subj, $indicator) !== false) {
                $this->line("❌ FOUND: '$indicator'");
                $foundLaravel = true;
            }
        }

        if (!$foundLaravel) {
            $this->line("✅ No Laravel references found");
        }

        // Check global template
        $this->line("\nGlobal Template Check:");
        $general = gs();
        $globalTemplate = $general->email_template;
        $this->line("Global template length: " . strlen($globalTemplate) . " characters");
        $this->line("Contains {{message}}: " . (strpos($globalTemplate, '{{message}}') !== false ? 'YES' : 'NO'));
        $this->line("Contains MBFX: " . (strpos($globalTemplate, 'MBFX') !== false ? 'YES' : 'NO'));
        $this->line("Contains Laravel: " . (strpos($globalTemplate, 'Laravel') !== false ? 'YES' : 'NO'));
        $this->line("First 200 chars: " . substr($globalTemplate, 0, 200));

        // Check if content has proper structure
        $this->line("\nStructure Check:");
        $this->line("- Has DOCTYPE: " . (strpos($content, '<!DOCTYPE html') !== false ? '✅ YES' : '❌ NO'));
        $this->line("- Has header-banner: " . (strpos($content, 'header-banner') !== false ? '✅ YES' : '❌ NO'));
        $this->line("- Has logo-section: " . (strpos($content, 'logo-section') !== false ? '✅ YES' : '❌ NO'));
        $this->line("- Has footer-section: " . (strpos($content, 'footer-section') !== false ? '✅ YES' : '❌ NO'));
    }
}
