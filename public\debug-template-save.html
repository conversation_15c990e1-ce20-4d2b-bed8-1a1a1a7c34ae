<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Template Save Debug - Live Server</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: #dc3545; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .debug-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .debug-section h3 { color: #dc3545; margin-top: 0; }
        .test-button { background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .test-button:hover { background: #c82333; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .code { background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; margin: 10px 0; }
        .url { background: #f8f9fa; padding: 10px; border-left: 4px solid #dc3545; margin: 10px 0; font-family: monospace; }
        .status { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .checklist { list-style: none; padding: 0; }
        .checklist li { padding: 8px 0; border-bottom: 1px solid #eee; }
        .checklist li:before { content: "☐ "; color: #dc3545; font-weight: bold; }
        .checklist li.checked:before { content: "✅ "; }
        .log-output { background: #000; color: #0f0; padding: 15px; border-radius: 4px; font-family: monospace; max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Template Save Debug - Live Server Investigation</h1>
            <p>Comprehensive debugging for email template save functionality issues</p>
        </div>

        <!-- Critical Issue Analysis -->
        <div class="debug-section">
            <h3><span class="status status-error"></span>Critical Issue Analysis</h3>
            <div class="error">
                <h4>Current Problem:</h4>
                <ul>
                    <li>Template edits are not being saved to database</li>
                    <li>System loads same old data after "Update Template" click</li>
                    <li>No errors in Laravel logs or browser console</li>
                    <li>Template structure preserved (no corruption)</li>
                    <li>All caches cleared, files uploaded to live server</li>
                </ul>
            </div>
        </div>

        <!-- Live Server Testing -->
        <div class="debug-section">
            <h3><span class="status status-warning"></span>Live Server Testing</h3>
            <p>Run these tests on your live server to identify the issue:</p>
            
            <div class="url">
                <strong>Template Edit URL:</strong> https://your-domain.com/admin/notification/template/edit/1
            </div>
            
            <h4>Step 1: Browser Console Tests</h4>
            <div class="code">
// Open browser console (F12) and run these commands:

// 1. Check if Visual Builder is loaded
console.log('Visual Builder Instance:', typeof visualBuilderInstance);
console.log('Visual Builder Available:', visualBuilderInstance ? 'YES' : 'NO');

// 2. Check template data
console.log('Template Data:', typeof window.templateData);
console.log('Template Data Content:', window.templateData);

// 3. Check form fields
const emailBodyField = document.querySelector('textarea[name="email_body"]');
const emailBodyFinalField = document.getElementById('email_body_final');
const originalField = document.querySelector('input[name="original_email_body"]');

console.log('Email Body Field:', emailBodyField ? 'FOUND' : 'NOT FOUND');
console.log('Email Body Final Field:', emailBodyFinalField ? 'FOUND' : 'NOT FOUND');
console.log('Original Email Body Field:', originalField ? 'FOUND' : 'NOT FOUND');

// 4. Check current field values
if (emailBodyField) console.log('Email Body Length:', emailBodyField.value.length);
if (emailBodyFinalField) console.log('Email Body Final Length:', emailBodyFinalField.value.length);

// 5. Test form submission preparation
if (typeof prepareFormSubmission === 'function') {
    prepareFormSubmission();
    console.log('✅ Form submission preparation completed');
} else {
    console.log('❌ prepareFormSubmission function not found');
}

// 6. Check if content is being synced
if (visualBuilderInstance && typeof visualBuilderInstance.getContent === 'function') {
    const content = visualBuilderInstance.getContent();
    console.log('Visual Builder Content Length:', content ? content.length : 0);
    console.log('Visual Builder Content Preview:', content ? content.substring(0, 100) : 'EMPTY');
}
            </div>
            
            <button class="test-button" onclick="runBrowserTests()">Run Browser Tests</button>
            <div id="browserTestResults" class="result" style="display: none;"></div>
        </div>

        <!-- Network Request Analysis -->
        <div class="debug-section">
            <h3><span class="status status-warning"></span>Network Request Analysis</h3>
            <p>Check the actual form submission in browser Developer Tools:</p>
            
            <ol>
                <li><strong>Open Developer Tools (F12)</strong></li>
                <li><strong>Go to Network tab</strong></li>
                <li><strong>Edit template content</strong></li>
                <li><strong>Click "Update Template"</strong></li>
                <li><strong>Look for POST request to template update endpoint</strong></li>
                <li><strong>Check request payload for:</strong>
                    <ul>
                        <li>email_body field content</li>
                        <li>email_body_final field content</li>
                        <li>original_email_body field content</li>
                    </ul>
                </li>
                <li><strong>Check response status (should be 200 or 302)</strong></li>
                <li><strong>Check response content for success/error messages</strong></li>
            </ol>
            
            <div class="warning">
                <h4>What to Look For:</h4>
                <ul>
                    <li><strong>Missing Fields:</strong> If email_body_final is empty in request</li>
                    <li><strong>Wrong Content:</strong> If email_body_final contains old content</li>
                    <li><strong>Server Errors:</strong> 500 status code or error messages</li>
                    <li><strong>Redirect Issues:</strong> Multiple redirects or wrong redirect URL</li>
                </ul>
            </div>
        </div>

        <!-- Laravel Log Analysis -->
        <div class="debug-section">
            <h3><span class="status status-warning"></span>Laravel Log Analysis</h3>
            <p>Check Laravel logs for detailed debugging information:</p>
            
            <div class="code">
# SSH into your server and run:
tail -f storage/logs/laravel.log

# Or check the latest log entries:
tail -100 storage/logs/laravel.log | grep "TEMPLATE UPDATE DEBUG"

# Look for these debug messages:
# - "=== TEMPLATE UPDATE DEBUG START ==="
# - "=== EMAIL BODY PROCESSING DEBUG ==="
# - "=== FINAL PROCESSING RESULTS ==="
# - "=== DATABASE SAVE OPERATION DEBUG ==="
# - "=== TEMPLATE UPDATE DEBUG END ==="
            </div>
            
            <div class="info">
                <h4>Key Debug Information to Check:</h4>
                <ul>
                    <li><strong>Request Data:</strong> All form fields being received</li>
                    <li><strong>Content Processing:</strong> email_body_final vs email_body</li>
                    <li><strong>Database Operation:</strong> Save success/failure</li>
                    <li><strong>Content Verification:</strong> Database content matches input</li>
                </ul>
            </div>
        </div>

        <!-- Database Direct Check -->
        <div class="debug-section">
            <h3><span class="status status-warning"></span>Database Direct Check</h3>
            <p>Directly check the database to see if changes are being saved:</p>
            
            <div class="code">
# Connect to your database and run:
SELECT id, subj, LENGTH(email_body) as content_length, 
       LEFT(email_body, 100) as content_preview,
       updated_at
FROM notification_templates 
WHERE id = 1;

# Run this query before and after template update to see if:
# 1. content_length changes
# 2. content_preview changes  
# 3. updated_at timestamp changes
            </div>
        </div>

        <!-- Potential Solutions -->
        <div class="debug-section">
            <h3><span class="status status-success"></span>Potential Solutions</h3>
            <div class="success">
                <h4>Based on Investigation Results:</h4>
                
                <h5>If JavaScript Issues Found:</h5>
                <ul>
                    <li>Visual Builder not initializing properly</li>
                    <li>Form fields not being populated</li>
                    <li>prepareFormSubmission not executing</li>
                </ul>
                
                <h5>If Network Issues Found:</h5>
                <ul>
                    <li>Form data not being sent correctly</li>
                    <li>Server not receiving email_body_final</li>
                    <li>Request timing out or failing</li>
                </ul>
                
                <h5>If Database Issues Found:</h5>
                <ul>
                    <li>Database connection problems</li>
                    <li>Transaction rollback issues</li>
                    <li>Field length limitations</li>
                    <li>Character encoding problems</li>
                </ul>
                
                <h5>If PHP 8.4 Issues Found:</h5>
                <ul>
                    <li>Deprecated function usage</li>
                    <li>Type casting problems</li>
                    <li>Null handling issues</li>
                </ul>
            </div>
        </div>

        <!-- Action Plan -->
        <div class="debug-section">
            <h3><span class="status status-success"></span>Action Plan</h3>
            <div class="info">
                <h4>Complete This Checklist:</h4>
                <ul class="checklist">
                    <li id="check1">Run browser console tests</li>
                    <li id="check2">Check network requests in DevTools</li>
                    <li id="check3">Monitor Laravel logs during template update</li>
                    <li id="check4">Check database directly for changes</li>
                    <li id="check5">Verify all files uploaded to live server</li>
                    <li id="check6">Clear all caches again</li>
                    <li id="check7">Test with different template ID</li>
                    <li id="check8">Check server error logs</li>
                    <li id="check9">Verify PHP 8.4 compatibility</li>
                    <li id="check10">Test form submission manually</li>
                </ul>
                
                <button class="test-button" onclick="markAllChecked()">Mark All Checked</button>
                <button class="test-button" onclick="resetChecklist()">Reset Checklist</button>
            </div>
        </div>
    </div>

    <script>
        function runBrowserTests() {
            const resultDiv = document.getElementById('browserTestResults');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.innerHTML = '🔄 Running browser tests... Check console for detailed results.';
            
            let results = [];
            
            // Test 1: Visual Builder
            if (typeof visualBuilderInstance !== 'undefined' && visualBuilderInstance) {
                results.push('✅ Visual Builder instance found');
            } else {
                results.push('❌ Visual Builder instance not found');
            }
            
            // Test 2: Template data
            if (typeof window.templateData !== 'undefined') {
                results.push('✅ Template data available');
            } else {
                results.push('❌ Template data not available');
            }
            
            // Test 3: Functions
            if (typeof prepareFormSubmission === 'function') {
                results.push('✅ prepareFormSubmission function available');
            } else {
                results.push('❌ prepareFormSubmission function not available');
            }
            
            // Test 4: Form fields
            const emailBodyField = document.querySelector('textarea[name="email_body"]');
            const emailBodyFinalField = document.getElementById('email_body_final');
            
            if (emailBodyField) {
                results.push('✅ email_body field found');
            } else {
                results.push('❌ email_body field not found');
            }
            
            if (emailBodyFinalField) {
                results.push('✅ email_body_final field found');
            } else {
                results.push('❌ email_body_final field not found');
            }
            
            setTimeout(() => {
                resultDiv.className = 'result ' + (results.some(r => r.includes('❌')) ? 'error' : 'success');
                resultDiv.innerHTML = '<h4>Browser Test Results:</h4><ul><li>' + results.join('</li><li>') + '</li></ul>';
            }, 1000);
        }
        
        function markAllChecked() {
            const checklistItems = document.querySelectorAll('.checklist li');
            checklistItems.forEach(item => item.classList.add('checked'));
        }
        
        function resetChecklist() {
            const checklistItems = document.querySelectorAll('.checklist li');
            checklistItems.forEach(item => item.classList.remove('checked'));
        }
        
        // Auto-run initial check
        window.onload = function() {
            console.log('🔧 Template Save Debug Page Loaded');
            console.log('📋 Use this page to systematically debug template save issues');
        };
    </script>
</body>
</html>
