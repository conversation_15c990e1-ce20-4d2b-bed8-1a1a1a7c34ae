<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 CHECKING MT5 DATABASE STRUCTURE\n";
echo "===================================\n";

try {
    // Check mt5_users table structure
    echo "\n📋 MT5_USERS TABLE COLUMNS:\n";
    $columns = \DB::connection('mbf-dbmt5')->select('DESCRIBE mt5_users');
    
    foreach($columns as $col) {
        echo "   - {$col->Field} ({$col->Type})\n";
    }
    
    // Get a sample record to see actual data
    echo "\n📊 SAMPLE MT5 USER RECORD:\n";
    $sample = \DB::connection('mbf-dbmt5')->select('SELECT * FROM mt5_users LIMIT 1');
    
    if (!empty($sample)) {
        $record = $sample[0];
        foreach($record as $field => $value) {
            echo "   - {$field}: " . (is_null($value) ? 'NULL' : $value) . "\n";
        }
    } else {
        echo "   No records found in mt5_users table\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n✅ MT5 DATABASE STRUCTURE CHECK COMPLETED!\n";
