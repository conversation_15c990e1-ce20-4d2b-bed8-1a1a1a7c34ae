<?php
    $user=auth()->user();
?>
<div class="dashboard-header">
    <div class="dashboard-header__inner">
        <div class="dashboard-header__left">
            <div class="copy-link">
                <input type="text" class="copyText" value="<?php echo e(route('home')); ?>?reference=<?php echo e($user->username); ?>" readonly>
                <button class="copy-link__button copyTextBtn" data-bs-toggle="tooltip"  data-bs-placement="right" title="<?php echo app('translator')->get('Copy URL'); ?>">
                    <span class="copy-link__icon"><i class="las la-copy"></i>
                    </span>
                </button>
            </div>
        </div>
           
        <div class="dashboard-header__right">
			<!-- <a href="https://web.mybrokerforex.com"target="_blank">
            <img src="<?php echo e(asset('assets/images/extra_images/web.png')); ?>" width="40px">
        	</a> -->
            <a href="https://web.mybrokerforex.com" target="_blank" class="btn btn--base outline btn--sm trade-btn">
                <span class="icon-trade"></span> <?php echo app('translator')->get('TRADE'); ?>
            </a>
            <div class="user-info">
                <div class="user-info__right">
                    <div class="user-info__button">
                        <div class="user-info__profile">
                            <p class="user-info__name"><?php echo e(__($user->username)); ?></p>
                        </div>
                    </div>
                </div>
                <ul class="user-info-dropdown">
                    <li class="user-info-dropdown__item">
                        <a class="user-info-dropdown__link" href="<?php echo e(route('user.profile.setting')); ?>">
                            <span class="icon"><i class="far fa-user-circle"></i></span>
                            <span class="text"><?php echo app('translator')->get('My Profile'); ?></span>
                        </a>
                    </li>
					<li class="user-info-dropdown__item">
                        <a class="user-info-dropdown__link" href="<?php echo e(route('user.walletOverview')); ?>">
                            <span class="icon"><i class="icon-wallet"></i></span>
                            <span class="text"><?php echo app('translator')->get('Balance Inquiry'); ?></span>
                        </a>
                    </li>
                    <li class="user-info-dropdown__item">
                        <a class="user-info-dropdown__link" href="<?php echo e(route('user.change.password')); ?>">
                            <span class="icon"><i class="fa fa-key"></i></span>
                            <span class="text"><?php echo app('translator')->get('Change Password'); ?></span>
                        </a>
                    </li>
                    <li class="user-info-dropdown__item">
                        <a class="user-info-dropdown__link" href="<?php echo e(route('user.logout')); ?>">
                            <span class="icon"><i class="far fa-user-circle"></i></span>
                            <span class="text"><?php echo app('translator')->get('Logout'); ?></span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\mbf.mybrokerforex.com-31052025\resources\views/templates/basic/partials/user_topbar.blade.php ENDPATH**/ ?>