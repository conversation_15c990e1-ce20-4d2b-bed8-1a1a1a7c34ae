<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Template Corruption Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        textarea { width: 100%; height: 200px; margin: 10px 0; }
        button { padding: 10px 20px; margin: 5px; }
        .log { background: #f8f9fa; padding: 10px; margin: 10px 0; font-family: monospace; }
    </style>
</head>
<body>
    <h1>🔧 Template Corruption Fix Test</h1>
    
    <div class="test-section">
        <h2>📋 Test Scenario</h2>
        <p>This test simulates the Windows Server 2022/Plesk environment where template content gets duplicated.</p>
        <p><strong>Expected Result:</strong> Content should be cleaned and deduplicated automatically.</p>
    </div>

    <div class="test-section">
        <h2>🧪 Test 1: Content Duplication Detection</h2>
        <textarea id="test-content-1" placeholder="Paste duplicated content here...">Deposit Approved Deposit Approved Your deposit request has been approved and processed. Your deposit request has been approved and processed.

Dear {{fullname}},Great news! Your deposit request has been approved and processed successfully. Dear {{fullname}},Great news! Your deposit request has been approved and processed successfully.

Approved Amount: {{amount}} {{currency}} Approved Amount: {{amount}} {{currency}}
Payment Method: {{method_name}} Payment Method: {{method_name}}
Transaction ID: {{trx}} Transaction ID: {{trx}}

Best regards,MBFX Team Best regards,MBFX Team</textarea>
        <button onclick="testContentCleaning(1)">🧹 Test Content Cleaning</button>
        <div id="result-1" class="log"></div>
    </div>

    <div class="test-section">
        <h2>🧪 Test 2: Windows Server Encoding Issues</h2>
        <textarea id="test-content-2" placeholder="Content with encoding issues...">﻿Deposit Approved
Your deposit request has been approved and processed.

Dear {{fullname}},
Great news! Your deposit request has been approved and processed successfully.

Best regards,
MBFX Team</textarea>
        <button onclick="testContentCleaning(2)">🧹 Test Encoding Cleanup</button>
        <div id="result-2" class="log"></div>
    </div>

    <div class="test-section">
        <h2>🧪 Test 3: Line Ending Normalization</h2>
        <textarea id="test-content-3" placeholder="Content with mixed line endings..."></textarea>
        <button onclick="testLineEndingNormalization()">🔄 Test Line Ending Fix</button>
        <div id="result-3" class="log"></div>
    </div>

    <div class="test-section">
        <h2>📊 Test Results Summary</h2>
        <div id="summary" class="log">
            <p>Run tests above to see results...</p>
        </div>
    </div>

    <script>
        // Simulate the cleanHtmlContent function from simple-email-editor.js
        function cleanHtmlContent(content) {
            if (!content) return '';
            
            // Basic HTML cleaning (simplified version)
            return content
                .replace(/<script[^>]*>.*?<\/script>/gi, '')
                .replace(/<style[^>]*>.*?<\/style>/gi, '')
                .trim();
        }

        // Enhanced content cleaning function (from our fix)
        function enhancedContentCleaning(content) {
            if (!content) return '';
            
            let cleanedContent = cleanHtmlContent(content);
            
            if (cleanedContent) {
                // Remove BOM characters
                cleanedContent = cleanedContent.replace(/^\uFEFF/, '');
                
                // Normalize line endings
                cleanedContent = cleanedContent.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
                
                // Remove null characters
                cleanedContent = cleanedContent.replace(/\0/g, '');
                
                // CRITICAL: Detect and remove content duplication patterns
                const lines = cleanedContent.split('\n');
                const uniqueLines = [];
                const seenContent = new Set();
                
                for (let line of lines) {
                    const trimmedLine = line.trim();
                    // Skip if we've seen this exact content before (but keep empty lines)
                    if (!trimmedLine || !seenContent.has(trimmedLine)) {
                        uniqueLines.push(line);
                        if (trimmedLine) seenContent.add(trimmedLine);
                    }
                }
                
                cleanedContent = uniqueLines.join('\n');
            }
            
            return cleanedContent;
        }

        function testContentCleaning(testNumber) {
            const textarea = document.getElementById(`test-content-${testNumber}`);
            const resultDiv = document.getElementById(`result-${testNumber}`);
            
            const originalContent = textarea.value;
            const cleanedContent = enhancedContentCleaning(originalContent);
            
            const originalLength = originalContent.length;
            const cleanedLength = cleanedContent.length;
            const reduction = originalLength - cleanedLength;
            const reductionPercent = originalLength > 0 ? ((reduction / originalLength) * 100).toFixed(1) : 0;
            
            let resultClass = 'success';
            let resultMessage = '✅ Content cleaned successfully';
            
            if (reduction === 0) {
                resultClass = 'warning';
                resultMessage = '⚠️ No changes needed';
            } else if (reduction < 0) {
                resultClass = 'error';
                resultMessage = '❌ Content increased (unexpected)';
            }
            
            resultDiv.className = `log ${resultClass}`;
            resultDiv.innerHTML = `
                <strong>${resultMessage}</strong><br>
                Original length: ${originalLength} characters<br>
                Cleaned length: ${cleanedLength} characters<br>
                Reduction: ${reduction} characters (${reductionPercent}%)<br>
                <br>
                <strong>Cleaned Content:</strong><br>
                <textarea readonly style="width: 100%; height: 150px;">${cleanedContent}</textarea>
            `;
            
            updateSummary();
        }

        function testLineEndingNormalization() {
            const textarea = document.getElementById('test-content-3');
            
            // Create content with mixed line endings
            const mixedContent = "Line 1\r\nLine 2\rLine 3\nLine 4\r\n\r\nEnd";
            textarea.value = mixedContent;
            
            testContentCleaning(3);
        }

        function updateSummary() {
            const results = [];
            for (let i = 1; i <= 3; i++) {
                const resultDiv = document.getElementById(`result-${i}`);
                if (resultDiv && resultDiv.innerHTML.trim()) {
                    const isSuccess = resultDiv.classList.contains('success');
                    const isWarning = resultDiv.classList.contains('warning');
                    const isError = resultDiv.classList.contains('error');
                    
                    if (isSuccess) results.push(`✅ Test ${i}: PASSED`);
                    else if (isWarning) results.push(`⚠️ Test ${i}: WARNING`);
                    else if (isError) results.push(`❌ Test ${i}: FAILED`);
                }
            }
            
            const summaryDiv = document.getElementById('summary');
            if (results.length > 0) {
                summaryDiv.innerHTML = `
                    <strong>Test Results:</strong><br>
                    ${results.join('<br>')}
                    <br><br>
                    <strong>Status:</strong> ${results.every(r => r.includes('✅')) ? '🎉 All tests passed!' : '⚠️ Some tests need attention'}
                `;
            }
        }

        // Initialize with sample content
        window.onload = function() {
            console.log('🧪 Template Corruption Fix Test loaded');
            console.log('📋 Ready to test Windows Server 2022/Plesk fixes');
        };
    </script>
</body>
</html>
