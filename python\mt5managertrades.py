import argparse
import logging
import sys
import json
import os
from datetime import datetime, timed<PERSON><PERSON>

# Try to import MT5Manager with wrapper for password support
try:
    # First import MT5Manager
    import MT5Manager
    from MT5Manager import MTUser, MTDeal, MTPosition, MTOrder

    # Then import the wrapper
    from mt5_wrapper import WrappedManagerAPI

    print("Using MT5Manager with WRAPPER TRADE SUPPORT")
    MT5_AVAILABLE = True
    MT5_PATCHED = True
except ImportError as e:
    try:
        # Fallback to original MT5Manager
        import MT5Manager
        from MT5Manager import MTUser, MTDeal, MTPosition, MTOrder
        print("Using MT5Manager WITHOUT wrapper support")
        MT5_AVAILABLE = True
        MT5_PATCHED = False
        WrappedManagerAPI = None  # Not available
    except ImportError as e2:
        print(f"MT5Manager not found: {e2}")
        print("Please install MT5Manager using: pip install MT5Manager")
        print("Or check if you're using the correct Python environment")
        MT5_AVAILABLE = False
        MT5_PATCHED = False
        WrappedManagerAPI = None

        # Create mock classes for testing ONLY when MT5Manager is not available
        class MTPosition:
            def __init__(self):
                self.Login = None
                self.Symbol = None
                self.Profit = None
                self.Commission = None
                self.Fee = None
                self.Ticket = None
                self.PriceSL = None
                self.PriceTP = None
                self.Volume = None
                self.Order = None
                self.TimeSetup = None
                self.Type = None

        class MTOrder:
            def __init__(self):
                self.Login = None
                self.Symbol = None
                self.Profit = None
                self.Commission = None
                self.Fee = None
                self.Ticket = None
                self.PriceSL = None
                self.PriceTP = None
                self.Volume = None
                self.Order = None
                self.TimeSetup = None
                self.Type = None

# Configuration (same as mt5manager.py)
DEFAULT_SERVER = r"188.240.63.163:443"
SERVER_LOGIN = 10007
SERVER_PASSWORD = "TfTe*wA1"

# Configure logging
logging.basicConfig(
    format='%(asctime)s %(levelname)s:%(message)s',
    level=logging.INFO
)

def connect_manager():
    """Connect to MT5 server (with trade support if available)"""
    if not MT5_AVAILABLE:
        raise ImportError("MT5Manager module is not available. Please install it first.")

    # Use wrapped manager if available for enhanced support
    if MT5_PATCHED and WrappedManagerAPI:
        manager = WrappedManagerAPI()
        logging.info("Using WrappedManagerAPI with enhanced trade support")
    else:
        manager = MT5Manager.ManagerAPI()
        logging.info("Using standard MT5Manager.ManagerAPI")

    logging.info("Connecting to MT5 server for trade data...")
    if not manager.Connect(DEFAULT_SERVER, SERVER_LOGIN, SERVER_PASSWORD, 0):
        error = MT5Manager.LastError()
        raise ConnectionError(f"Connection failed: Code {error[0]} ({error[1].name}) - {error[2]}")
    logging.info("Connection successful")
    return manager

def get_open_positions(args):
    """Get open positions for specific MT5 login account"""
    try:
        # Validate inputs
        try:
            login = int(args.login)
        except ValueError:
            raise ValueError("Login must be numeric")

        manager = connect_manager()

        # Check if account exists
        user = manager.UserRequest(login)
        if not user:
            raise ValueError(f"Account {login} not found")

        logging.info(f"Getting open positions for account: {login}")

        # Get open positions using correct MT5Manager methods
        positions = []
        try:
            # First, try to get deals for the account (this shows trading activity)
            logging.info(f"Attempting to get deals for account {login}")

            # Try to get all available methods from the manager
            available_methods = [method for method in dir(manager) if not method.startswith('_')]
            logging.info(f"Available MT5Manager methods: {available_methods}")

            # Try DealGetPage method if available
            if hasattr(manager, 'DealGetPage'):
                try:
                    deals = manager.DealGetPage(login, 0, 1000)
                    if deals:
                        logging.info(f"Found {len(deals)} deals for account {login}")
                        for deal in deals:
                            deal_data = {
                                "Login": getattr(deal, 'Login', login),
                                "Symbol": getattr(deal, 'Symbol', ''),
                                "Profit": float(getattr(deal, 'Profit', 0.0)),
                                "Commission": float(getattr(deal, 'Commission', 0.0)),
                                "Fee": float(getattr(deal, 'Fee', 0.0)),
                                "Ticket": getattr(deal, 'Ticket', 0),
                                "PriceSL": float(getattr(deal, 'PriceSL', 0.0)),
                                "PriceTP": float(getattr(deal, 'PriceTP', 0.0)),
                                "Volume": float(getattr(deal, 'Volume', 0.0)),
                                "Order": getattr(deal, 'Order', 0),
                                "TimeSetup": getattr(deal, 'Time', 0),
                                "Type": getattr(deal, 'Type', 0),
                                "PriceOpen": float(getattr(deal, 'Price', 0.0)),
                                "PriceCurrent": float(getattr(deal, 'Price', 0.0))
                            }
                            positions.append(deal_data)
                except Exception as e:
                    logging.warning(f"DealGetPage failed: {e}")

            # Try the correct position methods that we found
            position_methods = [
                'PositionGetByLogins',
                'PositionGet',
                'PositionRequestByLogins',
                'PositionRequest',
                'OrderGetOpen',
                'OrderGetByLogins'
            ]

            for method_name in position_methods:
                if hasattr(manager, method_name):
                    try:
                        logging.info(f"Trying method: {method_name}")
                        method = getattr(manager, method_name)

                        # Try different parameter combinations
                        result = None
                        if 'ByLogins' in method_name:
                            # Methods that take login array
                            result = method([login])
                        elif 'Open' in method_name:
                            # Methods for open orders/positions
                            result = method()
                        else:
                            # Standard methods
                            result = method()

                        if result:
                            logging.info(f"{method_name} returned {len(result) if hasattr(result, '__len__') else 'data'}")

                            # Process the result
                            if hasattr(result, '__iter__') and not isinstance(result, str):
                                for item in result:
                                    # Check if this item belongs to our login
                                    item_login = getattr(item, 'Login', None)
                                    if item_login == login:
                                        item_data = {
                                            "Login": item_login,
                                            "Symbol": getattr(item, 'Symbol', 'UNKNOWN'),
                                            "Profit": float(getattr(item, 'Profit', 0.0)),
                                            "Commission": float(getattr(item, 'Commission', 0.0)),
                                            "Fee": float(getattr(item, 'Fee', 0.0)),
                                            "Ticket": getattr(item, 'Ticket', 0),
                                            "PriceSL": float(getattr(item, 'PriceSL', 0.0)),
                                            "PriceTP": float(getattr(item, 'PriceTP', 0.0)),
                                            "Volume": float(getattr(item, 'Volume', 0.0)),
                                            "Order": getattr(item, 'Order', 0),
                                            "TimeSetup": getattr(item, 'Time', getattr(item, 'TimeSetup', 0)),
                                            "Type": getattr(item, 'Type', 0),
                                            "PriceOpen": float(getattr(item, 'Price', getattr(item, 'PriceOpen', 0.0))),
                                            "PriceCurrent": float(getattr(item, 'PriceCurrent', getattr(item, 'Price', 0.0)))
                                        }
                                        positions.append(item_data)
                                        logging.info(f"Added position from {method_name}: {item_data['Symbol']} - {item_data['Profit']}")
                            else:
                                # Single item result
                                if hasattr(result, 'Login') and getattr(result, 'Login') == login:
                                    item_data = {
                                        "Login": getattr(result, 'Login', login),
                                        "Symbol": getattr(result, 'Symbol', 'SINGLE_POSITION'),
                                        "Profit": float(getattr(result, 'Profit', 0.0)),
                                        "Commission": float(getattr(result, 'Commission', 0.0)),
                                        "Fee": float(getattr(result, 'Fee', 0.0)),
                                        "Ticket": getattr(result, 'Ticket', 0),
                                        "PriceSL": float(getattr(result, 'PriceSL', 0.0)),
                                        "PriceTP": float(getattr(result, 'PriceTP', 0.0)),
                                        "Volume": float(getattr(result, 'Volume', 0.0)),
                                        "Order": getattr(result, 'Order', 0),
                                        "TimeSetup": getattr(result, 'Time', getattr(result, 'TimeSetup', 0)),
                                        "Type": getattr(result, 'Type', 0),
                                        "PriceOpen": float(getattr(result, 'Price', getattr(result, 'PriceOpen', 0.0))),
                                        "PriceCurrent": float(getattr(result, 'PriceCurrent', getattr(result, 'Price', 0.0)))
                                    }
                                    positions.append(item_data)
                                    logging.info(f"Added single position from {method_name}: {item_data['Symbol']}")

                        if positions:
                            break  # Found positions, no need to try other methods

                    except Exception as e:
                        logging.warning(f"{method_name} failed: {e}")

            # Try to get user information and check for open positions
            if hasattr(manager, 'UserRequest'):
                try:
                    user_info = manager.UserRequest(login)
                    if user_info:
                        logging.info(f"User info retrieved for {login}: Balance={getattr(user_info, 'Balance', 0)}, Equity={getattr(user_info, 'Equity', 0)}")

                        # Just log the account info, don't create synthetic entries
                        balance = float(getattr(user_info, 'Balance', 0))
                        equity = float(getattr(user_info, 'Equity', 0))

                        logging.info(f"Account {login} - Balance: {balance}, Equity: {equity}, P&L: {equity - balance}")

                except Exception as e:
                    logging.warning(f"UserRequest failed: {e}")

            # Try alternative methods to get trading data
            if hasattr(manager, 'TradeRecord'):
                try:
                    # This might give us trade records
                    trade_record = manager.TradeRecord(login)
                    if trade_record:
                        logging.info(f"Trade record found for account {login}")
                        # Process trade record data
                except Exception as e:
                    logging.warning(f"TradeRecord failed: {e}")

        except Exception as e:
            logging.error(f"Error getting positions for account {login}: {e}")

        # If no positions found, try to get any available data about the account
        if not positions:
            try:
                # Check if account exists and has any activity
                user = manager.UserRequest(login)
                if user:
                    balance = float(getattr(user, 'Balance', 0))
                    equity = float(getattr(user, 'Equity', 0))
                    margin = float(getattr(user, 'Margin', 0))

                    logging.info(f"Account {login} exists - Balance: {balance}, Equity: {equity}, Margin: {margin}")

                    # Just log margin info, don't create synthetic entries
                    if margin > 0:
                        logging.info(f"Account {login} has margin used: {margin}, indicating active trading")
                else:
                    logging.warning(f"Account {login} not found or inaccessible")
            except Exception as e:
                logging.error(f"Failed to check account {login}: {e}")

        logging.info(f"Found {len(positions)} open positions for account {login}")

        # Return success response
        result = {
            "status": "success",
            "login": login,
            "positions": positions,
            "count": len(positions),
            "timestamp": datetime.now().isoformat()
        }

        print(json.dumps(result))
        return True

    except Exception as e:
        error_result = {
            "status": "error",
            "message": str(e),
            "login": getattr(args, 'login', None),
            "timestamp": datetime.now().isoformat()
        }
        print(json.dumps(error_result))
        logging.error(f"Error getting open positions: {str(e)}")
        return False
    finally:
        if 'manager' in locals():
            manager.Disconnect()
            logging.info("Disconnected from MT5 server")

def get_account_summary(args):
    """Get account summary including balance, equity, margin, etc."""
    try:
        # Validate inputs
        try:
            login = int(args.login)
        except ValueError:
            raise ValueError("Login must be numeric")

        manager = connect_manager()

        # Get user information
        user = manager.UserRequest(login)
        if not user:
            raise ValueError(f"Account {login} not found")

        # Get account summary
        result = {
            "status": "success",
            "login": login,
            "balance": float(getattr(user, 'Balance', 0.0)),
            "equity": float(getattr(user, 'Equity', 0.0)),
            "margin": float(getattr(user, 'Margin', 0.0)),
            "margin_free": float(getattr(user, 'MarginFree', 0.0)),
            "margin_level": float(getattr(user, 'MarginLevel', 0.0)),
            "leverage": getattr(user, 'Leverage', 0),
            "group": getattr(user, 'Group', ''),
            "name": f"{getattr(user, 'FirstName', '')} {getattr(user, 'LastName', '')}".strip(),
            "timestamp": datetime.now().isoformat()
        }

        print(json.dumps(result))
        return True

    except Exception as e:
        error_result = {
            "status": "error",
            "message": str(e),
            "login": getattr(args, 'login', None),
            "timestamp": datetime.now().isoformat()
        }
        print(json.dumps(error_result))
        logging.error(f"Error getting account summary: {str(e)}")
        return False
    finally:
        if 'manager' in locals():
            manager.Disconnect()
            logging.info("Disconnected from MT5 server")

def get_historical_deals(args):
    """Get historical deals for an account with optimized performance"""
    try:
        login = int(args.login)
        date_from = getattr(args, 'date_from', None)
        date_to = getattr(args, 'date_to', None)

        logging.info(f"Getting historical deals for account: {login}")

        # Connect to MT5 server with optimized settings
        manager = connect_manager()
        if not manager:
            print(json.dumps({"status": "error", "message": "Failed to connect to MT5 server"}))
            return

        deals = []

        try:
            # Enhanced date range for comprehensive historical data
            if date_from:
                from_time = int(datetime.strptime(date_from, '%Y-%m-%d').timestamp())
            else:
                # Default to last 30 days for comprehensive history
                from_time = int((datetime.now() - timedelta(days=30)).timestamp())

            if date_to:
                to_time = int(datetime.strptime(date_to + ' 23:59:59', '%Y-%m-%d %H:%M:%S').timestamp())
            else:
                to_time = int(datetime.now().timestamp())

            logging.info(f"Requesting deals from {from_time} to {to_time}")

            # Optimized method selection - try most efficient first
            methods_to_try = [
                ('DealRequestByLogins', 'DealGet'),
                ('HistoryRequestByLogins', 'HistoryGet')
            ]

            for method_name, get_method in methods_to_try:
                if hasattr(manager, method_name):
                    logging.info(f"Trying optimized method: {method_name}")
                    try:
                        # Use optimized API call
                        if method_name == 'DealRequestByLogins':
                            result = manager.DealRequestByLogins([login], from_time, to_time)
                        else:
                            result = manager.HistoryRequestByLogins([login], from_time, to_time)

                        if result:
                            # Handle both list and integer returns with optimization
                            if isinstance(result, list):
                                deal_list = result
                                logging.info(f"{method_name} returned {len(deal_list)} deals")
                            elif isinstance(result, int) and result > 0:
                                logging.info(f"{method_name} returned {result} deals")
                                deal_list = []
                                # Batch process deals for better performance
                                batch_size = min(100, result)  # Process in batches
                                for i in range(0, result, batch_size):
                                    batch_end = min(i + batch_size, result)
                                    for j in range(i, batch_end):
                                        try:
                                            deal_info = getattr(manager, get_method)(j)
                                            if deal_info:
                                                deal_list.append(deal_info)
                                        except Exception as e:
                                            logging.warning(f"Error getting deal {j}: {e}")
                                            continue
                            else:
                                continue

                            # Process the deal data with enhanced mapping
                            for deal_info in deal_list:
                                try:
                                    if deal_info and hasattr(deal_info, 'Login'):
                                        # Enhanced data mapping with proper symbol handling
                                        symbol = getattr(deal_info, 'Symbol', '').strip()
                                        volume = float(getattr(deal_info, 'Volume', 0))

                                        # Fix symbol display - ensure non-empty symbols are preserved
                                        if not symbol or symbol == '':
                                            # For balance operations, use a descriptive name
                                            action = getattr(deal_info, 'Action', 0)
                                            if action == 2:  # Balance operation
                                                symbol = 'BALANCE'
                                            else:
                                                symbol = 'UNKNOWN'

                                        deal_data = {
                                            "Login": getattr(deal_info, 'Login', 0),
                                            "Deal": getattr(deal_info, 'Deal', 0),
                                            "Order": getattr(deal_info, 'Order', 0),
                                            "Symbol": symbol,
                                            "Action": getattr(deal_info, 'Action', 0),
                                            "Entry": getattr(deal_info, 'Entry', 0),
                                            "Volume": volume,  # Keep original volume for proper calculation
                                            "Price": float(getattr(deal_info, 'Price', 0)),
                                            "Commission": float(getattr(deal_info, 'Commission', 0)),
                                            "Fee": float(getattr(deal_info, 'Fee', 0)),
                                            "Profit": float(getattr(deal_info, 'Profit', 0)),
                                            "Time": int(getattr(deal_info, 'Time', 0)),
                                            "TimeSetup": int(getattr(deal_info, 'TimeSetup', 0)),
                                            "PositionID": getattr(deal_info, 'PositionID', 0),
                                            "PriceOpen": float(getattr(deal_info, 'PriceOpen', 0)),
                                            "PriceClose": float(getattr(deal_info, 'PriceClose', 0)),
                                            "PriceSL": float(getattr(deal_info, 'PriceSL', 0)),
                                            "PriceTP": float(getattr(deal_info, 'PriceTP', 0))
                                        }
                                        deals.append(deal_data)

                                        # Optimized logging - only log actual trades, not balance operations
                                        if symbol not in ['BALANCE', 'UNKNOWN', '']:
                                            logging.info(f"Added trade: {symbol} - Volume: {volume} - Profit: {deal_data['Profit']}")

                                except Exception as e:
                                    logging.warning(f"Error processing deal: {e}")
                                    continue

                            if deals:
                                break

                    except Exception as e:
                        logging.warning(f"Method {method_name} failed: {e}")
                        continue

            # Sort deals by time (most recent first) for better performance
            deals.sort(key=lambda x: x.get('Time', 0), reverse=True)

            logging.info(f"Found {len(deals)} historical deals for account {login}")

            result = {
                "status": "success",
                "login": login,
                "deals": deals,
                "count": len(deals),
                "date_from": date_from,
                "date_to": date_to,
                "timestamp": datetime.now().isoformat()
            }

            print(json.dumps(result))

        except Exception as e:
            logging.error(f"Error getting historical deals: {e}")
            print(json.dumps({
                "status": "error",
                "message": f"Error getting historical deals: {str(e)}",
                "login": login
            }))
        finally:
            try:
                manager.Disconnect()
                logging.info("Disconnected from MT5 server")
            except:
                pass

    except Exception as e:
        logging.error(f"Exception in get_historical_deals: {e}")
        print(json.dumps({
            "status": "error",
            "message": f"Exception: {str(e)}"
        }))

def main():
    """Main function to handle command line arguments"""
    parser = argparse.ArgumentParser(description='MT5 Trading Data Manager')
    subparsers = parser.add_subparsers(dest='command', help='Available commands')

    # Get open positions command
    positions_parser = subparsers.add_parser('get_positions', help='Get open positions for account')
    positions_parser.add_argument('--login', required=True, help='MT5 login number')

    # Get account summary command
    summary_parser = subparsers.add_parser('get_summary', help='Get account summary')
    summary_parser.add_argument('--login', required=True, help='MT5 login number')

    # Get historical deals command
    history_parser = subparsers.add_parser('get_history', help='Get historical deals')
    history_parser.add_argument('--login', required=True, help='MT5 login number')
    history_parser.add_argument('--date_from', type=str, help='Start date (YYYY-MM-DD)')
    history_parser.add_argument('--date_to', type=str, help='End date (YYYY-MM-DD)')

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    try:
        if args.command == 'get_positions':
            get_open_positions(args)
        elif args.command == 'get_summary':
            get_account_summary(args)
        elif args.command == 'get_history':
            get_historical_deals(args)
        else:
            print(f"Unknown command: {args.command}")
            parser.print_help()
    except Exception as e:
        print(json.dumps({
            "status": "error",
            "message": str(e),
            "command": args.command
        }))

if __name__ == "__main__":
    main()
