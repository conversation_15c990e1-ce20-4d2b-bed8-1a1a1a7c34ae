{"__meta": {"id": "01JZ19NT07YE2SKJ4GKBJADN08", "datetime": "2025-06-30 20:50:21", "utime": **********.320064, "method": "GET", "uri": "/mbf.mybrokerforex.com-********/admin/notification/template/edit/36", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751316620.9225, "end": **********.320076, "duration": 0.39757609367370605, "duration_str": "398ms", "measures": [{"label": "Booting", "start": 1751316620.9225, "relative_start": 0, "end": **********.157878, "relative_end": **********.157878, "duration": 0.*****************, "duration_str": "235ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.157889, "relative_start": 0.*****************, "end": **********.320077, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "162ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.182862, "relative_start": 0.****************, "end": **********.187341, "relative_end": **********.187341, "duration": 0.004478931427001953, "duration_str": "4.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.24771, "relative_start": 0.****************, "end": **********.314739, "relative_end": **********.314739, "duration": 0.*****************, "duration_str": "67.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: admin.notification.edit", "start": **********.249649, "relative_start": 0.****************, "end": **********.249649, "relative_end": **********.249649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.back", "start": **********.256202, "relative_start": 0.*****************, "end": **********.256202, "relative_end": **********.256202, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin.layouts.app", "start": **********.257255, "relative_start": 0.33475518226623535, "end": **********.257255, "relative_end": **********.257255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin.partials.sidenav", "start": **********.293952, "relative_start": 0.37145209312438965, "end": **********.293952, "relative_end": **********.293952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin.partials.topnav", "start": **********.308762, "relative_start": 0.3862621784210205, "end": **********.308762, "relative_end": **********.308762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin.partials.breadcrumb", "start": **********.311982, "relative_start": 0.3894820213317871, "end": **********.311982, "relative_end": **********.311982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin.layouts.master", "start": **********.312601, "relative_start": 0.39010119438171387, "end": **********.312601, "relative_end": **********.312601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.notify", "start": **********.313523, "relative_start": 0.3910231590270996, "end": **********.313523, "relative_end": **********.313523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 29130248, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.12", "Environment": "production", "Debug Mode": "Enabled", "URL": "localhost/mbf.mybrokerforex.com-********", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 8, "nb_templates": 8, "templates": [{"name": "admin.notification.edit", "param_count": null, "params": [], "start": **********.249597, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/notification/edit.blade.phpadmin.notification.edit", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fadmin%2Fnotification%2Fedit.blade.php&line=1", "ajax": false, "filename": "edit.blade.php", "line": "?"}}, {"name": "components.back", "param_count": null, "params": [], "start": **********.256153, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/components/back.blade.phpcomponents.back", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fcomponents%2Fback.blade.php&line=1", "ajax": false, "filename": "back.blade.php", "line": "?"}}, {"name": "admin.layouts.app", "param_count": null, "params": [], "start": **********.257205, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/app.blade.phpadmin.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fadmin%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "admin.partials.sidenav", "param_count": null, "params": [], "start": **********.293901, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/partials/sidenav.blade.phpadmin.partials.sidenav", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fadmin%2Fpartials%2Fsidenav.blade.php&line=1", "ajax": false, "filename": "sidenav.blade.php", "line": "?"}}, {"name": "admin.partials.topnav", "param_count": null, "params": [], "start": **********.308709, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/partials/topnav.blade.phpadmin.partials.topnav", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fadmin%2Fpartials%2Ftopnav.blade.php&line=1", "ajax": false, "filename": "topnav.blade.php", "line": "?"}}, {"name": "admin.partials.breadcrumb", "param_count": null, "params": [], "start": **********.311935, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/partials/breadcrumb.blade.phpadmin.partials.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fadmin%2Fpartials%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}}, {"name": "admin.layouts.master", "param_count": null, "params": [], "start": **********.312556, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/master.blade.phpadmin.layouts.master", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fadmin%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}}, {"name": "partials.notify", "param_count": null, "params": [], "start": **********.313477, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/partials/notify.blade.phppartials.notify", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fpartials%2Fnotify.blade.php&line=1", "ajax": false, "filename": "notify.blade.php", "line": "?"}}]}, "queries": {"count": 16, "nb_statements": 15, "nb_visible_statements": 16, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.04196, "accumulated_duration_str": "41.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "vendor/beyondcode/laravel-query-detector/src/QueryDetector.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetector.php", "line": 25}, {"index": 10, "namespace": null, "name": "vendor/beyondcode/laravel-query-detector/src/QueryDetectorMiddleware.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetectorMiddleware.php", "line": 30}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21}], "start": **********.182091, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "QueryDetector.php:25", "source": {"index": 9, "namespace": null, "name": "vendor/beyondcode/laravel-query-detector/src/QueryDetector.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetector.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fvendor%2Fbeyondcode%2Flaravel-query-detector%2Fsrc%2FQueryDetector.php&line=25", "ajax": false, "filename": "QueryDetector.php", "line": "25"}, "connection": "mbf-db", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Middleware\\RedirectIfNotAdmin.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.214874, "duration": 0.02419, "duration_str": "24.19ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mbf-db", "explain": null, "start_percent": 0, "width_percent": 57.65}, {"sql": "select * from `notification_templates` where `notification_templates`.`id` = '36' limit 1", "type": "query", "params": [], "bindings": ["36"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/NotificationController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\NotificationController.php", "line": 93}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.244128, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "NotificationController.php:93", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/NotificationController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\NotificationController.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FNotificationController.php&line=93", "ajax": false, "filename": "NotificationController.php", "line": "93"}, "connection": "mbf-db", "explain": null, "start_percent": 57.65, "width_percent": 0.81}, {"sql": "select count(*) as aggregate from `users` where `status` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 63}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/app.blade.php", "line": 4}], "start": **********.259968, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:63", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 63}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FProviders%2FAppServiceProvider.php&line=63", "ajax": false, "filename": "AppServiceProvider.php", "line": "63"}, "connection": "mbf-db", "explain": null, "start_percent": 58.46, "width_percent": 1.12}, {"sql": "select count(*) as aggregate from `users` where `ev` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 64}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/app.blade.php", "line": 4}], "start": **********.26243, "duration": 0.00259, "duration_str": "2.59ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:64", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FProviders%2FAppServiceProvider.php&line=64", "ajax": false, "filename": "AppServiceProvider.php", "line": "64"}, "connection": "mbf-db", "explain": null, "start_percent": 59.581, "width_percent": 6.173}, {"sql": "select count(*) as aggregate from `users` where `sv` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 65}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/app.blade.php", "line": 4}], "start": **********.266495, "duration": 0.00228, "duration_str": "2.28ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:65", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FProviders%2FAppServiceProvider.php&line=65", "ajax": false, "filename": "AppServiceProvider.php", "line": "65"}, "connection": "mbf-db", "explain": null, "start_percent": 65.753, "width_percent": 5.434}, {"sql": "select count(*) as aggregate from `users` where `kv` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/app.blade.php", "line": 4}], "start": **********.2702708, "duration": 0.0025099999999999996, "duration_str": "2.51ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:66", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FProviders%2FAppServiceProvider.php&line=66", "ajax": false, "filename": "AppServiceProvider.php", "line": "66"}, "connection": "mbf-db", "explain": null, "start_percent": 71.187, "width_percent": 5.982}, {"sql": "select count(*) as aggregate from `users` where `kv` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 67}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/app.blade.php", "line": 4}], "start": **********.27457, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:67", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 67}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FProviders%2FAppServiceProvider.php&line=67", "ajax": false, "filename": "AppServiceProvider.php", "line": "67"}, "connection": "mbf-db", "explain": null, "start_percent": 77.169, "width_percent": 8.77}, {"sql": "select count(*) as aggregate from `users` where `profile_request` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 68}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/app.blade.php", "line": 4}], "start": **********.2798378, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:68", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FProviders%2FAppServiceProvider.php&line=68", "ajax": false, "filename": "AppServiceProvider.php", "line": "68"}, "connection": "mbf-db", "explain": null, "start_percent": 85.939, "width_percent": 5.601}, {"sql": "select count(*) as aggregate from `support_tickets` where `status` in (0, 2)", "type": "query", "params": [], "bindings": [0, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 69}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/app.blade.php", "line": 4}], "start": **********.284125, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:69", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FProviders%2FAppServiceProvider.php&line=69", "ajax": false, "filename": "AppServiceProvider.php", "line": "69"}, "connection": "mbf-db", "explain": null, "start_percent": 91.54, "width_percent": 0.786}, {"sql": "select count(*) as aggregate from `deposits` where `method_code` >= 1000 and `status` = 2", "type": "query", "params": [], "bindings": [1000, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 70}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/app.blade.php", "line": 4}], "start": **********.286421, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:70", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FProviders%2FAppServiceProvider.php&line=70", "ajax": false, "filename": "AppServiceProvider.php", "line": "70"}, "connection": "mbf-db", "explain": null, "start_percent": 92.326, "width_percent": 0.834}, {"sql": "select count(*) as aggregate from `withdrawals` where `status` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 71}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/app.blade.php", "line": 4}], "start": **********.288776, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:71", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 71}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FProviders%2FAppServiceProvider.php&line=71", "ajax": false, "filename": "AppServiceProvider.php", "line": "71"}, "connection": "mbf-db", "explain": null, "start_percent": 93.16, "width_percent": 2.55}, {"sql": "select count(*) as aggregate from `p2p_trades` where `status` = 4", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/app.blade.php", "line": 4}], "start": **********.291955, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:72", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FProviders%2FAppServiceProvider.php&line=72", "ajax": false, "filename": "AppServiceProvider.php", "line": "72"}, "connection": "mbf-db", "explain": null, "start_percent": 95.71, "width_percent": 0.834}, {"sql": "select * from `admin_notifications` where `is_read` = 0 order by `id` desc limit 10", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 78}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}, {"index": 22, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/app.blade.php", "line": 5}], "start": **********.2996478, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:78", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 78}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FProviders%2FAppServiceProvider.php&line=78", "ajax": false, "filename": "AppServiceProvider.php", "line": "78"}, "connection": "mbf-db", "explain": null, "start_percent": 96.544, "width_percent": 1.001}, {"sql": "select * from `users` where `users`.`id` in (10860)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 78}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}, {"index": 27, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/app.blade.php", "line": 5}], "start": **********.3029778, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:78", "source": {"index": 20, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 78}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FProviders%2FAppServiceProvider.php&line=78", "ajax": false, "filename": "AppServiceProvider.php", "line": "78"}, "connection": "mbf-db", "explain": null, "start_percent": 97.545, "width_percent": 1.454}, {"sql": "select count(*) as aggregate from `admin_notifications` where `is_read` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 79}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/app.blade.php", "line": 5}], "start": **********.3067749, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:79", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FProviders%2FAppServiceProvider.php&line=79", "ajax": false, "filename": "AppServiceProvider.php", "line": "79"}, "connection": "mbf-db", "explain": null, "start_percent": 98.999, "width_percent": 1.001}]}, "models": {"data": {"App\\Models\\AdminNotification": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FModels%2FAdminNotification.php&line=1", "ajax": false, "filename": "AdminNotification.php", "line": "?"}}, "App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "App\\Models\\NotificationTemplate": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FModels%2FNotificationTemplate.php&line=1", "ajax": false, "filename": "NotificationTemplate.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 13, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/edit/36", "action_name": "admin.setting.notification.template.edit", "controller_action": "App\\Http\\Controllers\\Admin\\NotificationController@templateEdit", "uri": "GET admin/notification/template/edit/{id}", "controller": "App\\Http\\Controllers\\Admin\\NotificationController@templateEdit<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FNotificationController.php&line=91\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin/notification", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FNotificationController.php&line=91\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/NotificationController.php:91-96</a>", "middleware": "web, admin", "duration": "397ms", "peak_memory": "30MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-213850164 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-213850164\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1382047452 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1382047452\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-790240310 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"84 characters\">https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/edit/36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">en-US,en;q=0.9,ms-MY;q=0.8,ms;q=0.7,zh-CN;q=0.6,zh;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImdIeUYrNGJJWVZucXp2V0taRXU2OWc9PSIsInZhbHVlIjoiOERiVGN6QXdITXAwcWRNYTZkeEtnRTQwLzdvKyt3WHNDZzBDR3VSWEMycE9tUlA5ZEFnVGJmV2lIUTRlTTU2bmVrZnA2Tkt3cnFENVRKeElHdm9rSUs4UHhaVXdGbUlOV1g0Rlpob0U2QlBlWVpaUTFzazEyaytBRFN6eGJ0aWoiLCJtYWMiOiJhNDFlYjI2ZjM2MDk0N2VmOWEyOWNhY2MyYzNiZTRiYmE1MzA3ODdiM2EzM2M3NzAyMzVkNzUxOGU0NDVlZDliIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlVlT2xxMm96R01HblpIN2tJeDJEVHc9PSIsInZhbHVlIjoiR2hKSS94U2FVeHhXYUVhRmJBbzZHTVpnWSsrbHU4OStBOGcxWHk3aS9RZWRscG1xQWJKN2tCRzg4MlNmNTIxNnZmczkySDRHbkoyalFhSEl5Y0VLbVlCQlZ4RUFiaDBtS01XV1FSM3lZWGxidEUwcmswVVA3WXN3MnR4UUx6K0MiLCJtYWMiOiI4NDQyNGFiMmQ3YTE4YzM2MGFlZWI2MjM1OGUzN2I2ODMxM2I3ZTg4NTlmYzhjNTExMTRmYWMwYzA2NzI4ODk2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-790240310\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-753378055 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wXhpYNXjoV1G8hmyoOBJ5RaeqtV8Jn5rLg80GkJd</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">uJzjJvTPT467sZOGGHgaE9IvQKgJYoGbJCvpmHcv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-753378055\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-739190953 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 20:50:21 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-739190953\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1268755538 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wXhpYNXjoV1G8hmyoOBJ5RaeqtV8Jn5rLg80GkJd</span>\"\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"84 characters\">https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/edit/36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>flasher::envelopes</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1268755538\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/edit/36", "action_name": "admin.setting.notification.template.edit", "controller_action": "App\\Http\\Controllers\\Admin\\NotificationController@templateEdit"}, "badge": null}}