<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\MT5UserMigrationService;
use Illuminate\Support\Facades\Log;

class MigrateAllMT5Users extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mt5:migrate-all-users
                            {--clear : Clear existing users table before migration}
                            {--dry-run : Show what would be migrated without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate all MT5 users to local database with complete data sync';

    /**
     * Migration service
     */
    protected $migrationService;

    /**
     * Create a new command instance.
     */
    public function __construct(MT5UserMigrationService $migrationService)
    {
        parent::__construct();
        $this->migrationService = $migrationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Starting Complete MT5 Users Migration');
        $this->info('==========================================');

        $clear = $this->option('clear');
        $dryRun = $this->option('dry-run');

        if ($clear) {
            $this->warn('⚠️  WARNING: This will DELETE ALL existing users!');
            if (!$this->confirm('Are you sure you want to continue?')) {
                $this->info('Migration cancelled.');
                return 0;
            }
        }

        if ($dryRun) {
            $this->warn('🧪 DRY RUN MODE - No changes will be made');
            $this->simulateMigration();
            return 0;
        }

        try {
            $this->info("📊 Starting migration process...");
            
            if ($clear) {
                $this->warn("🗑️  Clearing existing users table...");
            }
            
            $startTime = microtime(true);
            
            // Run the migration
            $result = $this->migrationService->migrateAllMT5Users($clear);
            
            if ($result['success']) {
                $this->displayResults($result);
                
                // Setup referral relationships
                $this->info("🔗 Setting up referral relationships...");
                $referralCount = $this->migrationService->setupReferralRelationships();
                $this->info("✅ Set up {$referralCount} referral relationships");
                
            } else {
                $this->error("❌ Migration failed: " . $result['error']);
                return 1;
            }

            $endTime = microtime(true);
            $duration = round($endTime - $startTime, 2);
            
            $this->info("⏱️  Migration completed in {$duration} seconds");
            $this->info('🎉 MT5 users migration finished successfully!');

        } catch (\Exception $e) {
            $this->error('❌ Error during migration: ' . $e->getMessage());
            Log::error('MT5 users migration error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }

        return 0;
    }

    /**
     * Display migration results
     */
    private function displayResults($result)
    {
        $this->info("✅ Migration Results:");
        $this->line("   📊 Total Processed: {$result['total_processed']}");
        $this->line("   🆕 New Users Created: {$result['migrated']}");
        $this->line("   🔄 Existing Users Updated: {$result['updated']}");
        $this->line("   ❌ Errors: {$result['errors']}");
        
        if ($result['errors'] > 0) {
            $this->warn("⚠️  Some users had errors during migration. Check logs for details.");
        }
    }

    /**
     * Simulate migration for dry run mode
     */
    private function simulateMigration()
    {
        $this->info('🧪 Simulating MT5 users migration...');
        
        // Get count of MT5 users
        $mt5Count = \DB::connection('mbf-dbmt5')->table('mt5_users')->count();
        $ibCount = \DB::connection('mbf-dbmt5')->table('mt5_users')
            ->whereIn('Group', [
                'real\\Affiliates',
                'real\\IB\\IB MAIN', 
                'real\\IB\\IB SUB',
                'real\\Multi-IB\\Default',
                'real\\Multi-IB\\Level1',
                'real\\Multi-IB\\Level2',
                'real\\Multi-IB\\Level3',
                'real\\Multi-IB\\Level4',
                'real\\Multi-IB\\Level5'
            ])->count();
        
        $this->info("📊 Would process {$mt5Count} total MT5 users");
        $this->info("👥 Would identify {$ibCount} IB users");
        $this->info("🔍 Would process each user:");
        $this->line("   - Extract all MT5 data fields");
        $this->line("   - Determine IB status from group");
        $this->line("   - Create/update local user record");
        $this->line("   - Set up referral relationships");
        $this->line("   - Sync commission data");
        
        $this->warn('🧪 DRY RUN - No actual changes would be made');
    }
}
