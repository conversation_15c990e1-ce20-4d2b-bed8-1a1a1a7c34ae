<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>
    <rewrite>
      <rules>
        <!-- Python API Gateway rule - CRITICAL for MT5 integration -->
        <rule name="Python API Gateway" stopProcessing="true">
          <match url="^api/(.*)" ignoreCase="true" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
          </conditions>
          <action type="Rewrite" url="python/mt5manager.py/{R:1}" appendQueryString="true" />
        </rule>

        <!-- Main Rule - Fixed to use index.php instead of / -->
        <rule name="Main Rule" stopProcessing="true">
          <match url=".*" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
          </conditions>
          <action type="Rewrite" url="index.php" />
        </rule>
      </rules>
    </rewrite>
    <tracing>
      <traceFailedRequests>
        <clear />
      </traceFailedRequests>
    </tracing>
    <security>
      <requestFiltering>
        <requestLimits>
          <headerLimits>
            <add header="maxAllowedContentLength" sizeLimit="30000000" />
            <add header="maxUrl" sizeLimit="1000" />
            <add header="MaxFieldLength" sizeLimit="10000" />
          </headerLimits>
        </requestLimits>
      </requestFiltering>
    </security>
    <httpErrors errorMode="Detailed">
      <remove statusCode="400" />
      <error statusCode="400" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\bad_request.html" />
      <remove statusCode="401" />
      <error statusCode="401" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\unauthorized.html" />
      <remove statusCode="403" />
      <error statusCode="403" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\forbidden.html" />
      <remove statusCode="404" />
      <error statusCode="404" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\not_found.html" />
      <remove statusCode="405" />
      <error statusCode="405" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\method_not_allowed.html" />
      <remove statusCode="406" />
      <error statusCode="406" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\not_acceptable.html" />
      <remove statusCode="407" />
      <error statusCode="407" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\proxy_authentication_required.html" />
      <remove statusCode="412" />
      <error statusCode="412" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\precondition_failed.html" />
      <remove statusCode="414" />
      <error statusCode="414" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\request-uri_too_long.html" />
      <remove statusCode="415" />
      <error statusCode="415" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\unsupported_media_type.html" />
      <remove statusCode="500" />
      <error statusCode="500" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\internal_server_error.html" />
      <remove statusCode="501" />
      <error statusCode="501" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\not_implemented.html" />
      <remove statusCode="502" />
      <error statusCode="502" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\bad_gateway.html" />
      <remove statusCode="503" />
      <error statusCode="503" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\maintenance.html" />
    </httpErrors>
        <handlers accessPolicy="Read, Execute, Script">
            <remove name="PythonScript" />
            <!-- Python FastCGI Handler for MT5 API integration -->
            <add name="PythonScript" path="*.py" verb="*" modules="FastCgiModule" scriptProcessor="C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe|C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\wfastcgi.py" resourceType="File" requireAccess="Script" />
        </handlers>
  </system.webServer>
  <system.web>
    <compilation tempDirectory="C:\Inetpub\vhosts\mybrokerforex.com\tmp" />
  </system.web>

  <!-- Python WSGI Application Settings for MT5 API Integration -->
  <appSettings>
    <!-- PYTHONPATH: Root directory of Python project -->
    <add key="PYTHONPATH" value="C:\Inetpub\vhosts\mybrokerforex.com\mbf.mybrokerforex.com" />

    <!-- WSGI_HANDLER: Entry point for WSGI application -->
    <add key="WSGI_HANDLER" value="python.mt5manager.application" />

    <!-- WSGI_LOG: Log file for Python application debugging -->
    <add key="WSGI_LOG" value="C:\inetpub\vhosts\mybrokerforex.com\mbf.mybrokerforex.com\wfastcgi.log" />

    <!-- WSGI_DEBUG: Enable for debugging (remove in production) -->
    <!-- <add key="WSGI_DEBUG" value="1" /> -->
  </appSettings>

</configuration>
