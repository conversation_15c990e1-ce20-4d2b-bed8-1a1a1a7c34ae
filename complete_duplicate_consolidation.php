<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔧 COMPLETE DUPLICATE EMAIL CONSOLIDATION (ISSUE 5)\n";
echo "===================================================\n";

// Find ALL duplicate emails
echo "\n🔍 Finding ALL duplicate emails:\n";

$duplicates = \DB::select("
    SELECT email, COUNT(*) as count 
    FROM users 
    GROUP BY email 
    HAVING COUNT(*) > 1 
    ORDER BY count DESC
");

echo "Found " . count($duplicates) . " emails with duplicates:\n";

$totalDuplicateUsers = 0;
foreach ($duplicates as $duplicate) {
    $totalDuplicateUsers += $duplicate->count - 1; // Subtract 1 to keep one user per email
    echo "   - {$duplicate->email}: {$duplicate->count} accounts\n";
}

echo "Total duplicate users to consolidate: {$totalDuplicateUsers}\n";

// Process ALL duplicate emails
echo "\n🔧 Processing ALL duplicate emails:\n";

$processedEmails = 0;
$consolidatedAccounts = 0;
$preservedIBs = 0;
$deletedUsers = 0;

foreach ($duplicates as $duplicate) {
    $email = $duplicate->email;
    echo "\n📧 Processing: {$email} ({$duplicate->count} accounts)\n";
    
    // Get all users with this email, ordered by priority
    $users = \DB::select("
        SELECT id, email, created_at, partner, ib_status, ib_type, kv, ev, sv, 
               mt5_login, mt5_balance, mt5_equity, referral_code, ib_approved_at
        FROM users 
        WHERE email = ? 
        ORDER BY 
            CASE WHEN partner = 1 THEN 1 ELSE 2 END,  -- Approved IBs first
            created_at DESC                            -- Then newest first
    ", [$email]);
    
    if (count($users) <= 1) {
        echo "   ⚠️ No duplicates found, skipping\n";
        continue;
    }
    
    $keepUser = $users[0]; // Keep the highest priority user
    $duplicateUsers = array_slice($users, 1); // Remove the rest
    
    echo "   ✅ Keeping user ID: {$keepUser->id} (created: {$keepUser->created_at})\n";
    echo "      - Partner: {$keepUser->partner}, IB Status: {$keepUser->ib_status}\n";
    
    // Check if any duplicate is an approved IB
    $hasApprovedIB = false;
    $ibData = null;
    
    foreach ($users as $user) {
        if ($user->partner == 1 && $user->ib_status == 1) {
            $hasApprovedIB = true;
            if ($user->id == $keepUser->id) {
                echo "      - ✅ Kept user is already an approved IB\n";
            } else {
                echo "      - 🔄 Found approved IB in duplicates, transferring IB status\n";
                $ibData = $user;
            }
            break;
        }
    }
    
    // If we found an approved IB in duplicates, transfer IB status to kept user
    if ($ibData && $ibData->id != $keepUser->id) {
        \DB::update("
            UPDATE users 
            SET partner = ?, ib_status = ?, ib_type = ?, referral_code = ?, ib_approved_at = ?
            WHERE id = ?
        ", [
            $ibData->partner,
            $ibData->ib_status, 
            $ibData->ib_type,
            $ibData->referral_code,
            $ibData->ib_approved_at,
            $keepUser->id
        ]);
        
        echo "      - ✅ Transferred IB status to kept user\n";
        $preservedIBs++;
    }
    
    // Transfer MT5 accounts to the kept user
    foreach ($duplicateUsers as $dupUser) {
        $transferredAccounts = \DB::update("
            UPDATE user_accounts 
            SET User_Id = ? 
            WHERE User_Id = ?
        ", [$keepUser->id, $dupUser->id]);
        
        if ($transferredAccounts > 0) {
            echo "      - 📦 Transferred {$transferredAccounts} MT5 accounts from user {$dupUser->id}\n";
            $consolidatedAccounts += $transferredAccounts;
        }
        
        // Update referral relationships
        $referralUpdates = \DB::update("
            UPDATE users 
            SET ref_by = ? 
            WHERE ref_by = ?
        ", [$keepUser->id, $dupUser->id]);
        
        if ($referralUpdates > 0) {
            echo "      - 🔗 Updated {$referralUpdates} referral relationships\n";
        }
        
        // Update commission records
        $commissionUpdates = \DB::update("
            UPDATE ib_commissions 
            SET to_ib_user_id = ? 
            WHERE to_ib_user_id = ?
        ", [$keepUser->id, $dupUser->id]);
        
        if ($commissionUpdates > 0) {
            echo "      - 💰 Updated {$commissionUpdates} commission records\n";
        }
        
        // Delete the duplicate user
        \DB::delete("DELETE FROM users WHERE id = ?", [$dupUser->id]);
        echo "      - 🗑️ Deleted duplicate user ID: {$dupUser->id}\n";
        $deletedUsers++;
    }
    
    $processedEmails++;
}

// Verify the consolidation
echo "\n🔍 Verification - Checking remaining duplicates:\n";

$remainingDuplicates = \DB::select("
    SELECT email, COUNT(*) as count 
    FROM users 
    GROUP BY email 
    HAVING COUNT(*) > 1 
    ORDER BY count DESC
    LIMIT 10
");

if (count($remainingDuplicates) > 0) {
    echo "Remaining duplicates:\n";
    foreach ($remainingDuplicates as $duplicate) {
        echo "   - {$duplicate->email}: {$duplicate->count} accounts\n";
    }
} else {
    echo "✅ No duplicates found! All emails consolidated successfully!\n";
}

// Check specific examples
echo "\n🔍 Checking specific examples:\n";

$testEmails = ['<EMAIL>', '<EMAIL>'];
foreach ($testEmails as $testEmail) {
    $user = \App\Models\User::where('email', $testEmail)->first();
    if ($user) {
        $mt5Count = \DB::scalar("SELECT COUNT(*) FROM user_accounts WHERE User_Id = ?", [$user->id]);
        $isIB = $user->isIb() ? 'YES' : 'NO';
        echo "   - {$testEmail}: 1 user, {$mt5Count} MT5 accounts, IB: {$isIB}\n";
        
        if ($user->isIb()) {
            echo "     - Partner: {$user->partner}, IB Status: {$user->ib_status}, Type: {$user->ib_type}\n";
        }
    } else {
        echo "   - {$testEmail}: Not found\n";
    }
}

// Summary
echo "\n📊 COMPLETE CONSOLIDATION SUMMARY:\n";
echo "==================================\n";
echo "   - Processed emails: {$processedEmails}\n";
echo "   - Consolidated MT5 accounts: {$consolidatedAccounts}\n";
echo "   - Preserved IB statuses: {$preservedIBs}\n";
echo "   - Deleted duplicate users: {$deletedUsers}\n";
echo "   - Remaining duplicates: " . count($remainingDuplicates) . "\n";

// Final verification
echo "\n🎯 FINAL VERIFICATION:\n";
echo "======================\n";

$totalUsers = \App\Models\User::count();
$uniqueEmails = \DB::scalar("SELECT COUNT(DISTINCT email) FROM users");
$approvedIBs = \App\Models\User::where('partner', 1)->where('ib_status', 1)->count();

echo "   - Total users: " . number_format($totalUsers) . "\n";
echo "   - Unique emails: " . number_format($uniqueEmails) . "\n";
echo "   - Email uniqueness: " . ($totalUsers == $uniqueEmails ? '✅ PERFECT' : '❌ DUPLICATES REMAIN') . "\n";
echo "   - Approved IBs: {$approvedIBs}\n";

// Test IB functionality
echo "\n🧪 Testing IB functionality:\n";
$testIBs = \App\Models\User::where('partner', 1)->where('ib_status', 1)->take(3)->get();
foreach ($testIBs as $ib) {
    $canAccess = $ib->isIb() ? 'YES' : 'NO';
    echo "   - {$ib->email}: Partnership access = {$canAccess}\n";
}

echo "\n✅ COMPLETE DUPLICATE EMAIL CONSOLIDATION FINISHED!\n";
echo "\n🎯 NEXT STEPS:\n";
echo "1. Test admin user list: /admin/users/all\n";
echo "2. Verify no duplicate emails in list\n";
echo "3. Test IB users can access partnership dashboard\n";
echo "4. Verify MT5 accounts show in hover tooltips\n";
echo "5. Test network visualization shows complete hierarchy\n";
