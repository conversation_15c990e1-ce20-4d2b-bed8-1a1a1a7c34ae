<?php

/**
 * Find a user with both MT5 accounts and Laravel wallets
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Finding User with Both MT5 Accounts and Wallets ===\n\n";

// First, find users who have wallets
$usersWithWallets = \App\Models\User::whereHas('wallets')->take(20)->get();

echo "Users with wallets:\n";
foreach ($usersWithWallets as $user) {
    echo "User: {$user->email} (ID: {$user->id})\n";
    
    // Check if this user has MT5 accounts
    $mt5Accounts = \App\Models\Mt5Users::getAccounts($user->email);
    
    if ($mt5Accounts->count() > 0) {
        echo "  ✅ Has {$mt5Accounts->count()} MT5 accounts:\n";
        foreach ($mt5Accounts as $account) {
            echo "    Login: {$account->Login} | Group: {$account->Group} | Balance: \${$account->Balance}\n";
        }
        
        // Check wallets
        $wallets = $user->wallets;
        echo "  💰 Has {$wallets->count()} wallets:\n";
        foreach ($wallets as $wallet) {
            echo "    {$wallet->currency->symbol} Wallet | Balance: \${$wallet->balance}\n";
        }
        
        echo "  🎯 This user is perfect for testing!\n";
        echo "  Testing MT5 account filtering...\n";
        
        // Test real account filtering
        $realAccounts = $mt5Accounts->filter(function ($account) {
            $isReal = stripos($account->Group, 'real') !== false || 
                     stripos($account->Group, 'Real') !== false ||
                     (stripos($account->Group, 'demo') === false && stripos($account->Group, 'Demo') === false);
            return $isReal;
        });
        
        echo "  Real accounts: {$realAccounts->count()}\n";
        foreach ($realAccounts as $account) {
            echo "    Real: {$account->Login} | {$account->Group} | \${$account->Balance}\n";
        }
        
        break; // Found a suitable user
    } else {
        echo "  ❌ No MT5 accounts\n";
    }
    echo "\n";
}

echo "\n=== Search Complete ===\n";
