@echo off
echo ========================================
echo  MBF MyBrokerForex - Plesk Deployment
echo ========================================
echo.

REM Step 1: Update Composer Dependencies
echo [1/8] Updating Composer dependencies...
composer update --no-dev --optimize-autoloader
if %errorlevel% neq 0 (
    echo ERROR: Composer update failed!
    pause
    exit /b 1
)
echo ✓ Composer dependencies updated successfully
echo.

REM Step 2: Clear all Laravel caches
echo [2/8] Clearing Laravel caches...
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan optimize:clear
echo ✓ All caches cleared
echo.

REM Step 3: Set up storage permissions
echo [3/8] Setting up storage permissions...
if exist "storage" (
    echo Setting storage folder permissions...
    icacls storage /grant Everyone:(OI)(CI)F /T
    echo ✓ Storage permissions set
) else (
    echo WARNING: Storage folder not found!
)
echo.

REM Step 4: Set up bootstrap/cache permissions
echo [4/8] Setting up bootstrap cache permissions...
if exist "bootstrap\cache" (
    echo Setting bootstrap/cache folder permissions...
    icacls "bootstrap\cache" /grant Everyone:(OI)(CI)F /T
    echo ✓ Bootstrap cache permissions set
) else (
    echo WARNING: Bootstrap cache folder not found!
)
echo.

REM Step 5: Check Python installation
echo [5/8] Checking Python installation...
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Python is installed and accessible
    python --version
) else (
    echo WARNING: Python not found in PATH!
    echo Please install Python and add it to PATH
)
echo.

REM Step 6: Check MT5 Python script
echo [6/8] Checking MT5 Python script...
if exist "python\mt5manager.py" (
    echo ✓ MT5 Python script found
    echo Setting executable permissions...
    icacls "python\mt5manager.py" /grant Everyone:(RX)
) else (
    echo WARNING: MT5 Python script not found at python\mt5manager.py
)
echo.

REM Step 7: Test Python script execution
echo [7/8] Testing Python script execution...
if exist "python\mt5manager.py" (
    echo Testing Python script...
    python python\mt5manager.py --help >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✓ Python script executes successfully
    ) else (
        echo WARNING: Python script execution failed
        echo Check Python dependencies and script permissions
    )
) else (
    echo SKIPPED: Python script not found
)
echo.

REM Step 8: Generate optimized autoloader
echo [8/8] Generating optimized autoloader...
composer dump-autoload --optimize
echo ✓ Autoloader optimized
echo.

echo ========================================
echo  Deployment Complete!
echo ========================================
echo.
echo Next Steps:
echo 1. Update your .env file with live server settings
echo 2. Run the diagnostic scripts:
echo    - https://yourdomain.com/debug_live_server.php
echo    - https://yourdomain.com/debug_mt5_api.php
echo 3. Test your application functionality
echo 4. Remove diagnostic scripts when done
echo.
echo IMPORTANT: Make sure to:
echo - Enable exec(), shell_exec() in PHP settings
echo - Install Python with MetaTrader5 package
echo - Set correct file permissions in Plesk
echo - Update .env with correct paths
echo.
pause
