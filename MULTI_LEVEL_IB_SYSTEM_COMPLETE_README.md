# Multi-Level IB (Introducing Broker) System - Complete Documentation

## Table of Contents
1. [System Overview](#system-overview)
2. [Multi-Level IB Structure & Workflows](#multi-level-ib-structure--workflows)
3. [Real-Time Balance Update System](#real-time-balance-update-system)
4. [Complete Admin Interface Guide](#complete-admin-interface-guide)
5. [Technical Implementation Details](#technical-implementation-details)
6. [Commission Calculation Examples](#commission-calculation-examples)
7. [Database Schema](#database-schema)
8. [API Integration](#api-integration)
9. [Troubleshooting](#troubleshooting)

---

## System Overview

The Multi-Level IB System is a comprehensive introducing broker management platform that enables hierarchical commission distribution across multiple levels of IBs. The system integrates real-time MT5 balance updates, automated commission processing, and comprehensive admin management tools.

### Key Features
- **Multi-Level Hierarchy**: Master IB → Sub-IB → Level 3 Sub-IB → Clients
- **Real-Time Commission Processing**: Immediate MT5 balance updates upon trade execution
- **Automated Approval System**: Streamlined commission approval with MT5 integration
- **Comprehensive Admin Interface**: Complete management tools for IB operations
- **Live Balance Synchronization**: Real-time sync between CRM and MT5 server balances

### Commission Structure
- **Master IB**: 50% commission rate
- **Sub-IB**: 30% commission rate  
- **Level 3 Sub-IB**: 20% commission rate

---

## Multi-Level IB Structure & Workflows

### 1. Master IB Creation Scenario

#### Step-by-Step Process:
```
User Registration → KYC Verification → IB Application → Admin Approval → Master IB Status
```

**Detailed Workflow:**

1. **User Registration**
   - User creates account with email/phone verification
   - Profile completion with personal information
   - MT5 account creation and linking

2. **KYC Verification**
   - Document upload (ID, proof of address)
   - Admin review and verification
   - KYC status: Pending → Approved/Rejected

3. **IB Application**
   - User submits IB application from dashboard
   - Application includes business information and experience
   - Status: Pending → Under Review

4. **Admin Approval**
   - Admin reviews application in `/admin/ib/pending`
   - Approval creates Master IB with `ib_type = 'master'`
   - User receives notification and access to IB features

5. **Master IB Operations**
   - Generate referral links for client acquisition
   - Direct client invitations and management
   - Commission earning from direct client trades

#### Commission Distribution (Master IB → Clients):
```
Client Trade Profit: $10
Master IB Commission: $10 × 50% = $5.00
```

### 2. Sub-IB Creation Scenario

#### Step-by-Step Process:
```
Master IB Invites Clients → Client Applies for Sub-IB → Master IB Approves → Sub-IB Status
```

**Detailed Workflow:**

1. **Client Acquisition**
   - Master IB shares referral link with potential clients
   - Clients register using Master IB's referral code
   - Clients complete KYC and start trading

2. **Sub-IB Application**
   - Existing client applies to become Sub-IB
   - Application submitted through user dashboard
   - Master IB receives notification for approval

3. **Master IB Approval**
   - Master IB reviews Sub-IB application
   - Approval in `/user/partnership/network` interface
   - Client upgraded to Sub-IB status with `ib_type = 'sub'`

4. **Sub-IB Operations**
   - Sub-IB can now invite their own clients
   - Generate independent referral links
   - Earn commissions from their client trades

#### Commission Distribution (Master IB → Sub-IB → Clients):
```
Sub-IB's Client Trade Profit: $10
Sub-IB Commission: $10 × 30% = $3.00
Master IB Commission: $10 × 50% = $5.00
Total Commission Distributed: $8.00
```

### 3. Multi-Level Sub-IB Scenario

#### Step-by-Step Process:
```
Sub-IB Invites Clients → Client Becomes Level 3 Sub-IB → Level 3 Invites Clients
```

**Detailed Workflow:**

1. **Level 3 Sub-IB Creation**
   - Sub-IB's client applies for IB status
   - Sub-IB approves the application
   - Client becomes Level 3 Sub-IB with `ib_type = 'sub'` and `level = 3`

2. **Level 3 Operations**
   - Level 3 Sub-IB invites their own clients
   - Independent client management and trading

#### Commission Distribution (3-Level Hierarchy):
```
Level 3's Client Trade Profit: $10
Level 3 Sub-IB Commission: $10 × 20% = $2.00
Sub-IB Commission: $10 × 30% = $3.00
Master IB Commission: $10 × 50% = $5.00
Total Commission Distributed: $10.00
```

---

## Real-Time Balance Update System

### Technical Architecture

The real-time balance update system ensures immediate commission crediting to IB MT5 accounts upon trade execution.

#### System Components:

1. **Trade Detection**
   - Monitor `mbf-dbmt5.mt5_deals_2025` table for new trades
   - Real-time processing of profitable trades
   - Automatic commission calculation based on IB hierarchy

2. **Commission Processing Service**
   ```php
   // MultiLevelIbCommissionService.php
   public function processMultiLevelCommission($tradeData)
   {
       // 1. Validate trade data and check for duplicates
       // 2. Find trader and build IB hierarchy
       // 3. Calculate and distribute commissions
       // 4. Update MT5 balances in real-time
       // 5. Create commission records with 'paid' status
   }
   ```

3. **MT5 Integration Workflow**
   ```
   Trade Execution → Commission Calculation → Python MT5 Script → Balance Update → CRM Sync
   ```

#### Real-Time Update Process:

1. **Trade Detection**
   - New trade detected in MT5 deals table
   - Trade data extracted: `deal_id`, `mt5_login`, `profit`, `symbol`, `volume`

2. **Commission Calculation**
   - Build IB hierarchy from trader upwards
   - Calculate commission for each level based on rates
   - Create commission records in `ib_commissions` table

3. **MT5 Balance Update**
   ```python
   # python/mt5manager.py add_balance command
   python mt5manager.py add_balance --login 878046 --amount 5.00 --comment "Commission - Deal 12345"
   ```

4. **Local Database Sync**
   - Update `users.mt5_balance` field
   - Update `users.commission_earnings` field
   - Sync commission status to 'paid'

5. **Real-Time Display Updates**
   - Admin interfaces show updated balances immediately
   - User dashboard reflects new commission earnings
   - Partnership network displays current balances

### Balance Synchronization

#### Data Flow:
```
MT5 Server ←→ Python Integration ←→ Local CRM Database ←→ Web Interfaces
```

#### Synchronization Points:
- **Real-time**: Commission processing and approval
- **Scheduled**: Hourly MT5 user data sync
- **Manual**: Admin-triggered balance refresh

---

## Complete Admin Interface Guide

### IB Management Pages

#### 1. Manage IB (`/admin/ib/manage`)
**Purpose**: Central hub for all IB account management

**Features:**
- Complete list of all IB accounts (Master, Sub, Level 3)
- IB status indicators: Active, Suspended, Pending
- Quick actions: View details, Edit, Suspend/Activate
- Search and filter by IB type, status, registration date
- Bulk operations for multiple IB accounts

**Key Metrics Displayed:**
- Total IBs by type (Master/Sub/Level 3)
- Active vs Inactive IBs
- Recent IB registrations
- Commission earnings summary

#### 2. Pending IB (`/admin/ib/pending`)
**Purpose**: Review and approve IB applications

**Features:**
- List of all pending IB applications
- Application details: Personal info, business experience, documents
- One-click approval/rejection with comments
- Bulk approval for multiple applications
- Application history and notes

**Approval Process:**
1. Review application details and documents
2. Verify KYC status and trading history
3. Approve/Reject with admin comments
4. Automatic notification to applicant
5. IB account creation upon approval

#### 3. Approved IB (`/admin/ib/approved`)
**Purpose**: Manage active IB accounts and monitor performance

**Features:**
- List of all approved and active IBs
- Performance metrics: Client count, trading volume, commissions earned
- IB hierarchy visualization
- Commission payment history
- Account management tools

**Performance Metrics:**
- Total clients referred
- Monthly trading volume
- Commission earnings (current month/total)
- Client retention rate
- Network growth statistics

#### 4. Rejected IB (`/admin/ib/rejected`)
**Purpose**: Track rejected applications and manage appeals

**Features:**
- List of rejected IB applications
- Rejection reasons and admin comments
- Appeal management system
- Re-application tracking
- Statistical analysis of rejection patterns

#### 5. All IB Logs (`/admin/ib/logs`)
**Purpose**: Comprehensive audit trail for IB activities

**Features:**
- Complete activity log for all IB operations
- Log types: Registration, Approval, Commission, Client Activity
- Advanced filtering by date, IB, activity type
- Export functionality for compliance reporting
- Real-time log updates

**Log Categories:**
- **Registration Logs**: New IB applications and approvals
- **Commission Logs**: Commission calculations and payments
- **Client Logs**: Client referrals and trading activities
- **System Logs**: Balance updates and technical operations

#### 6. IB Application (`/admin/ib/application`)
**Purpose**: Detailed application review interface

**Features:**
- Comprehensive application review form
- Document verification tools
- KYC status integration
- Trading history analysis
- Decision workflow with approval levels

#### 7. IB Resources (`/admin/ib/resources`)
**Purpose**: Manage IB marketing materials and resources

**Features:**
- Upload and manage marketing materials
- IB training resources and documentation
- Commission rate sheets and terms
- Promotional banner management
- Resource access tracking

### Commission Management Pages

#### 1. Comm Management (`/admin/commissions`)
**Purpose**: Central commission system administration

**Features:**
- Real-time commission processing dashboard
- Commission approval workflow
- Bulk commission operations
- Commission rate management
- Payment processing integration

**Dashboard Metrics:**
- Total commissions processed today/month
- Pending commission approvals
- Commission payment status
- Top performing IBs
- Commission trend analysis

#### 2. Commission Overview (`/admin/commissions/overview`)
**Purpose**: System-wide commission analytics and reporting

**Features:**
- Comprehensive commission statistics
- Revenue analysis and forecasting
- IB performance comparisons
- Commission trend visualization
- Export capabilities for financial reporting

**Key Reports:**
- Monthly commission summary
- IB performance rankings
- Commission distribution analysis
- Payment processing reports
- Profitability analysis

#### 3. Pending Commissions (`/admin/commissions/pending`)
**Purpose**: Review and approve pending commission payments

**Features:**
- List of all pending commission payments
- Commission verification and validation
- Bulk approval with MT5 balance updates
- Payment scheduling and processing
- Commission dispute resolution

**Approval Workflow:**
1. Review commission calculations
2. Verify trade data and IB hierarchy
3. Approve commission with MT5 balance update
4. Process payment to IB account
5. Update commission status to 'paid'

### Partnership System Pages

#### 1. Partnership (`/admin/partnership`)
**Purpose**: Main partnership system dashboard

**Features:**
- Partnership program overview
- IB recruitment metrics
- Network growth visualization
- Partnership performance analytics
- System configuration access

#### 2. Manage Levels (`/admin/partnership/levels`)
**Purpose**: Configure IB level hierarchy and permissions

**Features:**
- Define IB levels (Master, Sub, Level 3, etc.)
- Set level-specific permissions and capabilities
- Configure level advancement criteria
- Manage level-based commission rates
- Level transition workflows

#### 3. Multi IB Levels (`/admin/partnership/multi-levels`)
**Purpose**: Advanced multi-level commission structure setup

**Features:**
- Configure complex commission hierarchies
- Set up cascading commission rules
- Define level-specific commission rates
- Manage commission inheritance rules
- Advanced commission calculation settings

#### 4. Symbols (`/admin/partnership/symbols`)
**Purpose**: Trading symbol configuration for commission calculation

**Features:**
- Manage tradeable symbols and instruments
- Set symbol-specific commission rates
- Configure symbol groups and categories
- Symbol-based commission rules
- Market-specific commission settings

#### 5. Symbol Groups (`/admin/partnership/symbol-groups`)
**Purpose**: Group symbols for simplified commission management

**Features:**
- Create and manage symbol groups (Forex, Commodities, Indices)
- Assign commission rates to symbol groups
- Group-based commission calculations
- Bulk symbol management
- Commission rule inheritance

#### 6. Rebate Rules (`/admin/partnership/rebate-rules`)
**Purpose**: Define commission calculation rules and rebate structures

**Features:**
- Create complex commission calculation rules
- Volume-based commission tiers
- Time-based commission adjustments
- Performance-based rebate systems
- Rule priority and conflict resolution

#### 7. IB Groups (`/admin/partnership/ib-groups`)
**Purpose**: Categorize and manage IB accounts in groups

**Features:**
- Create IB groups based on performance, region, or type
- Group-specific commission rates and rules
- Bulk operations for IB groups
- Group-based reporting and analytics
- Access control and permissions management

---

## Technical Implementation Details

### Database Schema

#### Core Tables:

**1. users table**
```sql
-- IB-specific fields
ib_status: TINYINT (0=Not IB, 1=Active IB, 2=Pending, 3=Rejected)
ib_type: ENUM('master', 'sub') 
ib_parent_id: INT (References parent IB)
commission_earnings: DECIMAL(15,2)
mt5_balance: DECIMAL(15,2)
ref_by: INT (Referral parent)
```

**2. ib_commissions table**
```sql
id: PRIMARY KEY
from_user_id: INT (Client who generated commission)
to_ib_user_id: INT (IB receiving commission)
trade_id: VARCHAR(255) (Unique trade identifier)
mt5_deal_id: BIGINT (MT5 deal ID)
commission_amount: DECIMAL(15,2)
commission_rate: DECIMAL(5,2)
level: INT (IB level in hierarchy)
status: ENUM('pending', 'paid', 'cancelled')
created_at: TIMESTAMP
```

**3. MT5 Integration Tables**
```sql
-- External MT5 database (mbf-dbmt5)
mt5_users: User account data and balances
mt5_deals_2025: Trade execution data
```

### API Integration

#### Commission Processing API
```php
// Real-time commission processing
POST /api/commissions/process
{
    "deal_id": "12345",
    "mt5_login": "878012", 
    "symbol": "EURUSD",
    "volume": 1.0,
    "profit": 10.00
}
```

#### MT5 Balance Update API
```python
# Python MT5 integration
python mt5manager.py add_balance --login 878046 --amount 5.00 --comment "Commission"
```

### Performance Optimization

#### Database Optimization:
- Indexed foreign keys for fast hierarchy traversal
- Optimized queries with eager loading
- Commission calculation caching
- Real-time balance update queuing

#### Real-Time Updates:
- WebSocket integration for live balance updates
- Efficient commission calculation algorithms
- Batch processing for multiple commissions
- Error handling and retry mechanisms

---

## Commission Calculation Examples

### Example 1: Master IB Direct Client
```
Client Trade: $10 profit on EURUSD
Commission Rate: 50% (Master IB)
Commission Calculation: $10 × 0.50 = $5.00
Distribution: Master IB receives $5.00
```

### Example 2: Two-Level Hierarchy
```
Sub-IB's Client Trade: $10 profit on GBPUSD
Level 1 (Sub-IB): $10 × 0.30 = $3.00
Level 2 (Master IB): $10 × 0.50 = $5.00
Total Distributed: $8.00
```

### Example 3: Three-Level Hierarchy
```
Level 3 Sub-IB's Client Trade: $10 profit on GOLD
Level 1 (Level 3 Sub-IB): $10 × 0.20 = $2.00
Level 2 (Sub-IB): $10 × 0.30 = $3.00  
Level 3 (Master IB): $10 × 0.50 = $5.00
Total Distributed: $10.00
```

---

## Troubleshooting

### Common Issues and Solutions

#### 1. Commission Not Processing
**Symptoms**: Trades not generating commissions
**Solutions**:
- Check IB hierarchy relationships
- Verify trade data in mt5_deals_2025
- Confirm IB status is active
- Review commission calculation logs

#### 2. MT5 Balance Update Failures  
**Symptoms**: Commissions calculated but MT5 balance not updated
**Solutions**:
- Verify Python script path in .env
- Check MT5 server connection
- Review Python execution logs
- Confirm MT5 account permissions

#### 3. Duplicate Commission Processing
**Symptoms**: Same trade generating multiple commissions
**Solutions**:
- Check duplicate prevention logic
- Verify trade_id uniqueness
- Review commission processing logs
- Clear duplicate commission records

### System Monitoring

#### Key Metrics to Monitor:
- Commission processing success rate
- MT5 balance update success rate
- Average commission processing time
- IB hierarchy integrity
- Database performance metrics

#### Log Files:
- `/storage/logs/commission.log`: Commission processing logs
- `/storage/logs/mt5_integration.log`: MT5 balance update logs
- `/storage/logs/ib_system.log`: General IB system logs

### Error Handling and Recovery

#### Automatic Recovery Mechanisms:
- **Commission Retry Logic**: Failed commission processing automatically retries up to 3 times
- **MT5 Connection Failover**: Automatic reconnection to MT5 server on connection loss
- **Balance Sync Recovery**: Periodic balance reconciliation between CRM and MT5
- **Transaction Rollback**: Failed operations rollback to maintain data integrity

#### Manual Recovery Procedures:
```php
// Force reprocess commission for specific deal
php artisan commission:reprocess --deal-id=12345

// Sync MT5 balances manually
php artisan mt5:sync-balances --user-id=10921

// Rebuild IB hierarchy cache
php artisan ib:rebuild-hierarchy
```

---

## User Interface Workflows

### IB User Dashboard Workflows

#### 1. Master IB Dashboard (`/user/partnership/network`)
**Features Available:**
- **Network Overview**: Visual hierarchy of referred clients and Sub-IBs
- **Commission Earnings**: Real-time commission statistics and history
- **Client Management**: Direct client invitation and management tools
- **Sub-IB Approval**: Review and approve Sub-IB applications from clients
- **Referral Tools**: Generate and manage referral links
- **Performance Analytics**: Trading volume and commission trends

**Key Actions:**
- Invite new clients via referral links
- Approve/reject Sub-IB applications
- Monitor client trading activity
- Track commission earnings in real-time
- Download commission reports

#### 2. Sub-IB Dashboard
**Features Available:**
- **Parent IB Information**: Details of referring Master IB
- **Own Client Network**: Clients referred by this Sub-IB
- **Commission Breakdown**: Earnings from own clients vs parent commissions
- **Client Invitation Tools**: Independent referral system
- **Performance Metrics**: Sub-IB specific analytics

#### 3. Client Dashboard (Non-IB Users)
**Features Available:**
- **IB Application**: Apply to become Sub-IB under current referrer
- **Referrer Information**: Details of referring IB
- **Trading History**: Personal trading activity and performance
- **Account Management**: Standard user account features

### Admin Workflow Examples

#### IB Approval Workflow:
```
1. User submits IB application
2. Admin receives notification in /admin/ib/pending
3. Admin reviews application details and documents
4. Admin checks KYC status and trading history
5. Admin approves/rejects with comments
6. System automatically:
   - Updates user ib_status
   - Sends notification to user
   - Creates IB account permissions
   - Enables IB dashboard features
```

#### Commission Approval Workflow:
```
1. Trade executed by client
2. System calculates commissions for IB hierarchy
3. Commissions created with 'pending' status
4. Admin reviews in /admin/commissions/pending
5. Admin approves commission batch
6. System automatically:
   - Updates MT5 balances via Python integration
   - Marks commissions as 'paid'
   - Updates local database balances
   - Sends payment notifications
```

---

## Advanced Configuration

### Commission Rate Configuration

#### Global Commission Rates:
```php
// config/ib.php
'commission_rates' => [
    'master' => 50.00,  // 50% for Master IBs
    'sub' => 30.00,     // 30% for Sub-IBs
    'level_3' => 20.00, // 20% for Level 3 Sub-IBs
]
```

#### Symbol-Specific Rates:
```php
'symbol_rates' => [
    'EURUSD' => ['master' => 45.00, 'sub' => 25.00],
    'GBPUSD' => ['master' => 40.00, 'sub' => 20.00],
    'GOLD' => ['master' => 60.00, 'sub' => 35.00],
]
```

#### Volume-Based Commission Tiers:
```php
'volume_tiers' => [
    'tier_1' => ['min_volume' => 0, 'max_volume' => 10, 'multiplier' => 1.0],
    'tier_2' => ['min_volume' => 10, 'max_volume' => 50, 'multiplier' => 1.2],
    'tier_3' => ['min_volume' => 50, 'max_volume' => null, 'multiplier' => 1.5],
]
```

### MT5 Integration Configuration

#### Environment Variables:
```env
# MT5 Python Integration
PYTHON_EXE="C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe"
PYTHON_SCRIPT="C:\xampp\htdocs\project\python\mt5manager.py"

# MT5 Database Connection
MT5_DB_HOST=**************
MT5_DB_DATABASE=mbf-dbmt5
MT5_DB_USERNAME=mt5_user
MT5_DB_PASSWORD=mt5_password

# MT5 Server Settings
MT5_SERVER=**************:443
MT5_LOGIN=10007
MT5_PASSWORD=TfTe*wA1
```

#### Python Script Configuration:
```python
# python/mt5manager.py configuration
DEFAULT_SERVER = "**************:443"
SERVER_LOGIN = 10007
SERVER_PASSWORD = "TfTe*wA1"
DEFAULT_GROUP = "demo\\MBFX\\PREMIUM_200_USD_B"
```

---

## Security and Compliance

### Security Measures

#### Data Protection:
- **Encrypted Database Connections**: All database communications encrypted
- **Secure API Endpoints**: Authentication required for all commission operations
- **Audit Logging**: Complete audit trail for all IB and commission activities
- **Access Control**: Role-based permissions for admin and IB users
- **Data Validation**: Input validation and sanitization for all user inputs

#### Financial Security:
- **Commission Verification**: Multi-step verification before commission payments
- **Balance Reconciliation**: Regular reconciliation between CRM and MT5 balances
- **Transaction Logging**: Complete transaction history with timestamps
- **Fraud Detection**: Automated detection of suspicious commission patterns
- **Backup Systems**: Regular backups of commission and balance data

### Compliance Features

#### Regulatory Compliance:
- **KYC Integration**: Know Your Customer verification for all IBs
- **AML Monitoring**: Anti-Money Laundering checks for large commissions
- **Reporting Tools**: Compliance reporting for regulatory requirements
- **Document Management**: Secure storage of IB documents and agreements
- **Audit Trails**: Complete audit trails for compliance reviews

#### Financial Reporting:
- **Commission Reports**: Detailed commission reports for tax purposes
- **Payment Records**: Complete payment history and documentation
- **Performance Analytics**: IB performance metrics for compliance monitoring
- **Export Capabilities**: Export data in various formats for external reporting

---

## System Maintenance

### Regular Maintenance Tasks

#### Daily Tasks:
- Monitor commission processing success rates
- Check MT5 balance synchronization
- Review system error logs
- Verify database integrity

#### Weekly Tasks:
- Reconcile CRM and MT5 balances
- Review IB performance metrics
- Update commission rate configurations
- Backup commission and balance data

#### Monthly Tasks:
- Generate compliance reports
- Review and optimize database performance
- Update system documentation
- Conduct security audits

### Performance Monitoring

#### Key Performance Indicators:
- **Commission Processing Time**: Average time to process commissions
- **MT5 Integration Success Rate**: Percentage of successful balance updates
- **Database Query Performance**: Average query execution times
- **System Uptime**: Overall system availability percentage
- **User Satisfaction**: IB and admin user feedback scores

#### Monitoring Tools:
- **Laravel Telescope**: Application performance monitoring
- **Database Monitoring**: Query performance and optimization
- **Log Analysis**: Automated log analysis and alerting
- **Real-time Dashboards**: Live system status monitoring

---

## Conclusion

The Multi-Level IB System provides a comprehensive solution for managing introducing broker operations with real-time commission processing and MT5 integration. The system supports complex hierarchical structures while maintaining performance and reliability through optimized database design and efficient processing algorithms.

### Key Benefits:
- **Real-Time Processing**: Immediate commission calculation and MT5 balance updates
- **Scalable Architecture**: Supports unlimited levels of IB hierarchy
- **Comprehensive Management**: Complete admin tools for IB operations
- **Robust Integration**: Seamless MT5 server integration with fallback mechanisms
- **Security & Compliance**: Enterprise-level security and regulatory compliance features

### Future Enhancements:
- Mobile application for IB management
- Advanced analytics and machine learning insights
- Multi-currency commission support
- Enhanced reporting and dashboard features
- API integrations with third-party platforms

For technical support, system modifications, or additional features, contact the development team or system administrator.

---

**Last Updated**: June 2025
**Version**: 2.0
**System Status**: Production Ready
**Documentation Maintained By**: Development Team
