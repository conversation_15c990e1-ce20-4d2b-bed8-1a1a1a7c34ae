<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Mt5Account extends Model
{
    use HasFactory;

    protected $table = 'user_accounts';
    
    protected $fillable = [
        'User_Id',
        'Account',
        'Master_Password',
        'Investor_Password',
        'Phone_Password',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user that owns this MT5 account
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'User_Id');
    }

    /**
     * Get MT5 data from the Ireland database
     */
    public function mt5Data()
    {
        return $this->hasOne(Mt5User::class, 'Login', 'Account');
    }

    /**
     * Get MT5 deals for this account
     */
    public function mt5Deals()
    {
        return $this->hasMany(Mt5Deal::class, 'Login', 'Account');
    }

    /**
     * Get commissions earned by this account
     */
    public function commissions()
    {
        return $this->hasMany(IbCommission::class, 'mt5_login', 'Account');
    }

    /**
     * Get account balance from MT5 database
     */
    public function getBalanceAttribute()
    {
        if ($this->mt5Data) {
            return $this->mt5Data->Balance ?? 0;
        }
        return 0;
    }

    /**
     * Get account equity from MT5 database
     * Note: Equity column doesn't exist in MT5 database, using Balance instead
     */
    public function getEquityAttribute()
    {
        if ($this->mt5Data) {
            return $this->mt5Data->Balance ?? 0; // FIXED: Use Balance since Equity doesn't exist
        }
        return 0;
    }

    /**
     * Get account leverage from MT5 database
     */
    public function getLeverageAttribute()
    {
        if ($this->mt5Data) {
            return $this->mt5Data->Leverage ?? 100;
        }
        return 100;
    }

    /**
     * Get account group from MT5 database
     */
    public function getGroupAttribute()
    {
        if ($this->mt5Data) {
            return $this->mt5Data->Group ?? 'real\\default';
        }
        return 'real\\default';
    }

    /**
     * Get account status from MT5 database
     */
    public function getStatusAttribute()
    {
        if ($this->mt5Data) {
            return $this->mt5Data->Rights ?? 0;
        }
        return 0;
    }

    /**
     * Check if account is active
     */
    public function getIsActiveAttribute()
    {
        return $this->status > 0;
    }

    /**
     * Get formatted account display
     */
    public function getDisplayNameAttribute()
    {
        return "MT5: {$this->Account} (Balance: $" . number_format($this->balance, 2) . ")";
    }

    /**
     * Scope for active accounts
     */
    public function scopeActive($query)
    {
        return $query->whereHas('mt5Data', function($q) {
            $q->where('Rights', '>', 0);
        });
    }

    /**
     * Scope for IB accounts
     */
    public function scopeIb($query)
    {
        return $query->whereHas('mt5Data', function($q) {
            $q->where('Group', 'like', '%Affiliates%')
              ->orWhere('Group', 'like', '%IB%');
        });
    }
}
