<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Final Real-time MT5 Trading Data Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://maxst.icons8.com/vue-static/landings/line-awesome/line-awesome/1.3.0/css/line-awesome.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        .btn--primary {
            background-color: rgb(220, 53, 69) !important;
            border-color: rgb(220, 53, 69) !important;
            color: white !important;
        }
        .btn--primary:hover {
            background-color: rgb(200, 35, 51) !important;
            border-color: rgb(200, 35, 51) !important;
        }
        .real-time-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #28a745;
            margin-right: 5px;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .log-entry {
            font-family: monospace;
            font-size: 12px;
            padding: 2px 5px;
            margin: 1px 0;
            border-left: 3px solid #ccc;
        }
        .log-success { border-left-color: #28a745; background-color: #d4edda; }
        .log-error { border-left-color: #dc3545; background-color: #f8d7da; }
        .log-warning { border-left-color: #ffc107; background-color: #fff3cd; }
        .log-info { border-left-color: #17a2b8; background-color: #d1ecf1; }
        #rt-debug {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            z-index: 9999;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div id="rt-debug">RT Test</div>
    
    <div class="container mt-4">
        <h1>🔧 Final Real-time MT5 Trading Data Test</h1>
        <p class="text-muted">Testing the exact same functionality as the user dashboard</p>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5>
                            <span class="real-time-indicator"></span>
                            Real-time Orders for Account 
                            <select id="accountSelect" class="form-select d-inline-block" style="width: auto;">
                                <option value="873475">873475</option>
                                <option value="877421">877421</option>
                            </select>
                        </h5>
                        <div>
                            <button class="btn btn-sm btn--primary" onclick="loadRealTimeOrders()">
                                <i class="las la-sync-alt"></i> Manual Refresh
                            </button>
                            <button class="btn btn-sm btn-success" onclick="startAutoRefresh()">Auto Refresh</button>
                            <button class="btn btn-sm btn-danger" onclick="stopAutoRefresh()">Stop</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm" id="ordersTable">
                                <thead>
                                    <tr>
                                        <th>Time</th>
                                        <th>Symbol</th>
                                        <th>Profit</th>
                                        <th>Commission</th>
                                        <th>Fee</th>
                                        <th>Ticket</th>
                                        <th>Volume</th>
                                        <th>Price</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td colspan="8" class="text-center text-muted py-4">
                                            Click "Manual Refresh" to load real-time data
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6>Debug Log</h6>
                    </div>
                    <div class="card-body" style="height: 400px; overflow-y: auto;">
                        <div id="debugLog"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6>Raw API Response</h6>
                    </div>
                    <div class="card-body">
                        <pre id="rawResponse" class="bg-dark text-light p-3 rounded" style="height: 300px; overflow-y: auto;">No data yet...</pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let autoRefreshInterval = null;
        
        function addLog(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `<div class="log-entry log-${type}">[${timestamp}] ${message}</div>`;
            $('#debugLog').prepend(logEntry);
        }
        
        function loadRealTimeOrders() {
            const currentLogin = $('#accountSelect').val() || '873475';
            
            addLog('info', `Loading real-time orders for account ${currentLogin}...`);
            console.log('🔄 Loading real-time orders for account:', currentLogin);
            
            // Update debug indicator
            $('#rt-debug').css('background-color', 'orange').text('Loading...');
            
            // Use the exact same URL as the dashboard
            $.ajax({
                url: `/mbf.mybrokerforex.com-********/test-mt5-realtime/${currentLogin}`,
                type: 'GET',
                dataType: 'json',
                timeout: 15000,
                success: function(response) {
                    console.log('📥 Response received:', response);
                    addLog('success', 'API response received');
                    $('#rawResponse').text(JSON.stringify(response, null, 2));
                    
                    if (response.success && response.data && response.data.success) {
                        const orderData = response.data.data || [];
                        const count = response.data.count || 0;
                        
                        addLog('success', `Found ${count} positions for account ${response.login}`);
                        console.log(`✅ Success: ${count} positions for account ${response.login}`);
                        
                        // Update table
                        updateOrdersTable(orderData);
                        
                        // Update debug indicator
                        $('#rt-debug').css('background-color', 'green').text(`RT: ${count} orders`);
                        
                        // Update real-time indicator
                        $('.real-time-indicator').css('background-color', '#28a745');
                    } else {
                        const errorMsg = response.error || response.message || 'Unknown error';
                        addLog('error', `API returned error: ${errorMsg}`);
                        console.warn('⚠️ API returned error:', errorMsg);
                        $('#rt-debug').css('background-color', 'red').text('API Error');
                        $('.real-time-indicator').css('background-color', '#dc3545');
                    }
                },
                error: function(xhr, status, error) {
                    const errorMsg = `Status: ${xhr.status}, Error: ${error}`;
                    addLog('error', `AJAX Error: ${errorMsg}`);
                    console.error('❌ AJAX Error:', {
                        status: xhr.status,
                        error: error,
                        response: xhr.responseText.substring(0, 200)
                    });
                    
                    $('#rt-debug').css('background-color', 'red').text('AJAX Error');
                    $('.real-time-indicator').css('background-color', '#dc3545');
                    $('#rawResponse').text(`Error: ${xhr.status} ${error}\n\nResponse:\n${xhr.responseText}`);
                }
            });
        }
        
        function updateOrdersTable(orders) {
            const tbody = $('#ordersTable tbody');
            
            if (!orders || orders.length === 0) {
                tbody.html('<tr><td colspan="8" class="text-center text-muted py-4">No open positions found</td></tr>');
                addLog('warning', 'No orders to display');
                return;
            }
            
            let html = '';
            orders.forEach((order, index) => {
                console.log(`📊 Processing order ${index + 1}:`, order.Symbol, order.Profit);
                
                // Format timestamp
                let timeDisplay = 'N/A';
                if (order.TimeSetup && order.TimeSetup > 0) {
                    const timestamp = order.TimeSetup > 1000000000000 ? order.TimeSetup : order.TimeSetup * 1000;
                    timeDisplay = new Date(timestamp).toLocaleString();
                }
                
                // Format values
                const profit = parseFloat(order.Profit || 0).toFixed(2);
                const volume = parseFloat(order.Volume || 0);
                const volumeDisplay = volume > 1000 ? (volume / 100000).toFixed(3) + ' lot' : volume.toFixed(2);
                const commission = parseFloat(order.Commission || 0).toFixed(2);
                const fee = parseFloat(order.Fee || 0).toFixed(2);
                const ticket = order.Ticket || order.Order || 'N/A';
                
                html += `
                    <tr style="animation: fadeIn 0.5s ease-in;">
                        <td>${timeDisplay}</td>
                        <td><strong>${order.Symbol || 'N/A'}</strong></td>
                        <td class="${profit >= 0 ? 'text-success' : 'text-danger'}"><strong>$${profit}</strong></td>
                        <td>$${commission}</td>
                        <td>$${fee}</td>
                        <td>${ticket}</td>
                        <td>${volumeDisplay}</td>
                        <td>${order.PriceOpen || 0} → ${order.PriceCurrent || 0}</td>
                    </tr>
                `;
            });
            
            tbody.html(html);
            addLog('success', `Updated table with ${orders.length} orders`);
            console.log('✅ Table updated with', orders.length, 'rows');
        }
        
        function startAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
            
            addLog('info', 'Starting auto-refresh every 30 seconds');
            autoRefreshInterval = setInterval(loadRealTimeOrders, 30000);
            
            // Initial load
            loadRealTimeOrders();
        }
        
        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                addLog('warning', 'Auto-refresh stopped');
            }
        }
        
        // Initialize
        $(document).ready(function() {
            addLog('info', 'Test page loaded - ready to test real-time functionality');
            
            // Account change handler
            $('#accountSelect').on('change', function() {
                const newAccount = $(this).val();
                addLog('info', `Switched to account ${newAccount}`);
                if (autoRefreshInterval) {
                    loadRealTimeOrders(); // Refresh immediately
                }
            });
            
            // Auto-load data after 2 seconds
            setTimeout(() => {
                addLog('info', 'Auto-loading initial data...');
                loadRealTimeOrders();
            }, 2000);
        });
    </script>
</body>
</html>
