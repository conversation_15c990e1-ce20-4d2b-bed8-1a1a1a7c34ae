# Laravel Application Configuration for Live Server
APP_NAME="MBF MyBrokerForex"
APP_ENV=production
APP_KEY=base64:YOUR_APP_KEY_HERE
APP_DEBUG=false
APP_URL=https://yourdomain.com

# Logging Configuration
LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

# Database Configuration (Primary)
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=your_database_name
DB_USERNAME=your_database_user
DB_PASSWORD=your_database_password

# Database Configuration (MT5 Secondary)
DB_HOST_SECOND=127.0.0.1
DB_PORT_SECOND=3306
DB_DATABASE_SECOND=your_mt5_database_name
DB_USERNAME_SECOND=your_mt5_database_user
DB_PASSWORD_SECOND=your_mt5_database_password

# Cache Configuration
BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

# Redis Configuration (if using Redis)
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# Python Configuration for Windows Server 2022/Plesk
# CRITICAL: Use double backslashes (\\) for Windows Server 2022/Plesk
# Single backslashes (\) will cause 500 errors and crash the site
PYTHON_EXE="C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python.exe"
PYTHON_SCRIPT="C:\\Inetpub\\vhosts\\mybrokerforex.com\\mbf.mybrokerforex.com\\python\\mt5manager.py"

# Alternative Python paths (uncomment the one that works):
# PYTHON_EXE=python
# PYTHON_EXE=python3
# PYTHON_EXE=C:\Python39\python.exe
# PYTHON_EXE=C:\Python310\python.exe
# PYTHON_EXE=C:\Python311\python.exe

# MT5 Web API Configuration
MT5_SERVER=**************
MT5_PORT=443
MT5_MANAGER_LOGIN=877966
MT5_MANAGER_PASSWORD=ElVi!tL7
MT5_BUILD=484
MT5_AGENT=WebAPI

# MT5 Manager API Configuration (for Python script)
MT5_MANAGER_SERVER=**************:443
MT5_MANAGER_LOGIN_PYTHON=10007
MT5_MANAGER_PASSWORD_PYTHON=TfTe*wA1

# Payment Gateway Configuration
STRIPE_KEY=your_stripe_key
STRIPE_SECRET=your_stripe_secret

# Other API Keys (update as needed)
PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# Security Settings
SANCTUM_STATEFUL_DOMAINS=yourdomain.com,www.yourdomain.com
SESSION_DOMAIN=.yourdomain.com

# File Upload Settings
UPLOAD_MAX_FILESIZE=10M
POST_MAX_SIZE=10M
MAX_EXECUTION_TIME=300
MEMORY_LIMIT=512M

# Timezone
APP_TIMEZONE=UTC

# Additional Settings for Production
OPTIMIZE_AUTOLOADER=true
CACHE_CONFIG=true
CACHE_ROUTES=true
CACHE_VIEWS=true
