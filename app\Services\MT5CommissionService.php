<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserAccounts;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class MT5CommissionService
{
    protected $ibCommissionService;

    public function __construct()
    {
        $this->ibCommissionService = new IbCommissionService();
    }

    /**
     * Process MT5 trade closure and calculate commissions
     */
    public function processTradeCommission($tradeData)
    {
        try {
            Log::info("Processing MT5 trade commission", ['trade_data' => $tradeData]);

            // Validate trade data
            if (!$this->validateTradeData($tradeData)) {
                Log::warning("Invalid trade data received", ['trade_data' => $tradeData]);
                return false;
            }

            // Find user by MT5 login
            $user = $this->findUserByMT5Login($tradeData['login']);
            if (!$user) {
                Log::warning("User not found for MT5 login", ['login' => $tradeData['login']]);
                return false;
            }

            // Prepare commission calculation data
            $commissionData = [
                'email' => $user->email,
                'trade_id' => $tradeData['ticket'],
                'symbol' => $tradeData['symbol'],
                'volume' => $tradeData['volume'],
                'closed_at' => $tradeData['time_close'] ?? now(),
                'profit' => $tradeData['profit'] ?? 0,
                'commission' => $tradeData['commission'] ?? 0,
                'swap' => $tradeData['swap'] ?? 0
            ];

            // Calculate and distribute commissions
            $result = $this->ibCommissionService->calculateAndDistributeCommissions($commissionData);

            if ($result) {
                Log::info("Commission calculation successful", [
                    'user_id' => $user->id,
                    'trade_id' => $tradeData['ticket']
                ]);
            }

            return $result;

        } catch (\Exception $e) {
            Log::error("Error processing MT5 trade commission: " . $e->getMessage(), [
                'trade_data' => $tradeData,
                'error' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Validate incoming trade data
     */
    private function validateTradeData($tradeData)
    {
        $requiredFields = ['login', 'ticket', 'symbol', 'volume'];
        
        foreach ($requiredFields as $field) {
            if (!isset($tradeData[$field]) || empty($tradeData[$field])) {
                return false;
            }
        }

        return true;
    }

    /**
     * Find user by MT5 login
     */
    private function findUserByMT5Login($mt5Login)
    {
        // First try to find by MT5 account
        $userAccount = UserAccounts::where('login', $mt5Login)->first();
        if ($userAccount) {
            return $userAccount->user;
        }

        // Fallback: try to find user directly (if MT5 login is stored in users table)
        return User::where('mt5_login', $mt5Login)->first();
    }

    /**
     * Create webhook endpoint for MT5 trade notifications
     */
    public function handleMT5Webhook($request)
    {
        try {
            Log::info("MT5 webhook received", ['data' => $request->all()]);

            // Validate webhook signature if configured
            if (!$this->validateWebhookSignature($request)) {
                Log::warning("Invalid webhook signature");
                return response()->json(['error' => 'Invalid signature'], 401);
            }

            $tradeData = $request->all();
            
            // Process the trade commission
            $result = $this->processTradeCommission($tradeData);

            return response()->json([
                'success' => $result,
                'message' => $result ? 'Commission processed successfully' : 'Failed to process commission'
            ]);

        } catch (\Exception $e) {
            Log::error("MT5 webhook error: " . $e->getMessage());
            return response()->json(['error' => 'Internal server error'], 500);
        }
    }

    /**
     * Validate webhook signature for security
     */
    private function validateWebhookSignature($request)
    {
        $webhookSecret = env('MT5_WEBHOOK_SECRET');
        if (!$webhookSecret) {
            return true; // Skip validation if no secret is configured
        }

        $signature = $request->header('X-MT5-Signature');
        $payload = $request->getContent();
        $expectedSignature = hash_hmac('sha256', $payload, $webhookSecret);

        return hash_equals($expectedSignature, $signature);
    }

    /**
     * Monitor MT5 trades via API polling (alternative to webhooks)
     */
    public function monitorMT5Trades()
    {
        try {
            Log::info("Starting MT5 trade monitoring");

            // Get all active MT5 accounts
            $mt5Accounts = UserAccounts::where('status', 'active')
                ->where('server', '!=', '')
                ->get();

            foreach ($mt5Accounts as $account) {
                $this->checkAccountTrades($account);
            }

            Log::info("MT5 trade monitoring completed");

        } catch (\Exception $e) {
            Log::error("MT5 monitoring error: " . $e->getMessage());
        }
    }

    /**
     * Check trades for a specific MT5 account
     */
    private function checkAccountTrades($account)
    {
        try {
            // This would integrate with your MT5 API to get recent trades
            // For now, this is a placeholder implementation
            
            $trades = $this->getMT5Trades($account->login, $account->server);
            
            foreach ($trades as $trade) {
                // Check if we've already processed this trade
                if ($this->isTradeProcessed($trade['ticket'])) {
                    continue;
                }

                // Process the trade commission
                $this->processTradeCommission($trade);
                
                // Mark trade as processed
                $this->markTradeAsProcessed($trade['ticket']);
            }

        } catch (\Exception $e) {
            Log::error("Error checking trades for account {$account->login}: " . $e->getMessage());
        }
    }

    /**
     * Get MT5 trades via API (placeholder implementation)
     */
    private function getMT5Trades($login, $server)
    {
        // This would call your MT5 API to get recent closed trades
        // Implementation depends on your MT5 setup
        
        // Placeholder return
        return [];
    }

    /**
     * Check if trade has already been processed
     */
    private function isTradeProcessed($tradeId)
    {
        return \App\Models\IbCommission::where('trade_id', $tradeId)->exists();
    }

    /**
     * Mark trade as processed (could use a separate table for tracking)
     */
    private function markTradeAsProcessed($tradeId)
    {
        // Could create a processed_trades table to track this
        // For now, we rely on the ib_commissions table
    }

    /**
     * Get commission statistics for MT5 integration
     */
    public function getCommissionStats($startDate = null, $endDate = null)
    {
        $query = \App\Models\IbCommission::query();

        if ($startDate && $endDate) {
            $query->dateRange($startDate, $endDate);
        }

        return [
            'total_commissions' => $query->sum('commission_amount'),
            'total_trades' => $query->count(),
            'unique_traders' => $query->distinct('from_user_id')->count(),
            'unique_ibs' => $query->distinct('to_ib_user_id')->count(),
            'avg_commission_per_trade' => $query->avg('commission_amount'),
            'top_symbols' => $query->selectRaw('symbol, COUNT(*) as trade_count, SUM(commission_amount) as total_commission')
                ->groupBy('symbol')
                ->orderBy('total_commission', 'desc')
                ->limit(10)
                ->get()
        ];
    }

    /**
     * Sync MT5 account data with Laravel users
     */
    public function syncMT5Accounts()
    {
        try {
            Log::info("Starting MT5 account sync");

            // Get all MT5 accounts from MT5 server
            $mt5Accounts = $this->getAllMT5Accounts();

            foreach ($mt5Accounts as $mt5Account) {
                $this->syncSingleAccount($mt5Account);
            }

            Log::info("MT5 account sync completed");

        } catch (\Exception $e) {
            Log::error("MT5 sync error: " . $e->getMessage());
        }
    }

    /**
     * Get all MT5 accounts (placeholder implementation)
     */
    private function getAllMT5Accounts()
    {
        // This would call your MT5 API to get all accounts
        // Implementation depends on your MT5 setup
        
        return [];
    }

    /**
     * Sync a single MT5 account with Laravel
     */
    private function syncSingleAccount($mt5Account)
    {
        try {
            $userAccount = UserAccounts::where('login', $mt5Account['login'])->first();
            
            if ($userAccount) {
                // Update existing account
                $userAccount->update([
                    'balance' => $mt5Account['balance'],
                    'equity' => $mt5Account['equity'],
                    'margin' => $mt5Account['margin'],
                    'free_margin' => $mt5Account['margin_free'],
                    'leverage' => $mt5Account['leverage'],
                    'last_sync' => now()
                ]);
            }

        } catch (\Exception $e) {
            Log::error("Error syncing MT5 account {$mt5Account['login']}: " . $e->getMessage());
        }
    }
}
