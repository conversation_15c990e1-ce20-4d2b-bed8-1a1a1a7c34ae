@extends('admin.layouts.app')

@section('panel')
<div class="row">
    <div class="col-lg-12">
        <div class="card b-radius--10">
            <div class="card-header">
                <div class="row g-3 align-items-center">
                    <div class="col-md-6">
                        <h5 class="card-title">@lang('Pending Commissions')</h5>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('admin.commissions.index') }}" class="btn btn--primary btn-sm">
                                <i class="las la-arrow-left"></i> @lang('Back to Overview')
                            </a>
                            <button type="button" class="btn btn--success btn-sm" id="bulkApproveBtn" disabled>
                                <i class="las la-check"></i> @lang('Bulk Approve')
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive--sm table-responsive">
                    <table class="table table--light style--two">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th>@lang('IB User')</th>
                                <th>@lang('MT5 Login')</th>
                                <th>@lang('Deal ID')</th>
                                <th>@lang('Amount')</th>
                                <th>@lang('Symbol')</th>
                                <th>@lang('Date')</th>
                                <th>@lang('Action')</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($commissions as $commission)
                            <tr>
                                <td>
                                    <input type="checkbox" class="form-check-input commission-checkbox" 
                                           value="{{ $commission->id }}">
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ $commission->firstname }} {{ $commission->lastname }}</strong><br>
                                        <small class="text-muted">{{ $commission->email }}</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="fw-bold">{{ $commission->mt5_login }}</span>
                                </td>
                                <td>
                                    <span class="badge badge--dark">{{ $commission->mt5_deal_id }}</span>
                                </td>
                                <td>
                                    <span class="fw-bold text-success">${{ number_format($commission->commission_amount, 2) }}</span>
                                    <br><small class="text-muted">Level {{ $commission->level }}</small>
                                </td>
                                <td>
                                    <span class="badge badge--info">{{ $commission->symbol ?: 'N/A' }}</span>
                                    <br><small class="text-muted">{{ number_format($commission->volume, 2) }} lots</small>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ showDateTime($commission->deal_time) }}</strong><br>
                                        <small class="text-muted">{{ \Carbon\Carbon::parse($commission->deal_time)->diffForHumans() }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="button--group">
                                        <button type="button" class="btn btn-sm btn-outline--success approve-btn" 
                                                data-id="{{ $commission->id }}">
                                            <i class="las la-check"></i> @lang('Approve')
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline--danger reject-btn" 
                                                data-id="{{ $commission->id }}">
                                            <i class="las la-times"></i> @lang('Reject')
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="8" class="text-center">@lang('No pending commissions found')</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if($commissions->hasPages())
            <div class="card-footer py-4">
                {{ $commissions->links() }}
            </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('script')
<script>
$(document).ready(function() {
    // Select all functionality
    $('#selectAll').on('change', function() {
        $('.commission-checkbox').prop('checked', this.checked);
        toggleBulkApproveBtn();
    });

    $('.commission-checkbox').on('change', function() {
        toggleBulkApproveBtn();
    });

    function toggleBulkApproveBtn() {
        const checkedCount = $('.commission-checkbox:checked').length;
        $('#bulkApproveBtn').prop('disabled', checkedCount === 0);
    }

    // Bulk approve
    $('#bulkApproveBtn').on('click', function() {
        const selectedIds = $('.commission-checkbox:checked').map(function() {
            return this.value;
        }).get();

        if (selectedIds.length === 0) return;

        if (confirm(`Are you sure you want to approve ${selectedIds.length} commissions?`)) {
            $.post('{{ route("admin.commissions.bulk.approve") }}', {
                _token: '{{ csrf_token() }}',
                commission_ids: selectedIds
            }).done(function(response) {
                location.reload();
            }).fail(function() {
                alert('Failed to approve commissions');
            });
        }
    });

    // Individual approve
    $('.approve-btn').on('click', function() {
        const id = $(this).data('id');
        if (confirm('Are you sure you want to approve this commission?')) {
            $.post(`{{ route('admin.commissions.approve', '') }}/${id}`, {
                _token: '{{ csrf_token() }}'
            }).done(function(response) {
                location.reload();
            }).fail(function() {
                alert('Failed to approve commission');
            });
        }
    });

    // Individual reject
    $('.reject-btn').on('click', function() {
        const id = $(this).data('id');
        const reason = prompt('Enter rejection reason (optional):');
        if (reason !== null) {
            $.post(`{{ route('admin.commissions.reject', '') }}/${id}`, {
                _token: '{{ csrf_token() }}',
                reason: reason
            }).done(function(response) {
                location.reload();
            }).fail(function() {
                alert('Failed to reject commission');
            });
        }
    });
});
</script>
@endpush
