<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProfileRequestLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'request_data',
        'request_summary',
        'status',
        'submitted_at',
        'processed_by',
        'processed_at',
        'rejection_reason',
        'admin_notes',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'request_data' => 'array',
        'submitted_at' => 'datetime',
        'processed_at' => 'datetime',
    ];

    /**
     * Get the user that submitted the request
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the admin who processed the request
     */
    public function processedBy(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'processed_by');
    }

    /**
     * Scope for pending requests
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for approved requests
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope for rejected requests
     */
    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    /**
     * Scope for recent requests (last 30 days)
     */
    public function scopeRecent($query)
    {
        return $query->where('submitted_at', '>=', now()->subDays(30));
    }

    /**
     * Get formatted request summary
     */
    public function getFormattedSummaryAttribute()
    {
        // Always generate enhanced summary instead of using stored summary

        // Generate summary from request_data if summary is not available
        if ($this->request_data && is_array($this->request_data)) {
            $summary = [];
            $reason = '';

            foreach ($this->request_data as $field) {
                // Handle both object and array formats
                $name = is_array($field) ? ($field['name'] ?? '') : ($field->name ?? '');
                $value = is_array($field) ? ($field['value'] ?? '') : ($field->value ?? '');

                if (!empty($name) && !empty($value)) {
                    if (strtolower($name) === 'reason') {
                        $reason = $value;
                    } else {
                        $summary[] = $name . ': ' . (is_array($value) ? implode(', ', $value) : $value);
                    }
                }
            }

            $result = implode(' | ', $summary);
            if ($reason) {
                $result .= ' | Reason: ' . $reason;
            }

            // Add processing info if available
            if ($this->status !== 'pending') {
                // Load the relationship if not already loaded
                if (!$this->relationLoaded('processedBy') && $this->processed_by) {
                    $this->load('processedBy:id,name');
                }

                $processedBy = $this->processedBy ? $this->processedBy->name : 'System';
                $result .= ' | Processed by: ' . $processedBy;

                if ($this->status === 'rejected' && $this->rejection_reason) {
                    $result .= ' | Rejected: ' . $this->rejection_reason;
                }
            }

            return $result ?: 'Profile change request';
        }

        return 'Profile change request';
    }

    /**
     * Get status badge HTML
     */
    public function getStatusBadgeAttribute()
    {
        switch ($this->status) {
            case 'pending':
                return '<span class="badge badge--warning">Pending</span>';
            case 'approved':
                return '<span class="badge badge--success">Approved</span>';
            case 'rejected':
                return '<span class="badge badge--danger">Rejected</span>';
            default:
                return '<span class="badge badge--info">Unknown</span>';
        }
    }

    /**
     * Get detailed request information for modal display
     */
    public function getDetailedRequestInfoAttribute()
    {
        $details = [];

        if ($this->request_data && is_array($this->request_data)) {
            foreach ($this->request_data as $field) {
                $name = is_array($field) ? ($field['name'] ?? '') : ($field->name ?? '');
                $value = is_array($field) ? ($field['value'] ?? '') : ($field->value ?? '');
                $type = is_array($field) ? ($field['type'] ?? 'text') : ($field->type ?? 'text');

                if (!empty($name) && !empty($value)) {
                    $details[] = [
                        'name' => $name,
                        'value' => $value,
                        'type' => $type,
                        'is_file' => $type === 'file',
                        'is_image' => $type === 'file' && in_array(pathinfo($value, PATHINFO_EXTENSION), ['jpg', 'jpeg', 'png', 'gif']),
                        'is_pdf' => $type === 'file' && pathinfo($value, PATHINFO_EXTENSION) === 'pdf'
                    ];
                }
            }
        }

        return $details;
    }

    /**
     * Get user's reason for the request
     */
    public function getRequestReasonAttribute()
    {
        if ($this->request_data && is_array($this->request_data)) {
            foreach ($this->request_data as $field) {
                $name = is_array($field) ? ($field['name'] ?? '') : ($field->name ?? '');
                $value = is_array($field) ? ($field['value'] ?? '') : ($field->value ?? '');

                if (strtolower($name) === 'reason' && !empty($value)) {
                    return $value;
                }
            }
        }

        return null;
    }
}
