@extends('admin.layouts.app')

@section('panel')
<div class="row">
    <div class="col-lg-12">
        <!-- Commission Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-lg-6 col-sm-6 mb-30">
                <div class="widget-two style--two box--shadow2 b-radius--5 bg--primary">
                    <div class="widget-two__icon b-radius--5 bg--primary">
                        <i class="las la-dollar-sign"></i>
                    </div>
                    <div class="widget-two__content">
                        <h3 class="text-white">${{ number_format($stats['total_commission'], 2) }}</h3>
                        <p class="text-white">@lang('Total Commission')</p>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-lg-6 col-sm-6 mb-30">
                <div class="widget-two style--two box--shadow2 b-radius--5 bg--success">
                    <div class="widget-two__icon b-radius--5 bg--success">
                        <i class="las la-chart-line"></i>
                    </div>
                    <div class="widget-two__content">
                        <h3 class="text-white">{{ number_format($stats['total_trades']) }}</h3>
                        <p class="text-white">@lang('Total Trades')</p>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-lg-6 col-sm-6 mb-30">
                <div class="widget-two style--two box--shadow2 b-radius--5 bg--warning">
                    <div class="widget-two__icon b-radius--5 bg--warning">
                        <i class="las la-users"></i>
                    </div>
                    <div class="widget-two__content">
                        <h3 class="text-white">{{ $stats['unique_ibs'] }}</h3>
                        <p class="text-white">@lang('Active IBs')</p>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-lg-6 col-sm-6 mb-30">
                <div class="widget-two style--two box--shadow2 b-radius--5 bg--info">
                    <div class="widget-two__icon b-radius--5 bg--info">
                        <i class="las la-balance-scale"></i>
                    </div>
                    <div class="widget-two__content">
                        <h3 class="text-white">{{ number_format($stats['total_volume'], 2) }}</h3>
                        <p class="text-white">@lang('Total Volume (Lots)')</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Actions -->
        <div class="card b-radius--10">
            <div class="card-body p-0">
                <div class="table-responsive--md table-responsive">
                    <!-- Filter Form -->
                    <form method="GET" class="p-3 bg--light">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>@lang('IB User')</label>
                                    <select name="ib_user" class="form-control">
                                        <option value="">@lang('All IBs')</option>
                                        @foreach($ibUsers as $ib)
                                            <option value="{{ $ib->id }}" {{ request('ib_user') == $ib->id ? 'selected' : '' }}>
                                                {{ $ib->firstname }} {{ $ib->lastname }} ({{ $ib->username }})
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>@lang('Status')</label>
                                    <select name="status" class="form-control">
                                        <option value="">@lang('All Status')</option>
                                        <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>@lang('Pending')</option>
                                        <option value="processed" {{ request('status') == 'processed' ? 'selected' : '' }}>@lang('Processed')</option>
                                        <option value="paid" {{ request('status') == 'paid' ? 'selected' : '' }}>@lang('Paid')</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>@lang('Symbol')</label>
                                    <input type="text" name="symbol" class="form-control" value="{{ request('symbol') }}" placeholder="@lang('Symbol')">
                                </div>
                            </div>
                            
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>@lang('Date From')</label>
                                    <input type="date" name="date_from" class="form-control" value="{{ request('date_from') }}">
                                </div>
                            </div>
                            
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>@lang('Date To')</label>
                                    <input type="date" name="date_to" class="form-control" value="{{ request('date_to') }}">
                                </div>
                            </div>
                            
                            <div class="col-md-1">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn--primary btn-block">@lang('Filter')</button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Action Buttons -->
                    <div class="p-3 border-bottom">
                        <div class="row">
                            <div class="col-md-6">
                                <button type="button" class="btn btn--success" onclick="syncCommissions()">
                                    <i class="las la-sync"></i> @lang('Sync MT5 Data')
                                </button>
                            </div>
                            <div class="col-md-6 text-right">
                                <span class="text-muted">
                                    @lang('Pending'): ${{ number_format($stats['pending_commission'], 2) }} | 
                                    @lang('Processed'): ${{ number_format($stats['processed_commission'], 2) }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Commission Table -->
                    <table class="table--light style--two table">
                        <thead>
                            <tr>
                                <th>@lang('Deal ID')</th>
                                <th>@lang('IB')</th>
                                <th>@lang('Trader')</th>
                                <th>@lang('Symbol')</th>
                                <th>@lang('Volume')</th>
                                <th>@lang('Commission')</th>
                                <th>@lang('Level')</th>
                                <th>@lang('Status')</th>
                                <th>@lang('Deal Time')</th>
                                <th>@lang('Action')</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($commissions as $commission)
                            <tr>
                                <td>
                                    <span class="fw-bold">{{ $commission->mt5_deal_id ?? $commission->trade_id }}</span>
                                    @if($commission->mt5_login)
                                        <br><small class="text-muted">MT5: {{ $commission->mt5_login }}</small>
                                    @endif
                                </td>
                                <td>
                                    <span class="fw-bold">{{ $commission->toIbUser->fullname ?? 'N/A' }}</span>
                                    <br><small class="text-muted">{{ $commission->toIbUser->username ?? 'N/A' }}</small>
                                </td>
                                <td>
                                    <span class="fw-bold">{{ $commission->fromUser->fullname ?? 'N/A' }}</span>
                                    <br><small class="text-muted">{{ $commission->fromUser->username ?? 'N/A' }}</small>
                                </td>
                                <td>
                                    <span class="badge badge--primary">{{ $commission->symbol }}</span>
                                </td>
                                <td>{{ number_format($commission->volume, 2) }} lots</td>
                                <td>
                                    <span class="fw-bold text-success">${{ number_format($commission->commission_amount, 2) }}</span>
                                </td>
                                <td>
                                    <span class="badge badge--{{ $commission->level == 1 ? 'success' : 'info' }}">
                                        {{ $commission->level_display ?? 'Level ' . $commission->level }}
                                    </span>
                                </td>
                                <td>
                                    @if($commission->status == 'pending')
                                        <span class="badge badge--warning">@lang('Pending')</span>
                                    @elseif($commission->status == 'processed')
                                        <span class="badge badge--info">@lang('Processed')</span>
                                    @elseif($commission->status == 'paid')
                                        <span class="badge badge--success">@lang('Paid')</span>
                                    @else
                                        <span class="badge badge--dark">{{ ucfirst($commission->status) }}</span>
                                    @endif
                                </td>
                                <td>
                                    {{ $commission->deal_time ? $commission->deal_time->format('M d, Y H:i') : ($commission->trade_closed_at ? $commission->trade_closed_at->format('M d, Y H:i') : 'N/A') }}
                                </td>
                                <td>
                                    <a href="{{ route('admin.ib.commissions.show', $commission->to_ib_user_id) }}" 
                                       class="btn btn--primary btn-sm">
                                        <i class="las la-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="10" class="text-center">@lang('No commission data found')</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            
            @if($commissions->hasPages())
            <div class="card-footer py-4">
                {{ paginateLinks($commissions) }}
            </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('script')
<script>
function syncCommissions() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    
    btn.disabled = true;
    btn.innerHTML = '<i class="las la-spinner la-spin"></i> Syncing...';
    
    fetch('{{ route("admin.ib.commissions.sync") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            limit: 1000
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            notify('success', data.message || 'Commission data synced successfully');
            setTimeout(() => location.reload(), 2000);
        } else {
            notify('error', data.message || 'Sync failed');
        }
    })
    .catch(error => {
        notify('error', 'Sync error: ' + error.message);
    })
    .finally(() => {
        btn.disabled = false;
        btn.innerHTML = originalText;
    });
}
</script>
@endpush
