<div class="row mb--20">
  <div class="col-lg-12">
    <div class="card mt-30">
      <div class="card-body">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="card-title mb-0"><?php echo app('translator')->get('Change password'); ?></h5>
        </div>
        <br>
        <div class="row">
          <div class="col-md-6">
            <div class="mb-3">
              <label for="password" class="form-label"><?php echo app('translator')->get('New Password:'); ?></label>
              <input type="password" id="password" name="password" class="form-control" required>
            </div>
          </div>

          <div class="col-md-6">
            <div class="mb-3">
              <label for="confirm-password" class="form-label"><?php echo app('translator')->get('Confirm Password:'); ?></label>
              <input type="password" id="confirm-password" name="password_confirmation" class="form-control" required>
            </div>
          </div>
        </div>
      </div>

      <div class="card-footer text-end">
        <button type="submit" class="btn btn--primary"><?php echo app('translator')->get('Change Password'); ?></button>
      </div>
    </div>
  </div>
</div>

<?php if (isset($component)) { $__componentOriginalbd5922df145d522b37bf664b524be380 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbd5922df145d522b37bf664b524be380 = $attributes; } ?>
<?php $component = App\View\Components\ConfirmationModal::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('confirmation-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\ConfirmationModal::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbd5922df145d522b37bf664b524be380)): ?>
<?php $attributes = $__attributesOriginalbd5922df145d522b37bf664b524be380; ?>
<?php unset($__attributesOriginalbd5922df145d522b37bf664b524be380); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbd5922df145d522b37bf664b524be380)): ?>
<?php $component = $__componentOriginalbd5922df145d522b37bf664b524be380; ?>
<?php unset($__componentOriginalbd5922df145d522b37bf664b524be380); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\mbf.mybrokerforex.com-31052025\resources\views/components/user-detail/security.blade.php ENDPATH**/ ?>