<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔄 REAL-TIME MT5 COMMISSION SYNC\n";
echo "================================\n";

try {
    // Get real commission-earning IBs
    $realIBs = [
        '<EMAIL>' => 865607,
        '<EMAIL>' => 866207,
        '<EMAIL>' => 866426
    ];

    echo "\n📊 Syncing commissions for real IBs:\n";
    
    foreach ($realIBs as $email => $mt5Login) {
        echo "\n🔍 Processing: {$email} (MT5: {$mt5Login})\n";
        
        // Get IB user
        $ibUser = \App\Models\User::where('email', $email)->first();
        if (!$ibUser) {
            echo "   ❌ IB user not found\n";
            continue;
        }
        
        // Get their referrals
        $referrals = \App\Models\User::where('ref_by', $ibUser->id)
            ->whereNotNull('mt5_login')
            ->get();
        
        echo "   📈 Found {$referrals->count()} referrals with MT5 accounts\n";
        
        foreach ($referrals as $referral) {
            echo "   🔍 Checking trades for referral: {$referral->email} (MT5: {$referral->mt5_login})\n";
            
            // Get MT5 deals for this referral
            $mt5Deals = DB::connection('mbf-dbmt5')
                ->table('mt5_deals_2025')
                ->where('Login', $referral->mt5_login)
                ->where('Action', 1) // Close positions
                ->where('Profit', '>', 0) // Profitable trades only
                ->whereDate('Time', '>=', now()->subDays(30)) // Last 30 days
                ->get();
            
            echo "     📊 Found {$mt5Deals->count()} profitable trades\n";
            
            foreach ($mt5Deals as $deal) {
                // Calculate broker commission (example: 0.1% of volume)
                $brokerCommission = ($deal->Volume * 0.001); // Simplified calculation
                
                if ($brokerCommission > 0) {
                    // Calculate IB commission (50% of broker commission)
                    $ibCommissionRate = 50.00; // 50%
                    $ibCommission = ($brokerCommission * $ibCommissionRate) / 100;
                    
                    // Check if commission already exists
                    $existingCommission = DB::table('ib_commissions')
                        ->where('mt5_deal_id', $deal->Deal)
                        ->where('to_ib_user_id', $ibUser->id)
                        ->first();
                    
                    if (!$existingCommission) {
                        // Create commission record
                        DB::table('ib_commissions')->insert([
                            'to_ib_user_id' => $ibUser->id,
                            'from_user_id' => $referral->id,
                            'mt5_login' => $referral->mt5_login,
                            'mt5_deal_id' => $deal->Deal,
                            'commission_amount' => $ibCommission,
                            'symbol' => $deal->Symbol,
                            'volume' => $deal->Volume,
                            'profit' => $deal->Profit,
                            'deal_time' => $deal->Time,
                            'status' => 'pending',
                            'commission_rate' => $ibCommissionRate,
                            'broker_commission' => $brokerCommission,
                            'created_at' => now(),
                            'updated_at' => now()
                        ]);
                        
                        echo "     ✅ Created commission: $" . number_format($ibCommission, 2) . 
                             " for deal {$deal->Deal} ({$deal->Symbol})\n";
                    }
                }
            }
        }
    }
    
    // Summary
    echo "\n📊 COMMISSION SYNC SUMMARY\n";
    echo "==========================\n";
    
    $totalCommissions = DB::table('ib_commissions')->count();
    $pendingCommissions = DB::table('ib_commissions')->where('status', 'pending')->count();
    $paidCommissions = DB::table('ib_commissions')->where('status', 'paid')->count();
    $totalAmount = DB::table('ib_commissions')->sum('commission_amount');
    $pendingAmount = DB::table('ib_commissions')->where('status', 'pending')->sum('commission_amount');
    
    echo "✅ Total Commission Records: {$totalCommissions}\n";
    echo "⏳ Pending Commissions: {$pendingCommissions}\n";
    echo "💰 Paid Commissions: {$paidCommissions}\n";
    echo "💵 Total Amount: $" . number_format($totalAmount, 2) . "\n";
    echo "⏳ Pending Amount: $" . number_format($pendingAmount, 2) . "\n";
    
    // Update IB statistics
    echo "\n📊 UPDATING IB STATISTICS\n";
    echo "=========================\n";
    
    foreach ($realIBs as $email => $mt5Login) {
        $ibUser = \App\Models\User::where('email', $email)->first();
        if ($ibUser) {
            $totalEarned = DB::table('ib_commissions')
                ->where('to_ib_user_id', $ibUser->id)
                ->where('status', 'paid')
                ->sum('commission_amount');
            
            $pendingEarnings = DB::table('ib_commissions')
                ->where('to_ib_user_id', $ibUser->id)
                ->where('status', 'pending')
                ->sum('commission_amount');
            
            $ibUser->update([
                'commission_earnings' => $totalEarned,
                'pending_commission' => $pendingEarnings,
                'last_commission_sync' => now()
            ]);
            
            echo "✅ {$email}: Earned $" . number_format($totalEarned, 2) . 
                 ", Pending $" . number_format($pendingEarnings, 2) . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n✅ Real-time commission sync completed!\n";
