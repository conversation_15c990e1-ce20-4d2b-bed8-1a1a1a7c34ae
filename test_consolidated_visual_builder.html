<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Consolidated Visual Builder Test</title>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
</head>
<body>
    <h1>🔧 Consolidated Visual Builder Test</h1>
    
    <div class="test-container">
        <h2>📊 Initialization Test</h2>
        <div id="test-results" style="background: #f0f0f0; padding: 15px; margin: 10px 0; height: 300px; overflow-y: auto;"></div>
        
        <h2>🎨 Visual Builder Interface</h2>
        <div class="email-builder-interface" style="border: 2px solid #dc3545; min-height: 200px; padding: 20px; background: #f8f9fa;">
            <p>Visual Builder will be initialized here...</p>
        </div>
        
        <h2>📝 Form Test</h2>
        <form id="test-form">
            <textarea name="email_body" style="width: 100%; height: 100px;">Test email content</textarea>
            <input type="hidden" name="email_body_final" id="email_body_final" value="">
            <button type="submit" id="update-template-btn">Update Template</button>
        </form>
    </div>

    <script>
    // Simulate server data (like what comes from Laravel)
    window.serverTemplateData = {
        content: `<!DOCTYPE html>
<html>
<head>
    <title>Test Email Template</title>
</head>
<body>
    <h1>Welcome to MBFX</h1>
    <p>This is a test email template with {{username}} and {{email}} tokens.</p>
    <p>Thank you for using our service!</p>
</body>
</html>`,
        tokens: [
            { token: '{{username}}', description: 'Username of the user', category: 'User' },
            { token: '{{email}}', description: 'Email address', category: 'User' },
            { token: '{{site_name}}', description: 'Site name', category: 'Site' }
        ]
    };
    
    window.templateId = 1;
    window.testEmailRoute = '/test/email';
    window.csrfToken = 'test-token';
    
    // Capture console output
    const originalLog = console.log;
    const originalError = console.error;
    const originalWarn = console.warn;
    const resultsDiv = document.getElementById('test-results');
    
    function addToResults(type, message) {
        const div = document.createElement('div');
        div.style.color = type === 'error' ? 'red' : type === 'warn' ? 'orange' : 'black';
        div.style.marginBottom = '5px';
        div.textContent = `[${type.toUpperCase()}] ${message}`;
        resultsDiv.appendChild(div);
        resultsDiv.scrollTop = resultsDiv.scrollHeight;
    }
    
    console.log = function(...args) {
        originalLog.apply(console, args);
        addToResults('log', args.join(' '));
    };
    
    console.error = function(...args) {
        originalError.apply(console, args);
        addToResults('error', args.join(' '));
    };
    
    console.warn = function(...args) {
        originalWarn.apply(console, args);
        addToResults('warn', args.join(' '));
    };
    
    // Mock Visual Builder class for testing
    window.VisualBuilderEmailEditor = class {
        constructor(options) {
            this.options = options;
            this.content = options.content || '';
            console.log('✅ Mock Visual Builder initialized with content:', this.content.length + ' characters');
            
            // Simulate loading content into interface
            const container = document.querySelector(options.container);
            if (container) {
                container.innerHTML = `
                    <div style="border: 1px solid #ccc; padding: 10px; background: white;">
                        <h4>📧 Visual Builder Active</h4>
                        <p><strong>Content loaded:</strong> ${this.content.length} characters</p>
                        <p><strong>Tokens available:</strong> ${options.tokens.length}</p>
                        <div style="background: #f9f9f9; padding: 10px; margin: 10px 0; max-height: 100px; overflow-y: auto;">
                            <strong>Content preview:</strong><br>
                            ${this.content.substring(0, 200)}...
                        </div>
                    </div>
                `;
            }
        }
        
        getContent() {
            return this.content;
        }
        
        setContent(content) {
            this.content = content;
            console.log('✅ Content set in Visual Builder:', content.length + ' characters');
        }
    };
    
    // Test the consolidated initialization
    console.log('🔧 Starting consolidated Visual Builder test...');
    console.log('📊 Server data available:', typeof window.serverTemplateData !== 'undefined');
    console.log('📊 Template content length:', window.serverTemplateData.content.length);
    console.log('📊 Tokens count:', window.serverTemplateData.tokens.length);
    
    // Load the external JavaScript file simulation
    console.log('📦 Loading external JavaScript functions...');
    
    // Simulate the consolidated functions
    window.initializeTemplateData = function() {
        console.log('🔄 Initializing template data from server...');
        
        try {
            if (typeof window.serverTemplateData !== 'undefined') {
                window.templateData = window.serverTemplateData;
                console.log('✅ Template data loaded from server:', window.templateData.content.length + ' characters');
            } else {
                console.warn('⚠️ No server template data found, using fallback');
                window.templateData = { content: '', tokens: [] };
            }
            
            console.log('✅ Template data initialized successfully');
            return true;
        } catch (error) {
            console.error('❌ Template data initialization error:', error);
            window.templateData = { content: '', tokens: [] };
            return false;
        }
    };
    
    window.initializeEnhancedTemplateEditor = function() {
        console.log('🎨 Enhanced Template Editor Initialization...');
        
        // Step 1: Initialize template data
        window.initializeTemplateData();
        
        // Step 2: Initialize Visual Builder with content
        if (typeof window.templateData !== 'undefined' && window.templateData.content) {
            console.log('📄 Template data found, initializing Visual Builder with content...');
            
            if (typeof window.VisualBuilderEmailEditor !== 'undefined') {
                try {
                    window.visualBuilderInstance = new window.VisualBuilderEmailEditor({
                        container: '.email-builder-interface',
                        content: window.templateData.content || '',
                        tokens: window.templateData.tokens || [],
                        mode: 'visual'
                    });
                    
                    console.log('✅ Visual Builder initialized with content:', window.templateData.content.length + ' characters');
                    
                    // Force content loading
                    setTimeout(() => {
                        if (window.visualBuilderInstance && window.visualBuilderInstance.setContent) {
                            window.visualBuilderInstance.setContent(window.templateData.content);
                            console.log('✅ Content force-loaded into Visual Builder');
                        }
                    }, 500);
                    
                } catch (error) {
                    console.error('❌ Visual Builder initialization failed:', error);
                }
            } else {
                console.error('❌ VisualBuilderEmailEditor class not found');
            }
        } else {
            console.warn('⚠️ No template data available for Visual Builder');
        }
    };
    
    // Test initialization
    $(document).ready(function() {
        console.log('🚀 DOM ready - starting enhanced template editor initialization...');
        
        setTimeout(function() {
            window.initializeEnhancedTemplateEditor();
            
            // Test form submission
            $('#update-template-btn').click(function(e) {
                e.preventDefault();
                console.log('📝 Form submission test...');
                
                if (window.visualBuilderInstance) {
                    const content = window.visualBuilderInstance.getContent();
                    $('#email_body_final').val(content);
                    console.log('✅ Content synced to form field:', content.length + ' characters');
                } else {
                    console.warn('⚠️ No Visual Builder instance available');
                }
            });
            
        }, 100);
    });
    </script>
</body>
</html>
