<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\IbResource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;

class IbResourceController extends Controller
{
    /**
     * Display IB resources
     */
    public function index(Request $request)
    {
        $pageTitle = 'IB Resources Management';
        
        $query = IbResource::with('uploader');
        
        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }
        
        // Filter by type
        if ($request->filled('type')) {
            $query->byType($request->type);
        }
        
        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'inactive') {
                $query->inactive();
            }
        }
        
        $resources = $query->orderBy('created_at', 'desc')->paginate(20);
        
        // Get resource types for filter
        $resourceTypes = [
            'document' => 'Document',
            'image' => 'Image',
            'video' => 'Video',
            'banner' => 'Banner',
            'guide' => 'Guide'
        ];
        
        return view('admin.ib.resources.index', compact('pageTitle', 'resources', 'resourceTypes'));
    }

    /**
     * Show create form
     */
    public function create()
    {
        $pageTitle = 'Upload IB Resource';
        
        $resourceTypes = [
            'document' => 'Document',
            'image' => 'Image',
            'video' => 'Video',
            'banner' => 'Banner',
            'guide' => 'Guide'
        ];
        
        return view('admin.ib.resources.create', compact('pageTitle', 'resourceTypes'));
    }

    /**
     * Store new resource
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:document,image,video,banner,guide',
            'file' => 'required|file|max:50000', // 50MB max
        ]);

        if ($validator->fails()) {
            $notify[] = ['error', 'Validation failed: ' . $validator->errors()->first()];
            return back()->withNotify($notify)->withInput();
        }

        try {
            $file = $request->file('file');
            
            // Validate file type based on resource type
            $allowedMimes = $this->getAllowedMimeTypes($request->type);
            if (!in_array($file->getMimeType(), $allowedMimes)) {
                $notify[] = ['error', 'Invalid file type for selected resource type'];
                return back()->withNotify($notify)->withInput();
            }
            
            // Generate unique filename
            $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = 'ib_resources/' . $request->type . 's/' . $fileName;
            
            // Store file
            $file->storeAs('public', $filePath);
            
            // Create resource record
            IbResource::create([
                'title' => $request->title,
                'description' => $request->description,
                'type' => $request->type,
                'file_path' => $filePath,
                'file_name' => $fileName,
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'uploaded_by' => Auth::id(),
                'status' => true
            ]);

            $notify[] = ['success', 'IB resource uploaded successfully'];
            return redirect()->route('admin.ib.resources.index')->withNotify($notify);

        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to upload resource: ' . $e->getMessage()];
            return back()->withNotify($notify)->withInput();
        }
    }

    /**
     * Show edit form
     */
    public function edit($id)
    {
        $pageTitle = 'Edit IB Resource';
        $resource = IbResource::findOrFail($id);
        
        $resourceTypes = [
            'document' => 'Document',
            'image' => 'Image',
            'video' => 'Video',
            'banner' => 'Banner',
            'guide' => 'Guide'
        ];
        
        return view('admin.ib.resources.edit', compact('pageTitle', 'resource', 'resourceTypes'));
    }

    /**
     * Update resource
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:document,image,video,banner,guide',
            'file' => 'nullable|file|max:50000', // 50MB max
        ]);

        if ($validator->fails()) {
            $notify[] = ['error', 'Validation failed: ' . $validator->errors()->first()];
            return back()->withNotify($notify)->withInput();
        }

        try {
            $resource = IbResource::findOrFail($id);
            
            // Update basic info
            $resource->update([
                'title' => $request->title,
                'description' => $request->description,
                'type' => $request->type
            ]);
            
            // Handle file replacement
            if ($request->hasFile('file')) {
                $file = $request->file('file');
                
                // Validate file type
                $allowedMimes = $this->getAllowedMimeTypes($request->type);
                if (!in_array($file->getMimeType(), $allowedMimes)) {
                    $notify[] = ['error', 'Invalid file type for selected resource type'];
                    return back()->withNotify($notify)->withInput();
                }
                
                // Delete old file
                if (Storage::exists('public/' . $resource->file_path)) {
                    Storage::delete('public/' . $resource->file_path);
                }
                
                // Store new file
                $fileName = time() . '_' . $file->getClientOriginalName();
                $filePath = 'ib_resources/' . $request->type . 's/' . $fileName;
                $file->storeAs('public', $filePath);
                
                // Update file info
                $resource->update([
                    'file_path' => $filePath,
                    'file_name' => $fileName,
                    'file_size' => $file->getSize(),
                    'mime_type' => $file->getMimeType()
                ]);
            }

            $notify[] = ['success', 'IB resource updated successfully'];
            return redirect()->route('admin.ib.resources.index')->withNotify($notify);

        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to update resource: ' . $e->getMessage()];
            return back()->withNotify($notify)->withInput();
        }
    }

    /**
     * Toggle resource status
     */
    public function toggleStatus($id)
    {
        try {
            $resource = IbResource::findOrFail($id);
            $resource->toggleStatus();
            
            $status = $resource->status ? 'activated' : 'deactivated';
            $notify[] = ['success', "Resource has been {$status}"];
            
            return back()->withNotify($notify);
            
        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to toggle resource status: ' . $e->getMessage()];
            return back()->withNotify($notify);
        }
    }

    /**
     * Delete resource
     */
    public function destroy($id)
    {
        try {
            $resource = IbResource::findOrFail($id);
            $resource->delete(); // This will also delete the file via model event
            
            $notify[] = ['success', 'Resource deleted successfully'];
            return back()->withNotify($notify);
            
        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to delete resource: ' . $e->getMessage()];
            return back()->withNotify($notify);
        }
    }

    /**
     * Download resource
     */
    public function download($id)
    {
        try {
            $resource = IbResource::findOrFail($id);
            
            if (!Storage::exists('public/' . $resource->file_path)) {
                $notify[] = ['error', 'File not found'];
                return back()->withNotify($notify);
            }
            
            return Storage::download('public/' . $resource->file_path, $resource->file_name);
            
        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to download file: ' . $e->getMessage()];
            return back()->withNotify($notify);
        }
    }

    /**
     * Get allowed MIME types for resource type
     */
    private function getAllowedMimeTypes($type)
    {
        $mimeTypes = [
            'document' => [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'text/plain',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            ],
            'image' => [
                'image/jpeg',
                'image/png',
                'image/gif',
                'image/webp',
                'image/svg+xml'
            ],
            'video' => [
                'video/mp4',
                'video/avi',
                'video/quicktime',
                'video/x-msvideo'
            ],
            'banner' => [
                'image/jpeg',
                'image/png',
                'image/gif',
                'image/webp'
            ],
            'guide' => [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'text/plain'
            ]
        ];
        
        return $mimeTypes[$type] ?? [];
    }
}
