<?php
/**
 * VISUAL BUILDER DATA FLOW ANALYSIS
 * This script analyzes the complete Visual Builder data flow
 */

// Include Laravel bootstrap
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🔍 VISUAL BUILDER DATA FLOW ANALYSIS\n";
echo "====================================\n\n";

// 1. ANALYZE VISUAL BUILDER ARCHITECTURE
echo "1️⃣ VISUAL BUILDER ARCHITECTURE ANALYSIS\n";
echo "========================================\n";

// Check Visual Builder files
$vbFiles = [
    'assets/admin/js/visual-builder-email-editor.js',
    'resources/views/admin/notification/edit.blade.php',
    'vendor/visualbuilder/email-templates' // Check if package exists
];

foreach ($vbFiles as $file) {
    if (file_exists($file)) {
        echo "✅ {$file}: EXISTS\n";
        if (is_file($file)) {
            echo "   - Size: " . filesize($file) . " bytes\n";
            echo "   - Modified: " . date('Y-m-d H:i:s', filemtime($file)) . "\n";
        }
    } else {
        echo "❌ {$file}: NOT FOUND\n";
    }
}

echo "\n";

// 2. ANALYZE FORM STRUCTURE
echo "2️⃣ FORM STRUCTURE ANALYSIS\n";
echo "===========================\n";

try {
    // Read the edit template to understand form structure
    $editTemplate = file_get_contents('resources/views/admin/notification/edit.blade.php');
    
    // Check for form fields
    $formFields = [
        'email_body' => 'name="email_body"',
        'email_body_final' => 'name="email_body_final"',
        'original_email_body' => 'name="original_email_body"',
        'subject' => 'name="subject"',
        '_token' => 'name="_token"'
    ];
    
    foreach ($formFields as $field => $pattern) {
        $found = str_contains($editTemplate, $pattern);
        echo "✅ Form field '{$field}': " . ($found ? 'FOUND' : 'NOT FOUND') . "\n";
    }
    
    // Check for Visual Builder initialization
    $vbPatterns = [
        'visual-builder-email-editor.js' => 'visual-builder-email-editor.js',
        'visualBuilderInstance' => 'visualBuilderInstance',
        'getContent()' => 'getContent()',
        'setContent()' => 'setContent()'
    ];
    
    echo "\n📊 Visual Builder Integration:\n";
    foreach ($vbPatterns as $name => $pattern) {
        $found = str_contains($editTemplate, $pattern);
        echo "   - {$name}: " . ($found ? 'FOUND' : 'NOT FOUND') . "\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Form Analysis Error: " . $e->getMessage() . "\n";
}

echo "\n";

// 3. ANALYZE CONTROLLER DATA FLOW
echo "3️⃣ CONTROLLER DATA FLOW ANALYSIS\n";
echo "=================================\n";

try {
    // Read controller to understand data processing
    $controller = file_get_contents('app/Http/Controllers/Admin/NotificationController.php');
    
    // Check for key processing steps
    $processingSteps = [
        'Request validation' => 'validate(',
        'Template retrieval' => 'NotificationTemplate::find',
        'Email body processing' => 'email_body',
        'Save operation' => '->save()',
        'Logging' => 'Log::',
        'Error handling' => 'catch'
    ];
    
    foreach ($processingSteps as $step => $pattern) {
        $count = substr_count($controller, $pattern);
        echo "✅ {$step}: {$count} occurrences\n";
    }
    
    // Check for specific data flow patterns
    echo "\n📊 Data Flow Patterns:\n";
    $dataFlowPatterns = [
        'email_body_final processing' => 'email_body_final',
        'original_email_body processing' => 'original_email_body',
        'Corruption detection' => 'corruption',
        'Content validation' => 'validation',
        'Database save' => 'save()'
    ];
    
    foreach ($dataFlowPatterns as $pattern => $search) {
        $count = substr_count(strtolower($controller), strtolower($search));
        echo "   - {$pattern}: {$count} references\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Controller Analysis Error: " . $e->getMessage() . "\n";
}

echo "\n";

// 4. SIMULATE REQUEST DATA FLOW
echo "4️⃣ REQUEST DATA FLOW SIMULATION\n";
echo "================================\n";

try {
    // Simulate the exact request data from your logs
    $simulatedRequest = [
        '_token' => 'test_token',
        'subject' => 'Test Subject',
        'email_status' => 'on',
        'email_body' => 'Balance Added Successfully\r\n\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            Balance Notification\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Balance Added Successfully\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Your account balance has been updated with a new deposit.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},We are pleased to inform you that {{amount}} {{currency}} has been added to your account.Amount: {{amount}} {{currency}}New Balance: {{new_balance}} {{currency}}Transaction ID: {{transaction_id}}Date: {{transaction_date}}The funds are now available in your account.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,\r\n                            MBFX Team\r\n                            \r\n                                If you have any questions, please contact our support team.\r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            \r\n                                Account Settings |\r\n                                Contact Support |\r\n                                Privacy Policy\r\n                            \r\n                            \r\n                                &copy; 2025 MBFX. All rights reserved.\r\n                            \r\n                            \r\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\r\n                                update your preferences.',
        'original_email_body' => '&lt;!DOCTYPE html&gt;\r\n&lt;html lang=&quot;en&quot;&gt;\r\n&lt;head&gt;\r\n    &lt;meta charset=&quot;UTF-8&quot;&gt;\r\n    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;&gt;\r\n    &lt;title&gt;Balance Added Successfully&lt;/title&gt;\r\n&lt;/head&gt;\r\n&lt;body style=&quot;margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4; line-height: 1.6;&quot;&gt;...',
        'email_body_final' => 'Balance Added Successfully\r\n\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            Balance Notification\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Balance Added Successfully\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Your account balance has been updated with a new deposit.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},We are pleased to inform you that {{amount}} {{currency}} has been added to your account.Amount: {{amount}} {{currency}}New Balance: {{new_balance}} {{currency}}Transaction ID: {{transaction_id}}Date: {{transaction_date}}The funds are now available in your account.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,\r\n                            MBFX Team\r\n                            \r\n                                If you have any questions, please contact our support team.\r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            \r\n                                Account Settings |\r\n                                Contact Support |\r\n                                Privacy Policy\r\n                            \r\n                            \r\n                                &copy; 2025 MBFX. All rights reserved.\r\n                            \r\n                            \r\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\r\n                                update your preferences.',
        'force_save' => '1',
        'sms_body' => '{{amount}} {{wallet_currency}}  credited in your account. Your Current Balance {{post_balance}} {{wallet_currency}} . Transaction: #{{trx}}. Admin note is {{remark}}'
    ];
    
    echo "📊 Simulated Request Analysis:\n";
    foreach ($simulatedRequest as $key => $value) {
        $length = strlen($value);
        $preview = substr($value, 0, 50) . ($length > 50 ? '...' : '');
        echo "   - {$key}: {$length} chars | {$preview}\n";
    }
    
    // Analyze the corruption pattern
    echo "\n📊 Corruption Pattern Analysis:\n";
    $emailBody = $simulatedRequest['email_body'];
    $emailBodyFinal = $simulatedRequest['email_body_final'];
    $originalEmailBody = $simulatedRequest['original_email_body'];
    
    echo "   - email_body == email_body_final: " . ($emailBody === $emailBodyFinal ? 'YES' : 'NO') . "\n";
    echo "   - email_body has DOCTYPE: " . (str_contains($emailBody, '<!DOCTYPE') ? 'YES' : 'NO') . "\n";
    echo "   - email_body_final has DOCTYPE: " . (str_contains($emailBodyFinal, '<!DOCTYPE') ? 'YES' : 'NO') . "\n";
    echo "   - original_email_body has DOCTYPE: " . (str_contains($originalEmailBody, '&lt;!DOCTYPE') ? 'YES (encoded)' : 'NO') . "\n";
    
    // Decode original and compare
    $decodedOriginal = html_entity_decode($originalEmailBody);
    echo "   - decoded original has DOCTYPE: " . (str_contains($decodedOriginal, '<!DOCTYPE') ? 'YES' : 'NO') . "\n";
    echo "   - decoded original length: " . strlen($decodedOriginal) . "\n";
    
} catch (\Exception $e) {
    echo "❌ Request Simulation Error: " . $e->getMessage() . "\n";
}

echo "\n";

echo "📋 ANALYSIS COMPLETE\n";
echo "====================\n";
echo "This analysis reveals:\n";
echo "1. Visual Builder file structure and integration\n";
echo "2. Form field configuration and data flow\n";
echo "3. Controller processing patterns\n";
echo "4. Actual request data corruption patterns\n\n";

echo "🔍 Run this script to identify Visual Builder architecture issues.\n";

?>
