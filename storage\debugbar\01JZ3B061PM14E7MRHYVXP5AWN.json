{"__meta": {"id": "01JZ3B061PM14E7MRHYVXP5AWN", "datetime": "2025-07-01 15:51:58", "utime": **********.775544, "method": "POST", "uri": "/mbf.mybrokerforex.com-********/admin/notification/template/update/2", "ip": "::1"}, "messages": {"count": 40, "messages": [{"message": "[15:51:58] LOG.info: === TEMPLATE UPDATE DEBUG START ===", "message_html": null, "is_string": false, "label": "info", "time": **********.521517, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: Template ID: 2", "message_html": null, "is_string": false, "label": "info", "time": **********.522294, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: Request Method: POST", "message_html": null, "is_string": false, "label": "info", "time": **********.522721, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: Request URL: https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/update/2", "message_html": null, "is_string": false, "label": "info", "time": **********.523115, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "message_html": null, "is_string": false, "label": "info", "time": **********.523462, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: Server Environment: WINNT - PHP 8.2.12", "message_html": null, "is_string": false, "label": "info", "time": **********.523785, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: Content Type: multipart/form-data; boundary=----WebKitFormBoundary6Nz7k3uBbhAUrvIE", "message_html": null, "is_string": false, "label": "info", "time": **********.524116, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: Content Length: 14693", "message_html": null, "is_string": false, "label": "info", "time": **********.524434, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: Raw POST data keys: _token, template_id, subject, email_status, sms_status, sms_body, email_body_encoded, email_body, email_body_final", "message_html": null, "is_string": false, "label": "info", "time": **********.524783, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: Email body field exists: YES", "message_html": null, "is_string": false, "label": "info", "time": **********.52512, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: Email body final field exists: YES", "message_html": null, "is_string": false, "label": "info", "time": **********.525458, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: ✅ Validation passed", "message_html": null, "is_string": false, "label": "info", "time": **********.720963, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: ✅ Template found - Current subject: Your Account has been Debited", "message_html": null, "is_string": false, "label": "info", "time": **********.729553, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: ✅ Template found - Current email_body length: 4170", "message_html": null, "is_string": false, "label": "info", "time": **********.729986, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: ✅ Subject updated to: Your Account has been Debited", "message_html": null, "is_string": false, "label": "info", "time": **********.730484, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: === ENHANCED EMAIL BODY PROCESSING WITH BASE64 SUPPORT ===", "message_html": null, "is_string": false, "label": "info", "time": **********.730837, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: 📦 Base64 encoded content detected", "message_html": null, "is_string": false, "label": "info", "time": **********.7312, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: ✅ Base64 content decoded successfully, length: 4016", "message_html": null, "is_string": false, "label": "info", "time": **********.731571, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: 📝 Decoded content preview: \n\n\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Balance Deducted</title>\n\n\n    <!-- Full Width Container -->\n    <table width=\"100%\"", "message_html": null, "is_string": false, "label": "info", "time": **********.732007, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: Email body source: email_body_final", "message_html": null, "is_string": false, "label": "info", "time": **********.73238, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: Email body length: 4016", "message_html": null, "is_string": false, "label": "info", "time": **********.732715, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: Email body preview (first 200 chars): \n\n\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Balance Deducted</title>\n\n\n    <!-- Full Width Container -->\n    <table width=\"100%\"", "message_html": null, "is_string": false, "label": "info", "time": **********.733153, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: After minimal Windows cleanup - Email body length: 4016", "message_html": null, "is_string": false, "label": "info", "time": **********.733553, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: Template 2: Using content directly from editor with minimal cleanup", "message_html": null, "is_string": false, "label": "info", "time": **********.733879, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: === FINAL CONTENT READY FOR SAVE ===", "message_html": null, "is_string": false, "label": "info", "time": **********.734199, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: Final email body length: 4016", "message_html": null, "is_string": false, "label": "info", "time": **********.734507, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: Final email body preview (first 300 chars): \n\n\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Balance Deducted</title>\n\n\n    <!-- Full Width Container -->\n    <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: #f4f4f4;\">\n        <tbody><tr>\n", "message_html": null, "is_string": false, "label": "info", "time": **********.73483, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: Template 2: Skipping all corruption detection and complex processing to prevent issues", "message_html": null, "is_string": false, "label": "info", "time": **********.735135, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: === DATABASE SAVE OPERATION DEBUG ===", "message_html": null, "is_string": false, "label": "info", "time": **********.735439, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: Before save - Template email_body length: 4170", "message_html": null, "is_string": false, "label": "info", "time": **********.735771, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: Before save - New email_body length: 4016", "message_html": null, "is_string": false, "label": "info", "time": **********.736086, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: Before save - Template dirty: []", "message_html": null, "is_string": false, "label": "info", "time": **********.736437, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: After setting fields - Template dirty: {\"email_body\":\"\\n\\n\\n    <meta charset=\\\"UTF-8\\\">\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\">\\n    <title>Balance Deducted<\\/title>\\n\\n\\n    <!-- Full Width Container -->\\n    <table width=\\\"100%\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" border=\\\"0\\\" style=\\\"background-color: #f4f4f4;\\\">\\n        <tbody><tr>\\n            <td align=\\\"center\\\">\\n                <!-- Email Container -->\\n                <table width=\\\"600\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" border=\\\"0\\\" style=\\\"background-color: #ffffff; box-shadow: 0 0 10px rgba(0,0,0,0.1);\\\">\\n\\n                    \\n\\n                    <!-- Logo Section with White Background -->\\n                    <tbody><tr>\\n                        <td style=\\\"background-color: #ffffff; padding: 60px 30px; text-align: center; border-bottom: 1px solid #e0e0e0;\\\">\\n                            <img src=\\\"https:\\/\\/mbf.mybrokerforex.com\\/assets\\/images\\/logoIcon\\/logo.png\\\" alt=\\\"MBFX Logo\\\" style=\\\"max-width: 200px; height: auto; display: block; margin: 0 auto;\\\">\\n                        <\\/td>\\n                    <\\/tr>\\n\\n                    <!-- Notification Title Section -->\\n                    <tr>\\n                        <td style=\\\"text-align: center;\\\">\\n                            <h2 style=\\\"margin: 0; color: #dc3545; font-size: 18px; font-weight: bold; text-transform: uppercase; letter-spacing: 0.5px;\\\">Balance Deducted<\\/h2>\\n                        <\\/td>\\n                    <\\/tr>\\n\\n                    <!-- Description Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: #ffffff; padding: 0 40px 20px; text-align: center;\\\">\\n                            <p style=\\\"margin: 0; color: #6c757d; font-size: 16px;\\\">A deduction has been made from your account balance.<\\/p>\\n                        <\\/td>\\n                    <\\/tr>\\n\\n                    <!-- Main Content Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: #ffffff; padding: 20px 40px; color: #333333;\\\">\\n                            <p>Dear {{fullname}},<\\/p><p>A deduction has been made from your account balance as requested.<\\/p><ul><li><strong>Amount Deducted:<\\/strong> {{amount}} {{currency}}<\\/li><li><strong>Remaining Balance:<\\/strong> {{new_balance}} {{currency}}<\\/li><li><strong>Transaction ID:<\\/strong> {{transaction_id}}<\\/li><li><strong>Date:<\\/strong> {{transaction_date}}<\\/li><\\/ul><p>If you have any questions about this transaction, please contact our support team.<\\/p>\\n                        <\\/td>\\n                    <\\/tr>\\n\\n                    <!-- Regards Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: #ffffff; padding: 20px 40px 30px; color: #333333;\\\">\\n                            <p style=\\\"margin: 15px 0 0 0; font-size: 16px;\\\">Best regards,<br><strong>MBFX Team<\\/strong><\\/p>\\n                        <\\/td>\\n                    <\\/tr>\\n\\n                    <!-- Footer Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: #000000; padding: 30px 40px; text-align: center; color: #ffffff;\\\">\\n                            <p style=\\\"margin: 0 0 10px 0; font-size: 18px; color:dc3545; font-weight: bold;\\\">MBFX - Professional Trading Platform<\\/p>\\n                            <p style=\\\"margin: 0 0 15px 0; font-size: 14px; color: #cccccc;\\\">\\n                                {{footer_login_account}} | {{footer_contact_support}} | {{footer_privacy_policy}}\\n                            <\\/p>\\n                            <p style=\\\"margin: 0 0 10px 0; font-size: 14px; color: #cccccc;\\\">\\n                                \\ud83d\\udce7 Email: <EMAIL> | \\ud83c\\udf10 Website: www.mybrokerforex.com\\n                            <\\/p>\\n                            <p style=\\\"margin: 0; font-size: 12px; color: #999999;\\\">\\u00a9 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, contact support.<\\/p>\\n                        <\\/td>\\n                    <\\/tr>\\n\\n                <\\/tbody><\\/table>\\n            <\\/td>\\n        <\\/tr>\\n    <\\/tbody><\\/table>\\n\\n\"}", "message_html": null, "is_string": false, "label": "info", "time": **********.737582, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: After setting fields - Template email_body length: 4016", "message_html": null, "is_string": false, "label": "info", "time": **********.737979, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: Save operation result: SUCCESS", "message_html": null, "is_string": false, "label": "info", "time": **********.749143, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: After refresh - Template email_body length: 4016", "message_html": null, "is_string": false, "label": "info", "time": **********.757323, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: After refresh - Content matches: YES", "message_html": null, "is_string": false, "label": "info", "time": **********.757796, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: ✅ Template 2: Database operation completed", "message_html": null, "is_string": false, "label": "info", "time": **********.75817, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: === TEMPLATE UPDATE DEBUG END ===", "message_html": null, "is_string": false, "label": "info", "time": **********.75854, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:58] LOG.info: 📤 Returning AJAX response", "message_html": null, "is_string": false, "label": "info", "time": **********.758965, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751385117.7548, "end": **********.775662, "duration": 1.0208618640899658, "duration_str": "1.02s", "measures": [{"label": "Booting", "start": 1751385117.7548, "relative_start": 0, "end": **********.360986, "relative_end": **********.360986, "duration": 0.****************, "duration_str": "606ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.361007, "relative_start": 0.****************, "end": **********.775665, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "415ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.428924, "relative_start": 0.***************, "end": **********.440973, "relative_end": **********.440973, "duration": 0.012048959732055664, "duration_str": "12.05ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.768813, "relative_start": 1.****************, "end": **********.76993, "relative_end": **********.76993, "duration": 0.0011169910430908203, "duration_str": "1.12ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.12", "Environment": "production", "Debug Mode": "Enabled", "URL": "localhost/mbf.mybrokerforex.com-********", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 5, "nb_statements": 4, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.016110000000000003, "accumulated_duration_str": "16.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "vendor/beyondcode/laravel-query-detector/src/QueryDetector.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetector.php", "line": 25}, {"index": 10, "namespace": null, "name": "vendor/beyondcode/laravel-query-detector/src/QueryDetectorMiddleware.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetectorMiddleware.php", "line": 30}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21}], "start": **********.426732, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "QueryDetector.php:25", "source": {"index": 9, "namespace": null, "name": "vendor/beyondcode/laravel-query-detector/src/QueryDetector.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetector.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fvendor%2Fbeyondcode%2Flaravel-query-detector%2Fsrc%2FQueryDetector.php&line=25", "ajax": false, "filename": "QueryDetector.php", "line": "25"}, "connection": "mbf-db", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Middleware\\RedirectIfNotAdmin.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4983158, "duration": 0.01026, "duration_str": "10.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mbf-db", "explain": null, "start_percent": 0, "width_percent": 63.687}, {"sql": "select * from `notification_templates` where `notification_templates`.`id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/NotificationController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\NotificationController.php", "line": 122}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.7229838, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "NotificationController.php:122", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/NotificationController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\NotificationController.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FNotificationController.php&line=122", "ajax": false, "filename": "NotificationController.php", "line": "122"}, "connection": "mbf-db", "explain": null, "start_percent": 63.687, "width_percent": 8.07}, {"sql": "update `notification_templates` set `email_body` = '\\n\\n\\n    <meta charset=\\\"UTF-8\\\">\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\">\\n    <title>Balance Deducted</title>\\n\\n\\n    <!-- Full Width Container -->\\n    <table width=\\\"100%\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" border=\\\"0\\\" style=\\\"background-color: #f4f4f4;\\\">\\n        <tbody><tr>\\n            <td align=\\\"center\\\">\\n                <!-- Email Container -->\\n                <table width=\\\"600\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" border=\\\"0\\\" style=\\\"background-color: #ffffff; box-shadow: 0 0 10px rgba(0,0,0,0.1);\\\">\\n\\n                    \\n\\n                    <!-- Logo Section with White Background -->\\n                    <tbody><tr>\\n                        <td style=\\\"background-color: #ffffff; padding: 60px 30px; text-align: center; border-bottom: 1px solid #e0e0e0;\\\">\\n                            <img src=\\\"https://mbf.mybrokerforex.com/assets/images/logoIcon/logo.png\\\" alt=\\\"MBFX Logo\\\" style=\\\"max-width: 200px; height: auto; display: block; margin: 0 auto;\\\">\\n                        </td>\\n                    </tr>\\n\\n                    <!-- Notification Title Section -->\\n                    <tr>\\n                        <td style=\\\"text-align: center;\\\">\\n                            <h2 style=\\\"margin: 0; color: #dc3545; font-size: 18px; font-weight: bold; text-transform: uppercase; letter-spacing: 0.5px;\\\">Balance Deducted</h2>\\n                        </td>\\n                    </tr>\\n\\n                    <!-- Description Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: #ffffff; padding: 0 40px 20px; text-align: center;\\\">\\n                            <p style=\\\"margin: 0; color: #6c757d; font-size: 16px;\\\">A deduction has been made from your account balance.</p>\\n                        </td>\\n                    </tr>\\n\\n                    <!-- Main Content Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: #ffffff; padding: 20px 40px; color: #333333;\\\">\\n                            <p>Dear {{fullname}},</p><p>A deduction has been made from your account balance as requested.</p><ul><li><strong>Amount Deducted:</strong> {{amount}} {{currency}}</li><li><strong>Remaining Balance:</strong> {{new_balance}} {{currency}}</li><li><strong>Transaction ID:</strong> {{transaction_id}}</li><li><strong>Date:</strong> {{transaction_date}}</li></ul><p>If you have any questions about this transaction, please contact our support team.</p>\\n                        </td>\\n                    </tr>\\n\\n                    <!-- Regards Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: #ffffff; padding: 20px 40px 30px; color: #333333;\\\">\\n                            <p style=\\\"margin: 15px 0 0 0; font-size: 16px;\\\">Best regards,<br><strong>MBFX Team</strong></p>\\n                        </td>\\n                    </tr>\\n\\n                    <!-- Footer Section -->\\n                    <tr>\\n                        <td style=\\\"background-color: #000000; padding: 30px 40px; text-align: center; color: #ffffff;\\\">\\n                            <p style=\\\"margin: 0 0 10px 0; font-size: 18px; color:dc3545; font-weight: bold;\\\">MBFX - Professional Trading Platform</p>\\n                            <p style=\\\"margin: 0 0 15px 0; font-size: 14px; color: #cccccc;\\\">\\n                                {{footer_login_account}} | {{footer_contact_support}} | {{footer_privacy_policy}}\\n                            </p>\\n                            <p style=\\\"margin: 0 0 10px 0; font-size: 14px; color: #cccccc;\\\">\\n                                📧 Email: <EMAIL> | 🌐 Website: www.mybrokerforex.com\\n                            </p>\\n                            <p style=\\\"margin: 0; font-size: 12px; color: #999999;\\\">© 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, contact support.</p>\\n                        </td>\\n                    </tr>\\n\\n                </tbody></table>\\n            </td>\\n        </tr>\\n    </tbody></table>\\n\\n', `notification_templates`.`updated_at` = '2025-07-01 15:51:58' where `id` = 2", "type": "query", "params": [], "bindings": ["\n\n\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Balance Deducted</title>\n\n\n    <!-- Full Width Container -->\n    <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: #f4f4f4;\">\n        <tbody><tr>\n            <td align=\"center\">\n                <!-- Email Container -->\n                <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: #ffffff; box-shadow: 0 0 10px rgba(0,0,0,0.1);\">\n\n                    \n\n                    <!-- Logo Section with White Background -->\n                    <tbody><tr>\n                        <td style=\"background-color: #ffffff; padding: 60px 30px; text-align: center; border-bottom: 1px solid #e0e0e0;\">\n                            <img src=\"https://mbf.mybrokerforex.com/assets/images/logoIcon/logo.png\" alt=\"MBFX Logo\" style=\"max-width: 200px; height: auto; display: block; margin: 0 auto;\">\n                        </td>\n                    </tr>\n\n                    <!-- Notification Title Section -->\n                    <tr>\n                        <td style=\"text-align: center;\">\n                            <h2 style=\"margin: 0; color: #dc3545; font-size: 18px; font-weight: bold; text-transform: uppercase; letter-spacing: 0.5px;\">Balance Deducted</h2>\n                        </td>\n                    </tr>\n\n                    <!-- Description Section -->\n                    <tr>\n                        <td style=\"background-color: #ffffff; padding: 0 40px 20px; text-align: center;\">\n                            <p style=\"margin: 0; color: #6c757d; font-size: 16px;\">A deduction has been made from your account balance.</p>\n                        </td>\n                    </tr>\n\n                    <!-- Main Content Section -->\n                    <tr>\n                        <td style=\"background-color: #ffffff; padding: 20px 40px; color: #333333;\">\n                            <p>Dear {{fullname}},</p><p>A deduction has been made from your account balance as requested.</p><ul><li><strong>Amount Deducted:</strong> {{amount}} {{currency}}</li><li><strong>Remaining Balance:</strong> {{new_balance}} {{currency}}</li><li><strong>Transaction ID:</strong> {{transaction_id}}</li><li><strong>Date:</strong> {{transaction_date}}</li></ul><p>If you have any questions about this transaction, please contact our support team.</p>\n                        </td>\n                    </tr>\n\n                    <!-- Regards Section -->\n                    <tr>\n                        <td style=\"background-color: #ffffff; padding: 20px 40px 30px; color: #333333;\">\n                            <p style=\"margin: 15px 0 0 0; font-size: 16px;\">Best regards,<br><strong>MBFX Team</strong></p>\n                        </td>\n                    </tr>\n\n                    <!-- Footer Section -->\n                    <tr>\n                        <td style=\"background-color: #000000; padding: 30px 40px; text-align: center; color: #ffffff;\">\n                            <p style=\"margin: 0 0 10px 0; font-size: 18px; color:dc3545; font-weight: bold;\">MBFX - Professional Trading Platform</p>\n                            <p style=\"margin: 0 0 15px 0; font-size: 14px; color: #cccccc;\">\n                                {{footer_login_account}} | {{footer_contact_support}} | {{footer_privacy_policy}}\n                            </p>\n                            <p style=\"margin: 0 0 10px 0; font-size: 14px; color: #cccccc;\">\n                                📧 Email: <EMAIL> | 🌐 Website: www.mybrokerforex.com\n                            </p>\n                            <p style=\"margin: 0; font-size: 12px; color: #999999;\">© 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, contact support.</p>\n                        </td>\n                    </tr>\n\n                </tbody></table>\n            </td>\n        </tr>\n    </tbody></table>\n\n", "2025-07-01 15:51:58", 2], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/NotificationController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\NotificationController.php", "line": 215}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.73892, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "NotificationController.php:215", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/NotificationController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\NotificationController.php", "line": 215}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FNotificationController.php&line=215", "ajax": false, "filename": "NotificationController.php", "line": "215"}, "connection": "mbf-db", "explain": null, "start_percent": 71.757, "width_percent": 22.905}, {"sql": "select * from `notification_templates` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/NotificationController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\NotificationController.php", "line": 219}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.749715, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "NotificationController.php:219", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/NotificationController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\NotificationController.php", "line": 219}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FNotificationController.php&line=219", "ajax": false, "filename": "NotificationController.php", "line": "219"}, "connection": "mbf-db", "explain": null, "start_percent": 94.662, "width_percent": 5.338}]}, "models": {"data": {"App\\Models\\NotificationTemplate": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FModels%2FNotificationTemplate.php&line=1", "ajax": false, "filename": "NotificationTemplate.php", "line": "?"}}, "App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/update/2", "action_name": "admin.setting.notification.template.update", "controller_action": "App\\Http\\Controllers\\Admin\\NotificationController@templateUpdate", "uri": "POST admin/notification/template/update/{id}", "controller": "App\\Http\\Controllers\\Admin\\NotificationController@templateUpdate<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FNotificationController.php&line=98\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin/notification", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FNotificationController.php&line=98\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/NotificationController.php:98-259</a>", "middleware": "web, admin", "duration": "1.02s", "peak_memory": "30MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2127938778 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2127938778\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">km1Jt0kcPfGW8oS3h5CPM5uLhr566YNkFricmhnA</span>\"\n  \"<span class=sf-dump-key>template_id</span>\" => \"<span class=sf-dump-str>2</span>\"\n  \"<span class=sf-dump-key>subject</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Your Account has been Debited</span>\"\n  \"<span class=sf-dump-key>email_status</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>sms_status</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>sms_body</span>\" => \"<span class=sf-dump-str title=\"166 characters\">{{amount}} {{wallet_currency}}  debited from your account. Your Current Balance {{post_balance}} {{wallet_currency}} . Transaction: #{{trx}}. Admin Note is {{remark}}</span>\"\n  \"<span class=sf-dump-key>email_body_encoded</span>\" => \"<span class=sf-dump-str title=\"5356 characters\">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</span>\"\n  \"<span class=sf-dump-key>email_body</span>\" => \"\"\"\n    <span class=sf-dump-str title=\"4066 characters\">&lt;meta charset=&quot;UTF-8&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">    &lt;title&gt;Balance Deducted&lt;/title&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">    &lt;!-- Full Width Container --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">    &lt;table width=&quot;100%&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; style=&quot;background-color: #f4f4f4;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">        &lt;tbody&gt;&lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">            &lt;td align=&quot;center&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                &lt;!-- Email Container --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; style=&quot;background-color: #ffffff; box-shadow: 0 0 10px rgba(0,0,0,0.1);&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    <span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;!-- Logo Section with White Background --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;tbody&gt;&lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                        &lt;td style=&quot;background-color: #ffffff; padding: 60px 30px; text-align: center; border-bottom: 1px solid #e0e0e0;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                            &lt;img src=&quot;https://mbf.mybrokerforex.com/assets/images/logoIcon/logo.png&quot; alt=&quot;MBFX Logo&quot; style=&quot;max-width: 200px; height: auto; display: block; margin: 0 auto;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;!-- Notification Title Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                        &lt;td style=&quot;text-align: center;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                            &lt;h2 style=&quot;margin: 0; color: #dc3545; font-size: 18px; font-weight: bold; text-transform: uppercase; letter-spacing: 0.5px;&quot;&gt;Balance Deducted&lt;/h2&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;!-- Description Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                        &lt;td style=&quot;background-color: #ffffff; padding: 0 40px 20px; text-align: center;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                            &lt;p style=&quot;margin: 0; color: #6c757d; font-size: 16px;&quot;&gt;A deduction has been made from your account balance.&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;!-- Main Content Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                        &lt;td style=&quot;background-color: #ffffff; padding: 20px 40px; color: #333333;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                            &lt;p&gt;Dear {{fullname}},&lt;/p&gt;&lt;p&gt;A deduction has been made from your account balance as requested.&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Amount Deducted:&lt;/strong&gt; {{amount}} {{currency}}&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Remaining Balance:&lt;/strong&gt; {{new_balance}} {{currency}}&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Transaction ID:&lt;/strong&gt; {{transaction_id}}&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Date:&lt;/strong&gt; {{transaction_date}}&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;If you have any questions about this transaction, please contact our support team.&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;!-- Regards Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                        &lt;td style=&quot;background-color: #ffffff; padding: 20px 40px 30px; color: #333333;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                            &lt;p style=&quot;margin: 15px 0 0 0; font-size: 16px;&quot;&gt;Best regards,&lt;br&gt;&lt;strong&gt;MBFX Team&lt;/strong&gt;&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;!-- Footer Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                        &lt;td style=&quot;background-color: #000000; padding: 30px 40px; text-align: center; color: #ffffff;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                            &lt;p style=&quot;margin: 0 0 10px 0; font-size: 18px; color:dc3545; font-weight: bold;&quot;&gt;MBFX - Professional Trading Platform&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                            &lt;p style=&quot;margin: 0 0 15px 0; font-size: 14px; color: #cccccc;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                                {{footer_login_account}} | {{footer_contact_support}} | {{footer_privacy_policy}}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                            &lt;p style=&quot;margin: 0 0 10px 0; font-size: 14px; color: #cccccc;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                                &#128231; Email: <EMAIL> | &#127760; Website: www.mybrokerforex.com<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                            &lt;p style=&quot;margin: 0; font-size: 12px; color: #999999;&quot;&gt;&#169; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, contact support.&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                &lt;/tbody&gt;&lt;/table&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">            &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">        &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">    &lt;/tbody&gt;&lt;/table&gt;</span>\n    \"\"\"\n  \"<span class=sf-dump-key>email_body_final</span>\" => \"\"\"\n    <span class=sf-dump-str title=\"4066 characters\">&lt;meta charset=&quot;UTF-8&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">    &lt;title&gt;Balance Deducted&lt;/title&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">    &lt;!-- Full Width Container --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">    &lt;table width=&quot;100%&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; style=&quot;background-color: #f4f4f4;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">        &lt;tbody&gt;&lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">            &lt;td align=&quot;center&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                &lt;!-- Email Container --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; style=&quot;background-color: #ffffff; box-shadow: 0 0 10px rgba(0,0,0,0.1);&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    <span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;!-- Logo Section with White Background --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;tbody&gt;&lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                        &lt;td style=&quot;background-color: #ffffff; padding: 60px 30px; text-align: center; border-bottom: 1px solid #e0e0e0;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                            &lt;img src=&quot;https://mbf.mybrokerforex.com/assets/images/logoIcon/logo.png&quot; alt=&quot;MBFX Logo&quot; style=&quot;max-width: 200px; height: auto; display: block; margin: 0 auto;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;!-- Notification Title Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                        &lt;td style=&quot;text-align: center;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                            &lt;h2 style=&quot;margin: 0; color: #dc3545; font-size: 18px; font-weight: bold; text-transform: uppercase; letter-spacing: 0.5px;&quot;&gt;Balance Deducted&lt;/h2&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;!-- Description Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                        &lt;td style=&quot;background-color: #ffffff; padding: 0 40px 20px; text-align: center;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                            &lt;p style=&quot;margin: 0; color: #6c757d; font-size: 16px;&quot;&gt;A deduction has been made from your account balance.&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;!-- Main Content Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                        &lt;td style=&quot;background-color: #ffffff; padding: 20px 40px; color: #333333;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                            &lt;p&gt;Dear {{fullname}},&lt;/p&gt;&lt;p&gt;A deduction has been made from your account balance as requested.&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Amount Deducted:&lt;/strong&gt; {{amount}} {{currency}}&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Remaining Balance:&lt;/strong&gt; {{new_balance}} {{currency}}&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Transaction ID:&lt;/strong&gt; {{transaction_id}}&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Date:&lt;/strong&gt; {{transaction_date}}&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;If you have any questions about this transaction, please contact our support team.&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;!-- Regards Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                        &lt;td style=&quot;background-color: #ffffff; padding: 20px 40px 30px; color: #333333;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                            &lt;p style=&quot;margin: 15px 0 0 0; font-size: 16px;&quot;&gt;Best regards,&lt;br&gt;&lt;strong&gt;MBFX Team&lt;/strong&gt;&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;!-- Footer Section --&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                        &lt;td style=&quot;background-color: #000000; padding: 30px 40px; text-align: center; color: #ffffff;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                            &lt;p style=&quot;margin: 0 0 10px 0; font-size: 18px; color:dc3545; font-weight: bold;&quot;&gt;MBFX - Professional Trading Platform&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                            &lt;p style=&quot;margin: 0 0 15px 0; font-size: 14px; color: #cccccc;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                                {{footer_login_account}} | {{footer_contact_support}} | {{footer_privacy_policy}}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                            &lt;p style=&quot;margin: 0 0 10px 0; font-size: 14px; color: #cccccc;&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                                &#128231; Email: <EMAIL> | &#127760; Website: www.mybrokerforex.com<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                            &lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                            &lt;p style=&quot;margin: 0; font-size: 12px; color: #999999;&quot;&gt;&#169; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, contact support.&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                        &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                    &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">                &lt;/tbody&gt;&lt;/table&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">            &lt;/td&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">        &lt;/tr&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"4066 characters\">    &lt;/tbody&gt;&lt;/table&gt;</span>\n    \"\"\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-101088880 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">14693</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundary6Nz7k3uBbhAUrvIE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">https://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"83 characters\">https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/edit/2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">en-US,en;q=0.9,ms-MY;q=0.8,ms;q=0.7,zh-CN;q=0.6,zh;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImJqNElLUFhyLzd0ZldoVjFQWnBKWUE9PSIsInZhbHVlIjoiWkhPWTRRNWE1bEtVVTZIY3BMMGdaVHlIZ2N2Qm16SVBGSGF0anlKbGdhbTZxWHF0dUZ3TVFhSElJaTRjOE5VdkdyZWczVFdkRnVvT0x1Z0Z0WFlsRWhTNDhPVFpkQVZ4R2RpdWxpOGVtSEdmdVAyd1dDNHZXK25DaGxIN0E5THkiLCJtYWMiOiJiMzFkZWEzODAxNDYwM2Q5ZWFkYzFkOWQ3MjM1MzQzMjk5MjhkYTE4OTk3OGMyMzY2ZDc2NWYzYjEwMWJiNzAwIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlV5UlVvbzVSbjJoZGdzL1c3NlRoS2c9PSIsInZhbHVlIjoiZlpXcVMrNkRrMjVtZjdJTUpGRmpjbFl2M24wRTRURlU3YlppcHpOMjRGWUVZT2ZwZUl3bE0vWjA4Uk5MTWFLUUUwVmJZaDJERHA0c3Z4ODNPeW9ta3RJTVNvVHFEOWZQQ3hIcHliNk5BeHBWZk4wc0RMVDNGYk1qamVGVHpFQWIiLCJtYWMiOiI2MzczNWFjNjIxZDk5NGQ3MTA3OGM5ZDU0ZGQwZWE2M2U3ODljNjU4MDU1YmQ2MjI2MDcwZmJjNTk3NzA4NDM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-101088880\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1827518341 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">km1Jt0kcPfGW8oS3h5CPM5uLhr566YNkFricmhnA</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4DHxxtbYY5xt2mrNBPpWWCmpFks0uensJFa3HeUD</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1827518341\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-243338950 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 15:51:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-243338950\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2122819481 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">km1Jt0kcPfGW8oS3h5CPM5uLhr566YNkFricmhnA</span>\"\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"83 characters\">https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/edit/2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>flasher::envelopes</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2122819481\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/update/2", "action_name": "admin.setting.notification.template.update", "controller_action": "App\\Http\\Controllers\\Admin\\NotificationController@templateUpdate"}, "badge": null}}