
<?php $__env->startSection('content'); ?>
    <div class="row justify-content-start gy-4">
        <div class=" col-xxl-12 col-lg-12">
            <div class="row gy-3">
                <?php
                    $kycContent = getContent('kyc_content.content', true);
                ?>

                <?php if($user->kv == Status::KYC_UNVERIFIED && $user->kyc_rejection_reason): ?>
                    <div class="col-12">
                        <div class="alert alert--danger skeleton" role="alert">
                            <div class="flex-align justify-content-between">
                                <h5 class="alert-heading text--danger mb-2"><?php echo app('translator')->get('KYC Documents Rejected'); ?></h5>
                                <button data-bs-toggle="modal"
                                    data-bs-target="#kycRejectionReason"><?php echo app('translator')->get('Show Reason'); ?></button>
                            </div>
                            <p class="mb-0">
                                <?php echo e(__(@$kycContent->data_values->rejection_content)); ?>

                                <a href="<?php echo e(route('user.kyc.data')); ?>" class="text--base"><?php echo app('translator')->get('See KYC Data'); ?></a>
                            </p>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if($user->kv == Status::KYC_UNVERIFIED): ?>
                    <div class="col-12">
                        <div class="alert alert--danger skeleton" role="alert">
                            <h5 class="alert-heading text--danger mb-2"><?php echo app('translator')->get('KYC Verification Required'); ?></h5>
                            <p class="mb-0">
                                <?php echo e(__(@$kycContent->data_values->unverified_content)); ?>

                                <?php if($user->kyc_rejection_reason): ?>
                                    <br>
                                    <strong><?php echo app('translator')->get('Rejection Reason:'); ?></strong> <?php echo e($user->kyc_rejection_reason); ?>

                                <?php endif; ?>
                                <a href="<?php echo e(route('user.kyc.form')); ?>" class="text--base"><?php echo app('translator')->get('Click here to verify'); ?></a>
                            </p>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if($user->kv == Status::KYC_PENDING): ?>
                    <div class="col-12">
                        <div class="alert alert--warning flex-column justify-content-start align-items-start skeleton"
                            role="alert">
                            <h5 class="alert-heading text--warning mb-2"><?php echo app('translator')->get('KYC Verification Pending'); ?></h5>
                            <p class="mb-0"> <?php echo e(__(@$kycContent->data_values->pending_content)); ?>

                                <a href="<?php echo e(route('user.kyc.data')); ?>" class="text--base"><?php echo app('translator')->get('See KYC Data'); ?></a>
                            </p>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if($user->profile_request == Status::REQUEST_PENDING): ?>
                    <div class="col-12">
                        <div class="alert alert-warning alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo app('translator')->get('Your request is pending.'); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if($user->profile_request == 3 && $user->profile_request_reason): ?>
                    <div class="col-12">
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <div class="d-flex align-items-center"> 
                                <i class="fas fa-exclamation-triangle me-2"></i> 
                                <h5 class="alert-heading text--danger mb-0 me-3"><?php echo app('translator')->get('Your request Has been rejected.'); ?></h5>
                                <a href="<?php echo e(route('user.profile.setting')); ?>" class="text--base"><?php echo app('translator')->get('Edit Profile'); ?></a>
                                <button class="alert-heading text mb-2 ms-auto me-2" data-bs-toggle="modal"
                                    data-bs-target="#profileRejectionReason"><?php echo app('translator')->get('Show Reason'); ?></button>
                                <span class="delete-icon skeleton" data-bs-toggle="tooltip" data-bs-placement="top"
                                    data-bs-title="Delete" data-bs-dismiss="alert" aria-label="Close">
                                    <i class="las la-times"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if($user->profile_request == Status::REQUEST_APPROVE): ?>
                    <div class="col-12">
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo app('translator')->get('Your request has been approved!'); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            <a href="<?php echo e(route('user.profile.setting')); ?>" class="text--base"><?php echo app('translator')->get('Edit Profile'); ?></a>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if(!$user->ts): ?>
                    <div class="col-12">
                        <div class="alert-item 2fa-notice skeleton">
                            <span class="delete-icon skeleton" data-bs-toggle="tooltip" data-bs-placement="top"
                                data-bs-title="Delete">
                                <i class="las la-times"></i></span>
                            <div class="alert flex-align alert--danger remove-2fa-notice" role="alert">
                                <span class="alert__icon">
                                    <i class="fas fa-exclamation"></i>
                                </span>
                                <div class="alert__content">
                                    <span class="alert__title">
                                        <?php echo app('translator')->get('To secure your account add 2FA verification'); ?>.
                                        <a href="<?php echo e(route('user.twofactor')); ?>"
                                            class="text--base text--small"><?php echo app('translator')->get('Enable'); ?></a>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <div class="dashboard-card-wrapper">
                <div class="row gy-4 mb-3 justify-content-center">
                    <div class="col-xxl-2 col-lg-3 col-md-4 col-sm-6">
                        <div class="dashboard-card skeleton">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="dashboard-card__icon text--primary">
                                    <i class="las la-sign-in-alt"></i>
                                </span>
                                <div class="dashboard-card__content">
                                    <a href="<?php echo e(route('user.deposit')); ?>" class="dashboard-card__coin-name mb-0 ">
                                        <?php echo app('translator')->get('Deposit'); ?> </a>
                                    <!-- <h6 class="dashboard-card__coin-title"> <?php echo e(getAmount($widget['open_order'])); ?> </h6> -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xxl-2 col-lg-3 col-md-4 col-sm-6">
                        <div class="dashboard-card skeleton">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="dashboard-card__icon text--success">
                                    <i class="las la-sign-out-alt"></i>
                                </span>
                                <div class="dashboard-card__content">
                                    <a href="<?php echo e(route('user.withdraw')); ?>" class="dashboard-card__coin-name mb-0">
                                        <?php echo app('translator')->get('Withdraw'); ?> </a>
                                    <!-- <h6 class="dashboard-card__coin-title"> <?php echo e(getAmount($widget['completed_order'])); ?> -->
                                    </h6>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xxl-2 col-lg-3 col-md-4 col-sm-6">
                        <div class="dashboard-card skeleton">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="dashboard-card__icon text--danger">
                                    <i class="las la-exchange-alt"></i>
                                </span>
                                <div class="dashboard-card__content">
                                    <a href="<?php echo e(route('user.transfer')); ?>" class="dashboard-card__coin-name mb-0">
                                        <?php echo app('translator')->get('Transfer'); ?> </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xxl-2 col-lg-3 col-md-4 col-sm-6">
                        <div class="dashboard-card skeleton">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="dashboard-card__icon text--danger">
                                    <i class="las la-list-alt"></i>
                                </span>
                                <div class="dashboard-card__content">
                                    <a href="<?php echo e(route('user.transactions')); ?>" class="dashboard-card__coin-name mb-0 ">
                                        <?php echo app('translator')->get('Transactions'); ?> </a>
                                    <!-- <h6 class="dashboard-card__coin-title"> <?php echo e(getAmount($widget['canceled_order'])); ?> -->
                                    </h6>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xxl-2 col-lg-3 col-md-4 col-sm-6">
                        <div class="dashboard-card skeleton">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="dashboard-card__icon text--base">
                                    <span class="icon-trade fs-50"></span>
                                </span>
                                <div class="dashboard-card__content">
                                    <a href="<?php echo e(route('user.trade.history')); ?>"
                                        class="dashboard-card__coin-name mb-0"><?php echo app('translator')->get('Trade History'); ?>
                                    </a>
                                    <!-- <h6 class="dashboard-card__coin-title"> <?php echo e(getAmount($widget['total_trade'])); ?> </h6> -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row gy-4 mb-3 justify-content-center">
                    <div class="col-lg-6">
                        <div class="transection h-100">
                            <h5 class="transection__title skeleton"> <?php echo app('translator')->get('Recent Order'); ?> </h5>
                            <?php $__empty_1 = true; $__currentLoopData = $recentOrders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $recentOrder): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <div class="transection__item skeleton">
                                    <div class="d-flex flex-wrap align-items-center">
                                        <div class="transection__date">
                                            <h6 class="transection__date-number text-black">
                                                <?php echo e(showDateTime($recentOrder->created_at, 'd')); ?>

                                            </h6>
                                            <span class="transection__date-text">
                                                <?php echo e(__(strtoupper(showDateTime($recentOrder->created_at, 'M')))); ?>

                                            </span>
                                        </div>
                                        <div class="transection__content">
                                            <h6 class="transection__content-title">
                                                <?php echo $recentOrder->orderSideBadge; ?>
                                            </h6>
                                            <p class="transection__content-desc">
                                                <?php echo app('translator')->get('Placed an order in the '); ?>
                                                <?php echo e(@$recentOrder->pair->symbol); ?> <?php echo app('translator')->get('pair to'); ?>
                                                <?php echo e(__(strtolower(strip_tags($recentOrder->orderSideBadge)))); ?>

                                                <?php echo e(showAmount($recentOrder->amount)); ?>

                                                <?php echo e(@$recentOrder->pair->coin->symbol); ?>

                                            </p>
                                        </div>
                                    </div>
                                    <?php echo $recentOrder->statusBadge; ?>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <div class="transection__item justify-content-center p-5 skeleton">
                                    <div class="empty-thumb text-center">
                                        <img src="<?php echo e(asset('assets/images/extra_images/empty.png')); ?>" />
                                        <p class="fs-14"><?php echo app('translator')->get('No order found'); ?></p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="transection h-100">
                            <h5 class="transection__title skeleton"> <?php echo app('translator')->get('Recent Transactions'); ?> </h5>
                            <?php $__empty_1 = true; $__currentLoopData = $recentTransactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $recentTransaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <div class="transection__item skeleton">
                                    <div class="d-flex flex-wrap align-items-center">
                                        <div class="transection__date">
                                            <h6 class="transection__date-number text-black">
                                                <?php echo e(showDateTime($recentTransaction->created_at, 'd')); ?>

                                            </h6>
                                            <span class="transection__date-text">
                                                <?php echo e(__(strtoupper(showDateTime($recentTransaction->created_at, 'M')))); ?>

                                            </span>
                                        </div>
                                        <div class="transection__content">
                                            <h6 class="transection__content-title">
                                                <?php echo e(__(ucwords(keyToTitle($recentTransaction->remark)))); ?>

                                            </h6>
                                            <p class="transection__content-desc">
                                                <?php echo e(__($recentTransaction->details)); ?>

                                            </p>
                                        </div>
                                    </div>
                                    <?php if($recentTransaction->trx_type == '+'): ?>
                                        <span class="badge badge--success">
                                            <?php echo app('translator')->get('Plus'); ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="badge badge--danger">
                                            <?php echo app('translator')->get('Minus'); ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <div class="transection__item justify-content-center p-5 skeleton">
                                    <div class="empty-thumb text-center">
                                        <img src="<?php echo e(asset('assets/images/extra_images/empty.png')); ?>" />
                                        <p class="fs-14"><?php echo app('translator')->get('No transactions found'); ?></p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if($user->kv == Status::KYC_UNVERIFIED && $user->kyc_rejection_reason): ?>
        <div class="modal fade custom--modal" id="kycRejectionReason">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header"><i class="fas fa-ban"></i>
                        <h5 class="modal-title"><?php echo app('translator')->get('KYC Document Rejection Reason'); ?></h5>
                        <span type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                            <i class="las la-times"></i>
                        </span>
                    </div>
                    <div class="modal-body text-center">
                        <p><?php echo e(auth()->user()->kyc_rejection_reason); ?></p>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <?php if($user->profile_request == 3 && $user->profile_request_reason): ?>
        <div class="modal fade custom--modal" id="profileRejectionReason">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header"><i class="fas fa-ban"></i>
                        <h5 class="modal-title"><?php echo app('translator')->get('Profile Request Rejection Reason'); ?></h5>
                        <span type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                            <i class="las la-times"></i>
                        </span>
                    </div>
                    <div class="modal-body text-center">
                        <p><?php echo e($user->profile_request_reason); ?></p>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('style'); ?>
<style>
/* CRITICAL: Force transfer widget to use theme colors */
.dashboard-card__icon.text--danger {
    color: rgb(220, 53, 69) !important;
}

.dashboard-card__icon.text--base {
    color: rgb(220, 53, 69) !important;
}

/* Override any blue colors in dashboard widgets */
.dashboard-card a, .dashboard-card__coin-name {
    color: inherit !important;
}

.dashboard-card:hover .dashboard-card__icon {
    color: rgb(200, 35, 51) !important;
}

/* Ensure transfer widget specifically uses red */
.dashboard-card:has(a[href*="transfer"]) .dashboard-card__icon,
a[href*="transfer"] + .dashboard-card__content .dashboard-card__icon,
.dashboard-card .dashboard-card__icon:has(+ .dashboard-card__content a[href*="transfer"]) {
    color: rgb(220, 53, 69) !important;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('topContent'); ?>
    <h4 class="mb-4"><?php echo e(__($pageTitle)); ?></h4>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script'); ?>
    <script>
        $(document).on("click", ".delete-icon", function () {
            $(this).closest(".alert-item").fadeOut();
        });
    </script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make($activeTemplate . 'layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\mbf.mybrokerforex.com-31052025\resources\views/templates/basic/user/dashboard.blade.php ENDPATH**/ ?>