<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Editor Live Server Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-header {
            background: #007bff;
            color: white;
            padding: 15px;
            border-radius: 8px 8px 0 0;
            margin: -20px -20px 20px -20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .asset-test {
            display: inline-block;
            margin: 5px;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>Email Editor Live Server Test Suite</h1>
            <p>Comprehensive testing for Windows Server 2022 + PHP 8.4 + Laravel</p>
        </div>

        <div class="test-section">
            <h3>1. Environment Detection</h3>
            <div id="env-results"></div>
            <button onclick="testEnvironment()">Test Environment</button>
        </div>

        <div class="test-section">
            <h3>2. Asset Loading Test</h3>
            <div id="asset-results"></div>
            <button onclick="testAssets()">Test Asset Loading</button>
        </div>

        <div class="test-section">
            <h3>3. JavaScript Functionality Test</h3>
            <div id="js-results"></div>
            <button onclick="testJavaScript()">Test JavaScript</button>
        </div>

        <div class="test-section">
            <h3>4. DOM Elements Test</h3>
            <div id="dom-results"></div>
            <button onclick="testDOMElements()">Test DOM Elements</button>
        </div>

        <div class="test-section">
            <h3>5. Network Connectivity Test</h3>
            <div id="network-results"></div>
            <button onclick="testNetwork()">Test Network</button>
        </div>

        <div class="test-section">
            <h3>6. Console Log Monitor</h3>
            <div id="console-logs" class="log-output"></div>
            <button onclick="clearLogs()">Clear Logs</button>
            <button onclick="exportLogs()">Export Logs</button>
        </div>

        <div class="test-section">
            <h3>7. Quick Actions</h3>
            <button onclick="runAllTests()">Run All Tests</button>
            <button onclick="generateReport()">Generate Report</button>
            <button onclick="window.location.reload()">Refresh Page</button>
        </div>
    </div>

    <script>
        let testLogs = [];
        let originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn,
            info: console.info
        };

        // Override console methods to capture logs
        ['log', 'error', 'warn', 'info'].forEach(method => {
            console[method] = function(...args) {
                const timestamp = new Date().toISOString();
                const message = args.map(arg => 
                    typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
                ).join(' ');
                
                testLogs.push(`[${timestamp}] [${method.toUpperCase()}] ${message}`);
                updateConsoleDisplay();
                
                // Call original console method
                originalConsole[method].apply(console, args);
            };
        });

        function updateConsoleDisplay() {
            const logsDiv = document.getElementById('console-logs');
            logsDiv.textContent = testLogs.slice(-50).join('\n'); // Show last 50 logs
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }

        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            container.appendChild(div);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        function testEnvironment() {
            clearResults('env-results');
            
            // Browser info
            addResult('env-results', `Browser: ${navigator.userAgent}`, 'info');
            addResult('env-results', `Screen: ${screen.width}x${screen.height}`, 'info');
            addResult('env-results', `Viewport: ${window.innerWidth}x${window.innerHeight}`, 'info');
            
            // JavaScript features
            const features = {
                'ES6 Support': () => { try { eval('() => {}'); return true; } catch(e) { return false; } },
                'Fetch API': () => typeof fetch !== 'undefined',
                'Local Storage': () => typeof localStorage !== 'undefined',
                'Session Storage': () => typeof sessionStorage !== 'undefined',
                'WebSocket': () => typeof WebSocket !== 'undefined'
            };
            
            Object.entries(features).forEach(([name, test]) => {
                const supported = test();
                addResult('env-results', `${name}: ${supported ? 'Supported' : 'Not Supported'}`, 
                    supported ? 'success' : 'error');
            });
        }

        function testAssets() {
            clearResults('asset-results');
            
            const assets = [
                { type: 'css', url: '/assets/admin/css/simple-email-editor.css' },
                { type: 'js', url: '/assets/admin/js/simple-email-editor.js' },
                { type: 'js', url: '/assets/admin/js/simple-email-editor-enhanced.js' },
                { type: 'css', url: '/assets/admin/css/bootstrap.min.css' },
                { type: 'js', url: '/assets/admin/js/bootstrap.min.js' }
            ];
            
            assets.forEach(asset => {
                const element = asset.type === 'css' ? 
                    document.createElement('link') : 
                    document.createElement('script');
                
                if (asset.type === 'css') {
                    element.rel = 'stylesheet';
                    element.href = asset.url;
                } else {
                    element.src = asset.url;
                }
                
                element.onload = () => {
                    addResult('asset-results', `✓ ${asset.url}`, 'success');
                };
                
                element.onerror = () => {
                    addResult('asset-results', `✗ ${asset.url}`, 'error');
                };
                
                document.head.appendChild(element);
            });
        }

        function testJavaScript() {
            clearResults('js-results');
            
            // Test jQuery
            if (typeof $ !== 'undefined') {
                addResult('js-results', `jQuery ${$.fn.jquery} loaded`, 'success');
            } else {
                addResult('js-results', 'jQuery not loaded', 'error');
            }
            
            // Test Bootstrap
            if (typeof bootstrap !== 'undefined') {
                addResult('js-results', 'Bootstrap JS loaded', 'success');
            } else {
                addResult('js-results', 'Bootstrap JS not loaded', 'warning');
            }
            
            // Test global variables
            const globals = ['templateData', 'globalTemplateData', 'csrfToken'];
            globals.forEach(global => {
                if (typeof window[global] !== 'undefined') {
                    addResult('js-results', `Global ${global} available`, 'success');
                } else {
                    addResult('js-results', `Global ${global} missing`, 'warning');
                }
            });
            
            // Test CSRF token
            const csrfMeta = document.querySelector('meta[name="csrf-token"]');
            if (csrfMeta) {
                addResult('js-results', 'CSRF token meta tag found', 'success');
            } else {
                addResult('js-results', 'CSRF token meta tag missing', 'error');
            }
        }

        function testDOMElements() {
            clearResults('dom-results');
            
            const selectors = [
                '#visual-editor-tab',
                '#html-editor-tab',
                '#visual-editor',
                '#html-editor',
                '.shortcode-btn',
                '#test-email-btn',
                '#preview-btn',
                'form[action*="template"]'
            ];
            
            selectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                if (elements.length > 0) {
                    addResult('dom-results', `✓ ${selector} (${elements.length} found)`, 'success');
                } else {
                    addResult('dom-results', `✗ ${selector} not found`, 'warning');
                }
            });
        }

        function testNetwork() {
            clearResults('network-results');
            
            // Test basic connectivity
            fetch(window.location.origin + '/assets/admin/css/simple-email-editor.css', { method: 'HEAD' })
                .then(response => {
                    if (response.ok) {
                        addResult('network-results', 'CSS asset reachable', 'success');
                    } else {
                        addResult('network-results', `CSS asset error: ${response.status}`, 'error');
                    }
                })
                .catch(error => {
                    addResult('network-results', `CSS asset fetch failed: ${error.message}`, 'error');
                });
            
            // Test enhanced JS
            fetch(window.location.origin + '/assets/admin/js/simple-email-editor-enhanced.js', { method: 'HEAD' })
                .then(response => {
                    if (response.ok) {
                        addResult('network-results', 'Enhanced JS asset reachable', 'success');
                    } else {
                        addResult('network-results', `Enhanced JS asset error: ${response.status}`, 'error');
                    }
                })
                .catch(error => {
                    addResult('network-results', `Enhanced JS asset fetch failed: ${error.message}`, 'error');
                });
        }

        function runAllTests() {
            console.log('[EMAIL-EDITOR-TEST] Starting comprehensive test suite...');
            
            setTimeout(() => testEnvironment(), 100);
            setTimeout(() => testAssets(), 500);
            setTimeout(() => testJavaScript(), 1000);
            setTimeout(() => testDOMElements(), 1500);
            setTimeout(() => testNetwork(), 2000);
            
            setTimeout(() => {
                console.log('[EMAIL-EDITOR-TEST] All tests completed');
            }, 3000);
        }

        function clearLogs() {
            testLogs = [];
            updateConsoleDisplay();
        }

        function exportLogs() {
            const logData = testLogs.join('\n');
            const blob = new Blob([logData], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `email-editor-test-logs-${new Date().toISOString().slice(0,19).replace(/:/g,'-')}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function generateReport() {
            const report = {
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                url: window.location.href,
                viewport: `${window.innerWidth}x${window.innerHeight}`,
                logs: testLogs,
                testResults: {
                    environment: document.getElementById('env-results').innerHTML,
                    assets: document.getElementById('asset-results').innerHTML,
                    javascript: document.getElementById('js-results').innerHTML,
                    dom: document.getElementById('dom-results').innerHTML,
                    network: document.getElementById('network-results').innerHTML
                }
            };
            
            const reportData = JSON.stringify(report, null, 2);
            const blob = new Blob([reportData], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `email-editor-test-report-${new Date().toISOString().slice(0,19).replace(/:/g,'-')}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Auto-run basic tests on page load
        window.addEventListener('load', () => {
            console.log('[EMAIL-EDITOR-TEST] Test page loaded, running basic checks...');
            setTimeout(() => {
                testEnvironment();
                testJavaScript();
            }, 1000);
        });
    </script>
</body>
</html>