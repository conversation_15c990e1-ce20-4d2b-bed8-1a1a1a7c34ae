<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>
    <rewrite>
      <rules>
        <!--
        IMPORTANT: Rules are processed in order.
        More specific rules should come BEFORE more general rules.
        This rule directs all requests starting with /api/ to your Python script.
        -->
        <rule name="Python API Gateway" stopProcessing="true">
          <match url="^api/(.*)" ignoreCase="true" />
          <conditions logicalGrouping="MatchAll">
            <!-- Ensure the request is not for an actual file or directory named 'api' -->
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
          </conditions>
          <!--
          Action: Rewrite the URL internally to point to your mt5manager.py script
          inside the 'python' subdirectory. {R:1} captures everything after '/api/'
          and appends it to the script path, allowing your Python app to handle routing.
          appendQueryString="true" ensures any query parameters are passed along.
          -->
          <action type="Rewrite" url="python/mt5manager.py/{R:1}" appendQueryString="true" />
        </rule>

        <!--
        Main Rule: This is a general fallback rule.
        It rewrites any request that doesn't map to an existing file or directory
        to the root of your application (typically handled by index.php for PHP apps).
        It has stopProcessing="true" so it won't interfere with the API Gateway rule if that matches.
        -->
        <rule name="Main Rule" stopProcessing="true">
          <match url=".*" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
          </conditions>
          <action type="Rewrite" url="/" />
        </rule>
      </rules>
    </rewrite>

    <tracing>
      <traceFailedRequests>
        <clear />
      </traceFailedRequests>
    </tracing>

    <security>
      <requestFiltering>
        <requestLimits>
          <headerLimits>
            <add header="maxAllowedContentLength" sizeLimit="30000000" />
            <add header="maxUrl" sizeLimit="1000" />
            <add header="MaxFieldLength" sizeLimit="10000" />
          </headerLimits>
          <!-- FIX: Removed the extraneous '1' character that caused a configuration error. -->
        </requestLimits>
      </requestFiltering>
    </security>

    <httpErrors errorMode="Detailed">
      <remove statusCode="400" />
      <error statusCode="400" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\bad_request.html" />
      <remove statusCode="401" />
      <error statusCode="401" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\unauthorized.html" />
      <remove statusCode="403" />
      <error statusCode="403" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\forbidden.html" />
      <remove statusCode="404" />
      <error statusCode="404" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\not_found.html" />
      <remove statusCode="405" />
      <error statusCode="405" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\method_not_allowed.html" />
      <remove statusCode="406" />
      <error statusCode="406" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\not_acceptable.html" />
      <remove statusCode="407" />
      <error statusCode="407" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\proxy_authentication_required.html" />
      <remove statusCode="412" />
      <error statusCode="412" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\precondition_failed.html" />
      <remove statusCode="414" />
      <error statusCode="414" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\request-uri_too_long.html" />
      <remove statusCode="415" />
      <error statusCode="415" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\unsupported_media_type.html" />
      <remove statusCode="500" />
      <error statusCode="500" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\internal_server_error.html" />
      <remove statusCode="501" />
      <error statusCode="501" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\not_implemented.html" />
      <remove statusCode="502" />
      <error statusCode="502" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\bad_gateway.html" />
      <remove statusCode="503" />
      <error statusCode="503" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\maintenance.html" />
    </httpErrors>

    <handlers accessPolicy="Read, Execute, Script">
      <remove name="PythonScript" />
      <!--
      Python FastCGI Handler:
      This tells IIS how to process files ending with '.py'.
      It uses your Python 3.13 interpreter and the wfastcgi.py bridge.
      -->
      <add name="PythonScript" path="*.py" verb="*" modules="FastCgiModule" scriptProcessor="C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe|C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\wfastcgi.py" resourceType="File" requireAccess="Script" />
    </handlers>
  </system.webServer>

  <system.web>
    <compilation tempDirectory="C:\Inetpub\vhosts\mybrokerforex.com\tmp" />
  </system.web>

  <!--
  Application Settings (appSettings) for Python WSGI applications:
  This section provides environment variables that wfastcgi.py uses
  to locate and run your Python web application.
  -->
  <appSettings>
    <!--
    PYTHONPATH: The absolute path to the root directory of your Python project.
    This is where Python will start looking for modules to import.
    -->
    <add key="PYTHONPATH" value="C:\Inetpub\vhosts\mybrokerforex.com\mbf.mybrokerforex.com" />

    <!--
    WSGI_HANDLER: The entry point for your WSGI application.
    This is CRUCIAL and must exactly match the callable WSGI object in your Python code.
    Since mt5manager.py is in the 'python/' subfolder, and your PYTHONPATH is the parent,
    Python will import it as 'python.mt5manager'.
    You MUST verify 'application' is the correct callable name within mt5manager.py.
    Examples:
    - For a Flask app named 'app' in 'mt5manager.py': "python.mt5manager.app"
    - For a Django app: "your_project_name.wsgi.application"
    - For a generic WSGI callable named 'application' in 'mt5manager.py': "python.mt5manager.application"
    -->
    <add key="WSGI_HANDLER" value="python.mt5manager.application" />

    <!--
    WSGI_LOG: Optional but HIGHLY RECOMMENDED for debugging.
    wfastcgi will write its logs and Python application errors to this file.
    Ensure the IIS_IUSRS user (or your application pool identity) has WRITE permissions to this path.
    -->
    <add key="WSGI_LOG" value="C:\inetpub\vhosts\mybrokerforex.com\mbf.mybrokerforex.com\wfastcgi.log" />

    <!--
    WSGI_DEBUG: Optional. Set to "1" for more verbose debugging output from wfastcgi.
    Useful during initial setup. Remember to remove or set to "0" for production.
    -->
    <!-- <add key="WSGI_DEBUG" value="1" /> -->
  </appSettings>

</configuration>