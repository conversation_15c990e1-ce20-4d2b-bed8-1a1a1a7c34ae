<?php
/**
 * COMPREHENSIVE SYSTEM-LEVEL DIAGNOSTIC
 * This script performs deep system analysis beyond mass assignment
 */

// Include Laravel bootstrap
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🔍 COMPREHENSIVE SYSTEM-LEVEL DIAGNOSTIC\n";
echo "=========================================\n\n";

// 1. MIDDLEWARE ANALYSIS
echo "1️⃣ MIDDLEWARE ANALYSIS\n";
echo "======================\n";

try {
    // Get route information
    $route = \Route::getRoutes()->getByName('admin.setting.notification.template.update');
    if ($route) {
        echo "✅ Route found: " . $route->uri() . "\n";
        echo "✅ Route methods: " . implode(', ', $route->methods()) . "\n";
        
        // Get middleware
        $middleware = $route->middleware();
        echo "✅ Route middleware: " . (empty($middleware) ? 'NONE' : implode(', ', $middleware)) . "\n";
        
        // Check for specific problematic middleware
        $problematicMiddleware = ['throttle', 'verified', 'signed', 'cache.headers'];
        foreach ($problematicMiddleware as $mw) {
            if (in_array($mw, $middleware)) {
                echo "⚠️  POTENTIAL ISSUE: {$mw} middleware detected\n";
            }
        }
    } else {
        echo "❌ Route not found: admin.setting.notification.template.update\n";
    }
    
    // Check global middleware
    $globalMiddleware = app('router')->getMiddleware();
    echo "✅ Global middleware count: " . count($globalMiddleware) . "\n";
    
    // Check for CSRF issues
    echo "✅ CSRF protection: " . (config('session.driver') !== null ? 'ENABLED' : 'DISABLED') . "\n";
    
} catch (\Exception $e) {
    echo "❌ Middleware analysis error: " . $e->getMessage() . "\n";
}

echo "\n";

// 2. MODEL EVENTS & OBSERVERS ANALYSIS
echo "2️⃣ MODEL EVENTS & OBSERVERS ANALYSIS\n";
echo "====================================\n";

try {
    // Check for model events
    $model = new \App\Models\NotificationTemplate();
    $events = $model->getObservableEvents();
    echo "✅ Observable events: " . implode(', ', $events) . "\n";
    
    // Check for registered observers
    $dispatcher = app('events');
    $listeners = $dispatcher->getListeners();
    
    $modelEvents = [];
    foreach ($listeners as $event => $eventListeners) {
        if (str_contains($event, 'NotificationTemplate') || str_contains($event, 'eloquent.')) {
            $modelEvents[$event] = count($eventListeners);
        }
    }
    
    if (!empty($modelEvents)) {
        echo "⚠️  MODEL EVENTS DETECTED:\n";
        foreach ($modelEvents as $event => $count) {
            echo "   - {$event}: {$count} listeners\n";
        }
    } else {
        echo "✅ No problematic model events found\n";
    }
    
    // Test if events are blocking saves
    echo "\n📊 Testing model events impact...\n";
    
    $template = \App\Models\NotificationTemplate::find(1);
    $originalContent = $template->email_body;
    
    // Temporarily disable events
    \App\Models\NotificationTemplate::unsetEventDispatcher();
    
    $template->email_body = $originalContent . "\n<!-- Event test -->";
    $saveWithoutEvents = $template->save();
    
    // Re-enable events
    \App\Models\NotificationTemplate::setEventDispatcher(app('events'));
    
    echo "✅ Save without events: " . ($saveWithoutEvents ? 'SUCCESS' : 'FAILED') . "\n";
    
    // Restore original
    $template->email_body = $originalContent;
    $template->save();
    
} catch (\Exception $e) {
    echo "❌ Model events analysis error: " . $e->getMessage() . "\n";
}

echo "\n";

// 3. DATABASE TRANSACTION ANALYSIS
echo "3️⃣ DATABASE TRANSACTION ANALYSIS\n";
echo "=================================\n";

try {
    // Check transaction level
    $transactionLevel = \DB::transactionLevel();
    echo "✅ Current transaction level: {$transactionLevel}\n";
    
    // Check database configuration
    $dbConfig = config('database.connections.' . config('database.default'));
    echo "✅ Database engine: " . ($dbConfig['engine'] ?? 'default') . "\n";
    echo "✅ Database charset: " . ($dbConfig['charset'] ?? 'default') . "\n";
    echo "✅ Database collation: " . ($dbConfig['collation'] ?? 'default') . "\n";
    
    // Test transaction behavior
    echo "\n📊 Testing transaction behavior...\n";
    
    \DB::beginTransaction();
    
    $template = \App\Models\NotificationTemplate::find(1);
    $originalContent = $template->email_body;
    
    $template->email_body = $originalContent . "\n<!-- Transaction test -->";
    $saveInTransaction = $template->save();
    
    echo "✅ Save in transaction: " . ($saveInTransaction ? 'SUCCESS' : 'FAILED') . "\n";
    
    // Check if changes are visible before commit
    $template->refresh();
    $changesVisible = str_contains($template->email_body, 'Transaction test');
    echo "✅ Changes visible before commit: " . ($changesVisible ? 'YES' : 'NO') . "\n";
    
    \DB::rollback();
    
    // Check if rollback worked
    $template->refresh();
    $rolledBack = !str_contains($template->email_body, 'Transaction test');
    echo "✅ Rollback successful: " . ($rolledBack ? 'YES' : 'NO') . "\n";
    
} catch (\Exception $e) {
    echo "❌ Transaction analysis error: " . $e->getMessage() . "\n";
    try {
        \DB::rollback();
    } catch (\Exception $rollbackError) {
        // Ignore rollback errors
    }
}

echo "\n";

// 4. REQUEST/RESPONSE CYCLE ANALYSIS
echo "4️⃣ REQUEST/RESPONSE CYCLE ANALYSIS\n";
echo "==================================\n";

try {
    // Simulate the exact request
    echo "📊 Simulating actual form submission...\n";
    
    // Create a mock request
    $requestData = [
        '_token' => csrf_token(),
        'subject' => 'Test Subject [DIAGNOSTIC]',
        'email_status' => 'on',
        'email_body' => 'Test email body content',
        'email_body_final' => 'Test email body final content',
        'original_email_body' => htmlspecialchars('Original content'),
        'sms_body' => 'Test SMS body'
    ];
    
    // Test controller method directly
    $controller = new \App\Http\Controllers\Admin\NotificationController();
    
    // Create mock request
    $request = new \Illuminate\Http\Request();
    $request->merge($requestData);
    $request->setMethod('POST');
    
    echo "✅ Mock request created with " . count($requestData) . " fields\n";
    echo "✅ CSRF token: " . (strlen($requestData['_token']) > 0 ? 'PRESENT' : 'MISSING') . "\n";
    
    // Check if all required fields are present
    $requiredFields = ['subject', 'email_body', 'sms_body'];
    foreach ($requiredFields as $field) {
        $present = $request->has($field);
        echo "✅ Field '{$field}': " . ($present ? 'PRESENT' : 'MISSING') . "\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Request cycle analysis error: " . $e->getMessage() . "\n";
}

echo "\n";

// 5. FILE SYSTEM & PERMISSIONS ANALYSIS
echo "5️⃣ FILE SYSTEM & PERMISSIONS ANALYSIS\n";
echo "======================================\n";

try {
    // Check critical directories
    $criticalPaths = [
        'storage/logs' => 'Log directory',
        'storage/framework/cache' => 'Cache directory', 
        'storage/framework/sessions' => 'Session directory',
        'storage/framework/views' => 'View cache directory',
        'bootstrap/cache' => 'Bootstrap cache'
    ];
    
    foreach ($criticalPaths as $path => $description) {
        if (is_dir($path)) {
            $writable = is_writable($path);
            $perms = substr(sprintf('%o', fileperms($path)), -4);
            echo "✅ {$description}: {$perms} " . ($writable ? '(WRITABLE)' : '(READ-ONLY)') . "\n";
            
            if (!$writable) {
                echo "⚠️  POTENTIAL ISSUE: {$description} is not writable\n";
            }
        } else {
            echo "❌ {$description}: NOT FOUND\n";
        }
    }
    
    // Check disk space
    $freeSpace = disk_free_space('.');
    $totalSpace = disk_total_space('.');
    $usedPercent = (($totalSpace - $freeSpace) / $totalSpace) * 100;
    
    echo "✅ Disk usage: " . number_format($usedPercent, 1) . "%\n";
    
    if ($usedPercent > 95) {
        echo "⚠️  POTENTIAL ISSUE: Disk space critically low\n";
    }
    
} catch (\Exception $e) {
    echo "❌ File system analysis error: " . $e->getMessage() . "\n";
}

echo "\n";

// 6. PHP CONFIGURATION ANALYSIS
echo "6️⃣ PHP CONFIGURATION ANALYSIS\n";
echo "==============================\n";

try {
    // Check critical PHP settings
    $criticalSettings = [
        'memory_limit' => 'Memory limit',
        'max_execution_time' => 'Execution time limit',
        'post_max_size' => 'POST max size',
        'upload_max_filesize' => 'Upload max size',
        'max_input_vars' => 'Max input variables',
        'max_input_time' => 'Max input time'
    ];
    
    foreach ($criticalSettings as $setting => $description) {
        $value = ini_get($setting);
        echo "✅ {$description}: {$value}\n";
        
        // Check for problematic values
        if ($setting === 'max_input_vars' && (int)$value < 1000) {
            echo "⚠️  POTENTIAL ISSUE: max_input_vars too low for complex forms\n";
        }
        
        if ($setting === 'max_execution_time' && (int)$value < 30) {
            echo "⚠️  POTENTIAL ISSUE: execution time limit too low\n";
        }
    }
    
    // Check for disabled functions
    $disabledFunctions = ini_get('disable_functions');
    if (!empty($disabledFunctions)) {
        echo "⚠️  DISABLED FUNCTIONS: {$disabledFunctions}\n";
    } else {
        echo "✅ No disabled functions\n";
    }
    
} catch (\Exception $e) {
    echo "❌ PHP configuration analysis error: " . $e->getMessage() . "\n";
}

echo "\n📋 DIAGNOSTIC COMPLETE\n";
echo "======================\n";
echo "Review the results above to identify:\n";
echo "1. Middleware that might be blocking requests\n";
echo "2. Model events that might be preventing saves\n";
echo "3. Database transaction issues\n";
echo "4. Request/response cycle problems\n";
echo "5. File system permission issues\n";
echo "6. PHP configuration limitations\n\n";

echo "🔍 This diagnostic goes beyond mass assignment to find the real issue.\n";

?>
