<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('account_levels', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique(); // Premium, VIP, Copy Trading, etc.
            $table->string('image')->nullable(); // Account type image
            $table->string('platform_group_default')->nullable(); // Default platform group
            $table->string('trading_server_live')->nullable(); // Live trading server
            $table->boolean('enable_separate_swap_free')->default(false);
            $table->string('platform_group_swap_free')->nullable(); // Swap-free platform group
            $table->json('leverage_options')->nullable(); // Available leverage options
            $table->json('country_restrictions')->nullable(); // Country restrictions
            $table->json('tags')->nullable(); // Additional tags
            $table->boolean('status')->default(true);
            $table->timestamps();
            
            // Indexes
            $table->index('name');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('account_levels');
    }
};
