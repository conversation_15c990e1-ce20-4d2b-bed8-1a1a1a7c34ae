{"__meta": {"id": "01JZ2KYM2D9B0R9YDNBH3G4MAR", "datetime": "2025-07-01 09:09:10", "utime": **********.350859, "method": "GET", "uri": "/mbf.mybrokerforex.com-********/user/kyc-form", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751360948.869762, "end": **********.350889, "duration": 1.4811270236968994, "duration_str": "1.48s", "measures": [{"label": "Booting", "start": 1751360948.869762, "relative_start": 0, "end": **********.35509, "relative_end": **********.35509, "duration": 0.*****************, "duration_str": "485ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.355112, "relative_start": 0.****************, "end": **********.350893, "relative_end": 4.0531158447265625e-06, "duration": 0.****************, "duration_str": "996ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.410319, "relative_start": 0.****************, "end": **********.423288, "relative_end": **********.423288, "duration": 0.012969017028808594, "duration_str": "12.97ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.633355, "relative_start": 0.****************, "end": **********.339928, "relative_end": **********.339928, "duration": 0.****************, "duration_str": "707ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: templates.basic.user.kyc.form", "start": **********.641551, "relative_start": 0.****************, "end": **********.641551, "relative_end": **********.641551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.viser-form", "start": **********.996128, "relative_start": 1.***************, "end": **********.996128, "relative_end": **********.996128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.layouts.master", "start": **********.300565, "relative_start": 1.4308030605316162, "end": **********.300565, "relative_end": **********.300565, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.seo", "start": **********.309317, "relative_start": 1.4395551681518555, "end": **********.309317, "relative_end": **********.309317, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.partials.user_sidebar", "start": **********.313213, "relative_start": 1.443451166152954, "end": **********.313213, "relative_end": **********.313213, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.partials.user_topbar", "start": **********.31981, "relative_start": 1.4500479698181152, "end": **********.31981, "relative_end": **********.31981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.notify", "start": **********.322548, "relative_start": 1.4527859687805176, "end": **********.322548, "relative_end": **********.322548, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.plugins", "start": **********.325851, "relative_start": 1.4560890197753906, "end": **********.325851, "relative_end": **********.325851, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 28893792, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.12", "Environment": "production", "Debug Mode": "Enabled", "URL": "localhost/mbf.mybrokerforex.com-********", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 8, "nb_templates": 8, "templates": [{"name": "templates.basic.user.kyc.form", "param_count": null, "params": [], "start": **********.641284, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/templates/basic/user/kyc/form.blade.phptemplates.basic.user.kyc.form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fuser%2Fkyc%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}}, {"name": "components.viser-form", "param_count": null, "params": [], "start": **********.995965, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/components/viser-form.blade.phpcomponents.viser-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fcomponents%2Fviser-form.blade.php&line=1", "ajax": false, "filename": "viser-form.blade.php", "line": "?"}}, {"name": "templates.basic.layouts.master", "param_count": null, "params": [], "start": **********.300403, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/templates/basic/layouts/master.blade.phptemplates.basic.layouts.master", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}}, {"name": "partials.seo", "param_count": null, "params": [], "start": **********.309163, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/partials/seo.blade.phppartials.seo", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fpartials%2Fseo.blade.php&line=1", "ajax": false, "filename": "seo.blade.php", "line": "?"}}, {"name": "templates.basic.partials.user_sidebar", "param_count": null, "params": [], "start": **********.31305, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/templates/basic/partials/user_sidebar.blade.phptemplates.basic.partials.user_sidebar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Fuser_sidebar.blade.php&line=1", "ajax": false, "filename": "user_sidebar.blade.php", "line": "?"}}, {"name": "templates.basic.partials.user_topbar", "param_count": null, "params": [], "start": **********.319656, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/templates/basic/partials/user_topbar.blade.phptemplates.basic.partials.user_topbar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Fuser_topbar.blade.php&line=1", "ajax": false, "filename": "user_topbar.blade.php", "line": "?"}}, {"name": "partials.notify", "param_count": null, "params": [], "start": **********.322395, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/partials/notify.blade.phppartials.notify", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fpartials%2Fnotify.blade.php&line=1", "ajax": false, "filename": "notify.blade.php", "line": "?"}}, {"name": "partials.plugins", "param_count": null, "params": [], "start": **********.325701, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/partials/plugins.blade.phppartials.plugins", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fpartials%2Fplugins.blade.php&line=1", "ajax": false, "filename": "plugins.blade.php", "line": "?"}}]}, "queries": {"count": 7, "nb_statements": 6, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.08842000000000001, "accumulated_duration_str": "88.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "vendor/beyondcode/laravel-query-detector/src/QueryDetector.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetector.php", "line": 25}, {"index": 10, "namespace": null, "name": "vendor/beyondcode/laravel-query-detector/src/QueryDetectorMiddleware.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetectorMiddleware.php", "line": 30}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21}], "start": **********.408807, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "QueryDetector.php:25", "source": {"index": 9, "namespace": null, "name": "vendor/beyondcode/laravel-query-detector/src/QueryDetector.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetector.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fvendor%2Fbeyondcode%2Flaravel-query-detector%2Fsrc%2FQueryDetector.php&line=25", "ajax": false, "filename": "QueryDetector.php", "line": "25"}, "connection": "mbf-db", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 53630 limit 1", "type": "query", "params": [], "bindings": [53630], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.464907, "duration": 0.02578, "duration_str": "25.78ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mbf-db", "explain": null, "start_percent": 0, "width_percent": 29.156}, {"sql": "select * from `forms` where `act` = 'kyc' limit 1", "type": "query", "params": [], "bindings": ["kyc"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\User\\UserController.php", "line": 219}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.560572, "duration": 0.05886, "duration_str": "58.86ms", "memory": 0, "memory_str": null, "filename": "UserController.php:219", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\User\\UserController.php", "line": 219}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FUser%2FUserController.php&line=219", "ajax": false, "filename": "UserController.php", "line": "219"}, "connection": "mbf-db", "explain": null, "start_percent": 29.156, "width_percent": 66.569}, {"sql": "select * from `forms` where `act` = 'kyc' limit 1", "type": "query", "params": [], "bindings": ["kyc"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/View/Components/ViserForm.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\View\\Components\\ViserForm.php", "line": 25}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 102}, {"index": 18, "namespace": "view", "name": "templates.basic.user.kyc.form", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/templates/basic/user/kyc/form.blade.php", "line": 17}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9864068, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "ViserForm.php:25", "source": {"index": 16, "namespace": null, "name": "app/View/Components/ViserForm.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\View\\Components\\ViserForm.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FView%2FComponents%2FViserForm.php&line=25", "ajax": false, "filename": "ViserForm.php", "line": "25"}, "connection": "mbf-db", "explain": null, "start_percent": 95.725, "width_percent": 1.074}, {"sql": "select * from `frontends` where `data_keys` = 'seo.data' limit 1", "type": "query", "params": [], "bindings": ["seo.data"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 90}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}, {"index": 23, "namespace": "view", "name": "templates.basic.layouts.master", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/templates/basic/layouts/master.blade.php", "line": 8}], "start": **********.3031008, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:90", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FProviders%2FAppServiceProvider.php&line=90", "ajax": false, "filename": "AppServiceProvider.php", "line": "90"}, "connection": "mbf-db", "explain": null, "start_percent": 96.799, "width_percent": 1.063}, {"sql": "select * from `extensions` where `act` = 'tawk-chat' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["tawk-chat", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Helpers\\helpers.php", "line": 105}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.327311, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "helpers.php:105", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Helpers\\helpers.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=105", "ajax": false, "filename": "helpers.php", "line": "105"}, "connection": "mbf-db", "explain": null, "start_percent": 97.862, "width_percent": 1.165}, {"sql": "select * from `extensions` where `act` = 'google-analytics' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["google-analytics", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Helpers\\helpers.php", "line": 105}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.333642, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "helpers.php:105", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Helpers\\helpers.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=105", "ajax": false, "filename": "helpers.php", "line": "105"}, "connection": "mbf-db", "explain": null, "start_percent": 99.027, "width_percent": 0.973}]}, "models": {"data": {"App\\Models\\Form": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FModels%2FForm.php&line=1", "ajax": false, "filename": "Form.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Frontend": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FModels%2FFrontend.php&line=1", "ajax": false, "filename": "Frontend.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://localhost/mbf.mybrokerforex.com-********/user/kyc-form", "action_name": "user.kyc.form", "controller_action": "App\\Http\\Controllers\\User\\UserController@kycForm", "uri": "GET user/kyc-form", "controller": "App\\Http\\Controllers\\User\\UserController@kycForm<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FUser%2FUserController.php&line=208\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\User", "prefix": "/user", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FUser%2FUserController.php&line=208\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/User/UserController.php:208-221</a>", "middleware": "web, maintenance, auth, check.status, registration.complete", "duration": "1.48s", "peak_memory": "30MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1734169418 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1734169418\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1307458312 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1307458312\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1541251398 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">https://localhost/mbf.mybrokerforex.com-********/user/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">en-US,en;q=0.9,ms-MY;q=0.8,ms;q=0.7,zh-CN;q=0.6,zh;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Im4xMFVpekhQTUhJZlJpall5dmwybnc9PSIsInZhbHVlIjoib0twbjRqVmxCcFkveGFLalUvUTNhaFlSYzJ4b0pYZGllTE5qaUN1MCtIbUVOd2pQbExyK2lxUXFNY1o4akRhcklsMEtOaDJDREY5TC9tZFF3bUcwMW5LekJ2Q1JXZTI4WXgvVnBsSFpWVmZxTENVSis0VkwwSlFkb1NDS0ZqL2MiLCJtYWMiOiJlNjFjOTMzYmE5ODNiOTVmNjZjM2Y5MjdlZjdlMzc1M2FlZTBjMDViNzZiMTQ5YzQ3NWQzZDg1OTc5NTgyMDgxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Im5nUUlkSGc5R0JWRFNxZHRWcG4zeXc9PSIsInZhbHVlIjoibi85VkxRVDBKVXUyVUhTbHVXa2xVWDNOVmE0OXNkLzJpV01LUXY5UWdOdTZ5elBRNWIvNVJUamx5WkM3U3ZYMkhDQVlhVWNjN2d1ZkI2YktFUnRMb0lKWTJNTWdSaStqeVJ4TEpOZHdQWjd4ejBpZzRubjk2OGxIZ091bldQUEMiLCJtYWMiOiIzN2RhYWU1MDRhMDdiMmQwMzIzOTgwNThjYjFiNmNkZDhkM2RjNGMyMDgyYzY5Y2Q3MTBjMjlkYTdjNjRhZTM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1541251398\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2070577348 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6wydTXTQETk9uM050OalbDmHEZPTqQskjidNxWTA</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pKwSOwBxhh7a0b12K8rA1re0Q1SyO815L9Vl6qQN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2070577348\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1450485615 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 09:09:09 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1450485615\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2095334932 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6wydTXTQETk9uM050OalbDmHEZPTqQskjidNxWTA</span>\"\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"63 characters\">https://localhost/mbf.mybrokerforex.com-********/user/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>flasher::envelopes</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>53630</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2095334932\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://localhost/mbf.mybrokerforex.com-********/user/kyc-form", "action_name": "user.kyc.form", "controller_action": "App\\Http\\Controllers\\User\\UserController@kycForm"}, "badge": null}}