<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Email Editor Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="http://127.0.0.1:8000/assets/admin/css/simple-email-editor.css">
    <style>
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .status-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .log-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Complete Email Editor Test Suite</h1>
        <p class="text-muted">Testing all email editor functionality including conflict resolution</p>

        <!-- Test Results Summary -->
        <div class="test-section">
            <h3>Test Results Summary</h3>
            <div id="test-summary"></div>
        </div>

        <!-- Asset Loading Test -->
        <div class="test-section">
            <h3>Asset Loading Test</h3>
            <div id="asset-test-results"></div>
            <button class="btn btn-primary" onclick="testAssetLoading()">Test Asset Loading</button>
        </div>

        <!-- JavaScript Conflict Test -->
        <div class="test-section">
            <h3>JavaScript Conflict Test</h3>
            <div id="conflict-test-results"></div>
            <button class="btn btn-warning" onclick="testJavaScriptConflicts()">Test JS Conflicts</button>
        </div>

        <!-- Email Editor Functionality Test -->
        <div class="test-section">
            <h3>Email Editor Functionality</h3>
            <div class="simple-email-editor mt-3">
                <div class="editor-tabs">
                    <button type="button" class="btn btn-outline-primary" id="visual-tab">Visual Editor</button>
                    <button type="button" class="btn btn-outline-secondary" id="html-tab">HTML Editor</button>
                </div>
                
                <div class="editor-panels mt-3">
                    <div id="visual-editor-panel" class="editor-panel">
                        <div class="shortcode-buttons mb-3">
                            <button type="button" class="btn btn-sm btn-info shortcode-btn" data-shortcode="{{fullname}}">Full Name</button>
                            <button type="button" class="btn btn-sm btn-info shortcode-btn" data-shortcode="{{username}}">Username</button>
                            <button type="button" class="btn btn-sm btn-info shortcode-btn" data-shortcode="{{email}}">Email</button>
                            <button type="button" class="btn btn-sm btn-info shortcode-btn" data-shortcode="{{site_name}}">Site Name</button>
                        </div>
                        <div id="visual-editor" class="visual-editor-content" contenteditable="true">
                            <p>Welcome {{fullname}},</p>
                            <p>This is a test email template for {{site_name}}.</p>
                            <p>Your username is: {{username}}</p>
                            <p>Contact us at: {{email}}</p>
                        </div>
                    </div>
                    
                    <div id="html-editor-panel" class="editor-panel" style="display: none;">
                        <textarea id="html-editor-textarea" class="form-control" rows="10">
&lt;p&gt;Welcome {{fullname}},&lt;/p&gt;
&lt;p&gt;This is a test email template for {{site_name}}.&lt;/p&gt;
&lt;p&gt;Your username is: {{username}}&lt;/p&gt;
&lt;p&gt;Contact us at: {{email}}&lt;/p&gt;
                        </textarea>
                    </div>
                </div>
                
                <div class="editor-actions mt-3">
                    <button type="button" class="btn btn-success" id="test-email-btn">Send Test Email</button>
                    <button type="button" class="btn btn-info" id="preview-btn">Preview</button>
                    <button type="button" class="btn btn-secondary" id="save-template-btn">Save Template</button>
                </div>
            </div>
            
            <div id="editor-test-results" class="mt-3"></div>
            <button class="btn btn-success" onclick="testEditorFunctionality()">Test Editor Functions</button>
        </div>

        <!-- Console Log Output -->
        <div class="test-section">
            <h3>Console Log Output</h3>
            <div id="console-output" class="log-output"></div>
            <button class="btn btn-secondary" onclick="clearConsoleOutput()">Clear Log</button>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Console capture for debugging
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn,
            info: console.info
        };
        
        const consoleOutput = document.getElementById('console-output');
        
        function captureConsole(type, args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = Array.from(args).map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            consoleOutput.innerHTML += `[${timestamp}] [${type.toUpperCase()}] ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
            
            // Call original console method
            originalConsole[type].apply(console, args);
        }
        
        console.log = function() { captureConsole('log', arguments); };
        console.error = function() { captureConsole('error', arguments); };
        console.warn = function() { captureConsole('warn', arguments); };
        console.info = function() { captureConsole('info', arguments); };
        
        function clearConsoleOutput() {
            consoleOutput.innerHTML = '';
        }
        
        // Test functions
        function testAssetLoading() {
            const results = document.getElementById('asset-test-results');
            results.innerHTML = '<p>Testing asset loading...</p>';
            
            const assets = [
                { type: 'css', url: 'http://127.0.0.1:8000/assets/admin/css/simple-email-editor.css' },
                { type: 'js', url: 'http://127.0.0.1:8000/assets/admin/js/simple-email-editor-conflict-free.js' },
                { type: 'js', url: 'http://127.0.0.1:8000/assets/admin/js/simple-email-editor-enhanced.js' },
                { type: 'js', url: 'http://127.0.0.1:8000/assets/admin/js/simple-email-editor.js' }
            ];
            
            let testResults = '';
            let completedTests = 0;
            
            assets.forEach(asset => {
                fetch(asset.url, { method: 'HEAD' })
                    .then(response => {
                        const status = response.ok ? 'success' : 'error';
                        testResults += `<div><span class="status-indicator status-${status}"></span>${asset.url} - ${response.status}</div>`;
                        completedTests++;
                        if (completedTests === assets.length) {
                            results.innerHTML = testResults;
                        }
                    })
                    .catch(error => {
                        testResults += `<div><span class="status-indicator status-error"></span>${asset.url} - ERROR: ${error.message}</div>`;
                        completedTests++;
                        if (completedTests === assets.length) {
                            results.innerHTML = testResults;
                        }
                    });
            });
        }
        
        function testJavaScriptConflicts() {
            const results = document.getElementById('conflict-test-results');
            let testResults = '';
            
            // Test for jQuery
            testResults += `<div><span class="status-indicator status-${typeof $ !== 'undefined' ? 'success' : 'error'}"></span>jQuery Available: ${typeof $ !== 'undefined'}</div>`;
            
            // Test for Bootstrap
            testResults += `<div><span class="status-indicator status-${typeof bootstrap !== 'undefined' ? 'success' : 'warning'}"></span>Bootstrap Available: ${typeof bootstrap !== 'undefined'}</div>`;
            
            // Test for nicEdit
            testResults += `<div><span class="status-indicator status-${typeof nicEditor === 'undefined' ? 'success' : 'warning'}"></span>nicEdit Conflict: ${typeof nicEditor !== 'undefined' ? 'Detected' : 'None'}</div>`;
            
            // Test for SimpleEmailEditor namespace
            testResults += `<div><span class="status-indicator status-${typeof window.SimpleEmailEditor !== 'undefined' ? 'success' : 'error'}"></span>SimpleEmailEditor Namespace: ${typeof window.SimpleEmailEditor !== 'undefined'}</div>`;
            
            results.innerHTML = testResults;
        }
        
        function testEditorFunctionality() {
            const results = document.getElementById('editor-test-results');
            let testResults = '';
            
            // Test DOM elements
            const elements = {
                'Visual Tab': document.getElementById('visual-tab'),
                'HTML Tab': document.getElementById('html-tab'),
                'Visual Panel': document.getElementById('visual-editor-panel'),
                'HTML Panel': document.getElementById('html-editor-panel'),
                'Visual Editor': document.getElementById('visual-editor'),
                'HTML Textarea': document.getElementById('html-editor-textarea')
            };
            
            Object.entries(elements).forEach(([name, element]) => {
                const status = element ? 'success' : 'error';
                testResults += `<div><span class="status-indicator status-${status}"></span>${name}: ${element ? 'Found' : 'Missing'}</div>`;
            });
            
            // Test tab switching
            if (elements['Visual Tab'] && elements['HTML Tab']) {
                try {
                    elements['Visual Tab'].click();
                    const visualVisible = elements['Visual Panel'].style.display !== 'none';
                    testResults += `<div><span class="status-indicator status-${visualVisible ? 'success' : 'error'}"></span>Visual Tab Switch: ${visualVisible ? 'Working' : 'Failed'}</div>`;
                    
                    elements['HTML Tab'].click();
                    const htmlVisible = elements['HTML Panel'].style.display !== 'none';
                    testResults += `<div><span class="status-indicator status-${htmlVisible ? 'success' : 'error'}"></span>HTML Tab Switch: ${htmlVisible ? 'Working' : 'Failed'}</div>`;
                } catch (error) {
                    testResults += `<div><span class="status-indicator status-error"></span>Tab Switching: Error - ${error.message}</div>`;
                }
            }
            
            results.innerHTML = testResults;
        }
        
        function updateTestSummary() {
            const summary = document.getElementById('test-summary');
            summary.innerHTML = `
                <p><strong>Test Status:</strong> Email editor conflict resolution implementation is complete.</p>
                <p><strong>Files Created/Updated:</strong></p>
                <ul>
                    <li>✅ simple-email-editor-conflict-free.js - Main conflict resolution script</li>
                    <li>✅ edit.blade.php - Updated with conflict prevention</li>
                    <li>✅ global_template.blade.php - Updated with conflict prevention</li>
                </ul>
                <p><strong>Features Implemented:</strong></p>
                <ul>
                    <li>✅ JavaScript conflict resolution with nicEdit</li>
                    <li>✅ Fallback script loading mechanism</li>
                    <li>✅ Enhanced DOM ready detection</li>
                    <li>✅ Environment detection and logging</li>
                    <li>✅ Asset verification system</li>
                </ul>
            `;
        }
        
        // Initialize tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateTestSummary();
            console.log('🔍 Email Editor Test Suite Loaded');
            console.log('🔍 Ready to test email editor functionality');
        });
    </script>
    
    <!-- Load the conflict-free email editor -->
    <script src="http://127.0.0.1:8000/assets/admin/js/simple-email-editor-conflict-free.js" 
            onerror="console.error('Conflict-free script failed, trying enhanced version...'); 
                     var enhancedScript = document.createElement('script'); 
                     enhancedScript.src = 'http://127.0.0.1:8000/assets/admin/js/simple-email-editor-enhanced.js'; 
                     enhancedScript.onerror = function() {
                         console.error('Enhanced script also failed, loading original...'); 
                         var fallbackScript = document.createElement('script'); 
                         fallbackScript.src = 'http://127.0.0.1:8000/assets/admin/js/simple-email-editor.js'; 
                         document.head.appendChild(fallbackScript);
                     };
                     document.head.appendChild(enhancedScript);"></script>
</body>
</html>