# Multi-Level IB (Partnership) System - User Guide

## 📋 Table of Contents
1. [What is the Multi-Level IB System?](#what-is-the-multi-level-ib-system)
2. [How It Works](#how-it-works)
3. [User Side Features](#user-side-features)
4. [Admin Side Features](#admin-side-features)
5. [Step-by-Step Process](#step-by-step-process)
6. [Commission Structure](#commission-structure)
7. [Network Visualization](#network-visualization)

---

## 🤝 What is the Multi-Level IB System?

The Multi-Level IB (Introducing Broker) System, also called the **Partnership System**, allows users to earn commissions by referring new clients and building a network of sub-partners. It's a hierarchical system where:

- **Master IBs** can recruit **Sub IBs**
- **Sub IBs** can recruit more **Sub IBs** (multiple levels)
- Everyone earns commissions from their network's trading activity
- The deeper your network, the more you can earn

---

## ⚙️ How It Works

### Basic Structure:
```
Master IB (Level 1)
├── Sub IB (Level 2)
│   ├── Client
│   └── Sub IB (Level 3)
│       ├── Client
│       └── Client
├── Client
└── Sub IB (Level 2)
    ├── Client
    └── Client
```

### Key Concepts:
- **Referral Codes**: Each IB gets a unique referral code
- **Commission Sharing**: Commissions are shared across multiple levels
- **Real-time Tracking**: All activities are tracked in real-time
- **MT5 Integration**: Connected to MT5 trading accounts

---

## 👤 User Side Features

### 1. **Registration & Application**
- **Sign Up**: Create account with basic information
- **KYC Verification**: Complete identity verification
- **IB Application**: Apply to become an IB partner
- **Approval Process**: Wait for admin approval

### 2. **Dashboard Overview**
- **Commission Summary**: View total earnings
- **Network Statistics**: See your referral network size
- **Recent Activity**: Track latest commissions and referrals
- **MT5 Account Info**: View connected trading accounts

### 3. **Partnership Network Page**
- **Visual Network Tree**: Interactive tree showing your entire network
- **Network Statistics**: 
  - Direct referrals count
  - Total network size
  - Active traders
  - Commission breakdown
- **Export Options**: Download network charts as images

### 4. **Referral Management**
- **Referral Code**: Get your unique referral link
- **Share Tools**: Easy sharing via social media, email
- **Referral Tracking**: See who joined using your code
- **Performance Metrics**: Track conversion rates

### 5. **Commission Tracking**
- **Real-time Updates**: See commissions as they're earned
- **Detailed History**: View all commission transactions
- **Level Breakdown**: See earnings from each network level
- **MT5 Integration**: Commissions based on actual trading volume

### 6. **Network Visualization**
- **Interactive Tree**: Click to expand/collapse network branches
- **Color-coded Nodes**: 
  - Red: Master IB
  - Green: Sub IB
  - Gray: Regular Client
- **Detailed Information**: Each node shows:
  - Name and contact info
  - MT5 account details
  - Account balance
  - Total deposits
  - Number of referrals

---

## 🛠️ Admin Side Features

### 1. **IB Management**
- **Application Review**: Approve/reject IB applications
- **IB Level Management**: Set up commission levels and rules
- **Status Control**: Activate/deactivate IB accounts
- **Bulk Operations**: Manage multiple IBs at once

### 2. **Commission Configuration**
- **Level Setup**: Configure commission percentages for each level
- **Rule Management**: Set commission calculation rules
- **Symbol Groups**: Different commissions for different trading instruments
- **Real-time Adjustments**: Modify rates without system restart

### 3. **Network Monitoring**
- **Complete Network View**: See entire IB network structure
- **Performance Analytics**: Track network performance metrics
- **User Details**: Access detailed information for any network member
- **Activity Monitoring**: Real-time activity tracking

### 4. **Reporting & Analytics**
- **Commission Reports**: Detailed commission breakdowns
- **Network Growth**: Track network expansion over time
- **Performance Metrics**: Analyze IB effectiveness
- **Export Options**: Generate reports in various formats

### 5. **User Management**
- **Profile Management**: Edit user information
- **Account Linking**: Connect/disconnect MT5 accounts
- **Status Changes**: Modify user and IB statuses
- **Communication Tools**: Send notifications to IBs

---

## 📈 Step-by-Step Process

### For New Users:
1. **Register** → Create account on the platform
2. **Verify** → Complete KYC verification process
3. **Apply** → Submit IB application (optional)
4. **Wait** → Admin reviews and approves application
5. **Start** → Begin referring clients and earning commissions

### For Existing IBs:
1. **Share** → Use referral code to invite new clients
2. **Monitor** → Track referrals and network growth
3. **Earn** → Receive commissions from network trading
4. **Expand** → Recruit Sub IBs to grow your network
5. **Manage** → Use dashboard tools to optimize performance

### For Admins:
1. **Review** → Check IB applications
2. **Configure** → Set commission rates and rules
3. **Monitor** → Track network performance
4. **Support** → Assist IBs with questions and issues
5. **Analyze** → Generate reports and optimize system

---

## 💰 Commission Structure

### Default Structure:
- **Master IB**: 50% of total commission
- **Sub IB Level 1**: 30% of total commission  
- **Sub IB Level 2**: 20% of total commission

### Example:
If a client generates $100 in commissions:
- **Master IB** gets: $50
- **Sub IB Level 1** gets: $30
- **Sub IB Level 2** gets: $20

### Features:
- **Configurable Rates**: Admin can adjust percentages
- **Multiple Levels**: Support for unlimited levels
- **Real-time Calculation**: Commissions calculated instantly
- **MT5 Integration**: Based on actual trading data

---

## 🌐 Network Visualization

### Interactive Features:
- **Expand/Collapse**: Click nodes to show/hide sub-networks
- **Zoom & Pan**: Navigate large networks easily
- **Search Function**: Find specific users quickly
- **Export Options**: Save network charts as images

### Node Information:
Each network node displays:
- **User Name**: Full name of the person
- **IB Status**: Master IB, Sub IB, or Client
- **MT5 Account**: Trading account number
- **Balance**: Current account balance
- **Deposits**: Total deposit amount and count
- **Referrals**: Number of direct referrals

### Color Coding:
- **🔴 Red**: Master IB (top level)
- **🟢 Green**: Sub IB (intermediate levels)
- **⚫ Gray**: Regular Client (end users)

---

## 🎯 Key Benefits

### For IBs:
- **Passive Income**: Earn from network trading activity
- **Scalable Earnings**: More referrals = more income
- **Real-time Tracking**: Monitor performance instantly
- **Professional Tools**: Advanced dashboard and analytics

### For Clients:
- **Trusted Referrals**: Join through known contacts
- **Support Network**: Get help from referring IB
- **Transparent System**: Clear commission structure
- **Quality Service**: IBs motivated to provide good service

### For Admins:
- **Automated System**: Minimal manual intervention required
- **Scalable Growth**: System grows with user base
- **Complete Control**: Full oversight and configuration options
- **Detailed Analytics**: Comprehensive reporting and insights

---

## 📞 Support & Contact

For questions about the Multi-Level IB System:
- **Users**: Contact your referring IB or customer support
- **IBs**: Use the dashboard help section or contact admin
- **Admins**: Refer to technical documentation or system logs

---

## 📱 Page-by-Page User Walkthrough

### **User Dashboard**
- **Location**: Main dashboard after login
- **What you see**:
  - Commission widgets showing total earnings
  - Network size statistics
  - Recent activity feed
  - Quick action buttons
- **What you can do**:
  - View commission summary
  - Access partnership network
  - Check recent referrals
  - Navigate to other sections

### **Partnership Network Page**
- **Location**: Dashboard → Partnership → Network
- **What you see**:
  - Interactive network tree visualization
  - Your position at the center (Master IB)
  - All referrals branching out from you
  - Statistics panel on the side
- **What you can do**:
  - Click nodes to see detailed information
  - Expand/collapse network branches
  - Export network chart as image
  - Search for specific users

### **Referral Management**
- **Location**: Dashboard → Partnership → Referrals
- **What you see**:
  - Your unique referral code/link
  - List of people who used your code
  - Referral statistics and conversion rates
  - Social sharing buttons
- **What you can do**:
  - Copy referral link
  - Share on social media
  - Track referral performance
  - View referral details

### **Commission History**
- **Location**: Dashboard → Partnership → Commissions
- **What you see**:
  - Detailed list of all commission payments
  - Date, amount, and source of each commission
  - Filter options by date range
  - Total earnings summary
- **What you can do**:
  - Filter commissions by date
  - View commission details
  - Export commission reports
  - Track earning trends

---

## 🛡️ Page-by-Page Admin Walkthrough

### **Admin Dashboard**
- **Location**: Admin panel main page
- **What you see**:
  - Total IB statistics
  - Recent IB applications
  - System performance metrics
  - Quick action shortcuts
- **What you can do**:
  - Monitor overall system health
  - Access IB management tools
  - Review pending applications
  - Generate system reports

### **IB Applications Management**
- **Location**: Admin → Partnership → Applications
- **What you see**:
  - List of pending IB applications
  - User details and verification status
  - Application history and notes
  - Approval/rejection options
- **What you can do**:
  - Review application details
  - Approve or reject applications
  - Add notes to applications
  - Bulk process multiple applications

### **User Network View**
- **Location**: Admin → Users → [Select User] → Network Tab
- **What you see**:
  - Complete network tree for selected user
  - All levels of referrals and sub-IBs
  - Detailed user information in each node
  - Network statistics and metrics
- **What you can do**:
  - View any user's complete network
  - Access detailed user information
  - Monitor network growth
  - Export network visualizations

### **Commission Configuration**
- **Location**: Admin → Partnership → Commission Settings
- **What you see**:
  - Commission level settings
  - Percentage configuration for each level
  - Symbol group commission rates
  - Rule management interface
- **What you can do**:
  - Set commission percentages
  - Configure multi-level rates
  - Create symbol-specific rules
  - Apply changes system-wide

### **IB Management**
- **Location**: Admin → Partnership → Manage IBs
- **What you see**:
  - Complete list of all IBs
  - IB status and performance metrics
  - Network size and commission data
  - Management action buttons
- **What you can do**:
  - Activate/deactivate IBs
  - Modify IB levels and permissions
  - View detailed IB performance
  - Send notifications to IBs

---

## 🔄 Common Workflows

### **New User Becoming an IB**
1. **Registration** → User creates account
2. **Verification** → Completes KYC process
3. **Application** → Submits IB application form
4. **Review** → Admin reviews application
5. **Approval** → Admin approves and activates IB status
6. **Activation** → User gains access to IB features
7. **Start Earning** → User begins referring and earning

### **IB Building Their Network**
1. **Get Referral Code** → Access unique referral link
2. **Share Code** → Distribute to potential clients
3. **Track Referrals** → Monitor who joins using code
4. **Recruit Sub-IBs** → Invite others to become IBs
5. **Monitor Network** → Use network visualization tools
6. **Earn Commissions** → Receive payments from network activity

### **Admin Managing the System**
1. **Monitor Applications** → Review new IB requests
2. **Configure Rates** → Set commission percentages
3. **Approve IBs** → Activate new IB accounts
4. **Monitor Performance** → Track system metrics
5. **Generate Reports** → Create performance analytics
6. **Support Users** → Assist with questions and issues

---

*This comprehensive guide covers all aspects of the Multi-Level IB System from both user and admin perspectives. The system is designed to be intuitive while providing powerful tools for network growth and commission management.*
