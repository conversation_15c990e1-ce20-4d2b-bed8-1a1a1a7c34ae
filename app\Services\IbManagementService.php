<?php

namespace App\Services;

use App\Models\User;
use App\Models\FormIb;
use App\Models\IbGroup;
use App\Models\IbLevel;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class IbManagementService
{
    /**
     * Process IB application
     */
    public function processIbApplication($userId, $applicationData)
    {
        try {
            DB::beginTransaction();

            $user = User::findOrFail($userId);

            // Check if user already has an IB application
            if ($user->ib_status) {
                throw new \Exception('User already has an IB application');
            }

            // Create or update IB form
            $formData = array_merge($applicationData, [
                'user_id' => $userId,
                'username' => $user->username,
                'email' => $user->email,
                'ib_status' => 'pending'
            ]);

            FormIb::updateOrCreate(
                ['user_id' => $userId],
                $formData
            );

            // Update user status
            $user->ib_status = 'pending';
            $user->partner = 2; // Maintain compatibility with existing system
            $user->save();

            DB::commit();

            Log::info("IB application submitted", ['user_id' => $userId]);

            // Send notification to admin
            $this->notifyAdminOfNewApplication($user, $applicationData);

            // Send confirmation email to user
            $this->notifyUserOfApplicationSubmission($user);

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to process IB application: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Approve IB application
     */
    public function approveIbApplication($userId, $approvedBy, $ibType = 'master', $groupId = null, $parentId = null)
    {
        try {
            DB::beginTransaction();

            $user = User::findOrFail($userId);

            // Check if user can be approved
            if ($user->ib_status == User::IB_STATUS_APPROVED) {
                throw new \Exception('User is already an approved IB');
            }

            // Use hierarchy service to create IB structure
            $hierarchyService = new IbHierarchyService();
            $hierarchyService->createIbHierarchy($userId, $ibType, $parentId, $groupId, $approvedBy);

            // Update both partner and ib_status fields for compatibility - CRITICAL FIX
            $user->partner = 1; // Approved (legacy field)
            $user->ib_status = \App\Models\User::IB_STATUS_APPROVED; // Approved (new field)
            $user->save();

            DB::commit();

            Log::info("IB application approved", [
                'user_id' => $userId,
                'approved_by' => $approvedBy,
                'ib_type' => $ibType
            ]);

            // Send approval notification
            $this->notifyIbApproval($user);

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to approve IB application: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Reject IB application
     */
    public function rejectIbApplication($userId, $reason = null)
    {
        try {
            DB::beginTransaction();

            $user = User::findOrFail($userId);
            
            if ($user->ib_status !== 'pending') {
                throw new \Exception('User does not have a pending IB application');
            }

            $user->rejectIbApplication();
            $user->partner = 3; // Rejected
            $user->save();

            DB::commit();

            Log::info("IB application rejected", ['user_id' => $userId, 'reason' => $reason]);

            // Send rejection notification
            $this->notifyIbRejection($user, $reason);

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to reject IB application: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get IB applications by status
     */
    public function getIbApplicationsByStatus($status = null)
    {
        $query = User::with(['formsIb', 'ibGroup', 'ibParent'])
            ->whereNotNull('ib_status');

        if ($status) {
            $query->where('ib_status', $status);
        }

        return $query->orderBy('created_at', 'desc')->paginate(20);
    }

    /**
     * Get IB dashboard data
     */
    public function getIbDashboardData($ibId)
    {
        $ib = User::with([
            'ibGroup:id,name,commission_multiplier,max_levels',
            'ibChildren' => function($q) {
                $q->whereNotNull('ib_status')
                  ->select('id', 'username', 'firstname', 'lastname', 'ib_status', 'ib_type', 'ib_parent_id', 'ib_group_id');
            },
            'ibParent:id,username,firstname,lastname,ib_type'
        ])->findOrFail($ibId);

        if (!$ib->isIb()) {
            throw new \Exception('User is not an approved IB');
        }

        $commissionService = new IbCommissionService();
        $hierarchyService = new IbHierarchyService();

        return [
            'ib' => $ib,
            'commission_summary' => $commissionService->getIbCommissionSummary($ibId),
            'hierarchy_tree' => $hierarchyService->getIbHierarchyTree($ibId),
            'hierarchy_stats' => $hierarchyService->getHierarchyStats($ibId),
            'recent_commissions' => $ib->ibCommissionsEarned()
                ->with(['fromUser:id,username,firstname,lastname'])
                ->select('id', 'from_user_id', 'commission_amount', 'symbol', 'volume', 'status', 'trade_closed_at', 'created_at')
                ->latest()
                ->limit(10)
                ->get(),
            'sub_ibs' => $ib->ibChildren()
                ->whereNotNull('ib_status')
                ->with(['ibGroup:id,name,commission_multiplier'])
                ->select('id', 'username', 'firstname', 'lastname', 'ib_status', 'ib_type', 'ib_group_id')
                ->get()
        ];
    }

    /**
     * Update IB settings
     */
    public function updateIbSettings($ibId, $settings)
    {
        try {
            DB::beginTransaction();

            $ib = User::findOrFail($ibId);
            
            if (!$ib->isIb()) {
                throw new \Exception('User is not an approved IB');
            }

            // Update allowed settings
            if (isset($settings['ib_commission_rate'])) {
                $maxRate = $ib->ibGroup ? $ib->ibGroup->commission_multiplier * 100 : 100;
                if ($settings['ib_commission_rate'] <= $maxRate) {
                    $ib->ib_commission_rate = $settings['ib_commission_rate'];
                }
            }

            if (isset($settings['ib_max_levels']) && $ib->isMasterIb()) {
                $groupMaxLevels = $ib->ibGroup ? $ib->ibGroup->max_levels : 3;
                if ($settings['ib_max_levels'] <= $groupMaxLevels) {
                    $ib->ib_max_levels = $settings['ib_max_levels'];
                }
            }

            $ib->save();

            DB::commit();

            Log::info("IB settings updated", ['ib_id' => $ibId, 'settings' => $settings]);

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to update IB settings: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get available parent IBs for a user
     */
    public function getAvailableParentIbs($userId = null, $groupId = null)
    {
        $query = User::where('ib_status', 'approved')
            ->where('ib_type', 'master');

        if ($groupId) {
            $query->where('ib_group_id', $groupId);
        }

        if ($userId) {
            $query->where('id', '!=', $userId);
        }

        return $query->with(['ibGroup'])
            ->orderBy('username')
            ->get();
    }

    /**
     * Send notification to admin about new IB application
     */
    private function notifyAdminOfNewApplication($user, $applicationData = [])
    {
        // Create admin notification
        $adminNotification = new \App\Models\AdminNotification();
        $adminNotification->user_id = $user->id;
        $adminNotification->title = 'New IB application from ' . $user->username;
        $adminNotification->click_url = urlPath('admin.form_ib');
        $adminNotification->save();

        // Send email notification to admin
        $adminEmail = gs()->email_from;
        notify(['email' => $adminEmail, 'username' => 'Admin', 'fullname' => 'Administrator'], 'IB_APPLICATION_ADMIN', [
            'username' => $user->username,
            'email' => $user->email,
            'fullname' => $user->fullname,
            'expected_clients' => $applicationData['expected_clients'] ?? 'Not specified',
            'trading_volume' => $applicationData['trading_volume'] ?? 'Not specified',
        ], ['email'], false);
    }

    /**
     * Send confirmation notification to user about IB application submission
     */
    private function notifyUserOfApplicationSubmission($user)
    {
        // Send confirmation email to user
        notify($user, 'IB_APPLICATION_USER', [
            'username' => $user->username,
            'fullname' => $user->fullname,
        ], ['email']);
    }

    /**
     * Send approval notification to IB
     */
    private function notifyIbApproval($user)
    {
        notify($user, 'IB_APPLICATION_APPROVED', [
            'username' => $user->username,
            'ib_type' => $user->ib_type,
            'referral_code' => $user->referral_code
        ]);
    }

    /**
     * Send rejection notification to IB
     */
    private function notifyIbRejection($user, $reason = null)
    {
        notify($user, 'IB_APPLICATION_REJECTED', [
            'username' => $user->username,
            'reason' => $reason ?: 'Application did not meet requirements'
        ]);
    }

    /**
     * Get IB performance metrics
     */
    public function getIbPerformanceMetrics($ibId, $period = '30days')
    {
        $ib = User::findOrFail($ibId);
        
        if (!$ib->isIb()) {
            return null;
        }

        $startDate = match($period) {
            '7days' => now()->subDays(7),
            '30days' => now()->subDays(30),
            '90days' => now()->subDays(90),
            '1year' => now()->subYear(),
            default => now()->subDays(30)
        };

        $stats = $ib->getIbStats($startDate, now());
        
        return array_merge($stats, [
            'period' => $period,
            'start_date' => $startDate,
            'end_date' => now(),
            'conversion_rate' => $stats['total_trades'] > 0 ? 
                ($stats['total_trades'] / $ib->getTotalReferredClients()) * 100 : 0
        ]);
    }
}
