<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\IbGroup;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class IbGroupController extends Controller
{
    /**
     * Display IB groups
     */
    public function index()
    {
        $pageTitle = 'IB Groups Management';
        $ibGroups = IbGroup::with([
            'users' => function($q) {
                $q->whereNotNull('ib_status')->select('id', 'username', 'firstname', 'lastname', 'ib_status', 'ib_type', 'ib_group_id', 'ib_parent_id');
            },
            'rebateRules' => function($q) {
                $q->select('id', 'ib_group_id', 'symbol_group_id', 'rebate_per_lot');
            }
        ])->orderBy('name')->paginate(20);

        return view('admin.ib_system.groups.index', compact('pageTitle', 'ibGroups'));
    }

    /**
     * Show create form
     */
    public function create()
    {
        $pageTitle = 'Create IB Group';
        return view('admin.ib_system.groups.create', compact('pageTitle'));
    }

    /**
     * Store new IB group
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:ib_groups,name',
            'description' => 'nullable|string',
            'commission_multiplier' => 'required|numeric|min:0|max:10',
            'max_levels' => 'required|integer|min:1|max:10',
            'rules' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            $notify[] = ['error', 'Validation failed: ' . $validator->errors()->first()];
            return back()->withNotify($notify)->withInput();
        }

        try {
            IbGroup::create([
                'name' => $request->name,
                'description' => $request->description,
                'commission_multiplier' => $request->commission_multiplier,
                'max_levels' => $request->max_levels,
                'rules_json' => $request->rules,
                'status' => true
            ]);

            $notify[] = ['success', 'IB group created successfully'];
            return redirect()->route('admin.ib.groups.index')->withNotify($notify);

        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to create IB group: ' . $e->getMessage()];
            return back()->withNotify($notify)->withInput();
        }
    }

    /**
     * Show edit form
     */
    public function edit($id)
    {
        $pageTitle = 'Edit IB Group';
        $ibGroup = IbGroup::findOrFail($id);
        
        return view('admin.ib_system.groups.edit', compact('pageTitle', 'ibGroup'));
    }

    /**
     * Update IB group
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:ib_groups,name,' . $id,
            'description' => 'nullable|string',
            'commission_multiplier' => 'required|numeric|min:0|max:10',
            'max_levels' => 'required|integer|min:1|max:10',
            'rules' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            $notify[] = ['error', 'Validation failed: ' . $validator->errors()->first()];
            return back()->withNotify($notify)->withInput();
        }

        try {
            $ibGroup = IbGroup::findOrFail($id);
            
            $ibGroup->update([
                'name' => $request->name,
                'description' => $request->description,
                'commission_multiplier' => $request->commission_multiplier,
                'max_levels' => $request->max_levels,
                'rules_json' => $request->rules
            ]);

            $notify[] = ['success', 'IB group updated successfully'];
            return redirect()->route('admin.ib.groups.index')->withNotify($notify);

        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to update IB group: ' . $e->getMessage()];
            return back()->withNotify($notify)->withInput();
        }
    }

    /**
     * Toggle group status
     */
    public function toggleStatus($id)
    {
        try {
            $group = IbGroup::findOrFail($id);
            $group->status = !$group->status;
            $group->save();

            $status = $group->status ? 'activated' : 'deactivated';
            $notify[] = ['success', "IB group {$status} successfully"];
            
        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to update group status'];
        }

        return back()->withNotify($notify);
    }

    /**
     * Delete group
     */
    public function destroy($id)
    {
        try {
            $group = IbGroup::findOrFail($id);
            
            // Check if group has users
            $userCount = $group->users()->count();
            if ($userCount > 0) {
                $notify[] = ['error', 'Cannot delete group that has users assigned'];
                return back()->withNotify($notify);
            }

            $group->delete();
            $notify[] = ['success', 'IB group deleted successfully'];
            
        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to delete group'];
        }

        return back()->withNotify($notify);
    }

    /**
     * Show group details
     */
    public function show($id)
    {
        $pageTitle = 'IB Group Details';
        $ibGroup = IbGroup::with([
            'users' => function($q) {
                $q->whereNotNull('ib_status')
                  ->with(['ibCommissionsEarned' => function($q) {
                      $q->select('id', 'to_ib_user_id', 'commission_amount', 'status', 'created_at');
                  }, 'ibParent:id,username,firstname,lastname']);
            },
            'rebateRules' => function($q) {
                $q->with(['symbolGroup:id,name']);
            }
        ])->findOrFail($id);

        $stats = $ibGroup->getStats();

        return view('admin.ib_system.groups.show', compact('pageTitle', 'ibGroup', 'stats'));
    }

    /**
     * Get group statistics
     */
    public function statistics()
    {
        $pageTitle = 'IB Group Statistics';

        $groups = IbGroup::with([
            'users' => function($q) {
                $q->whereNotNull('ib_status')
                  ->select('id', 'username', 'firstname', 'lastname', 'ib_status', 'ib_type', 'ib_group_id', 'ib_approved_at', 'created_at');
            },
            'rebateRules' => function($q) {
                $q->select('id', 'ib_group_id', 'status');
            }
        ])->get();

        // Optimize commission calculation with single query
        $groupCommissions = \App\Models\IbCommission::selectRaw('
                ib_groups.id as group_id,
                SUM(ib_commissions.commission_amount) as total_commissions
            ')
            ->join('users', 'ib_commissions.to_ib_user_id', '=', 'users.id')
            ->join('ib_groups', 'users.ib_group_id', '=', 'ib_groups.id')
            ->groupBy('ib_groups.id')
            ->pluck('total_commissions', 'group_id');

        $stats = [];
        foreach ($groups as $group) {
            $stats[] = [
                'group' => $group,
                'stats' => $group->getStats(),
                'total_commissions' => $groupCommissions[$group->id] ?? 0
            ];
        }

        return view('admin.ib_system.groups.statistics', compact('pageTitle', 'stats'));
    }
}
