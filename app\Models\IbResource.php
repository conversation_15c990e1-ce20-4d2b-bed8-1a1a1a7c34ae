<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class IbResource extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'type',
        'file_path',
        'file_name',
        'file_size',
        'mime_type',
        'uploaded_by',
        'status'
    ];

    protected $casts = [
        'status' => 'boolean'
    ];

    /**
     * Get the user who uploaded this resource
     */
    public function uploader()
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Scope for active resources
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Scope for inactive resources
     */
    public function scopeInactive($query)
    {
        return $query->where('status', false);
    }

    /**
     * Scope by resource type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Toggle resource status
     */
    public function toggleStatus()
    {
        $this->status = !$this->status;
        $this->save();
        return $this;
    }

    /**
     * Get file URL
     */
    public function getFileUrlAttribute()
    {
        return asset('storage/' . $this->file_path);
    }

    /**
     * Get download URL
     */
    public function getDownloadUrlAttribute()
    {
        return route('admin.ib.resources.download', $this->id);
    }

    /**
     * Get formatted file size
     */
    public function getFormattedFileSizeAttribute()
    {
        if (!$this->file_size) {
            return 'Unknown';
        }
        
        $bytes = (int) $this->file_size;
        
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    /**
     * Get type label
     */
    public function getTypeLabelAttribute()
    {
        return ucfirst($this->type);
    }

    /**
     * Get type icon
     */
    public function getTypeIconAttribute()
    {
        $icons = [
            'document' => 'las la-file-alt',
            'image' => 'las la-image',
            'video' => 'las la-video',
            'banner' => 'las la-ad',
            'guide' => 'las la-book'
        ];
        
        return $icons[$this->type] ?? 'las la-file';
    }

    /**
     * Check if file is an image
     */
    public function getIsImageAttribute()
    {
        return in_array($this->type, ['image', 'banner']) || 
               str_starts_with($this->mime_type, 'image/');
    }

    /**
     * Check if file is a video
     */
    public function getIsVideoAttribute()
    {
        return $this->type === 'video' || 
               str_starts_with($this->mime_type, 'video/');
    }

    /**
     * Check if file is a document
     */
    public function getIsDocumentAttribute()
    {
        return in_array($this->type, ['document', 'guide']) ||
               in_array($this->mime_type, [
                   'application/pdf',
                   'application/msword',
                   'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                   'text/plain'
               ]);
    }

    /**
     * Delete file from storage
     */
    public function deleteFile()
    {
        if ($this->file_path && Storage::exists($this->file_path)) {
            Storage::delete($this->file_path);
        }
    }

    /**
     * Boot method to handle model events
     */
    protected static function boot()
    {
        parent::boot();
        
        static::deleting(function ($resource) {
            $resource->deleteFile();
        });
    }

    /**
     * Search resources
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('title', 'like', '%' . $search . '%')
              ->orWhere('description', 'like', '%' . $search . '%')
              ->orWhere('file_name', 'like', '%' . $search . '%');
        });
    }

    /**
     * Get resources by type for dropdown
     */
    public static function getByTypeForDropdown($type)
    {
        return self::active()->byType($type)->pluck('title', 'id');
    }
}
