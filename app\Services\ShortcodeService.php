<?php

namespace App\Services;

use Carbon\Carbon;

/**
 * Comprehensive Shortcode Service
 * Handles all email template shortcodes with real data
 */
class ShortcodeService
{
    /**
     * Get comprehensive shortcodes for a user and context
     */
    public static function getShortcodes($user = null, $context = [])
    {
        $general = gs();
        
        // Base shortcodes
        $shortcodes = [
            // Site information
            'site_name' => $general->site_name ?? 'MBFX',
            'site_url' => url('/'),
            'site_currency' => $general->cur_text ?? 'USD',
            
            // User information
            'fullname' => $user->fullname ?? 'User',
            'username' => $user->username ?? 'user',
            'email' => $user->email ?? '<EMAIL>',
            'user_id' => $user->id ?? '1',
            'account_id' => $user->id ?? '1', // For backward compatibility
            'registration_date' => isset($user->created_at) ? $user->created_at->format('Y-m-d H:i:s') : now()->format('Y-m-d H:i:s'),
            
            // System information
            'ip_address' => request()->ip() ?? '127.0.0.1',
            'location' => self::getLocationFromIP(request()->ip()),
            'browser' => self::getBrowserInfo(),
            'os' => self::getOSInfo(),
            'time' => now()->format('Y-m-d H:i:s'),
            'date' => now()->format('Y-m-d'),
            
            // Transaction shortcodes
            'amount' => $context['amount'] ?? '0.00',
            'currency' => $context['currency'] ?? $general->cur_text ?? 'USD',
            'new_balance' => $context['new_balance'] ?? $context['balance'] ?? '0.00',
            'post_balance' => $context['post_balance'] ?? $context['new_balance'] ?? '0.00',
            'transaction_id' => $context['transaction_id'] ?? $context['trx'] ?? self::generateTransactionId(),
            'transaction_date' => $context['transaction_date'] ?? now()->format('Y-m-d H:i:s'),
            'trx' => $context['trx'] ?? $context['transaction_id'] ?? self::generateTransactionId(),
            'charge' => $context['charge'] ?? '0.00',
            'rate' => $context['rate'] ?? '1.00',
            'total' => $context['total'] ?? $context['amount'] ?? '0.00',
            
            // Payment method shortcodes
            'method_name' => $context['method_name'] ?? 'Bank Transfer',
            'method_amount' => $context['method_amount'] ?? $context['amount'] ?? '0.00',
            'method_currency' => $context['method_currency'] ?? $context['currency'] ?? 'USD',
            'payment_method' => $context['payment_method'] ?? $context['method_name'] ?? 'Bank Transfer',
            
            // Wallet shortcodes
            'wallet_name' => $context['wallet_name'] ?? 'Main Wallet',
            'wallet_currency' => $context['wallet_currency'] ?? $context['currency'] ?? 'USD',
            
            // MT5 shortcodes
            'mt5_login' => $context['mt5_login'] ?? '********',
            'mt5_group' => $context['mt5_group'] ?? 'real\\MBFX\\B\\Sf\\Cp\\Fake',
            'leverage' => $context['leverage'] ?? '500',
            'old_leverage' => $context['old_leverage'] ?? '100',
            'new_leverage' => $context['new_leverage'] ?? '500',
            'server_name' => $context['server_name'] ?? 'MBFX-Live',

            // MT5 Internal Transfer shortcodes
            'from_account' => $context['from_account'] ?? '********',
            'to_account' => $context['to_account'] ?? '********',
            'from_group' => $context['from_group'] ?? 'real\\MBFX\\B\\Sf\\Cp\\Fake',
            'to_group' => $context['to_group'] ?? 'real\\MBFX\\B\\Sf\\Cp\\Live',
            'from_balance' => $context['from_balance'] ?? '1500.00',
            'to_balance' => $context['to_balance'] ?? '2500.00',
            'previous_balance' => $context['previous_balance'] ?? '2000.00',
            'transfer_date' => $context['transfer_date'] ?? now()->format('Y-m-d H:i:s'),
            
            // IB/Partnership shortcodes
            'ib_type' => $context['ib_type'] ?? 'Master IB',
            'referral_code' => $context['referral_code'] ?? $user->referral_code ?? self::generateReferralCode(),
            'commission_rate' => $context['commission_rate'] ?? '50%',
            'commission_amount' => $context['commission_amount'] ?? '0.00',
            'commission_type' => $context['commission_type'] ?? 'Trading Commission',
            'commission_date' => $context['commission_date'] ?? now()->format('Y-m-d H:i:s'),
            'requested_ib_type' => $context['requested_ib_type'] ?? 'Master IB',
            'ref_username' => $context['ref_username'] ?? 'referrer',
            'referred_user' => $context['referred_user'] ?? $user->username ?? 'user',
            
            // Date shortcodes
            'approval_date' => $context['approval_date'] ?? now()->format('Y-m-d H:i:s'),
            'application_date' => $context['application_date'] ?? now()->format('Y-m-d H:i:s'),
            'registration_date' => $context['registration_date'] ?? ($user->created_at ?? now())->format('Y-m-d H:i:s'),
            'submission_date' => $context['submission_date'] ?? now()->format('Y-m-d H:i:s'),
            'review_date' => $context['review_date'] ?? now()->format('Y-m-d H:i:s'),
            'change_date' => $context['change_date'] ?? now()->format('Y-m-d H:i:s'),
            'update_date' => $context['update_date'] ?? now()->format('Y-m-d H:i:s'),
            
            // Reason/Message shortcodes
            'reason' => $context['reason'] ?? 'Administrative action',
            'remark' => $context['remark'] ?? 'Transaction processed',
            'rejection_reason' => $context['rejection_reason'] ?? 'Additional verification required',
            'rejection_message' => $context['rejection_message'] ?? 'Please contact support for more information',
            'cancellation_reason' => $context['cancellation_reason'] ?? 'User requested cancellation',
            'report_reason' => $context['report_reason'] ?? 'Suspicious activity',
            
            // Trading shortcodes
            'order_id' => $context['order_id'] ?? self::generateOrderId(),
            'trade_id' => $context['trade_id'] ?? self::generateTradeId(),
            'symbol' => $context['symbol'] ?? 'EURUSD',
            'pair' => $context['pair'] ?? 'EUR/USD',
            'volume' => $context['volume'] ?? '0.01',
            'order_type' => $context['order_type'] ?? 'Buy',
            'open_price' => $context['open_price'] ?? '1.0000',
            'close_price' => $context['close_price'] ?? '1.0000',
            'profit_loss' => $context['profit_loss'] ?? '0.00',
            'filled_amount' => $context['filled_amount'] ?? $context['amount'] ?? '0.00',
            'price' => $context['price'] ?? '1.0000',
            
            // Coin/Asset shortcodes
            'coin_symbol' => $context['coin_symbol'] ?? 'BTC',
            'coin symbol' => $context['coin symbol'] ?? $context['coin_symbol'] ?? 'BTC',
            'asset_amount' => $context['asset_amount'] ?? $context['amount'] ?? '0.00',
            'fiat_amount' => $context['fiat_amount'] ?? $context['amount'] ?? '0.00',
            'market_currency_symbol' => $context['market_currency_symbol'] ?? 'USD',
            'final_rate' => $context['final_rate'] ?? $context['rate'] ?? '1.00',
            
            // Transfer shortcodes
            'from_account' => $context['from_account'] ?? 'Account 1',
            'to_account' => $context['to_account'] ?? 'Account 2',
            'from_username' => $context['from_username'] ?? 'sender',
            'to_username' => $context['to_username'] ?? 'recipient',
            'sender_name' => $context['sender_name'] ?? 'Sender',
            'recipient_name' => $context['recipient_name'] ?? 'Recipient',
            'transfer_fee' => $context['transfer_fee'] ?? '0.00',
            'transfer_message' => $context['transfer_message'] ?? 'Transfer completed',
            'counterparty' => $context['counterparty'] ?? 'Trading Partner',
            
            // Support/Admin shortcodes
            'ticket_id' => $context['ticket_id'] ?? self::generateTicketId(),
            'ticket_subject' => $context['ticket_subject'] ?? 'Support Request',
            'admin_details' => $context['admin_details'] ?? 'Administrative action taken',
            'admin_url' => $context['admin_url'] ?? url('/admin'),
            'document_count' => $context['document_count'] ?? '1',

            // Enhanced URL shortcodes for email links
            'login_url' => url('/user/login'),
            'dashboard_url' => url('/user/dashboard'),
            'kyc_url' => url('/user/kyc'),
            'deposit_url' => url('/user/deposit'),
            'withdraw_url' => url('/user/withdraw'),
            'profile_url' => url('/user/profile-setting'),
            'support_url' => url('/ticket'),
            'contact_url' => url('/contact'),
            'privacy_policy_url' => url('/policy/privacy-policy'),
            'terms_service_url' => url('/policy/terms-of-service'),
            'ib_dashboard_url' => url('/user/ib-dashboard'),
            'mt5_download_link' => 'https://download.mql5.com/cdn/web/metaquotes.software.corp/mt5/mt5setup.exe',
            'trading_platform_url' => url('/user/mt5'),
            'account_settings_url' => url('/user/profile-setting'),

            // Footer and action button shortcodes
            'footer_contact_support' => '<a href="' . url('/ticket') . '" style="color: #E3373F; text-decoration: none;">Contact Support</a>',
            'footer_login_account' => '<a href="' . url('/user/login') . '" style="color: #E3373F; text-decoration: none;">Login to Account</a>',
            'footer_privacy_policy' => '<a href="' . url('/policy/privacy-policy') . '" style="color: #E3373F; text-decoration: none;">Privacy Policy</a>',
            'footer_terms_service' => '<a href="' . url('/policy/terms-of-service') . '" style="color: #E3373F; text-decoration: none;">Terms of Service</a>',
            'footer_website_link' => '<a href="' . url('/') . '" style="color: #E3373F; text-decoration: none;">mbf.mybrokerforex.com</a>',

            // Action button shortcodes (styled buttons)
            'btn_login_account' => '<a href="' . url('/user/login') . '" style="display: inline-block; background: #E3373F; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold;">Login to Account</a>',
            'btn_verify_email' => '<a href="' . url('/user/authorization') . '" style="display: inline-block; background: #E3373F; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold;">Verify Email Address</a>',
            'btn_submit_kyc' => '<a href="' . url('/user/kyc') . '" style="display: inline-block; background: #E3373F; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold;">Submit KYC Documents</a>',
            'btn_view_dashboard' => '<a href="' . url('/user/dashboard') . '" style="display: inline-block; background: #E3373F; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold;">View Dashboard</a>',
            'btn_contact_support' => '<a href="' . url('/ticket') . '" style="display: inline-block; background: #E3373F; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold;">Contact Support</a>',
            'btn_download_mt5' => '<a href="https://download.mql5.com/cdn/web/metaquotes.software.corp/mt5/mt5setup.exe" style="display: inline-block; background: #E3373F; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold;">Download MT5 Platform</a>',
            
            // Security shortcodes
            'activity_type' => $context['activity_type'] ?? 'Login',
            'activity_date' => $context['activity_date'] ?? now()->format('Y-m-d H:i:s'),
            'ip' => $context['ip'] ?? request()->ip() ?? '127.0.0.1',
            
            // Verification shortcodes
            'code' => $context['code'] ?? self::generateVerificationCode(),
            
            // Generic shortcodes
            'subject' => $context['subject'] ?? 'Notification',
            'message' => $context['message'] ?? 'This is a notification message',
        ];

        // Merge any additional context shortcodes
        return array_merge($shortcodes, $context);
    }

    /**
     * Generate a random transaction ID
     */
    private static function generateTransactionId()
    {
        return 'TXN' . strtoupper(uniqid());
    }

    /**
     * Generate a random order ID
     */
    private static function generateOrderId()
    {
        return 'ORD' . time() . rand(1000, 9999);
    }

    /**
     * Generate a random trade ID
     */
    private static function generateTradeId()
    {
        return 'TRD' . time() . rand(1000, 9999);
    }

    /**
     * Generate a random ticket ID
     */
    private static function generateTicketId()
    {
        return 'TKT' . time() . rand(100, 999);
    }

    /**
     * Generate a random referral code
     */
    private static function generateReferralCode()
    {
        return 'REF' . strtoupper(substr(uniqid(), -6));
    }

    /**
     * Generate a verification code
     */
    private static function generateVerificationCode()
    {
        return rand(100000, 999999);
    }

    /**
     * Get location from IP address
     */
    private static function getLocationFromIP($ip)
    {
        // Simple implementation - in production you might use a GeoIP service
        if ($ip === '127.0.0.1' || $ip === '::1') {
            return 'Local';
        }
        return 'Unknown Location';
    }

    /**
     * Get browser information
     */
    private static function getBrowserInfo()
    {
        $userAgent = request()->header('User-Agent', 'Unknown Browser');
        
        if (strpos($userAgent, 'Chrome') !== false) {
            return 'Chrome';
        } elseif (strpos($userAgent, 'Firefox') !== false) {
            return 'Firefox';
        } elseif (strpos($userAgent, 'Safari') !== false) {
            return 'Safari';
        } elseif (strpos($userAgent, 'Edge') !== false) {
            return 'Edge';
        }
        
        return 'Unknown Browser';
    }

    /**
     * Get OS information
     */
    private static function getOSInfo()
    {
        $userAgent = request()->header('User-Agent', 'Unknown OS');
        
        if (strpos($userAgent, 'Windows') !== false) {
            return 'Windows';
        } elseif (strpos($userAgent, 'Mac') !== false) {
            return 'macOS';
        } elseif (strpos($userAgent, 'Linux') !== false) {
            return 'Linux';
        } elseif (strpos($userAgent, 'Android') !== false) {
            return 'Android';
        } elseif (strpos($userAgent, 'iOS') !== false) {
            return 'iOS';
        }
        
        return 'Unknown OS';
    }
}
