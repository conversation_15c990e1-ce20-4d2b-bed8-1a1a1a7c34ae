"use strict";!function(e){"object"==typeof module&&"object"==typeof module.exports?e(require("jquery"),window,document):e(jQuery,window,document)}(function(l,h,c,r){function t(e,t){this.$chartContainer=l(e),this.opts=t,this.defaultOptions={icons:{theme:"oci",parentNode:"oci-menu",expandToUp:"oci-chevron-up",collapseToDown:"oci-chevron-down",collapseToLeft:"oci-chevron-left",expandToRight:"oci-chevron-right",backToCompact:"oci-corner-top-left",backToLoose:"oci-corner-bottom-right",collapsed:"oci-plus-square",expanded:"oci-minus-square",spinner:"oci-spinner"},nodeTitle:"name",nodeId:"id",toggleSiblingsResp:!1,visibleLevel:999,chartClass:"",exportButton:!1,exportButtonName:"Export",exportFilename:"OrgChart",exportFileextension:"png",draggable:!1,direction:"t2b",pan:!1,zoom:!1,zoominLimit:7,zoomoutLimit:.5}}t.prototype={init:function(e){this.options=l.extend({},this.defaultOptions,this.opts,e);var e=this.$chartContainer,t=(this.$chart&&this.$chart.remove(),this.options.data),i=this.$chart=l("<div>",{data:{options:this.options},class:"orgchart"+(""!==this.options.chartClass?" "+this.options.chartClass:"")+("t2b"!==this.options.direction?" "+this.options.direction:""),click:function(e){l(e.target).closest(".node").length||i.find(".node.focused").removeClass("focused")}}),s=("undefined"!=typeof MutationObserver&&this.triggerInitEvent(),Array.isArray(t)?i.append(l('<ul class="nodes">').find(".nodes"):i.append(l('<ul class="hierarchy">').find(".hierarchy"));return t instanceof l?this.buildHierarchy(s,this.buildJsonDS(t.children()),0,this.options):t.relationship?this.buildHierarchy(s,t):this.buildHierarchy(s,Array.isArray(t)?t:this.attachRel(t,"00")),e.append(i),this.options.exportButton&&!l(".oc-export-btn").length&&this.attachExportButton(),this.options.pan&&this.bindPan(),this.options.zoom&&this.bindZoom(),this},handleCompactNodes:function(){this.$chart.find(".node.compact").each((e,t)=>{l(t).addClass(l(t).parents(".compact").length%2==0?"even":"odd")})},triggerInitEvent:function(){var n=this,o=new MutationObserver(function(e){o.disconnect();e:for(var t=0;t<e.length;t++){for(var i=e[t].addedNodes,s=0;s<i.length;s++)if(1===i[s].nodeType){n.$chart.trigger("init.orgchart");break e}}});o.observe(this.$chartContainer[0],{childList:!0,subtree:!0})},attachExportButton:function(){var t=this,e=l("<button>",{class:"oc-export-btn",text:this.options.exportButtonName,click:function(e){e.preventDefault(),t.export()}});this.$chartContainer.after(e)},setOptions:function(e,t){return"string"==typeof e&&("pan"===e&&(t?this.bindPan():this.unbindPan()),"zoom"===e)&&(t?this.bindZoom():this.unbindZoom()),"object"==typeof e&&(e.data?this.init(e):(void 0!==e.pan&&(e.pan?this.bindPan():this.unbindPan()),void 0!==e.zoom&&(e.zoom?this.bindZoom():this.unbindZoom()))),this},panStartHandler:function(e){var n=l(e.delegateTarget);if(l(e.target).closest(".node").length||e.touches&&1<e.touches.length)return!1;n.css("cursor","move"),n.data("panning",!0);var o=e.originalEvent,a=o.offsetX||o.targetTouches[0].pageX-l(o.target).offset().left,d=o.offsetY||o.targetTouches[0].pageY-l(o.target).offset().top;n.data("panStart",{x:a,y:d}),n.data("panPosition",n.css("transform"))},panHandler:function(e){var t=l(e.delegateTarget);if(!t.data("panning"))return!1;var i=e.originalEvent,s=t.data("panStart"),n=t.data("panPosition"),o=i.offsetX||i.targetTouches[0].pageX-l(i.target).offset().left,a=i.offsetY||i.targetTouches[0].pageY-l(i.target).offset().top,d=o-s.x,r=a-s.y,i=n.match(/translate\(([^,]*),\s*([^)]*)\)/);i?t.css("transform",n.replace(/translate\([^)]*\)/,"translate("+(parseInt(i[1])+d)+"px, "+(parseInt(i[2])+r)+"px)")):t.css("transform",n+" translate("+d+"px, "+r+"px)")},panEndHandler:function(e){l(e.delegateTarget).data("panning",!1).css("cursor","default")},bindPan:function(){this.$chartContainer.css("overflow","hidden").on("mousedown touchstart",this.panStartHandler).on("mousemove touchmove",this.panHandler).on("mouseup touchend",this.panEndHandler)},unbindPan:function(){this.$chartContainer.css("overflow","").off("mousedown touchstart",this.panStartHandler).off("mousemove touchmove",this.panHandler).off("mouseup touchend",this.panEndHandler)},zoomWheelHandler:function(e){var t=e.originalEvent,i=l(e.delegateTarget),s=this.options,n=1+.1*(0<t.deltaY?-1:1),o=i.css("transform"),a=o.match(/scale\(([^)]*)\)/),d=a?parseFloat(a[1]):1,r=d*n;r>s.zoomoutLimit&&r<s.zoominLimit&&(o=a?o.replace(/scale\([^)]*\)/,"scale("+r+")"):o+" scale("+r+")",i.css("transform",o)),e.preventDefault()},zoomHandler:function(e){var t=l(e.delegateTarget),i=this.options,s=e.originalEvent,n=s.targetTouches,o=t.css("transform"),a=o.match(/scale\(([^)]*)\)/),d=a?parseFloat(a[1]):1;if(2===n.length){var r=Math.sqrt(Math.pow(n[1].pageX-n[0].pageX,2)+Math.pow(n[1].pageY-n[0].pageY,2)),l=t.data("pinching");l?(l=r/l,d*=l,d>i.zoomoutLimit&&d<i.zoominLimit&&(o=a?o.replace(/scale\([^)]*\)/,"scale("+d+")"):o+" scale("+d+")",t.css("transform",o))):t.data("pinching",r),t.data("pinching",r)}},zoomEndHandler:function(e){l(e.delegateTarget).removeData("pinching")},bindZoom:function(){this.$chartContainer.on("wheel",this.zoomWheelHandler.bind(this)).on("touchmove",this.zoomHandler.bind(this)).on("touchend",this.zoomEndHandler.bind(this))},unbindZoom:function(){this.$chartContainer.off("wheel",this.zoomWheelHandler).off("touchmove",this.zoomHandler).off("touchend",this.zoomEndHandler)},buildJsonDS:function(e){var n=this,o=[];return e.each(function(){var e=l(this),t=e.contents().eq(0).text().trim(),i={name:t,relationship:e.data("relationship")||"001"};e.attr("id")&&(i.id=e.attr("id")),e.data("content")&&(i.content=e.data("content")),e.hasClass("slide-up")&&(i.collapsed=!0),e.children("ul").length&&(i.children=n.buildJsonDS(e.children("ul").children("li"))),o.push(i)}),o},attachRel:function(e,t){return e.relationship=t,e.children&&l.each(e.children,function(e){this.relationship=t+e}),e},loopChart:function(e){var t=this;return e.each(function(){var e=l(this);e.data("node",e.closest(".node")).data("nodeData",e.closest(".node").data("nodeData")),t.options.createNode&&t.options.createNode(e,e.data("nodeData"))}),e},repaint:function(e){e&&e.style&&(e.style.offsetWidth=e.offsetWidth)},nodeEnterLeaveHandler:function(e){var t=l(e.delegateTarget),i=t.find(".edge");i.length&&("mouseenter"===e.type?i.addClass("fa-plus-square").removeClass("fa-minus-square"):"mouseleave"===e.type&&i.addClass("fa-minus-square").removeClass("fa-plus-square"))},nodeClickHandler:function(e){var t=l(e.delegateTarget),i=this.$chart.find(".focused");i.length&&i.removeClass("focused"),t.addClass("focused")},buildHierarchy:function(s,e){var n,o=this,a=(this.options,0);e.level||e[0]?.level?a=e.level:(a=s.parentsUntil(".orgchart",".nodes").length,Array.isArray(e)&&Array.isArray(e[0])?l.each(e,function(){l.each(this,function(){this.level=a})}):e.level=a),Array.isArray(e)&&Array.isArray(e[0])?l.each(e,function(){var i=this;l.each(this,function(e){var t;n=o.createNode(this),2===i.length&&1===e?(s.find("#"+i[0].id).after(n),this.children&&this.children.length&&this.children[0].length&&o.buildInferiorNodes(s.find("#"+i[0].id).parent(),n,this,a)):(t=l(`<li class="hierarchy"></li>`),0===e?t.css({"--ft-width":"50%","--ft-left-offset":"50%"}):0<e&&t.css({"--ft-width":"100%","--ft-left-offset":"0%"}),t.append(n),s.append(t),this.children&&this.children.length&&o.buildInferiorNodes(t,n,this,a))})}):Array.isArray(e)?l.each(e,function(){var e=l(`<li class="hierarchy"></li>`);e.append(o.createNode(this)),s.append(e),this.children&&this.children.length&&o.buildInferiorNodes(e,e.children(".node"),this,a)}):(n=o.createNode(e),s.append(n),e.children&&e.children.length&&o.buildInferiorNodes(s,n,e,a))},startLoading:function(e){var t=l(`<i class="oci oci-spinner spinner"></i>`);return e.append(t),e.children().not(".spinner").css("opacity",.2),t.data("inAjax",!0),l(".oc-export-btn").prop("disabled",!0),!0},endLoading:function(e){var t=e.parent();e.removeClass("hidden"),t.find(".spinner").remove(),t.children().removeAttr("style"),this.$chart.data("inAjax",!1),l(".oc-export-btn").prop("disabled",!1)},isInAction:function(t){return[this.options.icons.expandToUp,this.options.icons.collapseToDown,this.options.icons.collapseToLeft,this.options.icons.expandToRight].some(e=>-1<t.attr("class").indexOf(e))},switchVerticalArrow:function(e){e.removeClass(this.options.icons.expandToUp+" "+this.options.icons.collapseToDown),e.hasClass("fa-chevron-up")?e.addClass(this.options.icons.collapseToDown):e.addClass(this.options.icons.expandToUp)},switchHorizontalArrow:function(e){var t=e.children(".leftEdge"),i=e.children(".rightEdge");t.length&&(t.hasClass("fa-chevron-left")?t.removeClass(this.options.icons.collapseToLeft).addClass(this.options.icons.expandToRight):t.removeClass(this.options.icons.expandToRight).addClass(this.options.icons.collapseToLeft)),i.length&&(i.hasClass("fa-chevron-right")?i.removeClass(this.options.icons.expandToRight).addClass(this.options.icons.collapseToLeft):i.removeClass(this.options.icons.collapseToLeft).addClass(this.options.icons.expandToRight))},getNodeState:function(e,t){var i,s,n={exist:!1,visible:!1};"parent"===t?(i=e.closest(".nodes").siblings(".node"),n.exist=!!i.length,n.visible=i.is(":visible")&&!i.hasClass("slide-up")):"children"===t?(s=e.siblings(".nodes"),n.exist=!!s.length,n.visible=s.is(":visible")):"siblings"===t&&(i=e.closest(".hierarchy").siblings(),n.exist=!!i.length,n.visible=!i.hasClass("hidden"));return n},hideParent:function(e){e.closest(".nodes").siblings(".node").addClass("slide-up")},showParent:function(e){e.closest(".nodes").siblings(".node").removeClass("slide-up")},hideChildren:function(e){var t=e.siblings(".nodes");this.isInAction(e)&&this.switchVerticalArrow(e.children(".bottomEdge")),t.addClass("hidden")},showChildren:function(e){var t=e.siblings(".nodes");this.isInAction(e)&&this.switchVerticalArrow(e.children(".bottomEdge")),t.removeClass("hidden")},hideSiblings:function(e,t){var i=e.closest(".hierarchy"),s=this.options;s.toggleSiblingsResp?(t="left"===t?i.prev():i.next()).addClass("hidden"):i.siblings().addClass("hidden"),this.isInAction(e)&&this.switchHorizontalArrow(e)},showSiblings:function(e,t){var i=e.closest(".hierarchy"),s=this.options;s.toggleSiblingsResp?(t="left"===t?i.prev():i.next()).removeClass("hidden"):i.siblings().removeClass("hidden"),this.isInAction(e)&&this.switchHorizontalArrow(e)},getChildren:function(e){return e.siblings(".nodes").children(".hierarchy").children(".node")},getSiblings:function(e){return e.closest(".hierarchy").siblings().children(".node")},hideDropZones:function(){this.$chart.find(".allowedDrop").removeClass("allowedDrop")},showDropZones:function(e){this.$chart.find(".node").each(function(e,t){l(t).addClass("allowedDrop")}),this.$chart.data("dragged",l(e))},export:function(t,i){var s=this;if(t=void 0!==t?t:this.options.exportFilename,i=void 0!==i?i:this.options.exportFileextension,l(this).children(".spinner").length)return!1;var n=this.$chartContainer,e=n.find(".mask"),e=(e.length?e.removeClass("hidden"):n.append(`<div class="mask"><i class="oci oci-spinner"></i></div>`),n.addClass("canvasContainer").find('.orgchart:not(".hidden")').get(0)),o="l2r"===s.options.direction||"r2l"===s.options.direction;html2canvas(e,{width:o?e.clientHeight:e.clientWidth,height:o?e.clientWidth:e.clientHeight,onclone:function(e){l(e).find(".canvasContainer").css("overflow","visible").find('.orgchart:not(".hidden"):first').css("transform","")}}).then(function(e){n.find(".mask").addClass("hidden"),"pdf"===i.toLowerCase()?s.exportPDF(e,t):s.exportPNG(e,t),n.removeClass("canvasContainer")},function(){n.removeClass("canvasContainer")})}},l.fn.orgchart=function(e){return new t(this,e).init()}});