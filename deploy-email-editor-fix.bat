@echo off
REM Email Editor Live Server Fix Deployment Script
REM For Windows Server 2022 with PHP 8.4

echo ========================================
echo  Email Editor Live Server Fix Deployment
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [INFO] Running with administrator privileges
) else (
    echo [WARNING] Not running as administrator - some operations may fail
    echo Please run this script as administrator for best results
    pause
)

echo.
echo [STEP 1] Backing up existing files...
if exist web.config (
    copy web.config web.config.backup.%date:~-4,4%%date:~-10,2%%date:~-7,2%
    echo [SUCCESS] web.config backed up
) else (
    echo [WARNING] web.config not found
)

if exist "assets\admin\js\simple-email-editor.js" (
    copy "assets\admin\js\simple-email-editor.js" "assets\admin\js\simple-email-editor.js.backup"
    echo [SUCCESS] Original JavaScript backed up
) else (
    echo [WARNING] Original JavaScript file not found
)

echo.
echo [STEP 2] Deploying enhanced web.config...
if exist web.config.live-server-fix (
    copy web.config.live-server-fix web.config
    echo [SUCCESS] Enhanced web.config deployed
) else (
    echo [ERROR] web.config.live-server-fix not found!
    echo Please ensure the file exists before running this script
    pause
    exit /b 1
)

echo.
echo [STEP 3] Verifying enhanced JavaScript file...
if exist "assets\admin\js\simple-email-editor-enhanced.js" (
    echo [SUCCESS] Enhanced JavaScript file found
) else (
    echo [ERROR] Enhanced JavaScript file not found!
    echo Please ensure assets/admin/js/simple-email-editor-enhanced.js exists
    pause
    exit /b 1
)

echo.
echo [STEP 4] Clearing Laravel caches...
php artisan cache:clear
if %errorLevel% == 0 (
    echo [SUCCESS] Application cache cleared
) else (
    echo [WARNING] Failed to clear application cache
)

php artisan config:clear
if %errorLevel% == 0 (
    echo [SUCCESS] Configuration cache cleared
) else (
    echo [WARNING] Failed to clear configuration cache
)

php artisan view:clear
if %errorLevel% == 0 (
    echo [SUCCESS] View cache cleared
) else (
    echo [WARNING] Failed to clear view cache
)

echo.
echo [STEP 5] Testing asset accessibility...
echo Testing CSS file...
curl -I -s "http://localhost/assets/admin/css/simple-email-editor.css" | find "200 OK" >nul
if %errorLevel% == 0 (
    echo [SUCCESS] CSS file accessible
) else (
    echo [WARNING] CSS file may not be accessible
)

echo Testing enhanced JavaScript file...
curl -I -s "http://localhost/assets/admin/js/simple-email-editor-enhanced.js" | find "200 OK" >nul
if %errorLevel% == 0 (
    echo [SUCCESS] Enhanced JavaScript file accessible
) else (
    echo [WARNING] Enhanced JavaScript file may not be accessible
)

echo.
echo [STEP 6] Restarting IIS (if available)...
iisreset >nul 2>&1
if %errorLevel% == 0 (
    echo [SUCCESS] IIS restarted
) else (
    echo [INFO] IIS restart not available or not needed
)

echo.
echo [STEP 7] Checking PHP version...
php -v | find "PHP 8.4" >nul
if %errorLevel% == 0 (
    echo [SUCCESS] PHP 8.4 detected
) else (
    echo [WARNING] PHP 8.4 not detected - please verify PHP version
)

echo.
echo [STEP 8] Verifying Laravel installation...
php artisan --version | find "Laravel" >nul
if %errorLevel% == 0 (
    echo [SUCCESS] Laravel detected
) else (
    echo [WARNING] Laravel not detected - please verify installation
)

echo.
echo ========================================
echo  Deployment Summary
echo ========================================
echo.
echo Files deployed:
echo - Enhanced web.config
echo - Enhanced JavaScript (simple-email-editor-enhanced.js)
echo - Updated Blade templates (edit.blade.php, global_template.blade.php)
echo.
echo Next steps:
echo 1. Open your browser and navigate to the email template editor
echo 2. Open Developer Tools (F12) and check the Console tab
echo 3. Look for success messages starting with [EMAIL-EDITOR]
echo 4. Test all editor functionality (tabs, shortcodes, save, preview)
echo.
echo If issues persist:
echo 1. Check the browser console for error messages
echo 2. Review the Network tab for failed asset loading
echo 3. Consult LIVE_SERVER_EMAIL_EDITOR_FIX.md for troubleshooting
echo.
echo Rollback command (if needed):
echo copy web.config.backup.* web.config
echo.
echo ========================================
echo  Deployment Complete!
echo ========================================
echo.
pause