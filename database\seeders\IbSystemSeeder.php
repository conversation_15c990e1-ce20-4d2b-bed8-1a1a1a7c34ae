<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\IbLevel;
use App\Models\IbGroup;
use App\Models\SymbolGroup;
use App\Models\RebateRule;

class IbSystemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create IB Levels
        $this->createIbLevels();
        
        // Create IB Groups
        $this->createIbGroups();
        
        // Create Symbol Groups
        $this->createSymbolGroups();
        
        // Create Rebate Rules
        $this->createRebateRules();
    }

    /**
     * Create default IB commission levels
     */
    private function createIbLevels()
    {
        $levels = [
            [
                'level' => 1,
                'name' => 'Level 1 - Direct IB',
                'commission_percent' => 50.00,
                'max_commission_percent' => 80.00,
                'description' => 'Direct IB commission - highest percentage',
                'status' => true
            ],
            [
                'level' => 2,
                'name' => 'Level 2 - Master IB',
                'commission_percent' => 30.00,
                'max_commission_percent' => 60.00,
                'description' => 'Second level IB commission',
                'status' => true
            ],
            [
                'level' => 3,
                'name' => 'Level 3 - Regional IB',
                'commission_percent' => 20.00,
                'max_commission_percent' => 40.00,
                'description' => 'Third level IB commission',
                'status' => true
            ]
        ];

        foreach ($levels as $level) {
            IbLevel::updateOrCreate(
                ['level' => $level['level']],
                $level
            );
        }

        $this->command->info('IB Levels created successfully.');
    }

    /**
     * Create default IB groups
     */
    private function createIbGroups()
    {
        $groups = [
            [
                'name' => 'Standard IB Group',
                'description' => 'Default group for standard IBs with basic commission rates',
                'commission_multiplier' => 1.00,
                'max_levels' => 3,
                'rules_json' => [
                    'min_clients' => 5,
                    'min_volume' => 10,
                    'regions' => ['Global']
                ],
                'status' => true
            ],
            [
                'name' => 'Premium IB Group',
                'description' => 'Premium group for high-performing IBs with enhanced rates',
                'commission_multiplier' => 1.25,
                'max_levels' => 5,
                'rules_json' => [
                    'min_clients' => 20,
                    'min_volume' => 100,
                    'regions' => ['Global'],
                    'special_benefits' => ['Priority support', 'Marketing materials']
                ],
                'status' => true
            ],
            [
                'name' => 'VIP IB Group',
                'description' => 'VIP group for top-tier IBs with maximum benefits',
                'commission_multiplier' => 1.50,
                'max_levels' => 7,
                'rules_json' => [
                    'min_clients' => 50,
                    'min_volume' => 500,
                    'regions' => ['Global'],
                    'special_benefits' => ['Dedicated account manager', 'Custom marketing', 'Higher rebates']
                ],
                'status' => true
            ]
        ];

        foreach ($groups as $group) {
            IbGroup::updateOrCreate(
                ['name' => $group['name']],
                $group
            );
        }

        $this->command->info('IB Groups created successfully.');
    }

    /**
     * Create symbol groups for different trading instruments
     */
    private function createSymbolGroups()
    {
        $symbolGroups = [
            [
                'name' => 'Forex Majors',
                'description' => 'Major currency pairs with highest liquidity',
                'symbols_json' => [
                    'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 
                    'AUDUSD', 'USDCAD', 'NZDUSD'
                ],
                'status' => true
            ],
            [
                'name' => 'Forex Minors',
                'description' => 'Minor currency pairs (cross currencies)',
                'symbols_json' => [
                    'EURGBP', 'EURJPY', 'EURCHF', 'EURAUD',
                    'EURCAD', 'GBPJPY', 'GBPCHF', 'GBPAUD',
                    'AUDCAD', 'AUDJPY', 'CADJPY', 'CHFJPY'
                ],
                'status' => true
            ],
            [
                'name' => 'Forex Exotics',
                'description' => 'Exotic currency pairs with emerging market currencies',
                'symbols_json' => [
                    'USDTRY', 'USDZAR', 'USDMXN', 'USDSEK',
                    'USDNOK', 'USDPLN', 'USDHUF', 'USDCZK'
                ],
                'status' => true
            ],
            [
                'name' => 'Precious Metals',
                'description' => 'Gold, Silver and other precious metals',
                'symbols_json' => [
                    'XAUUSD', 'XAGUSD', 'XPTUSD', 'XPDUSD'
                ],
                'status' => true
            ],
            [
                'name' => 'Energy',
                'description' => 'Oil and energy commodities',
                'symbols_json' => [
                    'USOIL', 'UKOIL', 'NGAS'
                ],
                'status' => true
            ],
            [
                'name' => 'Indices',
                'description' => 'Stock market indices',
                'symbols_json' => [
                    'US30', 'US500', 'NAS100', 'UK100',
                    'GER30', 'FRA40', 'JPN225', 'AUS200'
                ],
                'status' => true
            ],
            [
                'name' => 'Cryptocurrencies',
                'description' => 'Major cryptocurrencies',
                'symbols_json' => [
                    'BTCUSD', 'ETHUSD', 'LTCUSD', 'XRPUSD',
                    'ADAUSD', 'DOTUSD', 'LINKUSD', 'BCHUSD'
                ],
                'status' => true
            ]
        ];

        foreach ($symbolGroups as $group) {
            SymbolGroup::updateOrCreate(
                ['name' => $group['name']],
                $group
            );
        }

        $this->command->info('Symbol Groups created successfully.');
    }

    /**
     * Create rebate rules for different symbol groups and IB groups
     */
    private function createRebateRules()
    {
        $ibGroups = IbGroup::all();
        $symbolGroups = SymbolGroup::all();

        // Base rebate rates per symbol group (per lot)
        $baseRebates = [
            'Forex Majors' => 8.00,
            'Forex Minors' => 6.00,
            'Forex Exotics' => 4.00,
            'Precious Metals' => 12.00,
            'Energy' => 10.00,
            'Indices' => 5.00,
            'Cryptocurrencies' => 15.00
        ];

        foreach ($ibGroups as $ibGroup) {
            foreach ($symbolGroups as $symbolGroup) {
                $baseRebate = $baseRebates[$symbolGroup->name] ?? 5.00;
                
                // Apply group multiplier
                $rebateAmount = $baseRebate * $ibGroup->commission_multiplier;

                RebateRule::updateOrCreate([
                    'ib_group_id' => $ibGroup->id,
                    'symbol_group_id' => $symbolGroup->id,
                    'symbol' => null
                ], [
                    'rebate_per_lot' => $rebateAmount,
                    'min_volume' => 0.01,
                    'max_volume' => null,
                    'status' => true
                ]);
            }
        }

        $this->command->info('Rebate Rules created successfully.');
    }
}
