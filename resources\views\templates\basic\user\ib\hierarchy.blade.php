@extends($activeTemplate . 'layouts.master')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title">
                <h4>@lang('IB Hierarchy')</h4>
            </div>
        </div>
    </div>

    <!-- Hierarchy Statistics -->
    <div class="row gy-4">
        <div class="col-xl-3 col-lg-4 col-sm-6">
            <div class="dashboard-widget">
                <div class="dashboard-widget__icon">
                    <i class="las la-sitemap"></i>
                </div>
                <div class="dashboard-widget__content">
                    <h4 class="dashboard-widget__number">{{ $hierarchyStats['total_sub_ibs'] ?? 0 }}</h4>
                    <span class="dashboard-widget__caption">@lang('Total Sub IBs')</span>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-4 col-sm-6">
            <div class="dashboard-widget">
                <div class="dashboard-widget__icon">
                    <i class="las la-users"></i>
                </div>
                <div class="dashboard-widget__content">
                    <h4 class="dashboard-widget__number">{{ $hierarchyStats['total_clients'] ?? 0 }}</h4>
                    <span class="dashboard-widget__caption">@lang('Total Clients')</span>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-4 col-sm-6">
            <div class="dashboard-widget">
                <div class="dashboard-widget__icon">
                    <i class="las la-layer-group"></i>
                </div>
                <div class="dashboard-widget__content">
                    <h4 class="dashboard-widget__number">{{ $hierarchyStats['hierarchy_depth'] ?? 0 }}</h4>
                    <span class="dashboard-widget__caption">@lang('Hierarchy Depth')</span>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-4 col-sm-6">
            <div class="dashboard-widget">
                <div class="dashboard-widget__icon">
                    <i class="las la-dollar-sign"></i>
                </div>
                <div class="dashboard-widget__content">
                    <h4 class="dashboard-widget__number">{{ showAmount($hierarchyStats['total_commissions'] ?? 0) }}</h4>
                    <span class="dashboard-widget__caption">@lang('Total Commissions')</span>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <!-- Upline Hierarchy -->
        @if($uplineHierarchy->count() > 0)
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">@lang('My Upline Hierarchy')</h5>
                </div>
                <div class="card-body">
                    <div class="hierarchy-tree">
                        @foreach($uplineHierarchy->reverse() as $index => $uplineIb)
                        <div class="hierarchy-item mb-3">
                            <div class="d-flex align-items-center">
                                <div class="hierarchy-level">
                                    <span class="badge badge--primary">Level {{ $uplineHierarchy->count() - $index }}</span>
                                </div>
                                <div class="hierarchy-info ms-3">
                                    <h6 class="mb-0">{{ $uplineIb->fullname }}</h6>
                                    <small class="text-muted">{{ $uplineIb->username }}</small>
                                    @if($uplineIb->ibGroup)
                                        <br><small class="text-info">{{ $uplineIb->ibGroup->name }}</small>
                                    @endif
                                </div>
                                <div class="hierarchy-type ms-auto">
                                    <span class="badge badge--{{ $uplineIb->ib_type == 'master' ? 'success' : 'info' }}">
                                        {{ ucfirst($uplineIb->ib_type) }} IB
                                    </span>
                                </div>
                            </div>
                            @if(!$loop->last)
                            <div class="hierarchy-connector">
                                <i class="las la-arrow-down text-muted"></i>
                            </div>
                            @endif
                        </div>
                        @endforeach
                        
                        <!-- Current User -->
                        <div class="hierarchy-item current-user">
                            <div class="d-flex align-items-center">
                                <div class="hierarchy-level">
                                    <span class="badge badge--warning">You</span>
                                </div>
                                <div class="hierarchy-info ms-3">
                                    <h6 class="mb-0">{{ $user->fullname }}</h6>
                                    <small class="text-muted">{{ $user->username }}</small>
                                    @if($user->ibGroup)
                                        <br><small class="text-info">{{ $user->ibGroup->name }}</small>
                                    @endif
                                </div>
                                <div class="hierarchy-type ms-auto">
                                    <span class="badge badge--{{ $user->ib_type == 'master' ? 'success' : 'info' }}">
                                        {{ ucfirst($user->ib_type) }} IB
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <!-- Downline Hierarchy -->
        <div class="col-lg-{{ $uplineHierarchy->count() > 0 ? '6' : '12' }}">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">@lang('My Downline Structure')</h5>
                </div>
                <div class="card-body">
                    @if($hierarchyTree && isset($hierarchyTree['children']) && count($hierarchyTree['children']) > 0)
                    <div class="downline-tree">
                        @include('templates.basic.user.ib.partials.hierarchy_node', ['node' => $hierarchyTree, 'level' => 0])
                    </div>
                    @else
                    <div class="text-center py-4">
                        <i class="las la-sitemap text-muted" style="font-size: 4rem;"></i>
                        <h5 class="text-muted mt-3">@lang('No Sub IBs Yet')</h5>
                        <p class="text-muted">@lang('Your downline structure will appear here once you have Sub IBs')</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Hierarchy Performance -->
    @if($hierarchyTree && isset($hierarchyTree['children']) && count($hierarchyTree['children']) > 0)
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">@lang('Hierarchy Performance')</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table--light">
                            <thead>
                                <tr>
                                    <th>@lang('IB Name')</th>
                                    <th>@lang('Level')</th>
                                    <th>@lang('Type')</th>
                                    <th>@lang('Total Clients')</th>
                                    <th>@lang('Active Clients')</th>
                                    <th>@lang('Status')</th>
                                </tr>
                            </thead>
                            <tbody>
                                @include('templates.basic.user.ib.partials.hierarchy_table', ['nodes' => $hierarchyTree['children'] ?? []])
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

<style>
.hierarchy-tree .hierarchy-item {
    position: relative;
    padding: 15px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background: #f8f9fa;
}

.hierarchy-tree .hierarchy-item.current-user {
    background: #fff3cd;
    border-color: #ffeaa7;
}

.hierarchy-connector {
    text-align: center;
    margin: 10px 0;
}

.downline-tree .hierarchy-item {
    margin-left: 20px;
    margin-bottom: 10px;
}

.downline-tree .hierarchy-item:before {
    content: '';
    position: absolute;
    left: -15px;
    top: 50%;
    width: 10px;
    height: 1px;
    background: #dee2e6;
}
</style>
@endsection
