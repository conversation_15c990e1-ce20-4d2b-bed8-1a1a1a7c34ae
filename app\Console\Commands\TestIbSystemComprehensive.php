<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class TestIbSystemComprehensive extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:ib-system-comprehensive';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Comprehensive testing of the complete IB system with MT5 integration';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 COMPREHENSIVE IB SYSTEM TESTING');
        $this->info('===================================');

        $allTestsPassed = true;

        // Test 1: Database Migration Verification
        $allTestsPassed &= $this->testDatabaseMigration();

        // Test 2: Test User 873978 Verification
        $allTestsPassed &= $this->testSpecificUser();

        // Test 3: MT5 Commission Integration
        $allTestsPassed &= $this->testCommissionIntegration();

        // Test 4: IB User Detection
        $allTestsPassed &= $this->testIbUserDetection();

        // Test 5: Field Mapping Verification
        $allTestsPassed &= $this->testFieldMapping();

        // Test 6: Admin Interface Data
        $allTestsPassed &= $this->testAdminInterfaceData();

        if ($allTestsPassed) {
            $this->info("\n🎉 ALL TESTS PASSED! IB SYSTEM IS FULLY FUNCTIONAL!");
            $this->info("✅ Database Migration: Complete");
            $this->info("✅ MT5 Integration: Working");
            $this->info("✅ Commission Tracking: Active");
            $this->info("✅ IB Detection: Functional");
            $this->info("✅ Field Mapping: Correct");
            $this->info("✅ Admin Interface: Ready");
            
            $this->info("\n🔗 Test URLs:");
            $this->info("Admin User Detail: http://localhost/mbf.mybrokerforex.com-31052025/admin/users/detail/[USER_ID]");
            $this->info("User Partnership: http://localhost/mbf.mybrokerforex.com-31052025/user/partnership/network");
            
            return 0;
        } else {
            $this->error("\n❌ SOME TESTS FAILED! Please review the issues above.");
            return 1;
        }
    }

    /**
     * Test 1: Database Migration Verification
     */
    private function testDatabaseMigration()
    {
        $this->info("\n📋 TEST 1: Database Migration Verification");
        $this->info("==========================================");

        try {
            $totalUsers = User::count();
            $mt5Users = User::whereNotNull('mt5_login')->count();
            $ibUsers = User::where('ib_status', User::IB_STATUS_APPROVED)->count();

            $this->info("✅ Total Users Migrated: {$totalUsers}");
            $this->info("✅ Users with MT5 Login: {$mt5Users}");
            $this->info("✅ Approved IB Users: {$ibUsers}");

            if ($totalUsers < 10000) {
                $this->error("❌ Expected ~10,966 users, found {$totalUsers}");
                return false;
            }

            if ($mt5Users < $totalUsers * 0.9) {
                $this->warn("⚠️  Warning: Only {$mt5Users} users have MT5 logins");
            }

            return true;

        } catch (\Exception $e) {
            $this->error("❌ Database migration test failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Test 2: Test User 873978 Verification
     */
    private function testSpecificUser()
    {
        $this->info("\n📋 TEST 2: Test User 873978 Verification");
        $this->info("========================================");

        try {
            $testUser = User::where('mt5_login', 873978)->first();

            if (!$testUser) {
                $this->error('❌ Test user 873978 not found in database');
                return false;
            }

            $this->info("✅ Test user found: {$testUser->email}");
            $this->info("   - Name: {$testUser->firstname} {$testUser->lastname}");
            $this->info("   - MT5 Login: {$testUser->mt5_login}");
            $this->info("   - MT5 Group: {$testUser->mt5_group}");
            $this->info("   - IB Status: " . ($testUser->isIb() ? 'Approved IB (' . $testUser->ib_type . ')' : 'Not IB'));

            if (!$testUser->isIb()) {
                $this->error('❌ Test user should be an approved IB');
                return false;
            }

            if ($testUser->email !== '<EMAIL>') {
                $this->error('❌ Test user email mismatch');
                return false;
            }

            return true;

        } catch (\Exception $e) {
            $this->error("❌ Test user verification failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Test 3: MT5 Commission Integration
     */
    private function testCommissionIntegration()
    {
        $this->info("\n📋 TEST 3: MT5 Commission Integration");
        $this->info("====================================");

        try {
            $testUser = User::where('mt5_login', 873978)->first();
            
            if (!$testUser) {
                $this->error('❌ Test user not found for commission test');
                return false;
            }

            $commissions = DB::connection('mbf-dbmt5')
                ->table('mt5_deals_2025')
                ->where('Login', $testUser->mt5_login)
                ->where('Action', 18)
                ->get();

            $totalCommission = $commissions->sum('Profit');
            $commissionCount = $commissions->count();

            $this->info("✅ MT5 Commission Data Retrieved:");
            $this->info("   - Total Commission: $" . number_format($totalCommission, 2));
            $this->info("   - Commission Deals: {$commissionCount}");

            if ($commissionCount > 0) {
                $recentCommission = $commissions->first();
                $this->info("   - Latest Deal: {$recentCommission->Deal}");
                $this->info("   - Latest Amount: $" . number_format($recentCommission->Profit, 2));
                $this->info("   - Latest Date: {$recentCommission->Time}");
            }

            if ($totalCommission < 200) {
                $this->warn("⚠️  Warning: Expected commission ~$263.32, found $" . number_format($totalCommission, 2));
            }

            return true;

        } catch (\Exception $e) {
            $this->error("❌ Commission integration test failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Test 4: IB User Detection
     */
    private function testIbUserDetection()
    {
        $this->info("\n📋 TEST 4: IB User Detection");
        $this->info("============================");

        try {
            $ibGroups = [
                'real\\Affiliates',
                'real\\IB\\IB MAIN',
                'real\\IB\\IB SUB',
                'real\\Multi-IB\\Default',
                'real\\Multi-IB\\Level1',
                'real\\Multi-IB\\Level2',
                'real\\Multi-IB\\Level3',
                'real\\Multi-IB\\Level4',
                'real\\Multi-IB\\Level5'
            ];

            $totalIbUsers = 0;
            foreach ($ibGroups as $group) {
                $count = User::where('mt5_group', $group)->count();
                $totalIbUsers += $count;
                if ($count > 0) {
                    $this->info("✅ {$group}: {$count} users");
                }
            }

            $this->info("✅ Total IB Users Detected: {$totalIbUsers}");

            if ($totalIbUsers === 0) {
                $this->error("❌ No IB users detected from MT5 groups");
                return false;
            }

            return true;

        } catch (\Exception $e) {
            $this->error("❌ IB user detection test failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Test 5: Field Mapping Verification
     */
    private function testFieldMapping()
    {
        $this->info("\n📋 TEST 5: Field Mapping Verification");
        $this->info("====================================");

        try {
            $testUser = User::where('mt5_login', 873978)->first();
            
            if (!$testUser) {
                $this->error('❌ Test user not found for field mapping test');
                return false;
            }

            $this->info("✅ Field Mapping Verification:");
            $this->info("   - FirstName → firstname: '{$testUser->firstname}'");
            $this->info("   - LastName → lastname: '{$testUser->lastname}'");
            $this->info("   - Email → email: '{$testUser->email}'");
            $this->info("   - Login → mt5_login: '{$testUser->mt5_login}'");
            $this->info("   - Group → mt5_group: '{$testUser->mt5_group}'");
            $this->info("   - Balance → mt5_balance: '{$testUser->mt5_balance}'");

            // Check if names are properly mapped (not showing "name" instead of actual names)
            if ($testUser->firstname === 'name' || $testUser->lastname === 'name') {
                $this->error("❌ Name fields incorrectly mapped");
                return false;
            }

            if (empty($testUser->firstname)) {
                $this->warn("⚠️  Warning: FirstName is empty");
            }

            return true;

        } catch (\Exception $e) {
            $this->error("❌ Field mapping test failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Test 6: Admin Interface Data
     */
    private function testAdminInterfaceData()
    {
        $this->info("\n📋 TEST 6: Admin Interface Data");
        $this->info("==============================");

        try {
            $testUser = User::where('mt5_login', 873978)->first();
            
            if (!$testUser) {
                $this->error('❌ Test user not found for admin interface test');
                return false;
            }

            // Simulate admin interface data retrieval
            $this->info("✅ Admin Interface Data Ready:");
            $this->info("   - User ID: {$testUser->id}");
            $this->info("   - Partner Status: " . ($testUser->isIb() ? 'Approved IB' : 'Regular User'));
            $this->info("   - MT5 Integration: " . ($testUser->mt5_login ? 'Connected' : 'Not Connected'));
            $this->info("   - Commission Tracking: Available");

            return true;

        } catch (\Exception $e) {
            $this->error("❌ Admin interface test failed: " . $e->getMessage());
            return false;
        }
    }
}
