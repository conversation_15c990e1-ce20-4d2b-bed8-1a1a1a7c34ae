@extends('admin.layouts.app')

@section('panel')
    <!-- <PERSON> Header -->
    <div class="row mb-4">
        <div class="col-lg-12">
            <div class="card" style="background: white; border: 1px solid #e5e5e5; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <div class="card-body py-4">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h4 class="mb-1" style="font-size: 24px; font-weight: 600; color: #000;">@lang('Settings Dashboard')</h4>
                            <p class="mb-0" style="font-size: 14px; color: #6c757d;">Manage all system settings from one central location</p>
                        </div>
                        <div>
                            <i class="las la-cogs" style="font-size: 48px; color: #e5e5e5;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Categories -->
    @foreach($settingsCategories as $categoryKey => $category)
        <div class="row mb-4">
            <div class="col-lg-12">
                <div class="card" style="background: white; border: 1px solid #e5e5e5; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <div class="card-header" style="background: #f8f9fa; border-bottom: 1px solid #e5e5e5; border-radius: 8px 8px 0 0;">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <div class="rounded-circle d-flex align-items-center justify-content-center" 
                                     style="width: 40px; height: 40px; background-color: var(--bs-{{ $category['color'] }}); color: white;">
                                    <i class="{{ $category['icon'] }}" style="font-size: 20px;"></i>
                                </div>
                            </div>
                            <div>
                                <h5 class="mb-0" style="font-size: 18px; font-weight: 600; color: #000;">{{ $category['title'] }}</h5>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="row g-0">
                            @foreach($category['items'] as $item)
                                <div class="col-md-6 col-lg-4">
                                    <a href="{{ route($item['route']) }}" class="text-decoration-none">
                                        <div class="p-4 border-end border-bottom setting-item" style="border-color: #f0f0f0 !important; transition: all 0.3s ease;">
                                            <div class="d-flex align-items-start">
                                                <div class="me-3">
                                                    <div class="rounded d-flex align-items-center justify-content-center" 
                                                         style="width: 32px; height: 32px; background-color: #f8f9fa; color: #6c757d;">
                                                        <i class="{{ $item['icon'] }}" style="font-size: 16px;"></i>
                                                    </div>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <h6 class="mb-1" style="font-size: 14px; font-weight: 600; color: #000;">{{ $item['title'] }}</h6>
                                                    <p class="mb-0" style="font-size: 12px; color: #6c757d; line-height: 1.4;">{{ $item['description'] }}</p>
                                                </div>
                                                <div class="ms-2">
                                                    <i class="las la-chevron-right" style="font-size: 14px; color: #6c757d;"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endforeach

    <!-- Quick Stats -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card" style="background: white; border: 1px solid #e5e5e5; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <div class="card-body py-4">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="p-3">
                                <i class="las la-cog" style="font-size: 32px; color: #007bff; margin-bottom: 8px;"></i>
                                <h6 style="font-size: 14px; font-weight: 600; color: #000; margin-bottom: 4px;">System Status</h6>
                                <span class="badge bg-success" style="font-size: 12px;">Online</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="p-3">
                                <i class="las la-envelope" style="font-size: 32px; color: #28a745; margin-bottom: 8px;"></i>
                                <h6 style="font-size: 14px; font-weight: 600; color: #000; margin-bottom: 4px;">Email Service</h6>
                                <span class="badge bg-success" style="font-size: 12px;">Active</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="p-3">
                                <i class="las la-chart-line" style="font-size: 32px; color: #ffc107; margin-bottom: 8px;"></i>
                                <h6 style="font-size: 14px; font-weight: 600; color: #000; margin-bottom: 4px;">Trading</h6>
                                <span class="badge bg-success" style="font-size: 12px;">Enabled</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="p-3">
                                <i class="las la-shield-alt" style="font-size: 32px; color: #dc3545; margin-bottom: 8px;"></i>
                                <h6 style="font-size: 14px; font-weight: 600; color: #000; margin-bottom: 4px;">Security</h6>
                                <span class="badge bg-success" style="font-size: 12px;">Protected</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('style')
<style>
    .setting-item:hover {
        background-color: #f8f9fa !important;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;
    }
    
    .setting-item:hover .rounded {
        background-color: var(--bs-primary) !important;
        color: white !important;
    }
    
    .setting-item:hover h6 {
        color: var(--bs-primary) !important;
    }
    
    .card {
        transition: all 0.3s ease;
    }
    
    .badge {
        font-weight: 500;
    }
    
    /* Black/Red Theme Colors - RGB(220, 53, 69) */
    :root {
        --bs-primary: #dc3545;
        --bs-success: #28a745;
        --bs-info: #17a2b8;
        --bs-warning: #ffc107;
        --bs-danger: #dc3545;
        --bs-dark: #000000;
    }

    /* Enhanced hover effects with theme colors */
    .setting-item:hover .rounded {
        background-color: #dc3545 !important;
        color: white !important;
    }

    .setting-item:hover h6 {
        color: #dc3545 !important;
    }

    /* Category header colors */
    .card-header .rounded-circle {
        transition: all 0.3s ease;
    }

    .card-header .rounded-circle.bg-primary {
        background-color: #dc3545 !important;
    }

    .card-header .rounded-circle.bg-dark {
        background-color: #000000 !important;
    }
</style>
@endpush
