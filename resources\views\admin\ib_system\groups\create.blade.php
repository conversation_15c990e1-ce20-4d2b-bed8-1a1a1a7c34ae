@extends('admin.layouts.app')

@section('panel')
<div class="row">
    <div class="col-lg-12">
        <div class="card b-radius--10">
            <div class="card-header">
                <h5 class="card-title">@lang('Create New IB Group')</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.ib.groups.store') }}" method="POST">
                    @csrf
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Group Name') <span class="text-danger">*</span></label>
                                <input type="text" name="name" class="form-control" value="{{ old('name') }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Commission Multiplier') <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" name="commission_multiplier" class="form-control" step="0.01" min="0" max="10" value="{{ old('commission_multiplier', 1.00) }}" required>
                                    <div class="input-group-text">x</div>
                                </div>
                                <small class="text-muted">@lang('Multiplier for base commission rates (e.g., 1.5 = 150% of base rate)')</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Maximum Levels') <span class="text-danger">*</span></label>
                                <input type="number" name="max_levels" class="form-control" min="1" max="10" value="{{ old('max_levels', 3) }}" required>
                                <small class="text-muted">@lang('Maximum hierarchy levels allowed in this group')</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Status')</label>
                                <select name="status" class="form-control">
                                    <option value="1" {{ old('status', 1) == 1 ? 'selected' : '' }}>@lang('Active')</option>
                                    <option value="0" {{ old('status') == 0 ? 'selected' : '' }}>@lang('Inactive')</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>@lang('Description')</label>
                        <textarea name="description" class="form-control" rows="4">{{ old('description') }}</textarea>
                    </div>

                    <div class="form-group">
                        <label>@lang('Additional Rules') <small class="text-muted">(@lang('Optional - JSON format'))</small></label>
                        <textarea name="rules" class="form-control" rows="6" placeholder='{"min_clients": 10, "min_volume": 100, "regions": ["Asia", "Europe"]}'></textarea>
                        <small class="text-muted">@lang('Enter additional rules in JSON format for advanced configurations')</small>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn--primary h-45 w-100">@lang('Create IB Group')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('breadcrumb-plugins')
<div class="d-inline">
    <a href="{{ route('admin.ib.groups.index') }}" class="btn btn--dark">
        <i class="las la-arrow-left"></i> @lang('Back to Groups')
    </a>
</div>
@endpush

@push('script')
<script>
(function($) {
    "use strict";

    // JSON validation for rules field
    $('textarea[name="rules"]').on('blur', function() {
        const value = $(this).val().trim();
        if (value && value !== '') {
            try {
                JSON.parse(value);
                $(this).removeClass('is-invalid').addClass('is-valid');
            } catch (e) {
                $(this).removeClass('is-valid').addClass('is-invalid');
                alert('Invalid JSON format in Additional Rules field');
            }
        } else {
            $(this).removeClass('is-invalid is-valid');
        }
    });

})(jQuery);
</script>
@endpush
