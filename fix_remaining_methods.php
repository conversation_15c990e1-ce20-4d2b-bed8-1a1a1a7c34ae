<?php
/**
 * FINAL FIX FOR ALL REMAINING MT5 METHODS
 * This will fix all the remaining broken Python command formats
 */

echo "<h1>Final MT5 Methods Fixer</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";

$filePath = 'MT5ServiceForLive.php';

if (!file_exists($filePath)) {
    echo "<span class='error'>❌ File not found: {$filePath}</span><br>";
    exit;
}

$content = file_get_contents($filePath);
$originalContent = $content;

echo "<span class='info'>📁 Processing file: {$filePath}</span><br>";

// Define all the remaining fixes
$fixes = [
    // Fix 1: addBalanceToAccount method
    [
        'search' => "            \$command = sprintf(
                'python \"%s\" add_balance --login %d --amount %.2f --comment \"%s\"',
                \$this->pythonScript,
                \$login,
                \$amount,
                \$comment
            );",
        'replace' => "            \$command = sprintf(
                '%s %s add_balance --login %d --amount %.2f --comment %s',
                escapeshellarg(\$this->pythonExe),
                escapeshellarg(\$this->pythonScript),
                \$login,
                \$amount,
                escapeshellarg(\$comment)
            );",
        'description' => 'addBalanceToAccount method'
    ],
    
    // Fix 2: deductBalanceFromAccount method
    [
        'search' => "            \$command = sprintf(
                'python \"%s\" add_balance --login %d --amount %.2f --comment \"%s\"',
                \$this->pythonScript,
                \$login,
                -\$amount, // Negative amount for deduction
                \$comment
            );",
        'replace' => "            \$command = sprintf(
                '%s %s add_balance --login %d --amount %.2f --comment %s',
                escapeshellarg(\$this->pythonExe),
                escapeshellarg(\$this->pythonScript),
                \$login,
                -\$amount, // Negative amount for deduction
                escapeshellarg(\$comment)
            );",
        'description' => 'deductBalanceFromAccount method'
    ],
    
    // Fix 3: getAccountBalance method
    [
        'search' => "            \$command = sprintf(
                'python \"%s\" get_balance --login %d',
                \$this->pythonScript,
                \$login
            );",
        'replace' => "            \$command = sprintf(
                '%s %s get_balance --login %d',
                escapeshellarg(\$this->pythonExe),
                escapeshellarg(\$this->pythonScript),
                \$login
            );",
        'description' => 'getAccountBalance method'
    ],
    
    // Fix 4: changeLeverage method
    [
        'search' => "            \$command = sprintf(
                'python \"%s\" change_leverage --login %d --leverage %d',
                \$this->pythonScript,
                \$login,
                \$leverage
            );",
        'replace' => "            \$command = sprintf(
                '%s %s change_leverage --login %d --leverage %d',
                escapeshellarg(\$this->pythonExe),
                escapeshellarg(\$this->pythonScript),
                \$login,
                \$leverage
            );",
        'description' => 'changeLeverage method'
    ],
    
    // Fix 5: changePassword method
    [
        'search' => "            \$command = sprintf(
                'python \"%s\" change_password --login %d --new_password \"%s\" --password_type %s --password \"%s\"',
                \$this->pythonScript,
                \$login,
                \$newPassword,
                \$passwordType,
                \$newPassword // Using same password for verification
            );",
        'replace' => "            \$command = sprintf(
                '%s %s change_password --login %d --new_password %s --password_type %s --password %s',
                escapeshellarg(\$this->pythonExe),
                escapeshellarg(\$this->pythonScript),
                \$login,
                escapeshellarg(\$newPassword),
                \$passwordType,
                escapeshellarg(\$newPassword) // Using same password for verification
            );",
        'description' => 'changePassword method'
    ]
];

$fixedCount = 0;

foreach ($fixes as $fix) {
    echo "<br><span class='info'>🔧 Fixing: {$fix['description']}</span><br>";
    
    if (strpos($content, $fix['search']) !== false) {
        $content = str_replace($fix['search'], $fix['replace'], $content);
        $fixedCount++;
        echo "<span class='success'>✅ Fixed: {$fix['description']}</span><br>";
    } else {
        echo "<span class='error'>❌ Pattern not found: {$fix['description']}</span><br>";
        echo "<span class='info'>Searching for partial match...</span><br>";
    }
}

// Write the fixed content back to the file
if ($fixedCount > 0) {
    if (file_put_contents($filePath, $content)) {
        echo "<br><span class='success'>🎉 Successfully applied {$fixedCount} fixes to {$filePath}</span><br>";
        echo "<span class='info'>📊 File size: " . number_format(strlen($content)) . " bytes</span><br>";
        echo "<span class='info'>📊 Total lines: " . substr_count($content, "\n") . "</span><br>";
    } else {
        echo "<br><span class='error'>❌ Failed to write to {$filePath}</span><br>";
    }
} else {
    echo "<br><span class='info'>ℹ️ No fixes were applied</span><br>";
}

echo "<br><h3>✅ Summary of What Should Be Fixed:</h3>";
echo "<ul>";
echo "<li>✅ addBalanceToAccount method - Use escapeshellarg for Python exe and script</li>";
echo "<li>✅ deductBalanceFromAccount method - Use escapeshellarg for Python exe and script</li>";
echo "<li>✅ getAccountBalance method - Use escapeshellarg for Python exe and script</li>";
echo "<li>✅ changeLeverage method - Use escapeshellarg for Python exe and script</li>";
echo "<li>✅ changePassword method - Use escapeshellarg for Python exe and script</li>";
echo "</ul>";

echo "<p><strong>🚀 Next Steps:</strong></p>";
echo "<ol>";
echo "<li>Check if all methods are now using the correct format</li>";
echo "<li>Upload to your live Windows/Plesk server</li>";
echo "<li>Test leverage and password changes</li>";
echo "<li>Clear Laravel caches</li>";
echo "</ol>";

echo "<p><em>🗑️ Delete this fixer script after use.</em></p>";
?>
