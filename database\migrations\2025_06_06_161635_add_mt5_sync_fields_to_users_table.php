<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Core MT5 Sync Fields
            $table->string('mt5_login')->nullable()->unique()->after('id');
            $table->bigInteger('mt5_timestamp')->nullable()->after('mt5_login');
            $table->string('mt5_group')->nullable()->after('mt5_timestamp');
            $table->bigInteger('mt5_cert_serial_number')->nullable()->after('mt5_group');
            $table->bigInteger('mt5_rights')->nullable()->after('mt5_cert_serial_number');
            $table->timestamp('mt5_registration')->nullable()->after('mt5_rights');
            $table->timestamp('mt5_last_access')->nullable()->after('mt5_registration');
            $table->timestamp('mt5_last_pass_change')->nullable()->after('mt5_last_access');

            // Personal Information (some may override existing fields)
            $table->string('mt5_first_name')->nullable()->after('mt5_last_pass_change');
            $table->string('mt5_last_name')->nullable()->after('mt5_first_name');
            $table->string('mt5_middle_name')->nullable()->after('mt5_last_name');
            $table->string('mt5_company')->nullable()->after('mt5_middle_name');
            $table->string('mt5_account')->nullable()->after('mt5_company');
            $table->string('mt5_country')->nullable()->after('mt5_account');
            $table->integer('mt5_language')->nullable()->after('mt5_country');
            $table->bigInteger('mt5_client_id')->nullable()->after('mt5_language');

            // Address Information
            $table->string('mt5_city')->nullable()->after('mt5_client_id');
            $table->string('mt5_state')->nullable()->after('mt5_city');
            $table->string('mt5_zip_code')->nullable()->after('mt5_state');
            $table->string('mt5_address')->nullable()->after('mt5_zip_code');
            $table->string('mt5_phone')->nullable()->after('mt5_address');
            $table->string('mt5_email')->nullable()->after('mt5_phone');
            $table->string('mt5_id')->nullable()->after('mt5_email');
            $table->string('mt5_status')->nullable()->after('mt5_id');
            $table->string('mt5_comment')->nullable()->after('mt5_status');
            $table->integer('mt5_color')->nullable()->after('mt5_comment');
            $table->string('mt5_phone_password')->nullable()->after('mt5_color');

            // Trading Information
            $table->integer('mt5_leverage')->nullable()->default(100)->after('mt5_phone_password');
            $table->bigInteger('mt5_agent')->nullable()->after('mt5_leverage');
            $table->string('mt5_trade_accounts')->nullable()->after('mt5_agent');
            $table->double('mt5_limit_positions')->nullable()->after('mt5_trade_accounts');
            $table->integer('mt5_limit_orders')->nullable()->after('mt5_limit_positions');
            $table->string('mt5_lead_campaign')->nullable()->after('mt5_limit_orders');
            $table->string('mt5_lead_source')->nullable()->after('mt5_lead_campaign');
            $table->bigInteger('mt5_timestamp_trade')->nullable()->after('mt5_lead_source');

            // Financial Information
            $table->decimal('mt5_balance', 15, 2)->nullable()->default(0)->after('mt5_timestamp_trade');
            $table->decimal('mt5_credit', 15, 2)->nullable()->default(0)->after('mt5_balance');
            $table->decimal('mt5_equity', 15, 2)->nullable()->default(0)->after('mt5_credit');
            $table->double('mt5_interest_rate')->nullable()->after('mt5_equity');
            $table->double('mt5_commission_daily')->nullable()->after('mt5_interest_rate');
            $table->double('mt5_commission_monthly')->nullable()->after('mt5_commission_daily');
            $table->double('mt5_balance_prev_day')->nullable()->after('mt5_commission_monthly');
            $table->double('mt5_balance_prev_month')->nullable()->after('mt5_balance_prev_day');
            $table->double('mt5_equity_prev_day')->nullable()->after('mt5_balance_prev_month');
            $table->double('mt5_equity_prev_month')->nullable()->after('mt5_equity_prev_day');

            // Additional Information
            $table->string('mt5_name')->nullable()->after('mt5_equity_prev_month');
            $table->string('mt5_mqid')->nullable()->after('mt5_name');
            $table->string('mt5_last_ip')->nullable()->after('mt5_mqid');
            $table->text('mt5_api_data')->nullable()->after('mt5_last_ip');

            // Sync tracking
            $table->string('mt5_currency', 10)->nullable()->default('USD')->after('mt5_api_data');
            $table->timestamp('mt5_synced_at')->nullable()->after('mt5_currency');

            // Add indexes for performance
            $table->index('mt5_login');
            $table->index('mt5_group');
            $table->index('mt5_email');
            $table->index('mt5_synced_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop indexes first (check if they exist)
            if (Schema::hasColumn('users', 'mt5_login')) {
                $table->dropIndex(['mt5_login']);
            }
            if (Schema::hasColumn('users', 'mt5_group')) {
                $table->dropIndex(['mt5_group']);
            }
            if (Schema::hasColumn('users', 'mt5_email')) {
                $table->dropIndex(['mt5_email']);
            }
            if (Schema::hasColumn('users', 'mt5_synced_at')) {
                $table->dropIndex(['mt5_synced_at']);
            }

            // Drop all MT5 columns
            $table->dropColumn([
                'mt5_login',
                'mt5_timestamp',
                'mt5_group',
                'mt5_cert_serial_number',
                'mt5_rights',
                'mt5_registration',
                'mt5_last_access',
                'mt5_last_pass_change',
                'mt5_first_name',
                'mt5_last_name',
                'mt5_middle_name',
                'mt5_company',
                'mt5_account',
                'mt5_country',
                'mt5_language',
                'mt5_client_id',
                'mt5_city',
                'mt5_state',
                'mt5_zip_code',
                'mt5_address',
                'mt5_phone',
                'mt5_email',
                'mt5_id',
                'mt5_status',
                'mt5_comment',
                'mt5_color',
                'mt5_phone_password',
                'mt5_leverage',
                'mt5_agent',
                'mt5_trade_accounts',
                'mt5_limit_positions',
                'mt5_limit_orders',
                'mt5_lead_campaign',
                'mt5_lead_source',
                'mt5_timestamp_trade',
                'mt5_balance',
                'mt5_credit',
                'mt5_equity',
                'mt5_interest_rate',
                'mt5_commission_daily',
                'mt5_commission_monthly',
                'mt5_balance_prev_day',
                'mt5_balance_prev_month',
                'mt5_equity_prev_day',
                'mt5_equity_prev_month',
                'mt5_name',
                'mt5_mqid',
                'mt5_last_ip',
                'mt5_api_data',
                'mt5_currency',
                'mt5_synced_at'
            ]);
        });
    }
};
