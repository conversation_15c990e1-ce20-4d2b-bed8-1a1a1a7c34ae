<?php

require_once 'vendor/autoload.php';
require_once 'bootstrap/app.php';

use App\Models\NotificationTemplate;

$template = NotificationTemplate::find(33);

echo "Template ID: " . $template->id . "\n";
echo "Template Name: " . $template->name . "\n";
echo "Action Code: " . $template->act . "\n";
echo "Subject: " . $template->subj . "\n";
echo "Content Length: " . strlen($template->email_body) . " characters\n";

// Check if content contains specific KYC content
$content = $template->email_body;
if (strpos($content, 'KYC') !== false) {
    echo "✅ Contains KYC-specific content\n";
} else {
    echo "❌ Missing KYC-specific content\n";
}

if (strpos($content, 'This is an important notification') !== false) {
    echo "❌ Contains generic placeholder text\n";
} else {
    echo "✅ No generic placeholder text found\n";
}

// Show a snippet of the main content
echo "\nMain content snippet:\n";
$start = strpos($content, '<div class="content-section">');
if ($start !== false) {
    $end = strpos($content, '</div>', $start);
    if ($end !== false) {
        $contentSection = substr($content, $start, $end - $start + 6);
        echo $contentSection . "\n";
    }
}
