<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\User;

class TestMT5Search extends Command
{
    protected $signature = 'test:mt5-search';
    protected $description = 'Test MT5 account search functionality';

    public function handle()
    {
        $this->info('🔍 TESTING MT5 ACCOUNT SEARCH FUNCTIONALITY');
        $this->info('===========================================');

        // Get some sample MT5 accounts to test with
        $sampleUsers = User::whereNotNull('mt5_login')
                          ->limit(5)
                          ->get(['id', 'email', 'mt5_login', 'all_mt5_accounts']);

        if ($sampleUsers->isEmpty()) {
            $this->error('❌ No MT5 users found for testing');
            return;
        }

        $this->info('📊 Sample MT5 accounts for testing:');
        foreach ($sampleUsers as $user) {
            $this->line("- {$user->email}: {$user->mt5_login} (All: {$user->all_mt5_accounts})");
        }

        $this->info('');
        $this->info('🧪 TESTING SEARCH FUNCTIONALITY:');
        $this->info('--------------------------------');

        $testCases = [];
        foreach ($sampleUsers as $user) {
            if ($user->mt5_login) {
                $testCases[] = $user->mt5_login;
            }
            
            // Test additional accounts from all_mt5_accounts
            if ($user->all_mt5_accounts) {
                $additionalAccounts = explode(',', $user->all_mt5_accounts);
                foreach ($additionalAccounts as $account) {
                    if ($account && $account !== $user->mt5_login) {
                        $testCases[] = trim($account);
                        break; // Just test one additional account per user
                    }
                }
            }
        }

        $testCases = array_unique(array_slice($testCases, 0, 5)); // Test max 5 accounts

        $passedTests = 0;
        $totalTests = count($testCases);

        foreach ($testCases as $searchTerm) {
            $this->info("🔍 Testing search for MT5 account: {$searchTerm}");
            
            // Simulate the same search logic as in ManageUsersController
            $results = User::where(function($q) use ($searchTerm) {
                $q->where('users.username', 'like', "%{$searchTerm}%")
                  ->orWhere('users.email', 'like', "%{$searchTerm}%")
                  ->orWhere('users.firstname', 'like', "%{$searchTerm}%")
                  ->orWhere('users.lastname', 'like', "%{$searchTerm}%")
                  ->orWhere('users.mt5_login', 'like', "%{$searchTerm}%")
                  ->orWhere('users.mobile', 'like', "%{$searchTerm}%")
                  ->orWhere('users.country_code', 'like', "%{$searchTerm}%")
                  // Search ALL MT5 accounts
                  ->orWhere('users.all_mt5_accounts', 'like', "%{$searchTerm}%")
                  ->orWhereExists(function($subQuery) use ($searchTerm) {
                      $subQuery->select(DB::raw(1))
                               ->from('users as u_search')
                               ->whereColumn('u_search.email', 'users.email')
                               ->where('u_search.mt5_login', 'like', "%{$searchTerm}%");
                  });
            })->count();

            if ($results > 0) {
                $this->line("   ✅ Found {$results} result(s)");
                $passedTests++;
            } else {
                $this->line("   ❌ No results found");
            }
        }

        $this->info('');
        $this->info('📊 SEARCH TEST RESULTS:');
        $this->info('----------------------');
        $this->line("Total Tests: {$totalTests}");
        $this->line("Passed: {$passedTests}");
        $this->line("Failed: " . ($totalTests - $passedTests));
        $successRate = $totalTests > 0 ? round(($passedTests / $totalTests) * 100, 2) : 0;
        $this->line("Success Rate: {$successRate}%");

        if ($successRate >= 80) {
            $this->info('✅ MT5 SEARCH FUNCTIONALITY: WORKING WELL');
        } elseif ($successRate >= 50) {
            $this->warn('⚠️ MT5 SEARCH FUNCTIONALITY: NEEDS IMPROVEMENT');
        } else {
            $this->error('❌ MT5 SEARCH FUNCTIONALITY: BROKEN');
        }

        // Test specific edge cases
        $this->info('');
        $this->info('🧪 TESTING EDGE CASES:');
        $this->info('---------------------');

        // Test partial MT5 login search
        if (!empty($testCases)) {
            $firstAccount = $testCases[0];
            $partialSearch = substr($firstAccount, 0, 4); // First 4 digits
            
            $partialResults = User::where(function($q) use ($partialSearch) {
                $q->where('users.mt5_login', 'like', "%{$partialSearch}%")
                  ->orWhere('users.all_mt5_accounts', 'like', "%{$partialSearch}%");
            })->count();

            $this->line("🔍 Partial search '{$partialSearch}': {$partialResults} results");
        }

        // Test all_mt5_accounts field population
        $usersWithAllAccounts = User::whereNotNull('all_mt5_accounts')
                                   ->where('all_mt5_accounts', '!=', '')
                                   ->count();
        
        $totalMT5Users = User::whereNotNull('mt5_login')->count();
        
        $this->line("📊 Users with all_mt5_accounts populated: {$usersWithAllAccounts}/{$totalMT5Users}");
        
        if ($usersWithAllAccounts < $totalMT5Users * 0.8) {
            $this->warn('⚠️ all_mt5_accounts field may not be properly populated');
        }

        $this->info('');
        $this->info('🎉 MT5 SEARCH TEST COMPLETED');
    }
}
