# 🔧 Windows Server 2022/Plesk Email Editor Complete Fix

## 🚨 **CRITICAL ISSUE ANALYSIS**

Based on your logs and screenshot, the email template editor has two separate problems:

### **1. Pre-existing Issue: Editor Display Broken**
- Editor not loading correctly on Windows Server 2022/Plesk
- Wrong template content being displayed
- Layout corruption and functionality issues

### **2. Post-edit Issue: Content Over-cleaning**
- Our previous fixes were too aggressive in content cleaning
- Content being stripped from 3788 characters to 1298 characters
- Essential email content being removed during processing

## ✅ **COMPREHENSIVE SOLUTION APPLIED**

### **Root Cause Identified:**
The issue was **over-aggressive content cleaning** combined with **improper content loading** in the template view. Our previous fixes to prevent duplication were removing too much content.

### **Fixed Components:**

#### **1. Template View (`resources/views/admin/notification/edit.blade.php`)**
```php
<!-- Visual Editor Panel -->
<div contenteditable="true" id="visual-editor-content" class="visual-editor-content">
    {{-- Content loaded by JavaScript from textarea to prevent Windows Server duplication --}}
</div>

<!-- HTML Editor Panel -->
<textarea id="html-editor-textarea" class="html-editor-textarea">{{ $template->email_body }}</textarea>
```
**Fix:** Content only loaded in textarea, visual editor populated by JavaScript to prevent duplication.

#### **2. JavaScript Content Initialization (`assets/admin/js/simple-email-editor.js`)**
```javascript
function initializeEditorContent() {
    // CRITICAL: Only use content from textarea (single source of truth)
    const initialContent = htmlTextarea.value || '';
    
    // WINDOWS SERVER FIX: Minimal cleaning to preserve content
    let cleanedContent = initialContent;
    
    if (cleanedContent) {
        // Only apply essential Windows Server fixes
        cleanedContent = cleanedContent.replace(/^\uFEFF/, ''); // Remove BOM
        cleanedContent = cleanedContent.replace(/\r\n/g, '\n').replace(/\r/g, '\n'); // Normalize line endings
        cleanedContent = cleanedContent.replace(/\0/g, ''); // Remove null characters
        
        // CRITICAL: Only remove exact consecutive duplicate lines
        const lines = cleanedContent.split('\n');
        const uniqueLines = [];
        let previousLine = '';
        
        for (let line of lines) {
            if (line !== previousLine || line.trim() === '') {
                uniqueLines.push(line);
            }
            previousLine = line;
        }
        
        cleanedContent = uniqueLines.join('\n');
    }
    
    // Apply basic HTML cleaning only (preserve content structure)
    cleanedContent = cleanHtmlContent(cleanedContent);
    
    // Set content to both editors
    visualContent.innerHTML = cleanedContent;
    htmlTextarea.value = cleanedContent;
}
```
**Fix:** Much less aggressive content cleaning that preserves email content while preventing duplication.

#### **3. Content Synchronization (`assets/admin/js/simple-email-editor.js`)**
```javascript
function syncEditorContent() {
    // Get content from active editor
    if (editorMode === 'html' && htmlEditor) {
        content = htmlEditor.value;
    } else if (visualEditor) {
        content = visualEditor.innerHTML;
    }
    
    // WINDOWS SERVER FIX: Minimal cleaning to preserve content
    if (content) {
        content = content.replace(/^\uFEFF/, ''); // Remove BOM
        content = content.replace(/\r\n/g, '\n').replace(/\r/g, '\n'); // Normalize line endings
        content = content.replace(/\0/g, ''); // Remove null characters
        content = content.trim(); // Trim only leading/trailing whitespace
        
        // CRITICAL: Only remove exact consecutive duplicate lines
        const lines = content.split('\n');
        const uniqueLines = [];
        let previousLine = '';
        
        for (let line of lines) {
            if (line !== previousLine || line.trim() === '') {
                uniqueLines.push(line);
            }
            previousLine = line;
        }
        
        content = uniqueLines.join('\n');
    }
    
    // Set content to form fields
    if (hiddenField) hiddenField.value = content;
    if (emailBodyField) emailBodyField.value = content;
}
```
**Fix:** Preserves content structure while preventing exact line duplication only.

#### **4. Controller Processing (`app/Http/Controllers/Admin/NotificationController.php`)**
```php
// SIMPLIFIED EMAIL BODY PROCESSING - PREVENT CORRUPTION
$emailBody = $request->input('email_body_final') ?: $request->input('email_body');

// CRITICAL: Minimal processing to prevent content loss on Windows Server
if (!empty($emailBody)) {
    // Only apply essential Windows Server fixes
    $emailBody = preg_replace('/^\xEF\xBB\xBF/', '', $emailBody); // Remove BOM
    $emailBody = str_replace(["\r\n", "\r"], "\n", $emailBody); // Normalize line endings
    $emailBody = str_replace("\0", '', $emailBody); // Remove null bytes
}

// CRITICAL: Use content as-is from editor - NO COMPLEX PROCESSING
```
**Fix:** Minimal server-side processing to preserve content integrity.

## 🧪 **TESTING INSTRUCTIONS**

### **Step 1: Deploy Updated Files**
Upload these files to your Windows Server 2022/Plesk:
1. `resources/views/admin/notification/edit.blade.php`
2. `assets/admin/js/simple-email-editor.js`
3. `app/Http/Controllers/Admin/NotificationController.php`

### **Step 2: Clear Caches**
```bash
# Clear Laravel caches
php artisan cache:clear
php artisan view:clear
php artisan config:clear

# Clear browser cache (Ctrl+F5 or Ctrl+Shift+R)
```

### **Step 3: Test Template Editor**
1. Go to: `https://mbf.mybrokerforex.com/admin/notification/template/edit/4`
2. **Expected Results:**
   - ✅ Editor loads correctly with proper content
   - ✅ Visual and HTML editors both show content
   - ✅ Content is not duplicated or corrupted
   - ✅ Editor interface functions properly

### **Step 4: Test Template Saving**
1. Make a small change in the editor
2. Click "Update Template"
3. **Expected Results:**
   - ✅ Content saves without duplication
   - ✅ No content loss (length should remain similar)
   - ✅ Template displays correctly after save

### **Step 5: Monitor Logs**
Check Laravel logs for debugging information:
```bash
tail -f storage/logs/laravel.log | grep "TEMPLATE UPDATE"
```
**Expected Log Output:**
- ✅ Content length should remain consistent (not dropping from 3788 to 1298)
- ✅ Content preview should show actual email content, not empty HTML
- ✅ Save operation should complete successfully

## 🎯 **EXPECTED RESULTS**

After applying these fixes:
- ✅ **Editor loads correctly** on Windows Server 2022/Plesk
- ✅ **Content displays properly** in both visual and HTML editors
- ✅ **No content duplication** when saving templates
- ✅ **No content loss** during processing
- ✅ **Consistent behavior** between XAMPP and live server
- ✅ **Proper template functionality** with all features working

## 📞 **IF ISSUES PERSIST**

1. **Check Browser Console** (F12) for JavaScript errors
2. **Review Laravel Logs** for detailed processing information
3. **Compare Content Lengths** before and after save operations
4. **Test with Different Templates** to ensure consistent behavior
5. **Verify File Uploads** completed successfully on the server

The key insight was that our previous fixes were **over-cleaning** the content, removing essential email body text while trying to prevent duplication. This new approach preserves content integrity while still preventing Windows Server specific issues.

---

## 🚨 **CRITICAL UPDATE: FIXES NOT WORKING ON LIVE SERVER**

**Status:** The above fixes are **NOT resolving** the issue on Windows Server 2022/Plesk. The editor remains broken despite implementing all changes.

### **🔍 COMPREHENSIVE DIAGNOSTIC APPROACH**

Since the standard fixes aren't working, we need to identify the **root cause** through systematic diagnosis:

#### **Step 1: Upload Diagnostic Files**
Upload these diagnostic files to your Windows Server:

1. **`windows_server_diagnostic.html`** - Client-side diagnostic (JavaScript, asset loading, browser errors)
2. **`windows_server_php_diagnostic.php`** - Server-side diagnostic (PHP, file system, Laravel environment)

#### **Step 2: Run Diagnostics**
1. Access: `https://mbf.mybrokerforex.com/windows_server_diagnostic.html`
2. Access: `https://mbf.mybrokerforex.com/windows_server_php_diagnostic.php`
3. Run all tests and capture results

#### **Step 3: Identify Specific Issues**
The diagnostics will reveal:
- ❌ **Asset Loading Failures** - JavaScript/CSS files not loading
- ❌ **File Permission Issues** - Files not readable/writable
- ❌ **PHP Extension Problems** - Missing required extensions
- ❌ **Laravel Environment Issues** - Configuration problems
- ❌ **JavaScript Errors** - Browser console errors preventing editor function
- ❌ **URL Rewriting Issues** - .htaccess not working on Windows/IIS

### **🎯 LIKELY ROOT CAUSES**

Based on the persistent issues, the problem is likely one of these:

#### **1. Asset Loading Failure**
- JavaScript files not loading due to path issues on Windows Server
- CSS files not accessible, causing layout problems
- IIS/Plesk URL rewriting differences from Apache

#### **2. JavaScript Execution Errors**
- Browser console errors preventing editor initialization
- Function conflicts or missing dependencies
- Windows Server serving different content than expected

#### **3. File System Issues**
- Incorrect file permissions on Windows Server
- Files not uploaded correctly or corrupted during transfer
- Path separator issues (Windows backslash vs Unix forward slash)

#### **4. Laravel Environment Problems**
- Cache not cleared properly on Windows Server
- Configuration differences between XAMPP and Plesk
- PHP version compatibility issues (8.1 vs 8.4)

### **🔧 ALTERNATIVE APPROACHES**

If diagnostics reveal specific issues, try these targeted fixes:

#### **Approach A: Force Asset Loading**
```html
<!-- Add to edit.blade.php head section -->
<script>
console.log('Testing asset loading...');
// Force load JavaScript with error handling
var script = document.createElement('script');
script.src = '/assets/admin/js/simple-email-editor.js?v=' + Date.now();
script.onerror = function() { console.error('Failed to load simple-email-editor.js'); };
script.onload = function() { console.log('simple-email-editor.js loaded successfully'); };
document.head.appendChild(script);
</script>
```

#### **Approach B: Inline JavaScript Fallback**
If external JavaScript files won't load, embed critical functions directly in the Blade template.

#### **Approach C: Server Configuration Fix**
Add Windows Server specific configuration:
```apache
# Add to .htaccess or web.config
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteRule ^assets/(.*)$ public/assets/$1 [L]
</IfModule>
```

#### **Approach D: PHP Path Fix**
```php
// Add to Laravel bootstrap or config
if (PHP_OS_FAMILY === 'Windows') {
    // Windows-specific path handling
    $assetPath = str_replace('/', DIRECTORY_SEPARATOR, $assetPath);
}
```

### **📞 IMMEDIATE ACTION REQUIRED**

1. **Upload diagnostic files** to Windows Server
2. **Run both diagnostics** and capture all output
3. **Check browser console** (F12) for JavaScript errors when accessing template editor
4. **Review Laravel logs** for specific error messages
5. **Test asset URLs directly** by accessing JavaScript/CSS files in browser

**The diagnostic results will reveal the exact issue preventing the editor from working on Windows Server 2022/Plesk.**
