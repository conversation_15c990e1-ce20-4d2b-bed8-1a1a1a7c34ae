<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Mt5User extends Model
{
    use HasFactory;

    protected $connection = 'mbf-dbmt5';
    protected $table = 'mt5_users';
    protected $primaryKey = 'Login';
    public $incrementing = false;
    public $timestamps = false;

    protected $fillable = [
        'Login',
        'Group',
        'CertSerialNumber',
        'Rights',
        'MQID',
        'Registration',
        'LastAccess',
        'LastPassChange',
        'LastIP',
        'Name',
        'Company',
        'Account',
        'Country',
        'Language',
        'ClientID',
        'City',
        'State',
        'ZipCode',
        'Address',
        'Phone',
        'Email',
        'ID',
        'Status',
        'Comment',
        'Color',
        'PhonePassword',
        'Leverage',
        'Agent',
        'TradeAccounts',
        'LimitPositions',
        'LimitOrders',
        'LeadCampaign',
        'LeadSource',
        'Balance',
        'Credit',
        'InterestRate',
        'CommissionDaily',
        'CommissionMonthly',
        'BalancePrevDay',
        'BalancePrevMonth',
        'EquityPrevDay',
        'EquityPrevMonth'
    ];

    protected $casts = [
        'Registration' => 'datetime',
        'LastAccess' => 'datetime',
        'LastPassChange' => 'datetime',
        'Balance' => 'decimal:2',
        'Credit' => 'decimal:2',
        'InterestRate' => 'decimal:2',
        'CommissionDaily' => 'decimal:2',
        'CommissionMonthly' => 'decimal:2',
        'BalancePrevDay' => 'decimal:2',
        'BalancePrevMonth' => 'decimal:2',
        'EquityPrevDay' => 'decimal:2',
        'EquityPrevMonth' => 'decimal:2',
        'Leverage' => 'integer',
        'Rights' => 'integer'
    ];

    /**
     * Get the local user account for this MT5 account
     */
    public function localAccount()
    {
        return $this->hasOne(Mt5Account::class, 'Account', 'Login');
    }

    /**
     * Get the local user for this MT5 account
     */
    public function localUser()
    {
        return $this->hasOneThrough(
            User::class,
            Mt5Account::class,
            'Account', // Foreign key on Mt5Account table
            'id', // Foreign key on User table
            'Login', // Local key on Mt5User table
            'User_Id' // Local key on Mt5Account table
        );
    }

    /**
     * Get deals for this MT5 account
     */
    public function deals()
    {
        return $this->hasMany(Mt5Deal::class, 'Login', 'Login');
    }

    /**
     * Check if this is an IB account
     */
    public function getIsIbAttribute()
    {
        return stripos($this->Group, 'Affiliates') !== false || 
               stripos($this->Group, 'IB') !== false;
    }

    /**
     * Get formatted balance
     */
    public function getFormattedBalanceAttribute()
    {
        return '$' . number_format($this->Balance, 2);
    }

    /**
     * Get formatted equity
     */
    public function getFormattedEquityAttribute()
    {
        return '$' . number_format($this->Equity, 2);
    }

    /**
     * Check if account is active
     */
    public function getIsActiveAttribute()
    {
        return $this->Rights > 0;
    }

    /**
     * Get account type based on group
     */
    public function getAccountTypeAttribute()
    {
        if ($this->is_ib) {
            return 'IB Account';
        }
        
        if (stripos($this->Group, 'demo') !== false) {
            return 'Demo Account';
        }
        
        return 'Live Account';
    }

    /**
     * Scope for IB accounts
     */
    public function scopeIb($query)
    {
        return $query->where(function($q) {
            $q->where('Group', 'like', '%Affiliates%')
              ->orWhere('Group', 'like', '%IB%');
        });
    }

    /**
     * Scope for active accounts
     */
    public function scopeActive($query)
    {
        return $query->where('Rights', '>', 0);
    }

    /**
     * Scope for live accounts
     */
    public function scopeLive($query)
    {
        return $query->where('Group', 'not like', '%demo%');
    }

    /**
     * Scope for demo accounts
     */
    public function scopeDemo($query)
    {
        return $query->where('Group', 'like', '%demo%');
    }
}
