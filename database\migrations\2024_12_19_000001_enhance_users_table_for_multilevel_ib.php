<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            // Check if columns don't exist before adding them
            if (!Schema::hasColumn('users', 'ib_status')) {
                $table->enum('ib_status', ['pending', 'approved', 'rejected'])->nullable()->after('partner');
            }
            
            if (!Schema::hasColumn('users', 'ib_type')) {
                $table->enum('ib_type', ['master', 'sub'])->nullable()->after('ib_status');
            }
            
            if (!Schema::hasColumn('users', 'ib_parent_id')) {
                $table->unsignedBigInteger('ib_parent_id')->nullable()->after('ib_type');
                $table->foreign('ib_parent_id')->references('id')->on('users')->onDelete('set null');
            }
            
            if (!Schema::hasColumn('users', 'ib_group_id')) {
                $table->unsignedBigInteger('ib_group_id')->nullable()->after('ib_parent_id');
            }
            
            if (!Schema::hasColumn('users', 'referral_code')) {
                $table->string('referral_code')->unique()->nullable()->after('ib_group_id');
            }
            
            if (!Schema::hasColumn('users', 'ib_commission_rate')) {
                $table->decimal('ib_commission_rate', 5, 2)->default(0)->after('referral_code')->comment('Custom commission rate for this IB');
            }
            
            if (!Schema::hasColumn('users', 'ib_max_levels')) {
                $table->integer('ib_max_levels')->default(0)->after('ib_commission_rate')->comment('Maximum levels this IB can have under them');
            }
            
            if (!Schema::hasColumn('users', 'ib_approved_at')) {
                $table->timestamp('ib_approved_at')->nullable()->after('ib_max_levels');
            }
            
            if (!Schema::hasColumn('users', 'ib_approved_by')) {
                $table->unsignedBigInteger('ib_approved_by')->nullable()->after('ib_approved_at');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop foreign keys first
            if (Schema::hasColumn('users', 'ib_parent_id')) {
                $table->dropForeign(['ib_parent_id']);
            }
            
            // Drop columns
            $columns = [
                'ib_status',
                'ib_type', 
                'ib_parent_id',
                'ib_group_id',
                'referral_code',
                'ib_commission_rate',
                'ib_max_levels',
                'ib_approved_at',
                'ib_approved_by'
            ];
            
            foreach ($columns as $column) {
                if (Schema::hasColumn('users', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
