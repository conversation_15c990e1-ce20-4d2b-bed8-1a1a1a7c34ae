<?php
/**
 * TEST MASS ASSIGNMENT FIX
 * This script tests that the fillable property fix works
 */

// Include Laravel bootstrap
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🔧 TESTING MASS ASSIGNMENT FIX\n";
echo "===============================\n\n";

try {
    // 1. Test model configuration
    echo "1️⃣ MODEL CONFIGURATION TEST\n";
    echo "============================\n";
    
    $model = new \App\Models\NotificationTemplate();
    
    $fillable = $model->getFillable();
    $guarded = $model->getGuarded();
    
    echo "✅ Fillable fields: " . implode(', ', $fillable) . "\n";
    echo "✅ Guarded fields: " . (empty($guarded) ? 'NONE' : implode(', ', $guarded)) . "\n";
    
    // Test specific fields
    $testFields = ['email_body', 'subj', 'email_status', 'sms_body'];
    echo "\n📊 Field Fillability Test:\n";
    foreach ($testFields as $field) {
        $isFillable = $model->isFillable($field);
        echo "   - {$field}: " . ($isFillable ? '✅ FILLABLE' : '❌ BLOCKED') . "\n";
    }
    
    echo "\n";
    
    // 2. Test mass assignment
    echo "2️⃣ MASS ASSIGNMENT TEST\n";
    echo "========================\n";
    
    $template = \App\Models\NotificationTemplate::find(1);
    if (!$template) {
        echo "❌ Template ID 1 not found\n";
        exit;
    }
    
    echo "✅ Template loaded: ID {$template->id}\n";
    
    // Store original values
    $originalSubject = $template->subj;
    $originalEmailBody = $template->email_body;
    
    // Test mass assignment
    $testData = [
        'subj' => $originalSubject . ' [MASS ASSIGNMENT TEST]',
        'email_body' => $originalEmailBody . "\n<!-- Mass assignment test at " . date('Y-m-d H:i:s') . " -->",
        'email_status' => 1,
        'sms_body' => 'Test SMS body'
    ];
    
    echo "\n📊 Testing mass assignment...\n";
    
    try {
        // This should now work with fillable fields
        $updateResult = $template->update($testData);
        echo "✅ Mass assignment result: " . ($updateResult ? 'SUCCESS' : 'FAILED') . "\n";
        
        // Verify changes
        $template->refresh();
        echo "✅ Subject updated: " . (str_contains($template->subj, '[MASS ASSIGNMENT TEST]') ? 'YES' : 'NO') . "\n";
        echo "✅ Email body updated: " . (str_contains($template->email_body, 'Mass assignment test') ? 'YES' : 'NO') . "\n";
        
        // Restore original content
        $template->update([
            'subj' => $originalSubject,
            'email_body' => $originalEmailBody
        ]);
        echo "✅ Original content restored\n";
        
    } catch (\Exception $e) {
        echo "❌ Mass assignment failed: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
    
    // 3. Test Visual Builder simulation
    echo "3️⃣ VISUAL BUILDER SIMULATION\n";
    echo "=============================\n";
    
    // Simulate the exact request data from your logs
    $simulatedRequest = [
        'subject' => 'Your Account has been Credited [VB TEST]',
        'email_status' => 'on',
        'email_body_final' => 'Balance Added Successfully\r\n\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            Balance Notification\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Balance Added Successfully\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Your account balance has been updated with a new deposit.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},We are pleased to inform you that {{amount}} {{currency}} has been added to your account.Amount: {{amount}} {{currency}}New Balance: {{new_balance}} {{currency}}Transaction ID: {{transaction_id}}Date: {{transaction_date}}The funds are now available in your account.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,\r\n                            MBFX Team\r\n                            \r\n                                If you have any questions, please contact our support team.\r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            \r\n                                Account Settings |\r\n                                Contact Support |\r\n                                Privacy Policy\r\n                            \r\n                            \r\n                                &copy; 2025 MBFX. All rights reserved.\r\n                            \r\n                            \r\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\r\n                                update your preferences.',
        'sms_body' => '{{amount}} {{wallet_currency}} credited in your account. Your Current Balance {{post_balance}} {{wallet_currency}}. Transaction: #{{trx}}. Admin note is {{remark}}'
    ];
    
    // Simulate controller logic
    $emailBody = $simulatedRequest['email_body_final'];
    
    echo "📊 Simulating Visual Builder update...\n";
    echo "   - Email body length: " . strlen($emailBody) . "\n";
    echo "   - Email body preview: " . substr($emailBody, 0, 100) . "...\n";
    
    try {
        $updateData = [
            'subj' => $simulatedRequest['subject'],
            'email_body' => $emailBody,
            'email_status' => isset($simulatedRequest['email_status']) ? 1 : 0,
            'sms_body' => $simulatedRequest['sms_body']
        ];
        
        $updateResult = $template->update($updateData);
        echo "✅ Visual Builder simulation result: " . ($updateResult ? 'SUCCESS' : 'FAILED') . "\n";
        
        // Verify changes
        $template->refresh();
        echo "✅ Subject contains test marker: " . (str_contains($template->subj, '[VB TEST]') ? 'YES' : 'NO') . "\n";
        echo "✅ Email body length matches: " . (strlen($template->email_body) === strlen($emailBody) ? 'YES' : 'NO') . "\n";
        
        // Restore original content
        $template->update([
            'subj' => $originalSubject,
            'email_body' => $originalEmailBody
        ]);
        echo "✅ Original content restored\n";
        
    } catch (\Exception $e) {
        echo "❌ Visual Builder simulation failed: " . $e->getMessage() . "\n";
    }
    
    echo "\n📋 TEST RESULTS\n";
    echo "===============\n";
    echo "If all tests show SUCCESS, the mass assignment fix is working!\n";
    echo "You can now edit templates through the Visual Builder interface.\n\n";
    
    echo "🎯 NEXT STEPS:\n";
    echo "==============\n";
    echo "1. Upload the fixed NotificationTemplate.php model\n";
    echo "2. Clear Laravel caches\n";
    echo "3. Test template editing in the admin interface\n";
    echo "4. Templates should now save correctly!\n";
    
} catch (\Exception $e) {
    echo "❌ Test Error: " . $e->getMessage() . "\n";
    echo "❌ File: " . $e->getFile() . "\n";
    echo "❌ Line: " . $e->getLine() . "\n";
}

echo "\n✅ MASS ASSIGNMENT FIX TEST COMPLETED\n";

?>
