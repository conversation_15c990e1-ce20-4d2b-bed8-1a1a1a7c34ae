<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript Fix Test</title>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
</head>
<body>
    <h1>🔧 JavaScript Fix Test</h1>
    
    <div class="test-container">
        <h2>Select2 Test</h2>
        <select class="select2-basic">
            <option value="1">Option 1</option>
            <option value="2">Option 2</option>
        </select>
        
        <h2>Form Test</h2>
        <form id="test-form">
            <input type="text" name="test_field" value="Test Value">
            <button type="submit" id="test-submit">Test Submit</button>
        </form>
        
        <h2>Console Output</h2>
        <div id="console-output" style="background: #f0f0f0; padding: 10px; height: 200px; overflow-y: auto;"></div>
    </div>

    <script>
    // Capture console output
    const originalLog = console.log;
    const originalError = console.error;
    const originalWarn = console.warn;
    const outputDiv = document.getElementById('console-output');
    
    function addToOutput(type, message) {
        const div = document.createElement('div');
        div.style.color = type === 'error' ? 'red' : type === 'warn' ? 'orange' : 'black';
        div.textContent = `[${type.toUpperCase()}] ${message}`;
        outputDiv.appendChild(div);
        outputDiv.scrollTop = outputDiv.scrollHeight;
    }
    
    console.log = function(...args) {
        originalLog.apply(console, args);
        addToOutput('log', args.join(' '));
    };
    
    console.error = function(...args) {
        originalError.apply(console, args);
        addToOutput('error', args.join(' '));
    };
    
    console.warn = function(...args) {
        originalWarn.apply(console, args);
        addToOutput('warn', args.join(' '));
    };
    
    // Test the fixed Select2 initialization code
    console.log('🔧 Testing JavaScript fix...');
    
    // FIXED Select2 initialization (same as in app.js)
    $(document).ready(function() {
        try {
            // Check if Select2 is loaded
            if (typeof $.fn.select2 !== 'undefined') {
                console.log('✅ Select2 library detected');
                
                // Only initialize if elements exist
                if ($('.select2-basic').length > 0) {
                    $('.select2-basic').select2();
                    console.log('✅ Select2 basic initialized');
                }
                if ($('.select2-multi-select').length > 0) {
                    $('.select2-multi-select').select2();
                    console.log('✅ Select2 multi-select initialized');
                }
                if ($('.select2-auto-tokenize').length > 0) {
                    $(".select2-auto-tokenize").select2({
                        tags: true,
                        tokenSeparators: [',']
                    });
                    console.log('✅ Select2 auto-tokenize initialized');
                }
                console.log('✅ Select2 initialized successfully');
            } else {
                console.warn('⚠️ Select2 library not loaded - skipping initialization');
            }
        } catch (error) {
            console.error('❌ Select2 initialization error:', error);
            // Don't let this error break other JavaScript
        }
        
        // Test that other JavaScript still works
        console.log('🧪 Testing other JavaScript functionality...');
        
        $('#test-submit').click(function(e) {
            e.preventDefault();
            console.log('✅ Form submission handler works');
            console.log('✅ Form data:', $('#test-form').serialize());
        });
        
        console.log('✅ All JavaScript initialization complete');
    });
    
    // Test error handling
    setTimeout(function() {
        console.log('🔍 Testing error scenarios...');
        
        // Test what happens if Select2 wasn't loaded
        try {
            // This would normally cause an error
            if (typeof $.fn.select2 === 'undefined') {
                console.log('✅ Select2 not loaded scenario handled correctly');
            }
        } catch (error) {
            console.error('❌ Error handling failed:', error);
        }
        
        console.log('🎯 JavaScript fix test completed');
    }, 1000);
    </script>
</body>
</html>
