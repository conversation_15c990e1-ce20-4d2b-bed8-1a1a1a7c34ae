<?php
/**
 * MT5 API Diagnostic Script
 * Place this file in your project root and access via browser
 * URL: https://yourdomain.com/debug_mt5_api.php
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>MT5 API Diagnostic Report</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .section{margin:20px 0;padding:15px;border:1px solid #ddd;} .success{color:green;} .error{color:red;} .warning{color:orange;} pre{background:#f5f5f5;padding:10px;}</style>";

// Load environment variables
if (file_exists('.env')) {
    $envContent = file_get_contents('.env');
    $envLines = explode("\n", $envContent);
    $envVars = [];
    
    foreach ($envLines as $line) {
        if (strpos($line, '=') !== false && !str_starts_with(trim($line), '#')) {
            list($key, $value) = explode('=', $line, 2);
            $envVars[trim($key)] = trim($value);
        }
    }
}

// 1. Python Environment Check
echo "<div class='section'>";
echo "<h2>1. Python Environment</h2>";

$pythonExe = $envVars['PYTHON_EXE'] ?? 'python';
$pythonScript = $envVars['PYTHON_SCRIPT'] ?? '';

echo "<strong>Python Executable:</strong> {$pythonExe}<br>";
echo "<strong>Python Script Path:</strong> {$pythonScript}<br>";

// Test Python version
$command = escapeshellcmd($pythonExe) . " --version 2>&1";
$output = shell_exec($command);
if ($output) {
    echo "<span class='success'>✓ Python Version: " . trim($output) . "</span><br>";
} else {
    echo "<span class='error'>✗ Python not accessible</span><br>";
}

// Check if script exists
if (file_exists($pythonScript)) {
    echo "<span class='success'>✓ MT5 Python script exists</span><br>";
    echo "<strong>Script Size:</strong> " . filesize($pythonScript) . " bytes<br>";
    echo "<strong>Script Permissions:</strong> " . substr(sprintf('%o', fileperms($pythonScript)), -4) . "<br>";
} else {
    echo "<span class='error'>✗ MT5 Python script not found at: {$pythonScript}</span><br>";
}
echo "</div>";

// 2. MT5 Configuration Test
echo "<div class='section'>";
echo "<h2>2. MT5 Configuration</h2>";

$mt5Config = [
    'server_ip' => '**************',
    'manager' => '10007',
    'manager_pswd' => 'TfTe*wA1'
];

foreach ($mt5Config as $key => $value) {
    if ($key === 'manager_pswd') {
        echo "<strong>{$key}:</strong> " . str_repeat('*', strlen($value)) . "<br>";
    } else {
        echo "<strong>{$key}:</strong> {$value}<br>";
    }
}

// Test network connectivity to MT5 server
$serverIp = $mt5Config['server_ip'];
$serverPort = 443; // Common MT5 port

echo "<br><strong>Testing connectivity to MT5 server...</strong><br>";
$connection = @fsockopen($serverIp, $serverPort, $errno, $errstr, 10);
if ($connection) {
    echo "<span class='success'>✓ Can connect to MT5 server {$serverIp}:{$serverPort}</span><br>";
    fclose($connection);
} else {
    echo "<span class='error'>✗ Cannot connect to MT5 server {$serverIp}:{$serverPort} - Error: {$errstr} ({$errno})</span><br>";
}
echo "</div>";

// 3. Python Script Test
echo "<div class='section'>";
echo "<h2>3. Python Script Test</h2>";

if (file_exists($pythonScript)) {
    // Test basic script execution
    $testCommand = escapeshellcmd($pythonExe) . " " . escapeshellarg($pythonScript) . " --help 2>&1";
    echo "<strong>Test Command:</strong> {$testCommand}<br>";
    
    $output = shell_exec($testCommand);
    if ($output) {
        echo "<span class='success'>✓ Python script executed</span><br>";
        echo "<strong>Output:</strong><pre>" . htmlspecialchars($output) . "</pre>";
    } else {
        echo "<span class='error'>✗ Python script failed to execute</span><br>";
    }
    
    // Test specific MT5 command
    echo "<br><strong>Testing MT5 connection...</strong><br>";
    $mt5TestCommand = escapeshellcmd($pythonExe) . " " . escapeshellarg($pythonScript) . " test_connection 2>&1";
    echo "<strong>MT5 Test Command:</strong> {$mt5TestCommand}<br>";
    
    $mt5Output = shell_exec($mt5TestCommand);
    if ($mt5Output) {
        echo "<strong>MT5 Test Output:</strong><pre>" . htmlspecialchars($mt5Output) . "</pre>";
        
        if (strpos($mt5Output, 'success') !== false) {
            echo "<span class='success'>✓ MT5 connection test successful</span><br>";
        } else {
            echo "<span class='error'>✗ MT5 connection test failed</span><br>";
        }
    } else {
        echo "<span class='error'>✗ No output from MT5 test command</span><br>";
    }
}
echo "</div>";

// 4. PHP exec() and shell_exec() Test
echo "<div class='section'>";
echo "<h2>4. PHP Execution Functions Test</h2>";

// Test if exec functions are enabled
$disabledFunctions = explode(',', ini_get('disable_functions'));
$execFunctions = ['exec', 'shell_exec', 'system', 'passthru', 'proc_open'];

foreach ($execFunctions as $func) {
    if (in_array($func, $disabledFunctions)) {
        echo "<span class='error'>✗ {$func}() is disabled</span><br>";
    } else {
        echo "<span class='success'>✓ {$func}() is enabled</span><br>";
    }
}

// Test basic command execution
echo "<br><strong>Testing basic command execution...</strong><br>";
$testCmd = 'echo "Hello from command line"';
$result = shell_exec($testCmd);
if ($result) {
    echo "<span class='success'>✓ shell_exec() works: " . trim($result) . "</span><br>";
} else {
    echo "<span class='error'>✗ shell_exec() not working</span><br>";
}
echo "</div>";

// 5. File Permissions and Paths
echo "<div class='section'>";
echo "<h2>5. File Permissions and Paths</h2>";

$paths = [
    'Python Script' => $pythonScript,
    'Storage Directory' => 'storage',
    'Logs Directory' => 'storage/logs',
    'Current Directory' => getcwd()
];

foreach ($paths as $name => $path) {
    if (file_exists($path)) {
        $perms = substr(sprintf('%o', fileperms($path)), -4);
        $readable = is_readable($path) ? 'Yes' : 'No';
        $writable = is_writable($path) ? 'Yes' : 'No';
        $executable = is_executable($path) ? 'Yes' : 'No';
        
        echo "<strong>{$name}:</strong> {$path}<br>";
        echo "&nbsp;&nbsp;Permissions: {$perms} | Readable: {$readable} | Writable: {$writable} | Executable: {$executable}<br>";
    } else {
        echo "<strong>{$name}:</strong> <span class='error'>Not found - {$path}</span><br>";
    }
}
echo "</div>";

// 6. Environment Variables
echo "<div class='section'>";
echo "<h2>6. Environment Variables</h2>";

$envVarsToCheck = ['PYTHON_EXE', 'PYTHON_SCRIPT', 'PATH'];
foreach ($envVarsToCheck as $var) {
    $value = $envVars[$var] ?? getenv($var);
    if ($value) {
        echo "<strong>{$var}:</strong> {$value}<br>";
    } else {
        echo "<strong>{$var}:</strong> <span class='error'>Not set</span><br>";
    }
}

// Show PATH for debugging
echo "<br><strong>System PATH:</strong><br>";
$path = getenv('PATH');
if ($path) {
    $pathDirs = explode(PATH_SEPARATOR, $path);
    foreach ($pathDirs as $dir) {
        echo "&nbsp;&nbsp;{$dir}<br>";
    }
}
echo "</div>";

// 7. Laravel Specific Tests
echo "<div class='section'>";
echo "<h2>7. Laravel Route Tests</h2>";

echo "<p>Test these routes manually (login required):</p>";
echo "<ul>";
echo "<li><a href='/user/accounts/create' target='_blank'>Account Creation</a></li>";
echo "<li><a href='/user/accounts' target='_blank'>User Accounts</a></li>";
echo "</ul>";

echo "<p><strong>CSRF Token Test:</strong></p>";
echo "<input type='hidden' name='_token' value='" . (function_exists('csrf_token') ? csrf_token() : 'N/A') . "'>";
echo "<br>CSRF Token: " . (function_exists('csrf_token') ? csrf_token() : 'N/A');
echo "</div>";

// 8. Recommendations
echo "<div class='section'>";
echo "<h2>8. Troubleshooting Steps</h2>";
echo "<ol>";
echo "<li><strong>If Python is not accessible:</strong>";
echo "<ul><li>Check if Python is installed on the server</li>";
echo "<li>Verify the PYTHON_EXE path in .env</li>";
echo "<li>Check if the web server user has permission to execute Python</li></ul></li>";

echo "<li><strong>If MT5 script fails:</strong>";
echo "<ul><li>Verify the PYTHON_SCRIPT path in .env</li>";
echo "<li>Check script file permissions (should be executable)</li>";
echo "<li>Test the script manually via SSH/RDP</li></ul></li>";

echo "<li><strong>If exec functions are disabled:</strong>";
echo "<ul><li>Contact your hosting provider to enable exec functions</li>";
echo "<li>Or use alternative methods like proc_open()</li></ul></li>";

echo "<li><strong>For Plesk-specific issues:</strong>";
echo "<ul><li>Check PHP settings in Plesk panel</li>";
echo "<li>Verify file permissions in Plesk File Manager</li>";
echo "<li>Check error logs in Plesk Logs section</li></ul></li>";
echo "</ol>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>9. Quick Fixes to Try</h2>";
echo "<p><strong>1. Update .env file with correct paths:</strong></p>";
echo "<pre>";
echo "PYTHON_EXE=/usr/bin/python3\n";
echo "PYTHON_SCRIPT=/path/to/your/project/python/mt5manager.py\n";
echo "</pre>";

echo "<p><strong>2. Set correct file permissions:</strong></p>";
echo "<pre>";
echo "chmod +x python/mt5manager.py\n";
echo "chmod 755 storage -R\n";
echo "</pre>";

echo "<p><strong>3. Clear Laravel caches:</strong></p>";
echo "<pre>";
echo "php artisan cache:clear\n";
echo "php artisan config:clear\n";
echo "php artisan route:clear\n";
echo "</pre>";
echo "</div>";
?>
