@extends($activeTemplate . 'layouts.master')
@section('content')

<!-- Include OrgChart.js CSS -->
 @push('style')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/orgchart@4.0.1/dist/css/jquery.orgchart.min.css" />
@endpush


<div class="container-fluid">
    <div class="row justify-content-center mt-4">
        <div class="col-xl-12">
            <div class="card">
                
                <div class="card-body">
                    <!-- Network Statistics - Exact Admin Style Match -->
                     <div class="row mb-4">
                <div class="col-md-3">
                    <div class="widget-two style--two box--shadow2 b-radius--5 bg--19">
                        <div class="widget-two__icon b-radius--5">
                            <i class="las la-user-tie"></i>
                        </div>
                        <div class="widget-two__content">
                            <h3 class="text-white">
                                @if($user->isIb())
                                    {{ ucfirst($user->ib_type ?? 'Standard') }} IB
                                @else
                                    @lang('Client')
                                @endif
                            </h3>
                            <p class="text-white">@lang('Status')</p>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="widget-two style--two box--shadow2 b-radius--5 bg--primary">
                        <div class="widget-two__icon b-radius--5">
                            <i class="las la-users"></i>
                        </div>
                        <div class="widget-two__content">
                            <h3 class="text-white">{{ $networkData['direct_referrals'] ?? 0 }}</h3>
                            <p class="text-white">@lang('Direct Referrals')</p>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="widget-two style--two box--shadow2 b-radius--5 bg--1">
                        <div class="widget-two__icon b-radius--5">
                            <i class="las la-layer-group"></i>
                        </div>
                        <div class="widget-two__content">
                            <h3 class="text-white">{{ $networkData['total_referrals'] ?? 0 }}</h3>
                            <p class="text-white">@lang('Total Network')</p>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="widget-two style--two box--shadow2 b-radius--5 bg--17">
                        <div class="widget-two__icon b-radius--5 bg--dark">
                            <i class="las la-dollar-sign"></i>
                        </div>
                        <div class="widget-two__content">
                            <h3 class="text-white">
                                @if($user->isIb())
                                    {{ showAmount($mt5CommissionData['total_commission'] ?? 0) }}
                                @else
                                    {{ showAmount(0) }}
                                @endif
                            </h3>
                            <p class="text-white">@lang('Total Commissions')</p>
                        </div>
                    </div>
                </div>
            </div>

                    <!-- FIXED: Add view toggle buttons like admin interface -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3 class="transection__title skeleton">@lang('Professional Network Tree')</h3>

                        {{-- FIXED: View Toggle Buttons with Theme Colors --}}
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn--dark active" id="hierarchyViewBtn" onclick="toggleView('hierarchy')">
                                <i class="las la-sitemap"></i> @lang('Network Tree')
                            </button>
                            <button type="button" class="btn btn-sm btn-outline--dark" id="tableViewBtn" onclick="toggleView('table')">
                                <i class="las la-table"></i> @lang('Table View')
                            </button>
                            
                        </div>
                    </div>

                    <!-- ENHANCED Navigation Tabs - Admin Quality Design -->
                    <div class="card">
                        <div class="card-header">
                            <ul class="nav nav-tabs card-header-tabs" id="networkTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="tree-tab" data-bs-toggle="tab" data-bs-target="#tree" type="button" role="tab">
                                        <i class="las la-sitemap text-dark"></i> @lang('Network Tree')
                                        <span class="badge bg-dark ms-2">{{ $networkData['total_referrals'] ?? 0 }}</span>
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="referrals-tab" data-bs-toggle="tab" data-bs-target="#referrals" type="button" role="tab">
                                        <i class="las la-users text-danger"></i> @lang('Direct Referrals')
                                        <span class="badge bg-danger ms-2">{{ $networkData['direct_referrals'] ?? 0 }}</span>
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="commissions-tab" data-bs-toggle="tab" data-bs-target="#commissions" type="button" role="tab">
                                        <i class="las la-dollar-sign text-secondary"></i> @lang('Commissions')
                                        <span class="badge bg-secondary ms-2">{{ $mt5CommissionData['commission_count'] ?? 0 }}</span>
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="activity-tab" data-bs-toggle="tab" data-bs-target="#activity" type="button" role="tab">
                                        <i class="las la-chart-line text-dark"></i> @lang('Recent Activity')
                                        <span class="badge bg-dark ms-2">{{ count($mt5CommissionData['recent_commissions'] ?? []) }}</span>
                                    </button>
                                </li>
                                @if($user->isIb())
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="calculator-tab" data-bs-toggle="tab" data-bs-target="#calculator" type="button" role="tab">
                                        <i class="las la-calculator text-danger"></i> @lang('Commission Calculator')
                                        <span class="badge bg-danger ms-2">@lang('Tool')</span>
                                    </button>
                                </li>
                                @endif
                            </ul>
                        </div>

                        <!-- ENHANCED Tab Content - Admin Quality Design -->
                        <div class="tab-content card-body" id="networkTabContent">
                            <!-- Network Tree Tab - Enhanced Design -->
                            <div class="tab-pane fade show active" id="tree" role="tabpanel">
                                <div class="row">
                                    <div class="col-12">
                                        <div class="card border-0 shadow-sm">
                                            
                                            
                                                <!-- Enhanced Network Container -->
                                                <div id="userNetworkContainer" class="network-container">
                                                    <div class="text-center py-5">
                                                        <div class="loading-state">
                                                            <div class="spinner-border text-dark mb-3" role="status" style="width: 3rem; height: 3rem;">
                                                                <span class="visually-hidden">Loading...</span>
                                                            </div>
                                                            <h4 class="text-muted">@lang('Loading Network Tree...')</h4>
                                                            <p class="text-muted">@lang('Building your partnership network visualization')</p>
                                                            <div class="mt-3">
                                                                <small class="text-muted">@lang('If this persists, check browser console for errors')</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Direct Referrals Tab - FIXED: System Pagination & Clean Header -->
                            <div class="tab-pane fade" id="referrals" role="tabpanel">
                                <div class="mt-3">
                                    <div class="table-responsive">
                                        <table class="table table--light">
                                            <thead>
                                                <tr>
                                                    <th>@lang('User')</th>
                                                    <th>@lang('MT5 Account')</th>
                                                    <th>@lang('Balance')</th>
                                                    <th>@lang('IB Status')</th>
                                                    <th>@lang('Joined Date')</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @if(isset($networkData['direct_referrals_data']) && count($networkData['direct_referrals_data']) > 0)
                                                    @foreach($networkData['direct_referrals_data'] as $referral)
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <div class="avatar-sm me-3" style="width: 32px; height: 32px; background: {{ $referral->ib_status == 1 ? '#dc3545' : '#6c757d' }}; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold;">
                                                                    {{ substr($referral->firstname, 0, 1) }}{{ substr($referral->lastname, 0, 1) }}
                                                                </div>
                                                                <div>
                                                                    <strong>{{ $referral->firstname }} {{ $referral->lastname }}</strong>
                                                                    <br><small class="text-muted">{{ $referral->email }}</small>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-secondary">{{ $referral->mt5_login ?: 'N/A' }}</span>
                                                        </td>
                                                        <td>
                                                            <strong class="text-dark">${{ number_format($referral->mt5_balance ?? 0, 2) }}</strong>
                                                        </td>
                                                        <td>
                                                            @if($referral->ib_status == 1)
                                                                <span class="badge bg-danger">{{ ucfirst($referral->ib_type ?? 'Sub') }} IB</span>
                                                            @else
                                                                <span class="badge bg-secondary">Client</span>
                                                            @endif
                                                        </td>
                                                        <td>
                                                            <small class="text-muted">{{ showDateTime($referral->created_at) }}</small>
                                                        </td>
                                                    </tr>
                                                    @endforeach
                                                @else
                                                    <tr>
                                                        <td colspan="5" class="text-center py-4">
                                                            <i class="las la-users text-muted" style="font-size: 2rem;"></i>
                                                            <br>@lang('No direct referrals found')
                                                        </td>
                                                    </tr>
                                                @endif
                                            </tbody>
                                        </table>
                                    </div>

                                    {{-- FIXED: Use Laravel's built-in pagination system --}}
                                    @if(isset($networkData['direct_referrals_data']) && $networkData['direct_referrals_data']->hasPages())
                                        <div class="d-flex justify-content-center mt-3">
                                            {{ $networkData['direct_referrals_data']->links() }}
                                        </div>
                                    @endif
                                </div>
                            </div>

                        <!-- Commissions Tab - FIXED DATA LOADING -->
                        <div class="tab-pane fade" id="commissions" role="tabpanel">
                            <div class="mt-3">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6>@lang('Commission Summary')</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="commission-stats">
                                                    {{-- TASK 1: Real-time commission display with IDs for updates --}}
                                                    <div class="d-flex justify-content-between mb-2">
                                                        <span>@lang('Total Earned'):</span>
                                                        <strong class="text-dark" id="user-total-commission">{{ showAmount($mt5CommissionData['total_commission'] ?? 0) }}</strong>
                                                    </div>
                                                    <div class="d-flex justify-content-between mb-2">
                                                        <span>@lang('Paid Commissions'):</span>
                                                        <strong class="text-success" id="user-paid-commission">{{ showAmount($mt5CommissionData['paid_commission'] ?? 0) }}</strong>
                                                    </div>
                                                    <div class="d-flex justify-content-between mb-2">
                                                        <span>@lang('Pending Commissions'):</span>
                                                        <strong class="text-warning" id="user-pending-commission">{{ showAmount($mt5CommissionData['pending_commission'] ?? 0) }}</strong>
                                                    </div>
                                                    <div class="d-flex justify-content-between mb-2">
                                                        <span>@lang('Current MT5 Balance'):</span>
                                                        <strong class="text-primary" id="user-mt5-balance">${{ number_format($user->mt5_balance ?? 0, 2) }}</strong>
                                                    </div>
                                                    <div class="d-flex justify-content-between mb-2">
                                                        <span>@lang('Commission Count'):</span>
                                                        <strong id="user-commission-count">{{ $mt5CommissionData['commission_count'] ?? 0 }}</strong>
                                                    </div>
                                                    <div class="d-flex justify-content-between mb-2">
                                                        <span>@lang('Period'):</span>
                                                        <strong>{{ $mt5CommissionData['period_days'] ?? 365 }} days</strong>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6>@lang('Recent Commissions')</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="table-responsive">
                                                    <table class="table table-sm">
                                                        <thead>
                                                            <tr>
                                                                <th>@lang('Deal')</th>
                                                                <th>@lang('Date')</th>
                                                                <th>@lang('Amount')</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            @if(isset($mt5CommissionData['recent_commissions']) && count($mt5CommissionData['recent_commissions']) > 0)
                                                                @foreach($mt5CommissionData['recent_commissions'] as $commission)
                                                                <tr>
                                                                    <td>{{ $commission->Deal ?? 'N/A' }}</td>
                                                                    <td>{{ isset($commission->Time) ? \Carbon\Carbon::parse($commission->Time)->format('M d') : 'N/A' }}</td>
                                                                    <td class="text-dark">${{ number_format($commission->Commission ?? 0, 2) }}</td>
                                                                </tr>
                                                                @endforeach
                                                            @else
                                                                <tr>
                                                                    <td colspan="3" class="text-center text-muted">@lang('No recent commissions')</td>
                                                                </tr>
                                                            @endif
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Activity Tab - FIXED DATA LOADING -->
                        <div class="tab-pane fade" id="activity" role="tabpanel">
                            <div class="mt-3">
                                <div class="table-responsive">
                                    <table class="table table--light">
                                        <thead>
                                            <tr>
                                                <th>@lang('Date')</th>
                                                <th>@lang('Type')</th>
                                                <th>@lang('Description')</th>
                                                <th>@lang('Amount')</th>
                                            </tr>
                                        </thead>
                                        <tbody id="activityTableBody">
                                            @if(isset($mt5CommissionData['recent_commissions']) && count($mt5CommissionData['recent_commissions']) > 0)
                                                @foreach($mt5CommissionData['recent_commissions'] as $commission)
                                                <tr>
                                                    <td>{{ isset($commission->Time) ? \Carbon\Carbon::parse($commission->Time)->format('M d, Y H:i') : 'N/A' }}</td>
                                                    <td><span class="badge bg-danger">Commission</span></td>
                                                    <td>{{ $commission->Comment ?: 'MT5 Commission Payment' }}</td>
                                                    <td class="text-dark">+${{ number_format($commission->Commission ?? 0, 2) }}</td>
                                                </tr>
                                                @endforeach
                                            @else
                                                <tr>
                                                    <td colspan="4" class="text-center text-muted py-4">
                                                        <i class="las la-chart-line" style="font-size: 2rem;"></i>
                                                        <br>@lang('No recent activity found')
                                                    </td>
                                                </tr>
                                            @endif
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- TASK 4: Commission Calculator Tab -->
                        @if($user->isIb())
                        <div class="tab-pane fade" id="calculator" role="tabpanel">
                            <div class="mt-3">
                                <div class="row">
                                    <div class="col-lg-8">
                                        <div class="card">
                                            <div class="card-header">
                                                <h5 class="card-title mb-0">
                                                    <i class="las la-calculator me-2"></i>@lang('Commission Calculator')
                                                </h5>
                                                <small class="text-muted">@lang('Calculate potential commissions based on trading volume')</small>
                                            </div>
                                            <div class="card-body">
                                                <form id="commission-calculator-form">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label class="form-label">@lang('Trading Volume (Lots)')</label>
                                                                <input type="number" class="form-control" id="trading-volume"
                                                                       placeholder="100" min="0" step="0.01" value="100">
                                                                <small class="text-muted">@lang('Enter the expected trading volume in lots')</small>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label class="form-label">@lang('Symbol Group')</label>
                                                                <select class="form-control" id="symbol-group">
                                                                    <option value="forex" data-rate="7">@lang('Forex (Major Pairs)') - $7/lot</option>
                                                                    <option value="forex-minor" data-rate="5">@lang('Forex (Minor Pairs)') - $5/lot</option>
                                                                    <option value="metals" data-rate="10">@lang('Metals (Gold/Silver)') - $10/lot</option>
                                                                    <option value="indices" data-rate="8">@lang('Indices') - $8/lot</option>
                                                                    <option value="crypto" data-rate="15">@lang('Crypto') - $15/lot</option>
                                                                </select>
                                                                <small class="text-muted">@lang('Select the trading instrument type')</small>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label class="form-label">@lang('Time Period')</label>
                                                                <select class="form-control" id="time-period">
                                                                    <option value="daily">@lang('Daily')</option>
                                                                    <option value="weekly" selected>@lang('Weekly')</option>
                                                                    <option value="monthly">@lang('Monthly')</option>
                                                                    <option value="yearly">@lang('Yearly')</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label class="form-label">@lang('Network Levels')</label>
                                                                <select class="form-control" id="network-levels">
                                                                    <option value="1">@lang('Level 1 Only (Direct Referrals)')</option>
                                                                    <option value="2" selected>@lang('Level 1 + 2 (Sub-IBs)')</option>
                                                                    <option value="3">@lang('All Levels (1, 2, 3)')</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="text-center mt-3">
                                                        <button type="button" class="btn btn-sm btn--dark active" id="calculate-btn">
                                                            <i class="las la-calculator me-2"></i>@lang('Calculate Commissions')
                                                        </button>
                                                        <button type="button" class="btn btn--base outline btn--md trade-btn" id="reset-calculator">
                                                            <i class="las la-redo me-2"></i>@lang('Reset')
                                                        </button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-lg-4">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="card-title mb-0">@lang('Commission Rates')</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="commission-rates">
                                                    <div class="d-flex justify-content-between mb-2">
                                                        <span class="badge bg-danger">@lang('Master IB'):</span>
                                                        <strong class="text-danger">50%</strong>
                                                    </div>
                                                    <div class="d-flex justify-content-between mb-2">
                                                        <span class="badge bg-dark">@lang('Sub IB'):</span>
                                                        <strong class="text-dark">30%</strong>
                                                    </div>
                                                    <div class="d-flex justify-content-between mb-2">
                                                        <span class="badge bg-secondary">@lang('Level 3'):</span>
                                                        <strong class="text-secondary">20%</strong>
                                                    </div>
                                                </div>
                                                <hr>
                                                <small class="text-muted">
                                                    @lang('These are the standard commission rates for each level in the partnership network.')
                                                </small>
                                            </div>
                                        </div>

                                        <div class="card mt-3">
                                            <div class="card-header">
                                                <h6 class="card-title mb-0">@lang('Your Network Stats')</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="network-stats">
                                                    <div class="d-flex justify-content-between mb-2">
                                                        <span>@lang('Your Level'):</span>
                                                        <strong class="text-danger">{{ ucfirst($user->ib_type ?? 'Master') }} IB</strong>
                                                    </div>
                                                    <div class="d-flex justify-content-between mb-2">
                                                        <span>@lang('Direct Referrals'):</span>
                                                        <strong>{{ $networkData['direct_referrals'] ?? 0 }}</strong>
                                                    </div>
                                                    <div class="d-flex justify-content-between mb-2">
                                                        <span>@lang('Total Network'):</span>
                                                        <strong>{{ $networkData['total_referrals'] ?? 0 }}</strong>
                                                    </div>
                                                    <div class="d-flex justify-content-between mb-2">
                                                        <span>@lang('Commission Rate'):</span>
                                                        <strong>{{ $user->ib_commission_rate ?? 50 }}%</strong>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Calculation Results -->
                                <div class="row mt-4" id="calculation-results" style="display: none;">
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header">
                                                <h5 class="card-title mb-0">
                                                    <i class="las la-chart-bar me-2"></i>@lang('Commission Calculation Results')
                                                </h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-3">
                                                        <div class="text-center p-3 border rounded">
                                                            <h4 class="text-danger mb-1" id="level1-commission">$0.00</h4>
                                                            <small class="text-muted">@lang('Level 1 Commission')</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="text-center p-3 border rounded">
                                                            <h4 class="text-dark mb-1" id="level2-commission">$0.00</h4>
                                                            <small class="text-muted">@lang('Level 2 Commission')</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="text-center p-3 border rounded">
                                                            <h4 class="text-secondary mb-1" id="level3-commission">$0.00</h4>
                                                            <small class="text-muted">@lang('Level 3 Commission')</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="text-center p-3 border rounded bg-light">
                                                            <h4 class="text-danger mb-1" id="total-commission">$0.00</h4>
                                                            <small class="text-muted">@lang('Total Commission')</small>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="mt-4">
                                                    <h6>@lang('Calculation Breakdown'):</h6>
                                                    <div id="calculation-breakdown" class="text-muted">
                                                        <!-- Breakdown will be populated by JavaScript -->
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



@push('style')
<style>
/* ENHANCED TAB DESIGN - ADMIN QUALITY STYLING */


/* Enhanced Tab Navigation */
.nav-tabs .nav-link {
    border: none;
    border-radius: 0.5rem 0.5rem 0 0;
    padding: 1rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.1);
    color: #6c757d;
    margin-right: 0.25rem;
    font-size:12px;
}

.nav-tabs .nav-link:hover {
    background: rgba(255, 255, 255, 0.2);
    color: #495057;
    transform: translateY(-2px);
}

.nav-tabs .nav-link.active {
    background: #fff;
    color: #495057;
    border-bottom: 3px solid #dc3545;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.nav-tabs .nav-link .badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

/* Enhanced Card Design - THEME COLORS */
.card.border-0 {
    border-radius: 1rem;
    overflow: hidden;
}




/* Enhanced Network Container */
.network-container {
    min-height: 500px;
    border: 2px dashed #e9ecef;
    border-radius: 1rem;
    padding: 2rem;
    background: linear-gradient(45deg, #f8f9fa 25%, transparent 25%),
                linear-gradient(-45deg, #f8f9fa 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, #f8f9fa 75%),
                linear-gradient(-45deg, transparent 75%, #f8f9fa 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    overflow: auto;
}

/* Enhanced Loading State */
.loading-state {
    padding: 3rem 2rem;
}

.loading-state .spinner-border {
    border-width: 0.25rem;
}

/* Enhanced Empty State */
.empty-state {
    padding: 3rem 2rem;
}

.empty-state i {
    opacity: 0.5;
}

/* Enhanced Table Design - THEME COLORS */
.table-hover tbody tr:hover {
    background-color: rgba(220, 53, 69, 0.05);
    transform: scale(1.01);
    transition: all 0.2s ease;
}

.avatar-sm {
    transition: all 0.3s ease;
}

.avatar-sm:hover {
    transform: scale(1.1);
}

/* Enhanced Badge Design */
.badge {
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
}

/* FIXED: View Toggle Button Styling */
.btn-group .btn--dark.active {
    background-color: #343a40 !important;
    border-color: #343a40 !important;
    color: #fff !important;
}

.btn-group .btn-outline--dark {
    color: #343a40 !important;
    border-color: #343a40 !important;
    background-color: transparent !important;
}

.btn-group .btn-outline--dark:hover {
    background-color: #343a40 !important;
    border-color: #343a40 !important;
    color: #fff !important;
}

.btn-group .btn {
    transition: all 0.3s ease;
}

/* CLEAN NETWORK VISUALIZATION CSS - IDENTICAL TO ADMIN */
.orgchart {
    text-align: center !important;
}

.orgchart .node {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px;
    min-width: 120px;
    max-width: 200px;
    text-align: center;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);   
}

.orgchart .node:hover {
    border-color: #dc3545;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}
.orgchart .node .title {
    font-size:10px !important;
     width: 160px !important;
}
.orgchart .node .content {
    font-size:8px !important;
    font-weight: bold;
}
/* Node Types */
.orgchart .node.master-ib {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

.orgchart .node.sub-ib {
    background: #28a745;
    color: white;
    border-color: #28a745;
}

.orgchart .node .content {
    width: auto !important;
    height: auto !important;
    line-height: 12px;
}

/* Node Content */
.node-name {
    font-weight: bold;
    font-size: 8px;
    margin-bottom: 4px;
}

.node-info {
    font-size: 8px;
    line-height: 1.2;
}

/* Loading State */
.loading-spinner {
    text-align: center;
    padding: 40px;
}

.loading-spinner .spinner-border {
    width: 3rem;
    height: 3rem;
}

.widget-two {
    padding: 15px 15px;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    position: relative;
    overflow: hidden;
    align-items: center;
    height: 100%;
}
.widget-two__icon {
    width: 65px;
    height: 65px;
    display: -ms-flexbox;
    display: flex;   
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    border-radius: 5px;
    background: #ffffff21;
}
.widget-two__content {
    width: calc(100% - 65px);
    padding-left: 20px;
}

.bg--19 {
background: #342ead !important;
}
.bg--primary {
    background-color: #E3373F !important;
}
.bg--1 {
    background-color: #127681 !important;
} 
.bg--17 {
    background-color: #035aa6 !important;
}
.orgchart .edge:hover::before {
    border-color: #e17572 !important;
    padding: 4px !important;
}
.ib-labels {
    color: #ea5455;
    background: #ffffff;
    padding: 2px 6px;              /* Wider horizontal padding */
    border-radius: 999px;          /* Fully rounded */
    margin-left: 5px;
    font-size: 10px;               /* Slightly bigger text for better shape */
    display: inline-block;         /* Required for border-radius to work properly */
    line-height: 1;                /* Avoids tall pill */
}
</style>
@endpush

@push('script-lib')
<script src="https://cdn.jsdelivr.net/npm/orgchart@4.0.1/dist/js/jquery.orgchart.min.js"></script>
@endpush

@push('script')

<script>
        window.networkDataJson = @json($networkData ?? []);
        window.userDataJson = {
            id: {{ $user->id ?? 0 }},
            firstname: @json($user->firstname ?? ''),
            lastname: @json($user->lastname ?? ''),
            ib_type: @json($user->ib_type ?? 'master'),
            mt5_login: @json($user->mt5_login ?? 'N/A'),
            mt5_balance: {{ $user->mt5_balance ?? 0 }}
        };
        window.routesJson = {
            getReferrals: @json(route('user.partnership.get-referrals')),
            network: @json(route('user.partnership.network'))
        };
        console.log('✅ USER NETWORK: PHP Data passed successfully');

        // Debug output for user network page
        console.log('🔍 USER NETWORK DEBUG:', {
            networkData: window.networkDataJson,
            userData: window.userDataJson,
            routes: window.routesJson
        });


        // Pass PHP data to JavaScript - Following network-clean pattern
        window.userPhpData = {
            networkData: @json($networkData ?? []),
            user: {
                id: {{ $user->id ?? 0 }},
                name: @json(($user->firstname ?? '') . ' ' . ($user->lastname ?? '')),
                mt5_login: @json($user->mt5_login ?? 'N/A'),
                mt5_balance: {{ $user->mt5_balance ?? 0 }},
                ib_status: {{ $user->ib_status ?? 0 }},
                ib_type: @json($user->ib_type ?? 'master')
            }
        };
        console.log('User PHP Data passed:', window.userPhpData);
        console.log('User Tree data structure:', window.userPhpData.networkData.tree_data);

        $(document).ready(function() {
            console.log('User Document ready - starting network initialization');

            // Create BACKEND_DATA from window.userPhpData - Following network-clean pattern
            window.USER_BACKEND_DATA = {
                networkData: window.userPhpData.networkData || [],
                user: window.userPhpData.user || {},
                routes: {
                    getReferrals: @json(route('user.partnership.get-referrals')),
                    network: @json(route('user.partnership.network'))
                }
            };

            console.log('USER_BACKEND_DATA created:', window.USER_BACKEND_DATA);

            // Check if OrgChart.js is loaded
            if (typeof $.fn.orgchart !== 'undefined') {
                console.log('✅ OrgChart.js loaded successfully');
                initializeUserSimpleTree();
            } else {
                console.log('⚠️ OrgChart.js not available, showing fallback');
                showUserSimpleFallback();
            }
        });

        // Update debug status
        $('#jsStatus').html('✅ Working');
        $('#backendDataStatus').html('✅ Created');
        $('#orgchartStatus').html(typeof $.fn.orgchart !== 'undefined' ? '✅ Loaded' : '❌ Missing');

        // Working functions for test buttons
        function testOriginalTree() {
            console.log('✅ USER NETWORK: Test tree button clicked');
            if (typeof initializeOrgChart === 'function' && typeof treeData !== 'undefined') {
                initializeOrgChart(treeData);
            } else {
                alert('Tree initialization function not available');
            }
        }

        function testOriginalFallback() {
            console.log('✅ USER NETWORK: Test fallback button clicked');
            if (typeof showFallbackTree === 'function' && typeof treeData !== 'undefined') {
                showFallbackTree(treeData);
            } else {
                alert('Fallback function not available');
            }
        }

        function showOriginalDebug() {
            console.log('✅ USER NETWORK: Debug button clicked');
            console.log('BACKEND_DATA:', typeof BACKEND_DATA !== 'undefined' ? BACKEND_DATA : 'UNDEFINED');
            console.log('treeData:', typeof treeData !== 'undefined' ? treeData : 'UNDEFINED');
            alert('Debug data logged to console. Check F12 → Console tab');
        }

        // Initialize tree with COMPLETE NODE INFORMATION - ENHANCED ERROR HANDLING
        function initializeUserSimpleTree() {
            console.log('🚀 Initializing user tree with complete node information...');

            // ENHANCED: Better error handling and data validation
            let treeData;

            try {
                if (window.USER_BACKEND_DATA &&
                    window.USER_BACKEND_DATA.networkData &&
                    window.USER_BACKEND_DATA.networkData.tree_data &&
                    window.USER_BACKEND_DATA.networkData.tree_data.id) {
                    // Use the actual tree data from backend
                    treeData = window.USER_BACKEND_DATA.networkData.tree_data;
                    console.log('✅ USER: Using actual tree data from backend');
                    console.log('📊 Tree data:', treeData);
                } else {
                    // Create enhanced fallback tree data
                    const userData = window.USER_BACKEND_DATA?.user || {};
                    treeData = {
                        id: userData.id || 1,
                        name: userData.name || 'Current User',
                        title: userData.ib_status == 1 ? 'Master IB' : 'Client',
                        mt5_login: userData.mt5_login || 'N/A',
                        mt5_balance: (userData.mt5_balance || 0).toFixed(2),
                        total_deposit: '0.00',
                        node_type: userData.ib_status == 1 ? 'master' : 'client',
                        children: []
                    };
                    console.log('⚠️ USER: Using enhanced fallback tree data');
                    console.log('📊 Fallback data:', treeData);
                }
            } catch (error) {
                console.error('❌ Error preparing tree data:', error);
                // Ultimate fallback
                treeData = {
                    id: 1,
                    name: 'Network User',
                    title: 'Client',
                    mt5_login: 'N/A',
                    mt5_balance: '0.00',
                    total_deposit: '0.00',
                    node_type: 'client',
                    children: []
                };
            }

            console.log('🔍 USER: Tree data being used:', treeData);
            console.log('🔍 USER: Backend network data:', window.USER_BACKEND_DATA.networkData);
            console.log('🔍 USER: Tree data has children:', treeData.children ? treeData.children.length : 'NO CHILDREN');
            console.log('🔍 USER: Tree data structure check:', {
                hasTreeData: !!window.USER_BACKEND_DATA.networkData.tree_data,
                hasChildren: !!(treeData.children && treeData.children.length > 0),
                childrenCount: treeData.children ? treeData.children.length : 0,
                fallbackUsed: !window.USER_BACKEND_DATA.networkData.tree_data
            });

            try {
                // Clear the container first
                $('#userNetworkContainer').empty();

                // Initialize OrgChart - ORIGINAL FUNCTIONALITY RESTORED
                $('#userNetworkContainer').orgchart({
                    data: treeData,
                    direction: 't2b',
                    visibleLevel: 1,
                    nodeTemplate: function(data) {
                        // Determine IB type indicator
                        let ibIndicator = '';
                        if (data.ib_status == 1) {
                            if (data.ib_type === 'master') {
                                ibIndicator = ' <span class="ib-labels">M</span>';
                            } else if (data.ib_type === 'sub') {
                                ibIndicator = '<span class="ib-labels">S</span>';
                            }
                        } else {
                            ibIndicator = '<span class="ib-labels">C</span>';
                        }

                        return `
                            <div class="title">${data.name || 'Unknown'}${ibIndicator}</div>
                            <div class="content">
                                MT5: ${data.mt5_login || 'N/A'}<br>
                                Balance: $${data.mt5_balance || '0.00'}<br>
                                Deposits: $${data.total_deposit || '0.00'}
                            </div>
                        `;
                    }
                });

                console.log('User tree with complete information initialized successfully');

                // Add success message
                setTimeout(() => {
                    $('#userNetworkContainer').prepend('<div class="alert alert-success">✅ MultiIB tree loaded successfully!</div>');
                }, 500);

            } catch (error) {
                console.error('User OrgChart error:', error);
                showUserSimpleFallback();
            }
        }

        function showUserSimpleFallback() {
            console.log('Showing user simple fallback...');

            const fallbackHtml = `
                <div class="alert alert-warning">
                    <h5>⚠️ OrgChart.js not available</h5>
                    <p>Showing simplified network view:</p>
                    <ul>
                        <li><strong>${window.USER_BACKEND_DATA.user.name}</strong> (Master IB)</li>
                        <li>Direct Referrals: ${window.USER_BACKEND_DATA.networkData.direct_referrals || 0}</li>
                        <li>Total Network: ${window.USER_BACKEND_DATA.networkData.total_referrals || 0}</li>
                    </ul>
                </div>
            `;

            $('#userNetworkContainer').html(fallbackHtml);
        }

        $(document).ready(function() {
            console.log('✅ USER NETWORK: Document ready started');

            // Create BACKEND_DATA from window.USER_BACKEND_DATA to fix undefined error
            window.BACKEND_DATA = window.USER_BACKEND_DATA;

            // Create tree data
            const treeData = window.BACKEND_DATA.networkData.tree_data || {
                id: window.BACKEND_DATA.user.id,
                name: window.BACKEND_DATA.user.name,
                title: window.BACKEND_DATA.user.title,
                children: []
            };

        }); // Close $(document).ready

        // FIXED: Add view toggle functionality with theme colors
        function toggleView(viewType) {
            console.log('🔄 Toggling view to:', viewType);

            if (viewType === 'hierarchy') {
                // Show network tree
                $('#tree').addClass('show active');
                $('#referrals, #commissions, #activity').removeClass('show active');

                // FIXED: Update button states with theme colors
                $('#hierarchyViewBtn').addClass('active').removeClass('btn-outline--dark').addClass('btn--dark');
                $('#tableViewBtn').removeClass('active').removeClass('btn--dark').addClass('btn-outline--dark');

                // Update tab states
                $('#tree-tab').addClass('active');
                $('#referrals-tab, #commissions-tab, #activity-tab').removeClass('active');

                console.log('✅ Switched to hierarchy view');
            } else if (viewType === 'table') {
                // Show direct referrals table
                $('#referrals').addClass('show active');
                $('#tree, #commissions, #activity').removeClass('show active');

                // FIXED: Update button states with theme colors
                $('#tableViewBtn').addClass('active').removeClass('btn-outline--dark').addClass('btn--dark');
                $('#hierarchyViewBtn').removeClass('active').removeClass('btn--dark').addClass('btn-outline--dark');

                // Update tab states
                $('#referrals-tab').addClass('active');
                $('#tree-tab, #commissions-tab, #activity-tab').removeClass('active');

                console.log('✅ Switched to table view');
            }
        }

        // ENHANCED: Add tab switching functionality
        function switchToTab(tabId) {
            console.log('🔄 Switching to tab:', tabId);

            // Hide all tab panes
            $('.tab-pane').removeClass('show active');
            $('.nav-link').removeClass('active');

            // Show selected tab
            $('#' + tabId).addClass('show active');
            $('#' + tabId + '-tab').addClass('active');

            console.log('✅ Switched to tab:', tabId);
        }

        // ENHANCED: Add debugging function
        function debugNetworkData() {
            console.log('🔍 NETWORK DEBUG INFO:');
            console.log('USER_BACKEND_DATA:', window.USER_BACKEND_DATA);
            console.log('Network Data:', window.USER_BACKEND_DATA?.networkData);
            console.log('Tree Data:', window.USER_BACKEND_DATA?.networkData?.tree_data);
            console.log('Direct Referrals:', window.USER_BACKEND_DATA?.networkData?.direct_referrals);
            console.log('Total Referrals:', window.USER_BACKEND_DATA?.networkData?.total_referrals);

            // Show debug info in alert
            const debugInfo = `
Network Debug Info:
- Direct Referrals: ${window.USER_BACKEND_DATA?.networkData?.direct_referrals || 0}
- Total Network: ${window.USER_BACKEND_DATA?.networkData?.total_referrals || 0}
- Tree Data Available: ${!!window.USER_BACKEND_DATA?.networkData?.tree_data}
- OrgChart Loaded: ${typeof $.fn.orgchart !== 'undefined'}
            `;
            alert(debugInfo);
        }

        // TASK 4: Commission Calculator JavaScript
        $(document).ready(function() {
            // Calculate button click handler
            $('#calculate-btn').on('click', function() {
                calculateCommissions();
            });

            // Reset button click handler
            $('#reset-calculator').on('click', function() {
                resetCalculator();
            });

            // Auto-calculate on input change
            $('#trading-volume, #symbol-group, #time-period, #network-levels').on('change input', function() {
                if ($('#trading-volume').val() > 0) {
                    calculateCommissions();
                }
            });
        });

        function calculateCommissions() {
            const volume = parseFloat($('#trading-volume').val()) || 0;
            const symbolGroup = $('#symbol-group');
            const baseRate = parseFloat(symbolGroup.find(':selected').data('rate')) || 7;
            const timePeriod = $('#time-period').val();
            const networkLevels = parseInt($('#network-levels').val()) || 1;

            if (volume <= 0) {
                alert('@lang("Please enter a valid trading volume")');
                return;
            }

            // Time period multipliers
            const periodMultipliers = {
                'daily': 1,
                'weekly': 7,
                'monthly': 30,
                'yearly': 365
            };

            const periodMultiplier = periodMultipliers[timePeriod] || 1;
            const totalVolume = volume * periodMultiplier;

            // Commission rates (as per existing system)
            const rates = {
                level1: 0.50, // Master IB: 50%
                level2: 0.30, // Sub IB: 30%
                level3: 0.20  // Level 3: 20%
            };

            // Calculate base commission per lot
            const baseCommissionPerLot = baseRate;
            const totalBaseCommission = totalVolume * baseCommissionPerLot;

            // Calculate commissions by level
            let level1Commission = 0;
            let level2Commission = 0;
            let level3Commission = 0;

            if (networkLevels >= 1) {
                level1Commission = totalBaseCommission * rates.level1;
            }
            if (networkLevels >= 2) {
                level2Commission = totalBaseCommission * rates.level2;
            }
            if (networkLevels >= 3) {
                level3Commission = totalBaseCommission * rates.level3;
            }

            const totalCommission = level1Commission + level2Commission + level3Commission;

            // Update display
            $('#level1-commission').text('$' + level1Commission.toFixed(2));
            $('#level2-commission').text('$' + level2Commission.toFixed(2));
            $('#level3-commission').text('$' + level3Commission.toFixed(2));
            $('#total-commission').text('$' + totalCommission.toFixed(2));

            // Update breakdown
            const breakdown = `
                <strong>@lang('Calculation Details'):</strong><br>
                • @lang('Trading Volume'): ${volume.toLocaleString()} lots ${timePeriod}<br>
                • @lang('Total Volume') (${timePeriod}): ${totalVolume.toLocaleString()} lots<br>
                • @lang('Base Rate'): $${baseRate}/lot<br>
                • @lang('Total Base Commission'): $${totalBaseCommission.toFixed(2)}<br>
                • @lang('Network Levels'): ${networkLevels}<br><br>
                <strong>@lang('Commission Breakdown'):</strong><br>
                ${networkLevels >= 1 ? `• @lang('Level 1') (50%): $${level1Commission.toFixed(2)}<br>` : ''}
                ${networkLevels >= 2 ? `• @lang('Level 2') (30%): $${level2Commission.toFixed(2)}<br>` : ''}
                ${networkLevels >= 3 ? `• @lang('Level 3') (20%): $${level3Commission.toFixed(2)}<br>` : ''}
            `;

            $('#calculation-breakdown').html(breakdown);
            $('#calculation-results').show();
        }

        function resetCalculator() {
            $('#trading-volume').val('100');
            $('#symbol-group').val('forex');
            $('#time-period').val('weekly');
            $('#network-levels').val('2');
            $('#calculation-results').hide();
        }

        // TASK 1: Real-time commission and balance updates for user dashboard
        function updateUserCommissionData() {
            fetch('{{ route("user.partnership.commission.realtime") }}', {
                method: 'GET',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update commission statistics
                    const totalCommissionEl = document.getElementById('user-total-commission');
                    const paidCommissionEl = document.getElementById('user-paid-commission');
                    const pendingCommissionEl = document.getElementById('user-pending-commission');
                    const mt5BalanceEl = document.getElementById('user-mt5-balance');
                    const commissionCountEl = document.getElementById('user-commission-count');

                    if (totalCommissionEl) totalCommissionEl.textContent = '$' + parseFloat(data.total_commission).toFixed(2);
                    if (paidCommissionEl) paidCommissionEl.textContent = '$' + parseFloat(data.paid_commission).toFixed(2);
                    if (pendingCommissionEl) pendingCommissionEl.textContent = '$' + parseFloat(data.pending_commission).toFixed(2);
                    if (mt5BalanceEl) mt5BalanceEl.textContent = '$' + parseFloat(data.mt5_balance).toFixed(2);
                    if (commissionCountEl) commissionCountEl.textContent = data.commission_count;

                    console.log('Commission data updated:', data);
                }
            })
            .catch(error => {
                console.error('Real-time commission update error:', error);
            });
        }

        // Start real-time updates every 30 seconds
        setInterval(updateUserCommissionData, 30000);

        // Initial update after page load
        setTimeout(updateUserCommissionData, 2000);

</script>

<style>
    /* TASK 4: Commission Calculator Styling */
    #calculator .card {
        border: 1px solid #e9ecef;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }

    #calculator .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        padding: 15px 20px;
    }

    #calculator .card-body {
        padding: 20px;
    }

    #calculator .form-group {
        margin-bottom: 20px;
    }

    #calculator .form-label {
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
    }

    #calculator .form-control {
        border: 1px solid #ddd;
        border-radius: 6px;
        padding: 10px 12px;
        font-size: 14px;
    }

    #calculator .form-control:focus {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }

    #calculator .commission-rates .badge {
        font-size: 12px;
        padding: 6px 10px;
    }

    #calculator .network-stats .text-danger {
        font-weight: 600;
    }

    #calculation-results .border {
        border-color: #e9ecef !important;
    }

    #calculation-results .bg-light {
        background-color: #f8f9fa !important;
    }

    #calculation-results .col-md-3 {
        margin-bottom: 15px;
    }
</style>

@endpush

@endsection
