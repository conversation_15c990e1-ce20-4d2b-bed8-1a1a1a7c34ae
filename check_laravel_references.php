<?php

require_once 'vendor/autoload.php';
require_once 'bootstrap/app.php';

use App\Models\NotificationTemplate;

echo "Checking for Laravel references in email templates...\n";
echo "===================================================\n\n";

$templates = NotificationTemplate::all();
$foundLaravelReferences = false;

foreach ($templates as $template) {
    $content = $template->email_body;
    $subject = $template->subj;
    
    // Check for Laravel references
    $laravelIndicators = [
        'Laravel Team',
        'Laravel',
        'laravel.com',
        'Laravel Framework'
    ];
    
    foreach ($laravelIndicators as $indicator) {
        if (stripos($content, $indicator) !== false || stripos($subject, $indicator) !== false) {
            echo "❌ FOUND Laravel reference in Template {$template->id} ({$template->name}):\n";
            echo "   Action: {$template->act}\n";
            echo "   Reference: '{$indicator}'\n";
            
            if (stripos($content, $indicator) !== false) {
                echo "   Location: Email body\n";
            }
            if (stripos($subject, $indicator) !== false) {
                echo "   Location: Subject\n";
            }
            echo "\n";
            $foundLaravelReferences = true;
        }
    }
}

if (!$foundLaravelReferences) {
    echo "✅ No Laravel references found in any email templates\n";
}

echo "\nChecking template preview functionality...\n";
echo "==========================================\n";

// Test template preview
$testTemplate = NotificationTemplate::find(44);
if ($testTemplate) {
    $visualBuilderService = new \App\Services\VisualBuilderService();
    $previewData = $visualBuilderService->generatePreviewData($testTemplate);
    
    $previewContent = $previewData['content'];
    
    foreach ($laravelIndicators as $indicator) {
        if (stripos($previewContent, $indicator) !== false) {
            echo "❌ FOUND Laravel reference in preview for Template {$testTemplate->id}:\n";
            echo "   Reference: '{$indicator}'\n";
            $foundLaravelReferences = true;
        }
    }
    
    if (!$foundLaravelReferences) {
        echo "✅ No Laravel references found in template preview\n";
    }
}

echo "\nScan complete.\n";
