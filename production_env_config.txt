# PRODUCTION SERVER CONFIGURATION FOR WINDOWS/PLESK
# Copy these settings to your live server's .env file

# =============================================================================
# PYTHON CONFIGURATION FOR WINDOWS/PLESK SERVER
# =============================================================================

# Python executable path (from your web.config)
PYTHON_EXE="C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python.exe"

# Python script path (based on your server structure)
PYTHON_SCRIPT="C:\\Inetpub\\vhosts\\mybrokerforex.com\\mbf.mybrokerforex.com\\python\\mt5manager.py"

# Alternative paths if the above doesn't work:
# PYTHON_SCRIPT="C:\\Inetpub\\vhosts\\mybrokerforex.com\\httpdocs\\python\\mt5manager.py"
# PYTHON_SCRIPT="C:\\Inetpub\\vhosts\\mybrokerforex.com\\mbf.mybrokerforex.com-31052025\\python\\mt5manager.py"

# =============================================================================
# INSTRUCTIONS FOR LIVE SERVER SETUP
# =============================================================================

1. BACKUP YOUR CURRENT .env FILE FIRST!

2. UPDATE YOUR LIVE SERVER .env FILE:
   - Replace the PYTHON_EXE line with the one above
   - Replace the PYTHON_SCRIPT line with the one above
   - Keep all other settings unchanged

3. VERIFY PYTHON SCRIPT LOCATION:
   - Upload python/mt5manager.py to your live server
   - Ensure it's in the correct path as specified above
   - Set file permissions to 755 if needed

4. TEST THE CONFIGURATION:
   - Upload the fix_python_script.php file to your live server
   - Run it via browser to verify paths
   - Check if Python dependencies are installed

5. CLEAR LARAVEL CACHES:
   php artisan cache:clear
   php artisan config:clear
   php artisan route:clear

# =============================================================================
# TROUBLESHOOTING PATHS
# =============================================================================

If the main path doesn't work, try these alternatives in order:

Option 1 (Current):
PYTHON_SCRIPT="C:\\Inetpub\\vhosts\\mybrokerforex.com\\mbf.mybrokerforex.com\\python\\mt5manager.py"

Option 2 (If using httpdocs):
PYTHON_SCRIPT="C:\\Inetpub\\vhosts\\mybrokerforex.com\\httpdocs\\python\\mt5manager.py"

Option 3 (If using subdomain folder):
PYTHON_SCRIPT="C:\\Inetpub\\vhosts\\mybrokerforex.com\\subdomains\\mbf\\httpdocs\\python\\mt5manager.py"

Option 4 (Relative path - if others fail):
PYTHON_SCRIPT="python\\mt5manager.py"

# =============================================================================
# PYTHON DEPENDENCIES CHECK
# =============================================================================

Run these commands on your live server to ensure dependencies:

1. Check Python version:
   C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe --version

2. Install MetaTrader5 package:
   C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe -m pip install MetaTrader5

3. Test MT5 connection:
   C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe "C:\Inetpub\vhosts\mybrokerforex.com\mbf.mybrokerforex.com\python\mt5manager.py" test_connection

# =============================================================================
# WEB.CONFIG VERIFICATION
# =============================================================================

Your web.config already has the correct Python handler:
<add name="PythonScript" path="*.py" verb="*" modules="FastCgiModule" 
     scriptProcessor="C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" 
     resourceType="File" requireAccess="Script" />

This matches the PYTHON_EXE path above, which is good!

# =============================================================================
# TESTING COMMANDS FOR LIVE SERVER
# =============================================================================

Once configured, test these commands directly on your live server:

1. Test connection:
"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" "C:\Inetpub\vhosts\mybrokerforex.com\mbf.mybrokerforex.com\python\mt5manager.py" test_connection

2. Test leverage change:
"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" "C:\Inetpub\vhosts\mybrokerforex.com\mbf.mybrokerforex.com\python\mt5manager.py" change_leverage --login YOUR_MT5_LOGIN --leverage 300

3. Test password change:
"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" "C:\Inetpub\vhosts\mybrokerforex.com\mbf.mybrokerforex.com\python\mt5manager.py" change_password --login YOUR_MT5_LOGIN --new_password "NewPass123" --password_type main

# =============================================================================
# COMMON ISSUES AND SOLUTIONS
# =============================================================================

Issue 1: "HTTP ERROR 500"
Solution: Check if Python script path exists and has correct permissions

Issue 2: "Python not found"
Solution: Verify PYTHON_EXE path matches your server's Python installation

Issue 3: "MetaTrader5 module not found"
Solution: Install MetaTrader5 package using pip as shown above

Issue 4: "Permission denied"
Solution: Ensure IIS user has execute permissions on Python and script files

Issue 5: "Path not found"
Solution: Try the alternative paths listed above

# =============================================================================
# FINAL VERIFICATION STEPS
# =============================================================================

1. Upload fix_python_script.php to your live server
2. Run it via browser (https://mbf.mybrokerforex.com/fix_python_script.php)
3. Check all sections show green checkmarks
4. Test actual leverage/password change from Laravel admin
5. Check Laravel logs for any errors
6. Delete fix_python_script.php after testing for security
