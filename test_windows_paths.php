<?php
/**
 * WINDOWS SERVER 2022/PLESK PATH TESTING SCRIPT
 * 
 * This script specifically tests the double backslash path issue
 * Upload this to your live Windows/Plesk server to test paths
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Windows Server 2022/Plesk Path Testing</h1>";
echo "<style>
body{font-family:Arial;margin:20px;} 
.success{color:green;font-weight:bold;} 
.error{color:red;font-weight:bold;} 
.warning{color:orange;font-weight:bold;}
.code{background:#f5f5f5;padding:10px;border:1px solid #ddd;margin:10px 0;}
</style>";

echo "<h2>Testing Your Exact Paths</h2>";

// Your exact paths with double backslashes
$pythonExe = 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python.exe';
$pythonScript = 'C:\\Inetpub\\vhosts\\mybrokerforex.com\\mbf.mybrokerforex.com\\python\\mt5manager.py';

echo "<div class='code'>";
echo "<strong>Python EXE:</strong> " . htmlspecialchars($pythonExe) . "<br>";
echo "<strong>Python Script:</strong> " . htmlspecialchars($pythonScript) . "<br>";
echo "</div>";

echo "<h3>1. Testing Python Executable</h3>";
if (file_exists($pythonExe)) {
    echo "<span class='success'>✓ Python executable found!</span><br>";
    
    // Test Python version
    $versionCmd = '"' . $pythonExe . '" --version 2>&1';
    echo "<strong>Testing command:</strong> " . htmlspecialchars($versionCmd) . "<br>";
    
    $version = shell_exec($versionCmd);
    if ($version) {
        echo "<span class='success'>✓ Python version: " . trim($version) . "</span><br>";
    } else {
        echo "<span class='error'>✗ Could not get Python version</span><br>";
    }
} else {
    echo "<span class='error'>✗ Python executable not found at specified path</span><br>";
}

echo "<h3>2. Testing Python Script</h3>";
if (file_exists($pythonScript)) {
    echo "<span class='success'>✓ Python script found!</span><br>";
    echo "<strong>Script size:</strong> " . filesize($pythonScript) . " bytes<br>";
    echo "<strong>Script permissions:</strong> " . substr(sprintf('%o', fileperms($pythonScript)), -4) . "<br>";
} else {
    echo "<span class='error'>✗ Python script not found at specified path</span><br>";
    
    // Try alternative locations
    $alternatives = [
        'C:\\Inetpub\\vhosts\\mybrokerforex.com\\httpdocs\\python\\mt5manager.py',
        'C:\\Inetpub\\vhosts\\mybrokerforex.com\\mbf.mybrokerforex.com-31052025\\python\\mt5manager.py',
        getcwd() . '\\python\\mt5manager.py',
        'python\\mt5manager.py'
    ];
    
    echo "<br><strong>Checking alternative locations:</strong><br>";
    foreach ($alternatives as $alt) {
        if (file_exists($alt)) {
            echo "<span class='success'>✓ Found at: " . htmlspecialchars($alt) . "</span><br>";
        } else {
            echo "<span class='error'>✗ Not found: " . htmlspecialchars($alt) . "</span><br>";
        }
    }
}

echo "<h3>3. Testing Command Execution</h3>";
if (file_exists($pythonExe) && file_exists($pythonScript)) {
    
    // Test basic script execution
    $testCmd = '"' . $pythonExe . '" "' . $pythonScript . '" --help 2>&1';
    echo "<strong>Testing command:</strong> " . htmlspecialchars($testCmd) . "<br>";
    
    $output = shell_exec($testCmd);
    if ($output) {
        echo "<span class='success'>✓ Script executed successfully</span><br>";
        echo "<div class='code'><strong>Output:</strong><br>" . htmlspecialchars($output) . "</div>";
    } else {
        echo "<span class='error'>✗ Script execution failed or no output</span><br>";
    }
    
    // Test MT5 connection
    echo "<br><strong>Testing MT5 connection:</strong><br>";
    $mt5Cmd = '"' . $pythonExe . '" "' . $pythonScript . '" test_connection 2>&1';
    echo "<strong>Command:</strong> " . htmlspecialchars($mt5Cmd) . "<br>";
    
    $mt5Output = shell_exec($mt5Cmd);
    if ($mt5Output) {
        echo "<div class='code'><strong>MT5 Test Output:</strong><br>" . htmlspecialchars($mt5Output) . "</div>";
        
        if (stripos($mt5Output, 'success') !== false || stripos($mt5Output, 'connected') !== false) {
            echo "<span class='success'>✓ MT5 connection test successful</span><br>";
        } else {
            echo "<span class='warning'>⚠ MT5 connection test completed - check output above</span><br>";
        }
    } else {
        echo "<span class='error'>✗ No output from MT5 test</span><br>";
    }
    
} else {
    echo "<span class='error'>✗ Cannot test execution - files not found</span><br>";
}

echo "<h3>4. Testing MetaTrader5 Package</h3>";
if (file_exists($pythonExe)) {
    $mt5PackageCmd = '"' . $pythonExe . '" -c "import MetaTrader5; print(\'MetaTrader5 version:\', MetaTrader5.__version__)" 2>&1';
    echo "<strong>Command:</strong> " . htmlspecialchars($mt5PackageCmd) . "<br>";
    
    $packageOutput = shell_exec($mt5PackageCmd);
    if ($packageOutput && stripos($packageOutput, 'version') !== false) {
        echo "<span class='success'>✓ MetaTrader5 package installed: " . trim($packageOutput) . "</span><br>";
    } else {
        echo "<span class='error'>✗ MetaTrader5 package not installed or error occurred</span><br>";
        echo "<div class='code'><strong>Output:</strong><br>" . htmlspecialchars($packageOutput) . "</div>";
        echo "<p><strong>To install:</strong> Run this command on your server:</p>";
        echo "<div class='code'>\"" . htmlspecialchars($pythonExe) . "\" -m pip install MetaTrader5</div>";
    }
}

echo "<h3>5. Environment Variable Test</h3>";
echo "<p>Testing how Laravel will read these paths from .env:</p>";

// Simulate Laravel env() function behavior
$envPythonExe = 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python.exe';
$envPythonScript = 'C:\\Inetpub\\vhosts\\mybrokerforex.com\\mbf.mybrokerforex.com\\python\\mt5manager.py';

echo "<div class='code'>";
echo "<strong>PYTHON_EXE from .env:</strong> " . htmlspecialchars($envPythonExe) . "<br>";
echo "<strong>PYTHON_SCRIPT from .env:</strong> " . htmlspecialchars($envPythonScript) . "<br>";
echo "</div>";

// Test Laravel-style command building
$laravelCmd = escapeshellarg($envPythonExe) . ' ' . escapeshellarg($envPythonScript) . ' test_connection';
echo "<strong>Laravel-style command:</strong> " . htmlspecialchars($laravelCmd) . "<br>";

if (file_exists($envPythonExe) && file_exists($envPythonScript)) {
    $laravelOutput = shell_exec($laravelCmd . ' 2>&1');
    if ($laravelOutput) {
        echo "<span class='success'>✓ Laravel-style command executed</span><br>";
        echo "<div class='code'><strong>Output:</strong><br>" . htmlspecialchars($laravelOutput) . "</div>";
    } else {
        echo "<span class='error'>✗ Laravel-style command failed</span><br>";
    }
}

echo "<h3>6. Summary and Next Steps</h3>";

if (file_exists($pythonExe) && file_exists($pythonScript)) {
    echo "<div style='background:#e6ffe6;padding:15px;border:1px solid green;'>";
    echo "<h4>✅ SUCCESS: Paths are working!</h4>";
    echo "<p>Your double backslash paths are correct for Windows Server 2022/Plesk:</p>";
    echo "<ul>";
    echo "<li>Python executable found and working</li>";
    echo "<li>Python script found and accessible</li>";
    echo "<li>Commands can be executed</li>";
    echo "</ul>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ol>";
    echo "<li>Update your live server's .env file with these exact paths</li>";
    echo "<li>Clear Laravel caches: <code>php artisan cache:clear && php artisan config:clear</code></li>";
    echo "<li>Test MT5 functionality through your admin panel</li>";
    echo "</ol>";
    echo "</div>";
} else {
    echo "<div style='background:#ffe6e6;padding:15px;border:1px solid red;'>";
    echo "<h4>❌ ISSUE: Files not found</h4>";
    echo "<p>The paths need to be corrected. Check:</p>";
    echo "<ul>";
    echo "<li>Is Python installed at the specified location?</li>";
    echo "<li>Is the mt5manager.py script uploaded to the correct folder?</li>";
    echo "<li>Do the paths match your actual server structure?</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<p><strong>Important:</strong> Delete this test file after use for security.</p>";
echo "<p><strong>Current server time:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>
