<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ib_commissions', function (Blueprint $table) {
            // Add MT5 integration fields
            $table->bigInteger('mt5_deal_id')->nullable()->after('trade_id')->index();
            $table->bigInteger('mt5_login')->nullable()->after('mt5_deal_id')->index();
            $table->timestamp('deal_time')->nullable()->after('trade_closed_at');
            $table->decimal('deal_profit', 15, 2)->nullable()->after('deal_time');
            $table->decimal('deal_commission', 15, 2)->nullable()->after('deal_profit');
            $table->timestamp('processed_at')->nullable()->after('paid_at');
            $table->unsignedBigInteger('parent_commission_id')->nullable()->after('processed_at');
            
            // Add indexes for better performance
            $table->index(['to_ib_user_id', 'deal_time']);
            $table->index(['from_user_id', 'deal_time']);
            $table->index(['symbol', 'deal_time']);
            $table->index(['status', 'deal_time']);
            $table->index(['level', 'to_ib_user_id']);
            
            // Add foreign key for parent commission
            $table->foreign('parent_commission_id')->references('id')->on('ib_commissions')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ib_commissions', function (Blueprint $table) {
            // Drop foreign key first
            $table->dropForeign(['parent_commission_id']);
            
            // Drop indexes
            $table->dropIndex(['to_ib_user_id', 'deal_time']);
            $table->dropIndex(['from_user_id', 'deal_time']);
            $table->dropIndex(['symbol', 'deal_time']);
            $table->dropIndex(['status', 'deal_time']);
            $table->dropIndex(['level', 'to_ib_user_id']);
            $table->dropIndex(['mt5_deal_id']);
            $table->dropIndex(['mt5_login']);
            
            // Drop columns
            $table->dropColumn([
                'mt5_deal_id',
                'mt5_login',
                'deal_time',
                'deal_profit',
                'deal_commission',
                'processed_at',
                'parent_commission_id'
            ]);
        });
    }
};
