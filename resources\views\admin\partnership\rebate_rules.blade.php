@extends('admin.layouts.app')

@section('panel')
<div class="row">
    <div class="col-lg-12">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h4 class="page-title">@lang('All Rebate Rules')</h4>
            <div class="d-flex gap-2">
                <!-- Search Bar -->
                <div class="search-form">
                    <form action="" method="GET">
                        <div class="input-group">
                            <input type="text" name="search" class="form-control" placeholder="@lang('Search rebate rules...')" value="{{ request()->search }}">
                            <button class="btn btn--primary" type="submit"><i class="las la-search"></i></button>
                        </div>
                    </form>
                </div>
                <!-- Add Button -->
                <button type="button" class="btn btn--primary" data-bs-toggle="modal" data-bs-target="#addRebateRuleModal">
                    <i class="las la-plus"></i> @lang('Add Rebate Rules')
                </button>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row gy-4 mb-4">
            <div class="col-xxl-3 col-sm-6">
                <div class="widget-two style--two box--shadow2 b-radius--5 bg--success">
                    <div class="widget-two__icon b-radius--5 bg--success">
                        <i class="las la-chart-line"></i>
                    </div>
                    <div class="widget-two__content">
                        <h3 class="text-white">{{ $rebateRules->where('status', true)->count() }}</h3>
                        <p class="text-white">@lang('Active Rules')</p>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-sm-6">
                <div class="widget-two style--two box--shadow2 b-radius--5 bg--warning">
                    <div class="widget-two__icon b-radius--5 bg--warning">
                        <i class="las la-pause-circle"></i>
                    </div>
                    <div class="widget-two__content">
                        <h3 class="text-white">{{ $rebateRules->where('status', false)->count() }}</h3>
                        <p class="text-white">@lang('Inactive Rules')</p>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-sm-6">
                <div class="widget-two style--two box--shadow2 b-radius--5 bg--info">
                    <div class="widget-two__icon b-radius--5 bg--info">
                        <i class="las la-layer-group"></i>
                    </div>
                    <div class="widget-two__content">
                        <h3 class="text-white">{{ $rebateRules->whereNotNull('symbol_group_id')->count() }}</h3>
                        <p class="text-white">@lang('Group Rules')</p>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-sm-6">
                <div class="widget-two style--two box--shadow2 b-radius--5 bg--primary">
                    <div class="widget-two__icon b-radius--5 bg--primary">
                        <i class="las la-coins"></i>
                    </div>
                    <div class="widget-two__content">
                        <h3 class="text-white">${{ number_format($rebateRules->avg('rebate_per_lot'), 2) }}</h3>
                        <p class="text-white">@lang('Avg Rebate')</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="card b-radius--10">
            <div class="card-body p-0">
                <div class="table-responsive--md table-responsive">
                    <table class="table--light style--two table">
                        <thead>
                            <tr>
                                <th>@lang('Rule')</th>
                                <th>@lang('IB Group')</th>
                                <th>@lang('Symbol/Group')</th>
                                <th>@lang('Rebate Per Lot')</th>
                                <th>@lang('Volume Limits')</th>
                                <th>@lang('Status')</th>
                                <th>@lang('Action')</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($rebateRules as $rule)
                            <tr>
                                <td>
                                    <span class="fw-bold">{{ $rule->display_name }}</span>
                                </td>
                                <td>
                                    @if($rule->ibGroup)
                                        <span class="badge badge--info">{{ $rule->ibGroup->name }}</span>
                                    @else
                                        <span class="text-muted">@lang('All Groups')</span>
                                    @endif
                                </td>
                                <td>
                                    @if($rule->symbol)
                                        <span class="badge badge--primary">{{ $rule->symbol }}</span>
                                        <small class="d-block text-muted">@lang('Specific Symbol')</small>
                                    @elseif($rule->symbolGroup)
                                        <span class="badge badge--success">{{ $rule->symbolGroup->name }}</span>
                                        <small class="d-block text-muted">@lang('Symbol Group')</small>
                                    @else
                                        <span class="text-muted">@lang('All Symbols')</span>
                                    @endif
                                </td>
                                <td>
                                    <span class="fw-bold text-success">${{ number_format($rule->rebate_per_lot, 2) }}</span>
                                </td>
                                <td>
                                    <small>
                                        @lang('Min'): {{ number_format($rule->min_volume, 2) }}<br>
                                        @lang('Max'): {{ $rule->max_volume ? number_format($rule->max_volume, 2) : 'Unlimited' }}
                                    </small>
                                </td>
                                <td>
                                    @if($rule->status)
                                        <span class="badge badge--success">@lang('Active')</span>
                                    @else
                                        <span class="badge badge--warning">@lang('Inactive')</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="button--group">
                                        <button type="button" class="btn btn-sm btn-outline--primary editBtn" 
                                                data-id="{{ $rule->id }}"
                                                data-ib_group_id="{{ $rule->ib_group_id }}"
                                                data-symbol_group_id="{{ $rule->symbol_group_id }}"
                                                data-symbol="{{ $rule->symbol }}"
                                                data-rebate_per_lot="{{ $rule->rebate_per_lot }}"
                                                data-min_volume="{{ $rule->min_volume }}"
                                                data-max_volume="{{ $rule->max_volume }}">
                                            <i class="las la-pencil"></i> @lang('Edit')
                                        </button>
                                        
                                        <button type="button" class="btn btn-sm btn-outline--{{ $rule->status ? 'warning' : 'success' }} toggleStatusBtn"
                                                data-id="{{ $rule->id }}"
                                                data-status="{{ $rule->status }}">
                                            @if($rule->status)
                                                <i class="las la-eye-slash"></i> @lang('Disable')
                                            @else
                                                <i class="las la-eye"></i> @lang('Enable')
                                            @endif
                                        </button>
                                        
                                        <button type="button" class="btn btn-sm btn-outline--danger deleteBtn"
                                                data-id="{{ $rule->id }}">
                                            <i class="las la-trash"></i> @lang('Delete')
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="7" class="text-muted text-center">@lang('No rebate rules found')</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if($rebateRules->hasPages())
            <div class="card-footer py-4">
                {{ paginateLinks($rebateRules) }}
            </div>
            @endif
        </div>
    </div>
</div>



<!-- Add Rebate Rule Modal -->
<div class="modal fade" id="addRebateRuleModal" tabindex="-1" role="dialog" aria-labelledby="addRebateRuleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addRebateRuleModalLabel">@lang('Add New Rebate Rule')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('admin.partnership.store_rebate_rule') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('IB Group')</label>
                                <select class="form-control" name="ib_group_id">
                                    <option value="">@lang('All IB Groups')</option>
                                    @foreach($ibGroups as $group)
                                        <option value="{{ $group->id }}">{{ $group->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Symbol Group')</label>
                                <select class="form-control" name="symbol_group_id" id="symbolGroupSelect">
                                    <option value="">@lang('Select Symbol Group')</option>
                                    @foreach($symbolGroups as $group)
                                        <option value="{{ $group->id }}">{{ $group->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>@lang('Specific Symbol') <small class="text-muted">(@lang('Optional - leave empty for group-wide rule'))</small></label>
                        <input type="text" class="form-control" name="symbol" placeholder="e.g., EURUSD">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>@lang('Rebate Per Lot') <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" name="rebate_per_lot" step="0.01" min="0" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>@lang('Min Volume') <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" name="min_volume" step="0.01" min="0" value="0" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>@lang('Max Volume') <small class="text-muted">(@lang('Optional'))</small></label>
                                <input type="number" class="form-control" name="max_volume" step="0.01" min="0">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn--dark" data-bs-dismiss="modal">@lang('Close')</button>
                    <button type="submit" class="btn btn--primary">@lang('Save')</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Rebate Rule Modal -->
<div class="modal fade" id="editRebateRuleModal" tabindex="-1" role="dialog" aria-labelledby="editRebateRuleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editRebateRuleModalLabel">@lang('Edit Rebate Rule')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editRebateRuleForm" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('IB Group')</label>
                                <select class="form-control" name="ib_group_id" id="editIbGroupId">
                                    <option value="">@lang('All IB Groups')</option>
                                    @foreach($ibGroups as $group)
                                        <option value="{{ $group->id }}">{{ $group->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Symbol Group')</label>
                                <select class="form-control" name="symbol_group_id" id="editSymbolGroupId">
                                    <option value="">@lang('Select Symbol Group')</option>
                                    @foreach($symbolGroups as $group)
                                        <option value="{{ $group->id }}">{{ $group->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>@lang('Specific Symbol') <small class="text-muted">(@lang('Optional'))</small></label>
                        <input type="text" class="form-control" name="symbol" id="editSymbol">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>@lang('Rebate Per Lot') <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" name="rebate_per_lot" id="editRebatePerLot" step="0.01" min="0" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>@lang('Min Volume') <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" name="min_volume" id="editMinVolume" step="0.01" min="0" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>@lang('Max Volume') <small class="text-muted">(@lang('Optional'))</small></label>
                                <input type="number" class="form-control" name="max_volume" id="editMaxVolume" step="0.01" min="0">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn--dark" data-bs-dismiss="modal">@lang('Close')</button>
                    <button type="submit" class="btn btn--primary">@lang('Update')</button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

@push('script')
<script>
    (function ($) {
        'use strict';
        
        // Edit button click
        $('.editBtn').on('click', function() {
            const id = $(this).data('id');
            const ibGroupId = $(this).data('ib_group_id');
            const symbolGroupId = $(this).data('symbol_group_id');
            const symbol = $(this).data('symbol');
            const rebatePerLot = $(this).data('rebate_per_lot');
            const minVolume = $(this).data('min_volume');
            const maxVolume = $(this).data('max_volume');
            
            $('#editIbGroupId').val(ibGroupId);
            $('#editSymbolGroupId').val(symbolGroupId);
            $('#editSymbol').val(symbol);
            $('#editRebatePerLot').val(rebatePerLot);
            $('#editMinVolume').val(minVolume);
            $('#editMaxVolume').val(maxVolume);
            
            const updateUrl = `{{ route('admin.partnership.update_rebate_rule', ':id') }}`.replace(':id', id);
            $('#editRebateRuleForm').attr('action', updateUrl);
            
            $('#editRebateRuleModal').modal('show');
        });
        
    })(jQuery);
</script>
@endpush
