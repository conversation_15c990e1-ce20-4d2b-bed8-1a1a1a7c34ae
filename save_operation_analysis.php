<?php
/**
 * SAVE OPERATION ANALYSIS
 * This script analyzes the exact save operation that's failing
 */

// Include Laravel bootstrap
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🔍 SAVE OPERATION ANALYSIS\n";
echo "==========================\n\n";

try {
    // 1. MODEL CONFIGURATION ANALYSIS
    echo "1️⃣ MODEL CONFIGURATION ANALYSIS\n";
    echo "================================\n";
    
    $model = new \App\Models\NotificationTemplate();
    
    echo "✅ Model class: " . get_class($model) . "\n";
    echo "✅ Table: " . $model->getTable() . "\n";
    echo "✅ Primary key: " . $model->getKeyName() . "\n";
    echo "✅ Key type: " . $model->getKeyType() . "\n";
    echo "✅ Incrementing: " . ($model->getIncrementing() ? 'YES' : 'NO') . "\n";
    echo "✅ Timestamps: " . ($model->timestamps ? 'YES' : 'NO') . "\n";
    
    // Check fillable/guarded - THIS IS CRITICAL
    $fillable = $model->getFillable();
    $guarded = $model->getGuarded();
    
    echo "✅ Fillable: " . (empty($fillable) ? 'EMPTY ARRAY (ALL BLOCKED)' : implode(', ', $fillable)) . "\n";
    echo "✅ Guarded: " . (empty($guarded) ? 'EMPTY ARRAY (NOTHING GUARDED)' : implode(', ', $guarded)) . "\n";
    
    // Test if specific fields are fillable
    $testFields = ['email_body', 'subj', 'email_status', 'sms_body'];
    echo "\n📊 Field Fillability Test:\n";
    foreach ($testFields as $field) {
        $isFillable = $model->isFillable($field);
        echo "   - {$field}: " . ($isFillable ? 'FILLABLE' : 'BLOCKED') . "\n";
    }
    
    // Check for mass assignment protection
    echo "\n📊 Mass Assignment Protection:\n";
    echo "   - Total fillable: " . count($fillable) . "\n";
    echo "   - Total guarded: " . count($guarded) . "\n";
    
    if (empty($fillable) && empty($guarded)) {
        echo "   - ⚠️  CRITICAL: No fillable fields defined - ALL MASS ASSIGNMENT BLOCKED!\n";
    } elseif (in_array('*', $guarded)) {
        echo "   - ⚠️  CRITICAL: All fields guarded - MASS ASSIGNMENT BLOCKED!\n";
    }
    
    echo "\n";
    
    // 2. SIMULATE EXACT SAVE OPERATION
    echo "2️⃣ SAVE OPERATION SIMULATION\n";
    echo "=============================\n";
    
    // Get template
    $template = \App\Models\NotificationTemplate::find(1);
    if (!$template) {
        echo "❌ Template ID 1 not found\n";
        exit;
    }
    
    echo "✅ Template loaded: ID {$template->id}\n";
    echo "✅ Current subject: {$template->subj}\n";
    echo "✅ Current email_body length: " . strlen($template->email_body) . "\n";
    
    // Store original values
    $originalSubject = $template->subj;
    $originalEmailBody = $template->email_body;
    $originalUpdatedAt = $template->updated_at;
    
    // Simulate the exact data from your logs
    $newSubject = "Your Account has been Credited [TEST]";
    $newEmailBody = "Balance Added Successfully\r\n\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            Balance Notification\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Balance Added Successfully\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Your account balance has been updated with a new deposit.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},We are pleased to inform you that {{amount}} {{currency}} has been added to your account.Amount: {{amount}} {{currency}}New Balance: {{new_balance}} {{currency}}Transaction ID: {{transaction_id}}Date: {{transaction_date}}The funds are now available in your account.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,\r\n                            MBFX Team\r\n                            \r\n                                If you have any questions, please contact our support team.\r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            \r\n                                Account Settings |\r\n                                Contact Support |\r\n                                Privacy Policy\r\n                            \r\n                            \r\n                                &copy; 2025 MBFX. All rights reserved.\r\n                            \r\n                            \r\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\r\n                                update your preferences.";
    
    echo "\n📊 Testing different assignment methods:\n";
    
    // Method 1: Direct assignment (should work if fillable is configured)
    echo "\n🔧 Method 1: Direct Assignment\n";
    try {
        $template->subj = $newSubject;
        $template->email_body = $newEmailBody;
        
        $dirtyFields = $template->getDirty();
        echo "   - Dirty fields: " . implode(', ', array_keys($dirtyFields)) . "\n";
        echo "   - Subject changed: " . ($template->subj !== $originalSubject ? 'YES' : 'NO') . "\n";
        echo "   - Email body changed: " . ($template->email_body !== $originalEmailBody ? 'YES' : 'NO') . "\n";
        
        // Enable query logging
        \DB::enableQueryLog();
        
        $saveResult = $template->save();
        
        $queries = \DB::getQueryLog();
        echo "   - Save result: " . ($saveResult ? 'SUCCESS' : 'FAILED') . "\n";
        echo "   - Queries executed: " . count($queries) . "\n";
        
        foreach ($queries as $query) {
            echo "   - Query: " . $query['query'] . "\n";
            echo "   - Bindings: " . json_encode($query['bindings']) . "\n";
            echo "   - Time: {$query['time']}ms\n";
        }
        
        // Verify changes
        $template->refresh();
        echo "   - Subject persisted: " . ($template->subj === $newSubject ? 'YES' : 'NO') . "\n";
        echo "   - Email body persisted: " . (strlen($template->email_body) === strlen($newEmailBody) ? 'YES' : 'NO') . "\n";
        
    } catch (\Exception $e) {
        echo "   - ❌ Error: " . $e->getMessage() . "\n";
    }
    
    // Method 2: Mass assignment (will fail if not fillable)
    echo "\n🔧 Method 2: Mass Assignment\n";
    try {
        $template = \App\Models\NotificationTemplate::find(1);
        
        $updateData = [
            'subj' => $newSubject . ' [MASS]',
            'email_body' => $newEmailBody,
            'email_status' => 1
        ];
        
        \DB::enableQueryLog();
        
        $updateResult = $template->update($updateData);
        
        $queries = \DB::getQueryLog();
        echo "   - Update result: " . ($updateResult ? 'SUCCESS' : 'FAILED') . "\n";
        echo "   - Queries executed: " . count($queries) . "\n";
        
        foreach ($queries as $query) {
            echo "   - Query: " . $query['query'] . "\n";
        }
        
    } catch (\Exception $e) {
        echo "   - ❌ Error: " . $e->getMessage() . "\n";
    }
    
    // Method 3: Force fill (bypasses mass assignment protection)
    echo "\n🔧 Method 3: Force Fill\n";
    try {
        $template = \App\Models\NotificationTemplate::find(1);
        
        $template->forceFill([
            'subj' => $newSubject . ' [FORCE]',
            'email_body' => $newEmailBody,
            'email_status' => 1
        ]);
        
        \DB::enableQueryLog();
        
        $saveResult = $template->save();
        
        $queries = \DB::getQueryLog();
        echo "   - Save result: " . ($saveResult ? 'SUCCESS' : 'FAILED') . "\n";
        echo "   - Queries executed: " . count($queries) . "\n";
        
        foreach ($queries as $query) {
            echo "   - Query: " . $query['query'] . "\n";
        }
        
    } catch (\Exception $e) {
        echo "   - ❌ Error: " . $e->getMessage() . "\n";
    }
    
    // Restore original content
    echo "\n🔄 Restoring original content...\n";
    $template = \App\Models\NotificationTemplate::find(1);
    $template->subj = $originalSubject;
    $template->email_body = $originalEmailBody;
    $template->save();
    echo "✅ Original content restored\n";
    
} catch (\Exception $e) {
    echo "❌ Analysis Error: " . $e->getMessage() . "\n";
    echo "❌ File: " . $e->getFile() . "\n";
    echo "❌ Line: " . $e->getLine() . "\n";
}

echo "\n📋 SAVE OPERATION ANALYSIS COMPLETE\n";
echo "====================================\n";
echo "This analysis reveals:\n";
echo "1. Model mass assignment configuration issues\n";
echo "2. Actual save operation behavior\n";
echo "3. Query execution patterns\n";
echo "4. Database persistence verification\n\n";

echo "🔍 Run this script to identify the exact save operation failure.\n";

?>
