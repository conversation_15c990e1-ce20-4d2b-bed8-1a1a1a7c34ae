# 🚀 **LIVE SERVER DEPLOYMENT GUIDE**
## Windows Server 2022 / Plesk / PHP 8.4 Environment

### **📋 PRE-DEPLOYMENT CHECKLIST**

**Before starting deployment, ensure you have:**
- ✅ FTP/SFTP access to live server
- ✅ Plesk control panel access
- ✅ Database access (phpMyAdmin or direct MySQL)
- ✅ Backup of current live files
- ✅ Backup of current database

---

## **🔧 STEP 1: BACKUP CURRENT LIVE ENVIRONMENT**

### **1.1 Backup Files**
```bash
# Via Plesk File Manager or FTP
1. Navigate to httpdocs folder
2. Create backup folder: backup_YYYY-MM-DD
3. Copy all current files to backup folder
```

### **1.2 Backup Database**
```sql
-- Via phpMyAdmin or MySQL command line
1. Export current database
2. Save as: database_backup_YYYY-MM-DD.sql
3. Store in secure location
```

### **1.3 Backup Configuration**
```bash
# Important files to backup:
- .env (environment configuration)
- web.config (IIS configuration)
- composer.json (dependencies)
```

---

## **📁 STEP 2: FILE UPLOAD AND <PERSON><PERSON><PERSON>CEMENT**

### **2.1 Upload Modified Files**

**Upload these specific files to live server:**

```bash
# JavaScript Files
/assets/admin/js/simple-email-editor.js

# PHP Service Files  
/app/Services/TemplateRestorationService.php

# Blade Template Files
/resources/views/admin/notification/edit.blade.php

# CSS Files (if modified)
/assets/admin/css/simple-email-editor.css
```

### **2.2 File Upload Methods**

**Option A: Via Plesk File Manager**
1. Login to Plesk control panel
2. Navigate to Files > File Manager
3. Go to httpdocs directory
4. Upload files to respective folders
5. Overwrite existing files when prompted

**Option B: Via FTP/SFTP**
```bash
# Using FileZilla or similar FTP client
Host: your-server-ip
Username: your-ftp-username
Password: your-ftp-password
Port: 21 (FTP) or 22 (SFTP)

# Upload files maintaining directory structure
```

### **2.3 File Permissions**
```bash
# Set correct permissions via Plesk or FTP
Files: 644 (rw-r--r--)
Directories: 755 (rwxr-xr-x)

# Critical files:
/storage/logs/ - 755
/bootstrap/cache/ - 755
```

---

## **🔧 STEP 3: CONFIGURATION UPDATES**

### **3.1 Environment Configuration (.env)**

**No changes required for this deployment**
- Email template fixes don't require .env changes
- Existing configuration should work

### **3.2 Web.config Verification**

**Ensure web.config supports PHP 8.4:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <handlers>
            <add name="PHP84_via_FastCGI" path="*.php" verb="*" 
                 modules="FastCgiModule" 
                 scriptProcessor="C:\Program Files\PHP\v8.4\php-cgi.exe" 
                 resourceType="Either" />
        </handlers>
        <defaultDocument>
            <files>
                <add value="index.php" />
            </files>
        </defaultDocument>
    </system.webServer>
</configuration>
```

### **3.3 Composer Dependencies**

**Update dependencies if needed:**
```bash
# Via Plesk Terminal or SSH
cd /httpdocs
composer install --no-dev --optimize-autoloader
composer dump-autoload
```

---

## **🗄️ STEP 4: DATABASE UPDATES**

### **4.1 No Database Changes Required**

**This deployment includes:**
- ✅ JavaScript functionality fixes
- ✅ Email template content enhancements  
- ✅ Notification system improvements
- ✅ Logo standardization

**No database schema changes needed.**

### **4.2 Clear Application Cache**

```bash
# Via Plesk Terminal or create PHP script
php artisan cache:clear
php artisan config:clear
php artisan view:clear
php artisan route:clear
```

**Or create clear-cache.php file:**
```php
<?php
// Upload this file to httpdocs and run once
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->call('cache:clear');
$kernel->call('config:clear');
$kernel->call('view:clear');
echo "Cache cleared successfully!";
?>
```

---

## **🧪 STEP 5: TESTING PROCEDURES**

### **5.1 Email Template Editor Testing**

**Test Notification System:**
1. Navigate to: `/admin/setting/notification-templates`
2. Edit any email template
3. Send test email to: `<EMAIL>`
4. **Verify**: Only ONE notification appears (top-right corner)
5. **Verify**: No inline success message below email input

**Test Editor Functionality:**
1. Switch between Visual and HTML editor modes
2. Add content in Visual mode, switch to HTML - verify content preserved
3. Add content in HTML mode, switch to Visual - verify content preserved
4. Save template - verify content saves correctly
5. **Verify**: Both editor modes work without data loss

**Test Logo Standardization:**
1. Send test emails from different templates
2. **Verify**: All logos are consistent size (120px width, 32px height)
3. **Verify**: Logos remain centered and professional
4. Check email in different clients (Gmail, Outlook, etc.)

### **5.2 Cross-Browser Testing**

**Test in multiple browsers:**
- ✅ Chrome (latest)
- ✅ Firefox (latest)  
- ✅ Edge (latest)
- ✅ Safari (if available)

### **5.3 Mobile Responsiveness**

**Test email templates on mobile:**
- ✅ Gmail mobile app
- ✅ Outlook mobile app
- ✅ iPhone Mail app
- ✅ Android default mail app

---

## **🚨 STEP 6: TROUBLESHOOTING**

### **6.1 Common Issues and Solutions**

**Issue: JavaScript not loading**
```bash
Solution:
1. Check file permissions (644)
2. Clear browser cache (Ctrl+F5)
3. Verify file path in browser console
4. Check web.config for static file handling
```

**Issue: Notifications still duplicating**
```bash
Solution:
1. Clear application cache
2. Hard refresh browser (Ctrl+Shift+R)
3. Check browser console for JavaScript errors
4. Verify simple-email-editor.js uploaded correctly
```

**Issue: Editor modes not switching**
```bash
Solution:
1. Check browser console for JavaScript errors
2. Verify jQuery is loading properly
3. Clear browser cache and cookies
4. Test in incognito/private browsing mode
```

**Issue: Email templates not saving**
```bash
Solution:
1. Check PHP error logs in Plesk
2. Verify file permissions on storage directories
3. Check database connection
4. Verify CSRF token is working
```

### **6.2 Log File Locations**

**Check these logs for errors:**
```bash
# Laravel Logs
/storage/logs/laravel.log

# PHP Error Logs (Plesk)
/var/www/vhosts/domain.com/logs/error_log

# IIS Logs (Windows Server)
C:\inetpub\logs\LogFiles\
```

### **6.3 Emergency Rollback**

**If issues occur, rollback procedure:**
```bash
1. Stop website in Plesk
2. Restore files from backup folder
3. Restore database from backup SQL file
4. Clear all caches
5. Restart website in Plesk
6. Test functionality
```

---

## **✅ STEP 7: POST-DEPLOYMENT VERIFICATION**

### **7.1 Functionality Checklist**

**Email Template System:**
- [ ] Single notifications only (no duplicates)
- [ ] Visual editor works and saves content
- [ ] HTML editor works and saves content  
- [ ] Tab switching preserves content
- [ ] Test emails send successfully
- [ ] Logo size consistent across all templates
- [ ] Email links work correctly

**Admin Interface:**
- [ ] Template list loads correctly
- [ ] Edit template page loads without errors
- [ ] Save functionality works
- [ ] Preview functionality works
- [ ] No JavaScript console errors

### **7.2 Performance Verification**

**Check page load times:**
- [ ] Template list page: < 3 seconds
- [ ] Template edit page: < 3 seconds  
- [ ] Email sending: < 5 seconds
- [ ] No memory errors in PHP logs

### **7.3 User Acceptance Testing**

**Have admin users test:**
- [ ] Creating new email templates
- [ ] Editing existing templates
- [ ] Sending test emails
- [ ] Switching between editor modes
- [ ] Saving and previewing templates

---

## **📞 SUPPORT CONTACTS**

**If issues arise during deployment:**

**Technical Support:**
- Email: <EMAIL>
- Emergency: Contact system administrator

**Deployment Support:**
- Check Laravel documentation
- Review Plesk documentation for PHP 8.4
- Windows Server IIS troubleshooting guides

---

## **📝 DEPLOYMENT COMPLETION CHECKLIST**

- [ ] All files uploaded successfully
- [ ] File permissions set correctly
- [ ] Application cache cleared
- [ ] Email template editor tested
- [ ] Notification system verified (single notifications only)
- [ ] Editor modes tested (Visual/HTML)
- [ ] Logo standardization verified
- [ ] Cross-browser testing completed
- [ ] Mobile responsiveness verified
- [ ] Performance benchmarks met
- [ ] User acceptance testing passed
- [ ] Backup procedures documented
- [ ] Rollback plan confirmed

**🎉 Deployment Complete!**

**The email template system now provides:**
- ✅ Single, clear notifications
- ✅ Fully functional Visual and HTML editors
- ✅ Consistent, professional logo sizing
- ✅ Enhanced user experience
- ✅ Windows Server 2022/Plesk compatibility
