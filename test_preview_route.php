<?php
/**
 * Simple test script to verify email template preview route
 * Run this from the Laravel root directory: php test_preview_route.php
 */

// Include Laravel bootstrap
require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

// Create a test request
$request = Illuminate\Http\Request::create('/admin/notification/template/preview/1', 'GET');

try {
    echo "🧪 Testing Email Template Preview Route...\n";
    echo "==========================================\n";
    
    // Test 1: Check if route exists
    echo "📍 Route Registration Test:\n";
    $routes = app('router')->getRoutes();
    $previewRoute = null;
    
    foreach ($routes as $route) {
        if (str_contains($route->uri(), 'notification/template/preview')) {
            $previewRoute = $route;
            break;
        }
    }
    
    if ($previewRoute) {
        echo "✅ Preview route found: " . $previewRoute->uri() . "\n";
        echo "✅ Route name: " . $previewRoute->getName() . "\n";
        echo "✅ Route methods: " . implode(', ', $previewRoute->methods()) . "\n";
        echo "✅ Route action: " . $previewRoute->getActionName() . "\n";
    } else {
        echo "❌ Preview route not found!\n";
        exit(1);
    }
    
    // Test 2: Check if controller method exists
    echo "\n🎛️ Controller Method Test:\n";
    $controller = new App\Http\Controllers\Admin\NotificationController();
    
    if (method_exists($controller, 'templatePreview')) {
        echo "✅ templatePreview method exists in NotificationController\n";
    } else {
        echo "❌ templatePreview method not found in NotificationController\n";
        exit(1);
    }
    
    // Test 3: Check if template exists for testing
    echo "\n📧 Template Existence Test:\n";
    $template = App\Models\NotificationTemplate::first();
    
    if ($template) {
        echo "✅ Test template found: ID {$template->id} - {$template->name}\n";
        echo "✅ Template has content: " . (strlen($template->email_body) > 0 ? 'Yes' : 'No') . "\n";
        
        // Test 4: Generate preview URL
        echo "\n🔗 URL Generation Test:\n";
        $previewUrl = route('admin.setting.notification.template.preview', $template->id);
        echo "✅ Generated preview URL: {$previewUrl}\n";
        
        // Test 5: Check URL structure
        $expectedPath = "/admin/notification/template/preview/{$template->id}";
        if (str_contains($previewUrl, $expectedPath)) {
            echo "✅ URL path is correct\n";
        } else {
            echo "❌ URL path mismatch. Expected: {$expectedPath}\n";
        }
        
    } else {
        echo "❌ No templates found in database\n";
        exit(1);
    }
    
    echo "\n🎉 All tests passed! Preview route should work correctly.\n";
    echo "\n📋 Test Summary:\n";
    echo "- Route registered: ✅\n";
    echo "- Controller method exists: ✅\n";
    echo "- Test template available: ✅\n";
    echo "- URL generation working: ✅\n";
    
    echo "\n🌐 Test the preview URL in your browser:\n";
    echo "http://localhost/mbf.mybrokerforex.com-31052025{$expectedPath}\n";
    echo "https://localhost:443/mbf.mybrokerforex.com-31052025{$expectedPath}\n";
    
} catch (Exception $e) {
    echo "❌ Test failed with error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
    exit(1);
}
