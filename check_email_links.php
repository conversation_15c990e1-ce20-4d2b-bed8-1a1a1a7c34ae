<?php
/**
 * Check Email Template Links
 * Identify and analyze all links in email templates
 */

require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🔗 EMAIL TEMPLATE LINKS ANALYSIS\n";
echo "================================\n\n";

try {
    $templates = App\Models\NotificationTemplate::all();
    echo "📧 Found {$templates->count()} email templates\n\n";

    $allLinks = [];
    $brokenLinkPatterns = [];

    foreach ($templates as $template) {
        echo "📋 Template: {$template->name} (ID: {$template->id})\n";
        echo "   Type: {$template->act}\n";
        
        $content = $template->email_body;
        
        // Find all href links
        preg_match_all('/href=["\']([^"\']*)["\']/', $content, $matches);
        $links = $matches[1];
        
        if (empty($links)) {
            echo "   ⚠️  No links found\n";
        } else {
            echo "   🔗 Links found (" . count($links) . "):\n";
            foreach ($links as $link) {
                echo "      - {$link}\n";
                $allLinks[] = $link;
                
                // Check for problematic link patterns
                if (strpos($link, '#') === 0) {
                    $brokenLinkPatterns[] = "Anchor link without base URL: {$link}";
                }
                if (strpos($link, 'javascript:') === 0) {
                    $brokenLinkPatterns[] = "JavaScript link (won't work in email): {$link}";
                }
                if (strpos($link, 'mailto:') === false && strpos($link, 'http') === false && strpos($link, '/') === 0) {
                    $brokenLinkPatterns[] = "Relative link without domain: {$link}";
                }
                if (empty(trim($link))) {
                    $brokenLinkPatterns[] = "Empty link found";
                }
            }
        }
        
        // Check for action buttons
        preg_match_all('/class=["\'][^"\']*btn[^"\']*["\'][^>]*href=["\']([^"\']*)["\']/', $content, $buttonMatches);
        if (!empty($buttonMatches[1])) {
            echo "   🔘 Action buttons found (" . count($buttonMatches[1]) . "):\n";
            foreach ($buttonMatches[1] as $buttonLink) {
                echo "      - Button: {$buttonLink}\n";
            }
        }
        
        echo "\n";
    }

    // Summary of all unique links
    $uniqueLinks = array_unique($allLinks);
    echo "📊 LINK ANALYSIS SUMMARY\n";
    echo "========================\n";
    echo "Total links found: " . count($allLinks) . "\n";
    echo "Unique links: " . count($uniqueLinks) . "\n\n";

    echo "🔗 ALL UNIQUE LINKS:\n";
    foreach ($uniqueLinks as $link) {
        echo "- {$link}\n";
    }

    if (!empty($brokenLinkPatterns)) {
        echo "\n❌ POTENTIAL LINK ISSUES:\n";
        foreach (array_unique($brokenLinkPatterns) as $issue) {
            echo "- {$issue}\n";
        }
    } else {
        echo "\n✅ No obvious link issues detected\n";
    }

    // Check for common footer elements
    echo "\n🦶 FOOTER ELEMENTS CHECK:\n";
    $footerElements = [
        'Contact Support' => 0,
        'Login to Account' => 0,
        'Privacy Policy' => 0,
        'Terms of Service' => 0,
        'Unsubscribe' => 0,
        'support@' => 0,
        'mbf.mybrokerforex.com' => 0
    ];

    foreach ($templates as $template) {
        $content = strtolower($template->email_body);
        foreach ($footerElements as $element => $count) {
            if (strpos($content, strtolower($element)) !== false) {
                $footerElements[$element]++;
            }
        }
    }

    foreach ($footerElements as $element => $count) {
        $percentage = round(($count / $templates->count()) * 100);
        echo "- {$element}: {$count}/{$templates->count()} templates ({$percentage}%)\n";
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n🎯 RECOMMENDATIONS:\n";
echo "===================\n";
echo "1. Replace relative links with absolute URLs\n";
echo "2. Ensure all action buttons have working links\n";
echo "3. Add proper footer links (Contact Support, Privacy Policy, etc.)\n";
echo "4. Test all links in actual email clients\n";
echo "5. Use Laravel route helpers for internal links\n";

?>
