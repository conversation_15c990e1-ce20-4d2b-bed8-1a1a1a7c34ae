<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SymbolGroup extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'symbols_json',
        'status'
    ];

    protected $casts = [
        'symbols_json' => 'array',
        'status' => 'boolean'
    ];

    /**
     * Get symbols in this group
     */
    public function symbols()
    {
        if (empty($this->symbols_json)) {
            return collect();
        }
        return Symbol::whereIn('id', $this->symbols_json)->get();
    }

    /**
     * Get rebate rules for this symbol group
     */
    public function rebateRules()
    {
        return $this->hasMany(RebateRule::class);
    }

    /**
     * Scope for active groups
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Get all active symbol groups
     */
    public static function getActiveGroups()
    {
        return self::active()->orderBy('name')->get();
    }

    /**
     * Check if a symbol belongs to this group
     */
    public function hasSymbol($symbol)
    {
        return in_array($symbol, $this->symbols_json ?? []);
    }

    /**
     * Add symbol to group
     */
    public function addSymbol($symbol)
    {
        $symbols = $this->symbols_json ?? [];
        if (!in_array($symbol, $symbols)) {
            $symbols[] = $symbol;
            $this->symbols_json = $symbols;
            $this->save();
        }
        return $this;
    }

    /**
     * Remove symbol from group
     */
    public function removeSymbol($symbol)
    {
        $symbols = $this->symbols_json ?? [];
        $symbols = array_filter($symbols, function($s) use ($symbol) {
            return $s !== $symbol;
        });
        $this->symbols_json = array_values($symbols);
        $this->save();
        return $this;
    }

    /**
     * Get symbols as comma-separated string
     */
    public function getSymbolsStringAttribute()
    {
        return implode(', ', $this->symbols_json ?? []);
    }

    /**
     * Find symbol group by symbol
     */
    public static function findBySymbol($symbol)
    {
        return self::active()->get()->filter(function($group) use ($symbol) {
            return $group->hasSymbol($symbol);
        })->first();
    }
}
