<?php
// Comprehensive Error Detection Script for Email Template System
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "🔍 COMPREHENSIVE EMAIL TEMPLATE ERROR DETECTION\n";
echo "================================================\n\n";

// Function to capture and analyze page content
function analyzePageForErrors($url, $templateId = null) {
    $context = stream_context_create([
        'http' => [
            'timeout' => 15,
            'ignore_errors' => true,
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ],
        'ssl' => [
            'verify_peer' => false,
            'verify_peer_name' => false
        ]
    ]);
    
    $content = @file_get_contents($url, false, $context);
    $httpCode = 200;
    
    // Get HTTP response code
    if (isset($http_response_header)) {
        foreach ($http_response_header as $header) {
            if (preg_match('/HTTP\/\d\.\d\s+(\d+)/', $header, $matches)) {
                $httpCode = intval($matches[1]);
                break;
            }
        }
    }
    
    $errors = [];
    $warnings = [];
    
    if ($content === false) {
        $errors[] = "Cannot access URL";
        return ['errors' => $errors, 'warnings' => $warnings, 'http_code' => $httpCode];
    }
    
    // Check for PHP errors
    if (preg_match_all('/Parse error:([^<]+)/i', $content, $matches)) {
        foreach ($matches[1] as $error) {
            $errors[] = "PHP Parse Error: " . trim($error);
        }
    }
    
    if (preg_match_all('/Fatal error:([^<]+)/i', $content, $matches)) {
        foreach ($matches[1] as $error) {
            $errors[] = "PHP Fatal Error: " . trim($error);
        }
    }
    
    if (preg_match_all('/Warning:([^<]+)/i', $content, $matches)) {
        foreach ($matches[1] as $warning) {
            $warnings[] = "PHP Warning: " . trim($warning);
        }
    }
    
    // Check for JavaScript syntax errors
    if (preg_match_all('/SyntaxError:([^<\n]+)/i', $content, $matches)) {
        foreach ($matches[1] as $error) {
            $errors[] = "JavaScript Syntax Error: " . trim($error);
        }
    }
    
    if (preg_match_all('/Uncaught ([^<\n]+)/i', $content, $matches)) {
        foreach ($matches[1] as $error) {
            $errors[] = "JavaScript Uncaught Error: " . trim($error);
        }
    }
    
    // Check for specific Blade/PHP syntax issues
    if (strpos($content, 'unexpected token') !== false) {
        $errors[] = "JavaScript unexpected token error detected";
    }
    
    if (strpos($content, 'Undefined constant') !== false) {
        $errors[] = "PHP undefined constant error detected";
    }
    
    if (strpos($content, 'Unclosed') !== false) {
        $errors[] = "Unclosed bracket/parenthesis error detected";
    }
    
    // Check for 500 errors
    if ($httpCode >= 500) {
        $errors[] = "HTTP $httpCode Server Error";
    }
    
    return [
        'errors' => $errors,
        'warnings' => $warnings,
        'http_code' => $httpCode,
        'content_length' => strlen($content)
    ];
}

// Test 1: Check all template edit pages (1-45)
echo "1. TESTING ALL TEMPLATE EDIT PAGES (1-45)\n";
echo "==========================================\n";

$templateErrors = [];
$templateSuccess = 0;
$templateTotal = 45;

for ($i = 1; $i <= $templateTotal; $i++) {
    $url = "https://localhost/mbf.mybrokerforex.com-31052025/admin/notification/template/edit/$i";
    echo "Testing Template #$i... ";
    
    $result = analyzePageForErrors($url, $i);
    
    if (!empty($result['errors'])) {
        echo "❌ ERRORS FOUND\n";
        foreach ($result['errors'] as $error) {
            echo "   - $error\n";
        }
        $templateErrors[$i] = $result['errors'];
    } else {
        echo "✅ PASS\n";
        $templateSuccess++;
    }
    
    if (!empty($result['warnings'])) {
        echo "   ⚠️  WARNINGS:\n";
        foreach ($result['warnings'] as $warning) {
            echo "   - $warning\n";
        }
    }
}

echo "\nTemplate Edit Pages Summary: $templateSuccess/$templateTotal passed\n\n";

// Test 2: Check Global Template
echo "2. TESTING GLOBAL TEMPLATE\n";
echo "==========================\n";

$globalUrl = "https://localhost/mbf.mybrokerforex.com-31052025/admin/notification/global";
echo "Testing Global Template... ";

$globalResult = analyzePageForErrors($globalUrl);

if (!empty($globalResult['errors'])) {
    echo "❌ ERRORS FOUND\n";
    foreach ($globalResult['errors'] as $error) {
        echo "   - $error\n";
    }
} else {
    echo "✅ PASS\n";
}

if (!empty($globalResult['warnings'])) {
    echo "   ⚠️  WARNINGS:\n";
    foreach ($globalResult['warnings'] as $warning) {
        echo "   - $warning\n";
    }
}

// Test 3: Check Templates List
echo "\n3. TESTING TEMPLATES LIST\n";
echo "=========================\n";

$templatesUrl = "https://localhost/mbf.mybrokerforex.com-31052025/admin/notification/templates";
echo "Testing Templates List... ";

$templatesResult = analyzePageForErrors($templatesUrl);

if (!empty($templatesResult['errors'])) {
    echo "❌ ERRORS FOUND\n";
    foreach ($templatesResult['errors'] as $error) {
        echo "   - $error\n";
    }
} else {
    echo "✅ PASS\n";
}

if (!empty($templatesResult['warnings'])) {
    echo "   ⚠️  WARNINGS:\n";
    foreach ($templatesResult['warnings'] as $warning) {
        echo "   - $warning\n";
    }
}

// Test 4: File Syntax Validation
echo "\n4. FILE SYNTAX VALIDATION\n";
echo "=========================\n";

$files = [
    'resources/views/admin/notification/edit.blade.php',
    'resources/views/admin/notification/global_template.blade.php',
    'resources/views/admin/notification/templates.blade.php'
];

foreach ($files as $file) {
    echo "Checking $file... ";
    
    $output = [];
    $returnCode = 0;
    exec("php -l \"$file\" 2>&1", $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "✅ SYNTAX OK\n";
    } else {
        echo "❌ SYNTAX ERROR\n";
        foreach ($output as $line) {
            echo "   - $line\n";
        }
    }
}

// Final Summary
echo "\n📊 FINAL SUMMARY\n";
echo "================\n";

$totalErrors = count($templateErrors);
$globalErrors = !empty($globalResult['errors']) ? 1 : 0;
$templatesErrors = !empty($templatesResult['errors']) ? 1 : 0;

echo "Template Edit Pages: " . ($templateSuccess == $templateTotal ? "✅ ALL PASS" : "❌ $totalErrors FAILED") . "\n";
echo "Global Template: " . ($globalErrors == 0 ? "✅ PASS" : "❌ FAILED") . "\n";
echo "Templates List: " . ($templatesErrors == 0 ? "✅ PASS" : "❌ FAILED") . "\n";

$overallStatus = ($totalErrors == 0 && $globalErrors == 0 && $templatesErrors == 0) ? "✅ ALL SYSTEMS OPERATIONAL" : "❌ ERRORS DETECTED";
echo "\nOverall Status: $overallStatus\n";

if ($totalErrors > 0) {
    echo "\n🔧 TEMPLATES WITH ERRORS:\n";
    foreach ($templateErrors as $templateId => $errors) {
        echo "Template #$templateId:\n";
        foreach ($errors as $error) {
            echo "   - $error\n";
        }
    }
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "Error detection completed at " . date('Y-m-d H:i:s') . "\n";
?>
