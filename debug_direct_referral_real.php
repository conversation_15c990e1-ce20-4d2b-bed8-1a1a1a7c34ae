<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use Illuminate\Http\Request;

echo "=== REAL DEBUGGING OF DIRECT REFERRAL ISSUES ===\n\n";

// Test the actual form submission
echo "🔍 TESTING ACTUAL FORM SUBMISSION\n";
echo "=================================\n";

$testUser = User::find(11178); // sufyan aslam
$potentialReferral = User::find(1); // Brokeredge Test

echo "Test scenario:\n";
echo "  - User to add referral to: {$testUser->fullname} (ID: {$testUser->id})\n";
echo "  - Potential referral: {$potentialReferral->fullname} (ID: {$potentialReferral->id})\n";

// Simulate the exact form submission
$request = new Request();
$request->merge([
    'referral_user' => $potentialReferral->id,
    'assign_as_sub_ib' => false,
    'search_method' => 'user',
    '_token' => 'test-token'
]);

echo "\nSimulated form data:\n";
echo "  - referral_user: " . $request->get('referral_user') . "\n";
echo "  - assign_as_sub_ib: " . ($request->get('assign_as_sub_ib') ? 'true' : 'false') . "\n";
echo "  - search_method: " . $request->get('search_method') . "\n";

// Test validation
try {
    $request->validate([
        'referral_user' => 'required|exists:users,id',
        'assign_as_sub_ib' => 'nullable|boolean',
        'search_method' => 'nullable|in:user,mt5,email',
        'mt5_login' => 'nullable|string',
        'email_search' => 'nullable|email'
    ]);
    echo "\n✅ VALIDATION PASSED!\n";
} catch (Exception $e) {
    echo "\n❌ VALIDATION FAILED: " . $e->getMessage() . "\n";
}

// Test if user already has this referral
$existingReferral = User::where('id', $potentialReferral->id)->where('ref_by', $testUser->id)->first();
echo "\nExisting referral check: " . ($existingReferral ? 'Already exists' : 'New referral') . "\n";

// Test the actual controller method
echo "\n🔍 TESTING CONTROLLER METHOD\n";
echo "============================\n";

try {
    $controller = new \App\Http\Controllers\Admin\ManageUsersController();
    
    // We can't actually call the method without proper authentication, but we can check if it exists
    $reflection = new ReflectionClass($controller);
    if ($reflection->hasMethod('addDirectReferral')) {
        echo "✅ addDirectReferral method exists\n";
        
        $method = $reflection->getMethod('addDirectReferral');
        $parameters = $method->getParameters();
        echo "✅ Method parameters:\n";
        foreach($parameters as $param) {
            echo "  - " . $param->getName() . " (" . ($param->getType() ? $param->getType()->getName() : 'mixed') . ")\n";
        }
    } else {
        echo "❌ addDirectReferral method missing\n";
    }
} catch (Exception $e) {
    echo "❌ Controller error: " . $e->getMessage() . "\n";
}

// Test AJAX endpoints directly
echo "\n🔍 TESTING AJAX ENDPOINTS\n";
echo "=========================\n";

// Test MT5 accounts endpoint
try {
    $ajaxRequest = new Request();
    $ajaxRequest->headers->set('X-Requested-With', 'XMLHttpRequest');
    
    $controller = new \App\Http\Controllers\Admin\ManageUsersController();
    $response = $controller->getAllMT5Accounts($ajaxRequest);
    $data = json_decode($response->getContent(), true);
    
    if ($data['success']) {
        echo "✅ MT5 accounts endpoint working: " . count($data['accounts']) . " accounts\n";
    } else {
        echo "❌ MT5 accounts endpoint failed: " . $data['message'] . "\n";
    }
} catch (Exception $e) {
    echo "❌ MT5 accounts endpoint error: " . $e->getMessage() . "\n";
}

// Test MT5 search endpoint
try {
    $searchRequest = new Request();
    $searchRequest->merge(['mt5_id' => '878046']);
    $searchRequest->headers->set('X-Requested-With', 'XMLHttpRequest');
    
    $searchResponse = $controller->searchByMT5($searchRequest);
    $searchData = json_decode($searchResponse->getContent(), true);
    
    if ($searchData['success']) {
        echo "✅ MT5 search endpoint working: " . count($searchData['users']) . " results\n";
    } else {
        echo "❌ MT5 search endpoint failed\n";
    }
} catch (Exception $e) {
    echo "❌ MT5 search endpoint error: " . $e->getMessage() . "\n";
}

// Check route registration
echo "\n🔍 CHECKING ROUTE REGISTRATION\n";
echo "==============================\n";

$routes = [
    'admin.users.mt5.accounts.all',
    'admin.users.search.mt5',
    'admin.users.referral.add'
];

foreach($routes as $routeName) {
    try {
        $url = route($routeName, ['id' => 11178]);
        echo "✅ Route '{$routeName}' exists: {$url}\n";
    } catch (Exception $e) {
        echo "❌ Route '{$routeName}' missing: " . $e->getMessage() . "\n";
    }
}

// Check if the view file exists and is readable
echo "\n🔍 CHECKING VIEW FILE\n";
echo "====================\n";

$viewPath = 'resources/views/components/user-detail/referral.blade.php';
if (file_exists($viewPath)) {
    echo "✅ View file exists: {$viewPath}\n";
    echo "✅ File size: " . filesize($viewPath) . " bytes\n";
    echo "✅ Last modified: " . date('Y-m-d H:i:s', filemtime($viewPath)) . "\n";
} else {
    echo "❌ View file missing: {$viewPath}\n";
}

// Check for JavaScript errors in the view
$viewContent = file_get_contents($viewPath);
if (strpos($viewContent, 'loadAllMT5Accounts') !== false) {
    echo "✅ loadAllMT5Accounts function found in view\n";
} else {
    echo "❌ loadAllMT5Accounts function missing from view\n";
}

if (strpos($viewContent, 'mt5-search-section') !== false) {
    echo "✅ MT5 search section found in view\n";
} else {
    echo "❌ MT5 search section missing from view\n";
}

echo "\n🚨 CRITICAL DEBUGGING STEPS:\n";
echo "=============================\n";
echo "1. Open browser developer tools (F12)\n";
echo "2. Go to: https://localhost/mbf.mybrokerforex.com-********/admin/users/detail/11178\n";
echo "3. Check Console tab for JavaScript errors\n";
echo "4. Click Direct Referrals tab → Add Referral button\n";
echo "5. Look for console messages starting with '🔧'\n";
echo "6. Click 'MT5 Account' tab and watch console\n";
echo "7. Check Network tab for AJAX requests\n";
echo "8. Try to submit form and check console for errors\n";

echo "\n🔧 IMMEDIATE ACTIONS:\n";
echo "=====================\n";
echo "1. Hard refresh browser (Ctrl+Shift+R)\n";
echo "2. Clear browser cache completely\n";
echo "3. Check if JavaScript is enabled\n";
echo "4. Verify jQuery and Bootstrap are loaded\n";
echo "5. Check for any JavaScript syntax errors\n";

echo "\n📋 MANUAL VERIFICATION CHECKLIST:\n";
echo "==================================\n";
echo "[ ] Can you see the 'Add Referral' button?\n";
echo "[ ] Does the modal open when clicking 'Add Referral'?\n";
echo "[ ] Can you see 'Select Users' and 'MT5 Account' tabs?\n";
echo "[ ] Does clicking 'MT5 Account' tab show the MT5 section?\n";
echo "[ ] Are there any console errors when switching tabs?\n";
echo "[ ] Does the form submit when clicking 'Add Referral' button?\n";
echo "[ ] What exact error message appears?\n";

echo "\nIf none of the above work, the issue is likely:\n";
echo "1. Browser caching old JavaScript\n";
echo "2. JavaScript syntax error preventing execution\n";
echo "3. Missing jQuery or Bootstrap dependencies\n";
echo "4. CSRF token issues\n";
echo "5. Route configuration problems\n";

echo "\n🎯 NEXT STEPS:\n";
echo "==============\n";
echo "Please check the browser console and report:\n";
echo "1. Any JavaScript errors\n";
echo "2. Whether you see the '🔧' debug messages\n";
echo "3. What happens when you click the MT5 Account tab\n";
echo "4. The exact error message when submitting the form\n";
echo "5. Any failed network requests in the Network tab\n";
