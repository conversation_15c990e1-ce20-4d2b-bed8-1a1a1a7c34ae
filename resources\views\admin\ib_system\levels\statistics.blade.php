@extends('admin.layouts.app')

@section('panel')
<div class="row">
    <div class="col-lg-12">
        <div class="card b-radius--10">
            <div class="card-header">
                <h5 class="card-title">@lang('IB Level Statistics')</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive--sm table-responsive">
                    <table class="table table--light style--two">
                        <thead>
                            <tr>
                                <th>@lang('Level')</th>
                                <th>@lang('Level Name')</th>
                                <th>@lang('Commission %')</th>
                                <th>@lang('Total Commissions')</th>
                                <th>@lang('Total Trades')</th>
                                <th>@lang('Active IBs')</th>
                                <th>@lang('Average Commission')</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($stats as $stat)
                            <tr>
                                <td>
                                    <span class="badge badge--primary">{{ $stat['level']->level }}</span>
                                </td>
                                <td>{{ $stat['level']->name }}</td>
                                <td>{{ $stat['level']->commission_percent }}%</td>
                                <td>
                                    <span class="text--success">
                                        {{ showAmount($stat['total_commissions']) }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge badge--info">{{ $stat['total_trades'] }}</span>
                                </td>
                                <td>
                                    <span class="badge badge--warning">{{ $stat['active_ibs'] }}</span>
                                </td>
                                <td>
                                    <span class="text--primary">
                                        {{ showAmount($stat['avg_commission']) }}
                                    </span>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="7" class="text-center">@lang('No statistics available yet')</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mt-4">
    <div class="col-xl-3 col-lg-4 col-sm-6">
        <div class="widget-two style--two box--shadow2 b-radius--5 bg--info">
            <div class="widget-two__icon b-radius--5 bg--info">
                <i class="las la-layer-group"></i>
            </div>
            <div class="widget-two__content">
                <h3 class="text-white">{{ collect($stats)->sum('total_trades') }}</h3>
                <p class="text-white">@lang('Total Trades')</p>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-4 col-sm-6">
        <div class="widget-two style--two box--shadow2 b-radius--5 bg--success">
            <div class="widget-two__icon b-radius--5 bg--success">
                <i class="las la-dollar-sign"></i>
            </div>
            <div class="widget-two__content">
                <h3 class="text-white">{{ showAmount(collect($stats)->sum('total_commissions')) }}</h3>
                <p class="text-white">@lang('Total Commissions')</p>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-4 col-sm-6">
        <div class="widget-two style--two box--shadow2 b-radius--5 bg--warning">
            <div class="widget-two__icon b-radius--5 bg--warning">
                <i class="las la-users"></i>
            </div>
            <div class="widget-two__content">
                <h3 class="text-white">{{ collect($stats)->sum('active_ibs') }}</h3>
                <p class="text-white">@lang('Active IBs')</p>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-4 col-sm-6">
        <div class="widget-two style--two box--shadow2 b-radius--5 bg--primary">
            <div class="widget-two__icon b-radius--5 bg--primary">
                <i class="las la-chart-line"></i>
            </div>
            <div class="widget-two__content">
                <h3 class="text-white">
                    {{ collect($stats)->where('total_trades', '>', 0)->avg('avg_commission') ? showAmount(collect($stats)->where('total_trades', '>', 0)->avg('avg_commission')) : '0.00' }}
                </h3>
                <p class="text-white">@lang('Avg Commission')</p>
            </div>
        </div>
    </div>
</div>

<!-- Level Performance Chart -->
<div class="row mt-4">
    <div class="col-lg-12">
        <div class="card b-radius--10">
            <div class="card-header">
                <h5 class="card-title">@lang('Level Performance Overview')</h5>
            </div>
            <div class="card-body">
                @if(collect($stats)->sum('total_trades') > 0)
                <div class="row">
                    @foreach($stats as $stat)
                    <div class="col-md-4 mb-3">
                        <div class="text-center">
                            <h6>{{ $stat['level']->name }}</h6>
                            <div class="progress mb-2" style="height: 20px;">
                                @php
                                    $maxCommission = collect($stats)->max('total_commissions');
                                    $percentage = $maxCommission > 0 ? ($stat['total_commissions'] / $maxCommission) * 100 : 0;
                                @endphp
                                <div class="progress-bar bg--primary" role="progressbar" 
                                     style="width: {{ $percentage }}%" 
                                     aria-valuenow="{{ $percentage }}" 
                                     aria-valuemin="0" 
                                     aria-valuemax="100">
                                    {{ number_format($percentage, 1) }}%
                                </div>
                            </div>
                            <small class="text-muted">
                                {{ showAmount($stat['total_commissions']) }} from {{ $stat['total_trades'] }} trades
                            </small>
                        </div>
                    </div>
                    @endforeach
                </div>
                @else
                <div class="text-center py-4">
                    <i class="las la-chart-bar text-muted" style="font-size: 4rem;"></i>
                    <h5 class="text-muted mt-3">@lang('No commission data available yet')</h5>
                    <p class="text-muted">@lang('Statistics will appear once IBs start generating commissions')</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('breadcrumb-plugins')
<div class="d-inline">
    <a href="{{ route('admin.ib.levels.index') }}" class="btn btn--dark">
        <i class="las la-arrow-left"></i> @lang('Back to Levels')
    </a>
</div>
@endpush
