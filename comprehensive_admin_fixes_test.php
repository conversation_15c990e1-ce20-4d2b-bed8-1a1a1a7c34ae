<?php

/**
 * COMPREHENSIVE TEST SCRIPT FOR ADMIN USER LIST PERMANENT FIXES
 * 
 * This script tests all four critical issues and verifies permanent solutions:
 * 1. Duplicate Email Consolidation (CRITICAL)
 * 2. Real-time Auto Sync Implementation  
 * 3. MT5 Search Functionality
 * 4. Latest Data Synchronization
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use App\Models\User;
use Carbon\Carbon;

echo "🧪 COMPREHENSIVE ADMIN USER LIST FIXES TEST\n";
echo "==========================================\n\n";

// Test 1: Duplicate Email Consolidation (CRITICAL)
echo "📋 TEST 1: DUPLICATE EMAIL CONSOLIDATION (CRITICAL)\n";
echo "---------------------------------------------------\n";

try {
    // Check current duplicate status
    $duplicateEmails = DB::table('users')
        ->select('email', DB::raw('COUNT(*) as count'))
        ->whereNotNull('email')
        ->where('email', '!=', '')
        ->groupBy('email')
        ->having('count', '>', 1)
        ->get();

    echo "📊 Current duplicate emails: " . count($duplicateEmails) . "\n";
    
    if (count($duplicateEmails) > 0) {
        echo "⚠️ DUPLICATES FOUND - Testing consolidation...\n";
        
        // Show sample duplicates
        foreach ($duplicateEmails->take(3) as $duplicate) {
            echo "   - {$duplicate->email}: {$duplicate->count} accounts\n";
            
            $users = User::where('email', $duplicate->email)
                ->select('id', 'username', 'mt5_login', 'created_at', 'all_mt5_accounts')
                ->orderBy('created_at', 'desc')
                ->get();
                
            foreach ($users as $user) {
                $mt5Info = $user->mt5_login ? "MT5: {$user->mt5_login}" : "No MT5";
                $allMT5 = $user->all_mt5_accounts ? " (All: {$user->all_mt5_accounts})" : "";
                echo "     * ID {$user->id}: {$user->username} - {$mt5Info}{$allMT5}\n";
            }
        }
        
        echo "❌ ISSUE 1 NOT RESOLVED: Duplicates still exist\n";
    } else {
        echo "✅ ISSUE 1 RESOLVED: No duplicate emails found\n";
    }
    
} catch (Exception $e) {
    echo "❌ Test 1 failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 2: Real-time Auto Sync Implementation
echo "📋 TEST 2: REAL-TIME AUTO SYNC IMPLEMENTATION\n";
echo "---------------------------------------------\n";

try {
    // Check if auto sync is configured
    $cronJobs = file_exists(base_path('app/Console/Kernel.php')) ? 
        file_get_contents(base_path('app/Console/Kernel.php')) : '';
    
    if (strpos($cronJobs, 'mt5:sync-users') !== false && strpos($cronJobs, 'everyMinute') !== false) {
        echo "✅ Auto sync configured: Every 1 minute\n";
    } else {
        echo "❌ Auto sync not properly configured\n";
    }
    
    // Check last sync time
    $lastSync = Cache::get('mt5_sync_stats.last_sync');
    if ($lastSync) {
        $minutesAgo = Carbon::parse($lastSync)->diffInMinutes(now());
        echo "📅 Last sync: {$minutesAgo} minutes ago\n";
        
        if ($minutesAgo <= 2) {
            echo "✅ ISSUE 2 RESOLVED: Auto sync is working (recent sync)\n";
        } elseif ($minutesAgo <= 10) {
            echo "⚠️ ISSUE 2 PARTIAL: Auto sync working but not optimal\n";
        } else {
            echo "❌ ISSUE 2 NOT RESOLVED: Auto sync not working properly\n";
        }
    } else {
        echo "❌ ISSUE 2 NOT RESOLVED: No sync statistics found\n";
    }
    
} catch (Exception $e) {
    echo "❌ Test 2 failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: MT5 Search Functionality
echo "📋 TEST 3: MT5 SEARCH FUNCTIONALITY\n";
echo "-----------------------------------\n";

try {
    // Get sample MT5 accounts for testing
    $sampleMT5Accounts = User::whereNotNull('mt5_login')
        ->select('mt5_login', 'all_mt5_accounts', 'email')
        ->limit(5)
        ->get();
    
    echo "🔍 Testing search with " . count($sampleMT5Accounts) . " sample MT5 accounts:\n";
    
    $searchSuccessCount = 0;
    $totalSearchTests = 0;
    
    foreach ($sampleMT5Accounts as $testUser) {
        $totalSearchTests++;
        
        // Test primary MT5 account search
        $searchResult = User::where('mt5_login', 'like', "%{$testUser->mt5_login}%")
            ->orWhere('all_mt5_accounts', 'like', "%{$testUser->mt5_login}%")
            ->count();
        
        if ($searchResult > 0) {
            $searchSuccessCount++;
            echo "   ✅ {$testUser->mt5_login}: Found\n";
        } else {
            echo "   ❌ {$testUser->mt5_login}: NOT FOUND\n";
        }
        
        // Test all MT5 accounts search if available
        if ($testUser->all_mt5_accounts) {
            $allAccounts = explode(',', $testUser->all_mt5_accounts);
            foreach ($allAccounts as $account) {
                $totalSearchTests++;
                $searchResult = User::where('all_mt5_accounts', 'like', "%{$account}%")
                    ->count();
                
                if ($searchResult > 0) {
                    $searchSuccessCount++;
                    echo "   ✅ {$account} (from all_mt5_accounts): Found\n";
                } else {
                    echo "   ❌ {$account} (from all_mt5_accounts): NOT FOUND\n";
                }
            }
        }
    }
    
    $searchSuccessRate = $totalSearchTests > 0 ? ($searchSuccessCount / $totalSearchTests) * 100 : 0;
    echo "📊 Search success rate: {$searchSuccessRate}% ({$searchSuccessCount}/{$totalSearchTests})\n";
    
    if ($searchSuccessRate >= 95) {
        echo "✅ ISSUE 3 RESOLVED: MT5 search functionality working excellently\n";
    } elseif ($searchSuccessRate >= 80) {
        echo "⚠️ ISSUE 3 PARTIAL: MT5 search working but needs improvement\n";
    } else {
        echo "❌ ISSUE 3 NOT RESOLVED: MT5 search functionality failing\n";
    }
    
} catch (Exception $e) {
    echo "❌ Test 3 failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 4: Latest Data Synchronization
echo "📋 TEST 4: LATEST DATA SYNCHRONIZATION\n";
echo "--------------------------------------\n";

try {
    // Check MT5 connection and latest data
    $mt5Connection = DB::connection('mbf-dbmt5');
    $latestMT5Count = $mt5Connection->table('mt5_users')
        ->whereNotNull('Email')
        ->where('Email', '!=', '')
        ->count();
    
    $localMT5Count = User::whereNotNull('mt5_login')->count();
    
    echo "📊 MT5 Database users: {$latestMT5Count}\n";
    echo "📊 Local MT5 users: {$localMT5Count}\n";
    
    $syncCoverage = $latestMT5Count > 0 ? ($localMT5Count / $latestMT5Count) * 100 : 0;
    echo "📊 Sync coverage: {$syncCoverage}%\n";
    
    // Check for recent syncs
    $recentSyncs = User::whereNotNull('mt5_synced_at')
        ->where('mt5_synced_at', '>', now()->subHours(1))
        ->count();
    
    echo "📊 Recent syncs (1 hour): {$recentSyncs}\n";
    
    if ($syncCoverage >= 95 && $recentSyncs > 0) {
        echo "✅ ISSUE 4 RESOLVED: Latest data synchronization working\n";
    } elseif ($syncCoverage >= 80) {
        echo "⚠️ ISSUE 4 PARTIAL: Data sync working but coverage could be better\n";
    } else {
        echo "❌ ISSUE 4 NOT RESOLVED: Latest data synchronization failing\n";
    }
    
} catch (Exception $e) {
    echo "❌ Test 4 failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Performance Test
echo "📋 PERFORMANCE TEST\n";
echo "-------------------\n";

try {
    $startTime = microtime(true);
    
    // Simulate the admin user list query
    $users = User::whereIn('users.id', function($query) {
            $query->select(DB::raw('MAX(id)'))
                  ->from('users')
                  ->whereNotNull('email')
                  ->where('email', '!=', '')
                  ->groupBy('email');
        })
        ->with(['wallets:id,user_id,currency_id,balance', 'wallets.currency:id,symbol,sign'])
        ->orderBy('created_at', 'desc')
        ->limit(50)
        ->get();
    
    $endTime = microtime(true);
    $duration = round(($endTime - $startTime) * 1000, 2);
    
    echo "⚡ Query performance: {$duration}ms for " . count($users) . " users\n";
    
    if ($duration < 3000) {
        echo "✅ Performance: EXCELLENT (under 3 seconds)\n";
    } else {
        echo "⚠️ Performance: SLOW (over 3 seconds)\n";
    }
    
} catch (Exception $e) {
    echo "❌ Performance test failed: " . $e->getMessage() . "\n";
}

echo "\n";
echo "🎉 COMPREHENSIVE TEST COMPLETED\n";
echo "===============================\n";

// Summary
echo "💡 SUMMARY:\n";
echo "- Issue 1 (Duplicates): Check results above\n";
echo "- Issue 2 (Auto Sync): Check results above\n";
echo "- Issue 3 (MT5 Search): Check results above\n";
echo "- Issue 4 (Latest Data): Check results above\n";
echo "\n";
echo "🚀 NEXT STEPS:\n";
echo "1. Run migration: php artisan migrate\n";
echo "2. Run sync: php artisan mt5:sync-users --fast --limit=1000\n";
echo "3. Test admin user list in browser\n";
echo "4. Verify search functionality\n";
echo "5. Check duplicate consolidation\n";
