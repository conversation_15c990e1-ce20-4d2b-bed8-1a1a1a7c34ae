@extends('admin.layouts.app')

@section('panel')
<div class="row">
    <div class="col-lg-12">
        <div class="card b-radius--10">
            <div class="card-header">
                <h5 class="card-title">@lang('IB Group Statistics')</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive--sm table-responsive">
                    <table class="table table--light style--two">
                        <thead>
                            <tr>
                                <th>@lang('Group Name')</th>
                                <th>@lang('Multiplier')</th>
                                <th>@lang('Max Levels')</th>
                                <th>@lang('Total IBs')</th>
                                <th>@lang('Active IBs')</th>
                                <th>@lang('Pending IBs')</th>
                                <th>@lang('Total Commissions')</th>
                                <th>@lang('Active Rules')</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($stats as $stat)
                            <tr>
                                <td>
                                    <strong>{{ $stat['group']->name }}</strong>
                                    @if($stat['group']->status)
                                        <span class="badge badge--success badge--sm ml-1">@lang('Active')</span>
                                    @else
                                        <span class="badge badge--danger badge--sm ml-1">@lang('Inactive')</span>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge badge--primary">{{ $stat['group']->commission_multiplier }}x</span>
                                </td>
                                <td>
                                    <span class="badge badge--info">{{ $stat['group']->max_levels }}</span>
                                </td>
                                <td>
                                    <span class="badge badge--secondary">{{ $stat['stats']['total_ibs'] }}</span>
                                </td>
                                <td>
                                    <span class="badge badge--success">{{ $stat['stats']['active_ibs'] }}</span>
                                </td>
                                <td>
                                    <span class="badge badge--warning">{{ $stat['stats']['pending_ibs'] }}</span>
                                </td>
                                <td>
                                    <span class="text--success">
                                        {{ showAmount($stat['total_commissions']) }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge badge--info">{{ $stat['stats']['active_rules'] }}</span>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="8" class="text-center">@lang('No IB groups found')</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mt-4">
    <div class="col-xl-3 col-lg-4 col-sm-6">
        <div class="widget-two style--two box--shadow2 b-radius--5 bg--primary">
            <div class="widget-two__icon b-radius--5 bg--primary">
                <i class="las la-layer-group"></i>
            </div>
            <div class="widget-two__content">
                <h3 class="text-white">{{ count($stats) }}</h3>
                <p class="text-white">@lang('Total Groups')</p>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-4 col-sm-6">
        <div class="widget-two style--two box--shadow2 b-radius--5 bg--success">
            <div class="widget-two__icon b-radius--5 bg--success">
                <i class="las la-users"></i>
            </div>
            <div class="widget-two__content">
                <h3 class="text-white">{{ collect($stats)->sum('stats.active_ibs') }}</h3>
                <p class="text-white">@lang('Total Active IBs')</p>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-4 col-sm-6">
        <div class="widget-two style--two box--shadow2 b-radius--5 bg--warning">
            <div class="widget-two__icon b-radius--5 bg--warning">
                <i class="las la-clock"></i>
            </div>
            <div class="widget-two__content">
                <h3 class="text-white">{{ collect($stats)->sum('stats.pending_ibs') }}</h3>
                <p class="text-white">@lang('Pending IBs')</p>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-4 col-sm-6">
        <div class="widget-two style--two box--shadow2 b-radius--5 bg--info">
            <div class="widget-two__icon b-radius--5 bg--info">
                <i class="las la-dollar-sign"></i>
            </div>
            <div class="widget-two__content">
                <h3 class="text-white">{{ showAmount(collect($stats)->sum('total_commissions')) }}</h3>
                <p class="text-white">@lang('Total Commissions')</p>
            </div>
        </div>
    </div>
</div>

<!-- Group Performance Comparison -->
<div class="row mt-4">
    <div class="col-lg-8">
        <div class="card b-radius--10">
            <div class="card-header">
                <h5 class="card-title">@lang('Group Performance Comparison')</h5>
            </div>
            <div class="card-body">
                @if(collect($stats)->sum('stats.active_ibs') > 0)
                <div class="row">
                    @foreach($stats as $stat)
                    <div class="col-md-12 mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">{{ $stat['group']->name }}</h6>
                            <small class="text-muted">{{ $stat['stats']['active_ibs'] }} Active IBs</small>
                        </div>
                        
                        <!-- IB Count Progress -->
                        <div class="progress mb-2" style="height: 15px;">
                            @php
                                $maxIbs = collect($stats)->max('stats.active_ibs');
                                $ibPercentage = $maxIbs > 0 ? ($stat['stats']['active_ibs'] / $maxIbs) * 100 : 0;
                            @endphp
                            <div class="progress-bar bg--success" role="progressbar" 
                                 style="width: {{ $ibPercentage }}%" 
                                 aria-valuenow="{{ $ibPercentage }}" 
                                 aria-valuemin="0" 
                                 aria-valuemax="100">
                            </div>
                        </div>
                        
                        <!-- Commission Progress -->
                        <div class="progress" style="height: 15px;">
                            @php
                                $maxCommissions = collect($stats)->max('total_commissions');
                                $commissionPercentage = $maxCommissions > 0 ? ($stat['total_commissions'] / $maxCommissions) * 100 : 0;
                            @endphp
                            <div class="progress-bar bg--primary" role="progressbar" 
                                 style="width: {{ $commissionPercentage }}%" 
                                 aria-valuenow="{{ $commissionPercentage }}" 
                                 aria-valuemin="0" 
                                 aria-valuemax="100">
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-1">
                            <small class="text-muted">Commissions: {{ showAmount($stat['total_commissions']) }}</small>
                            <small class="text-muted">Multiplier: {{ $stat['group']->commission_multiplier }}x</small>
                        </div>
                    </div>
                    @endforeach
                </div>
                @else
                <div class="text-center py-4">
                    <i class="las la-chart-bar text-muted" style="font-size: 4rem;"></i>
                    <h5 class="text-muted mt-3">@lang('No IB data available yet')</h5>
                    <p class="text-muted">@lang('Statistics will appear once IBs are approved and start generating commissions')</p>
                </div>
                @endif
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card b-radius--10">
            <div class="card-header">
                <h5 class="card-title">@lang('Group Distribution')</h5>
            </div>
            <div class="card-body">
                @if(collect($stats)->sum('stats.total_ibs') > 0)
                @foreach($stats as $stat)
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <h6 class="mb-0">{{ $stat['group']->name }}</h6>
                        <small class="text-muted">{{ $stat['group']->commission_multiplier }}x multiplier</small>
                    </div>
                    <div class="text-end">
                        <span class="badge badge--primary">{{ $stat['stats']['total_ibs'] }}</span>
                        <br>
                        <small class="text-muted">IBs</small>
                    </div>
                </div>
                @endforeach
                @else
                <div class="text-center py-3">
                    <i class="las la-users text-muted" style="font-size: 3rem;"></i>
                    <p class="text-muted mt-2">@lang('No IBs assigned to groups yet')</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('breadcrumb-plugins')
<div class="d-inline">
    <a href="{{ route('admin.ib.groups.index') }}" class="btn btn--dark">
        <i class="las la-arrow-left"></i> @lang('Back to Groups')
    </a>
</div>
@endpush
