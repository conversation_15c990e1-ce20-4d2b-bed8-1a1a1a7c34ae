<?php $__env->startSection('panel'); ?>
    <?php if(@json_decode($general->system_info)->version > systemDetails()['version']): ?>
    <div class="row">
        <div class="col-md-12">
            <div class="card text-white bg-warning mb-3">
                <div class="card-header">
                    <h3 class="card-title"> <?php echo app('translator')->get('New Version Available'); ?> <button class="btn btn--dark float-end"><?php echo app('translator')->get('Version'); ?> <?php echo e(json_decode($general->system_info)->version); ?></button> </h3>
                </div>
                <div class="card-body">
                    <h5 class="card-title text-dark"><?php echo app('translator')->get('What is the Update ?'); ?></h5>
                    <p><pre  class="f-size--24"><?php echo e(json_decode($general->system_info)->details); ?></pre></p>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php if(@json_decode($general->system_info)->message): ?>
    <div class="row">
        <?php $__currentLoopData = json_decode($general->system_info)->message; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $msg): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-md-12">
                <div class="alert border border--primary" role="alert">
                    <div class="alert__icon bg--primary">
                        <i class="far fa-bell"></i></div>
                        <p class="alert__message"><?php echo $msg; ?></p>
                        <button type="button" class="close" data-bs-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">×</span></button>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
    <?php endif; ?>

    <div class="row mb-none-30 mb-3 align-items-center gy-4">
        <div class="col-xxl-3 col-sm-6">
            <?php if (isset($component)) { $__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.widget','data' => ['style' => '2','link' => ''.e(route('admin.users.all')).'','icon' => 'las la-users f-size--56','iconStyle' => 'false','title' => 'Total Users','value' => ''.e($widget['total_users']).'','color' => 'primary']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('widget'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['style' => '2','link' => ''.e(route('admin.users.all')).'','icon' => 'las la-users f-size--56','icon_style' => 'false','title' => 'Total Users','value' => ''.e($widget['total_users']).'','color' => 'primary']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9)): ?>
<?php $attributes = $__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9; ?>
<?php unset($__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9)): ?>
<?php $component = $__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9; ?>
<?php unset($__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9); ?>
<?php endif; ?>
        </div><!-- dashboard-w1 end -->
        <div class="col-xxl-3 col-sm-6">
            <?php if (isset($component)) { $__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.widget','data' => ['style' => '2','link' => ''.e(route('admin.users.active')).'','icon' => 'las la-user-check f-size--56','title' => 'Active Users','iconStyle' => 'false','value' => ''.e($widget['verified_users']).'','color' => 'success']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('widget'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['style' => '2','link' => ''.e(route('admin.users.active')).'','icon' => 'las la-user-check f-size--56','title' => 'Active Users','icon_style' => 'false','value' => ''.e($widget['verified_users']).'','color' => 'success']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9)): ?>
<?php $attributes = $__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9; ?>
<?php unset($__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9)): ?>
<?php $component = $__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9; ?>
<?php unset($__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9); ?>
<?php endif; ?>
        </div>
        <div class="col-xxl-3 col-sm-6">
            <?php if (isset($component)) { $__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.widget','data' => ['style' => '2','link' => ''.e(route('admin.users.email.unverified')).'','icon' => 'lar la-envelope f-size--56','iconStyle' => 'false','title' => 'Email Unverified Users','value' => ''.e($widget['email_unverified_users'] ?? '-0').'','color' => 'danger']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('widget'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['style' => '2','link' => ''.e(route('admin.users.email.unverified')).'','icon' => 'lar la-envelope f-size--56','icon_style' => 'false','title' => 'Email Unverified Users','value' => ''.e($widget['email_unverified_users'] ?? '-0').'','color' => 'danger']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9)): ?>
<?php $attributes = $__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9; ?>
<?php unset($__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9)): ?>
<?php $component = $__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9; ?>
<?php unset($__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9); ?>
<?php endif; ?>
        </div>
        <div class="col-xxl-3 col-sm-6">
            <?php if (isset($component)) { $__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.widget','data' => ['style' => '2','iconStyle' => 'false','link' => ''.e(route('admin.users.mobile.unverified')).'','icon' => 'las la-comment-slash f-size--56','title' => 'Mobile Unverified Users','value' => ''.e($widget['mobile_unverified_users']).'','color' => 'red']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('widget'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['style' => '2','icon_style' => 'false','link' => ''.e(route('admin.users.mobile.unverified')).'','icon' => 'las la-comment-slash f-size--56','title' => 'Mobile Unverified Users','value' => ''.e($widget['mobile_unverified_users']).'','color' => 'red']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9)): ?>
<?php $attributes = $__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9; ?>
<?php unset($__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9)): ?>
<?php $component = $__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9; ?>
<?php unset($__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9); ?>
<?php endif; ?>
        </div>
    </div>

    <div class="row mb-none-30 mb-3 align-items-center gy-4">
        <div class="col-xxl-3 col-sm-6">
            <?php if (isset($component)) { $__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.widget','data' => ['style' => '2','link' => ''.e(route('admin.order.history')).'','icon' => 'las la-list-alt','iconStyle' => 'false','title' => 'Total Orders','value' => ''.e($widget['order_count']['total']).'','color' => 'primary']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('widget'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['style' => '2','link' => ''.e(route('admin.order.history')).'','icon' => 'las la-list-alt','icon_style' => 'false','title' => 'Total Orders','value' => ''.e($widget['order_count']['total']).'','color' => 'primary']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9)): ?>
<?php $attributes = $__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9; ?>
<?php unset($__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9)): ?>
<?php $component = $__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9; ?>
<?php unset($__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9); ?>
<?php endif; ?>
        </div><!-- dashboard-w1 end -->
        <div class="col-xxl-3 col-sm-6">
            <?php if (isset($component)) { $__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.widget','data' => ['style' => '2','link' => ''.e(route('admin.order.open')).'','icon' => 'fa  fa-spinner','iconStyle' => 'false','title' => 'Open Orders','value' => ''.e($widget['order_count']['open']).'','color' => 'info']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('widget'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['style' => '2','link' => ''.e(route('admin.order.open')).'','icon' => 'fa  fa-spinner','icon_style' => 'false','title' => 'Open Orders','value' => ''.e($widget['order_count']['open']).'','color' => 'info']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9)): ?>
<?php $attributes = $__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9; ?>
<?php unset($__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9)): ?>
<?php $component = $__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9; ?>
<?php unset($__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9); ?>
<?php endif; ?>
        </div>
        <!-- dashboard-w1 end -->
        <div class="col-xxl-3 col-sm-6">
            <?php if (isset($component)) { $__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.widget','data' => ['style' => '2','link' => ''.e(route('admin.order.history')).'?status='.e(Status::ORDER_COMPLETED).'','icon' => 'las la-check-circle','iconStyle' => 'false','title' => 'Completed Orders','value' => ''.e($widget['order_count']['completed']).'','color' => 'success']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('widget'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['style' => '2','link' => ''.e(route('admin.order.history')).'?status='.e(Status::ORDER_COMPLETED).'','icon' => 'las la-check-circle','icon_style' => 'false','title' => 'Completed Orders','value' => ''.e($widget['order_count']['completed']).'','color' => 'success']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9)): ?>
<?php $attributes = $__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9; ?>
<?php unset($__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9)): ?>
<?php $component = $__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9; ?>
<?php unset($__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9); ?>
<?php endif; ?>
        </div> <!-- dashboard-w1 end -->
         <div class="col-xxl-3 col-sm-6">
            <?php if (isset($component)) { $__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.widget','data' => ['style' => '2','link' => ''.e(route('admin.order.history')).'?status='.e(Status::ORDER_CANCELED).'','icon' => 'las la-times-circle','iconStyle' => 'false','title' => 'Canceled Orders','value' => ''.e($widget['order_count']['canceled']).'','color' => 'danger']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('widget'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['style' => '2','link' => ''.e(route('admin.order.history')).'?status='.e(Status::ORDER_CANCELED).'','icon' => 'las la-times-circle','icon_style' => 'false','title' => 'Canceled Orders','value' => ''.e($widget['order_count']['canceled']).'','color' => 'danger']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9)): ?>
<?php $attributes = $__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9; ?>
<?php unset($__attributesOriginal433d6a5be5b58ac8aa6a74031c6196f9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9)): ?>
<?php $component = $__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9; ?>
<?php unset($__componentOriginal433d6a5be5b58ac8aa6a74031c6196f9); ?>
<?php endif; ?>
        </div>
        <!-- dashboard-w1 end -->
    </div>

    
        

        

        

        

    

    <!-- Real-Time Admin Dashboard Activity Tabs -->
    <div class="row mb-none-30 mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><?php echo app('translator')->get('Recent Activities'); ?></h5>
                    <small class="text-muted"><?php echo app('translator')->get('Real-time system activities across all business areas'); ?></small>
                </div>
                <div class="card-body">
                    <!-- Activity Tabs Navigation -->
                    <ul class="nav nav-tabs activity-tabs mb-3" id="activityTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="transactions-tab" data-bs-toggle="tab" data-bs-target="#transactions" type="button" role="tab" aria-controls="transactions" aria-selected="true">
                                <i class="las la-exchange-alt"></i> <?php echo app('translator')->get('Transactions'); ?>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="accounts-tab" data-bs-toggle="tab" data-bs-target="#accounts" type="button" role="tab" aria-controls="accounts" aria-selected="false">
                                <i class="las la-users"></i> <?php echo app('translator')->get('Accounts'); ?>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="mt5-tab" data-bs-toggle="tab" data-bs-target="#mt5" type="button" role="tab" aria-controls="mt5" aria-selected="false">
                                <i class="las la-chart-line"></i> <?php echo app('translator')->get('MT5'); ?>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="tickets-tab" data-bs-toggle="tab" data-bs-target="#tickets" type="button" role="tab" aria-controls="tickets" aria-selected="false">
                                <i class="las la-ticket-alt"></i> <?php echo app('translator')->get('Tickets'); ?>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="kyc-tab" data-bs-toggle="tab" data-bs-target="#kyc" type="button" role="tab" aria-controls="kyc" aria-selected="false">
                                <i class="las la-id-card"></i> <?php echo app('translator')->get('KYC'); ?>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="partnership-tab" data-bs-toggle="tab" data-bs-target="#partnership" type="button" role="tab" aria-controls="partnership" aria-selected="false">
                                <i class="las la-handshake"></i> <?php echo app('translator')->get('Partnership'); ?>
                            </button>
                        </li>
                    </ul>

                    <!-- Activity Tabs Content -->
                    <div class="tab-content" id="activityTabsContent">
                        <div class="tab-pane fade show active" id="transactions" role="tabpanel" aria-labelledby="transactions-tab">
                            <div class="activity-content" data-tab="transactions">
                                <div class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="mt-2 text-muted"><?php echo app('translator')->get('Loading transaction activities...'); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="accounts" role="tabpanel" aria-labelledby="accounts-tab">
                            <div class="activity-content" data-tab="accounts">
                                <div class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="mt-2 text-muted"><?php echo app('translator')->get('Loading account activities...'); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="mt5" role="tabpanel" aria-labelledby="mt5-tab">
                            <div class="activity-content" data-tab="mt5">
                                <div class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="mt-2 text-muted"><?php echo app('translator')->get('Loading MT5 activities...'); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="tickets" role="tabpanel" aria-labelledby="tickets-tab">
                            <div class="activity-content" data-tab="tickets">
                                <div class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="mt-2 text-muted"><?php echo app('translator')->get('Loading ticket activities...'); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="kyc" role="tabpanel" aria-labelledby="kyc-tab">
                            <div class="activity-content" data-tab="kyc">
                                <div class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="mt-2 text-muted"><?php echo app('translator')->get('Loading KYC activities...'); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="partnership" role="tabpanel" aria-labelledby="partnership-tab">
                            <div class="activity-content" data-tab="partnership">
                                <div class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="mt-2 text-muted"><?php echo app('translator')->get('Loading partnership activities...'); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- <div class="row mb-none-30 mb-3 gy-4">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <span><?php echo app('translator')->get('Order Summary'); ?></span>
                    <br>
                    <small class="text-muted text--small">
                        <?php echo app('translator')->get("order summary presents visual & listing data of order, categories by pair, excluding canceled orders & scroll below to show all pair."); ?>
                    </small>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-9">
                            <div class="order-list">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex flex-wrap gap-2 align-items-center justify-content-between pt-0">
                                        <span><?php echo app('translator')->get('Pair'); ?></span>
                                         <span class="text-start"><?php echo app('translator')->get('Amount'); ?></span>
                                    </li>
                                    <?php $__empty_1 = true; $__currentLoopData = $widget['order']['list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <li class="list-group-item d-flex flex-wrap gap-2 align-items-center mb-2 justify-content-between">
                                        <span><?php echo e(@$order->pair->symbol); ?></span>
                                         <span class="text-start">
                                            <?php echo e(showAmount($order->total_amount)); ?>  <?php echo e(@$order->pair->coin->symbol); ?>

                                        </span>
                                    </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <li class="list-group-item text-center text-muted">
                                        <?php echo e(__($emptyMessage)); ?>

                                    </li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        </div>
                        <div class="col-lg-5 d-flex align-items-center">
                            <canvas id="pair-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
    <!-- </div> > -->

    <div class="row mb-none-30 mb-3">
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header">
                    <span><?php echo app('translator')->get('Deposit Summary'); ?></span>
                    <br>
                    <small class="text-muted text--small">
                        <?php echo app('translator')->get("Deposit summary presents visual & listing data of deposit, categories by currency, excluding rejected deposit & scroll below to show all deposited amount for each currency."); ?>
                    </small>
                </div>
                <div class="cadr-body">
                    <div class="row">
                        <div class="col-lg-7">
                            <div class="deposit-wrapper">
                                <ul class="list-group list-group-flush  p-3 deposit-list">
                                    <li class="list-group-item d-flex flex-wrap gap-2 align-items-center justify-content-between pt-0">
                                        <span class="flex-fill text-start"><?php echo app('translator')->get('Currency'); ?></span>
                                        <span class="flex-fill text-center"><?php echo app('translator')->get('Amount'); ?></span>
                                        <span class="flex-fill text-center"><?php echo app('translator')->get('Amount'); ?>(<?php echo e(__($general->cur_text)); ?>)</span>
                                    </li>
                                    <?php $__empty_1 = true; $__currentLoopData = $widget['deposit']['list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $deposit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <li class="list-group-item d-flex flex-wrap gap-2 align-items-center mb-2 justify-content-between">
                                        <div class="flex-fill text-start">
                                            <div class="user">
                                                <div class="thumb">
                                                    <img src="<?php echo e(@$deposit->currency->image_url); ?>">
                                                </div>
                                                <div class="text-start ms-1">
                                                    <small><?php echo e(@$deposit->currency->symbol); ?></small> <br>
                                                    <small><?php echo e(__(strLimit(@$deposit->currency->name,9))); ?></small>
                                                </div>
                                            </div>
                                        </div>
                                        <span class="flex-fill text-center"><?php echo e(showAmount($deposit->total_amount)); ?></span>
                                        <span class="flex-fill text-center"><?php echo e(showAmount($deposit->total_amount * $deposit->currency->rate)); ?></span>
                                    </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <li class="list-group-item text-center text-muted">
                                        <?php echo e(__($emptyMessage)); ?>

                                    </li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        </div>
                        <div class="col-lg-5 d-flex align-items-center">
                            <canvas id="deposit-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header">
                    <span><?php echo app('translator')->get('Withdraw Summary'); ?></span>
                    <br>
                    <small class="text-muted text--small">
                        <?php echo app('translator')->get("Withdraw summary presents visual & listing data of withdraw, categories by currency, excluding rejected withdraw & scroll below to show all Withdraw amount for each currency."); ?>
                    </small>
                </div>
                <div class="cadr-body">
                    <div class="row">
                        <div class="col-lg-5 d-flex align-items-center">
                            <canvas id="withdraw" class="mt-3"></canvas>
                        </div>
                        <div class="col-lg-7">
                            <div class="withdraw-wrapper">
                                <ul class="list-group list-group-flush  p-3 withdraw-list">
                                    <li class="list-group-item d-flex flex-wrap gap-2 align-items-center justify-content-between pt-0">
                                        <span class="flex-fill text-start"><?php echo app('translator')->get('Currency'); ?></span>
                                        <span class="flex-fill text-center"><?php echo app('translator')->get('Amount'); ?></span>
                                        <span class="flex-fill text-center"><?php echo app('translator')->get('Amount'); ?>(<?php echo e(__($general->cur_text)); ?>)</span>
                                    </li>
                                    <?php $__empty_1 = true; $__currentLoopData = $widget['withdraw']['list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $withdraw): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <li class="list-group-item d-flex flex-wrap gap-2 align-items-center mb-2 justify-content-between">
                                        <div class="flex-fill text-start">
                                            <div class="user">
                                                <div class="thumb">
                                                    <img src="<?php echo e(@$withdraw->withdrawCurrency->image_url); ?>">
                                                </div>
                                                <div class="text-start ms-1">
                                                    <small><?php echo e(@$withdraw->withdrawCurrency->symbol); ?></small> <br>
                                                    <small><?php echo e(__(strLimit(@$withdraw->withdrawCurrency->name,9))); ?></small>
                                                </div>
                                            </div>
                                        </div>
                                        <span class="flex-fill text-center"><?php echo e(showAmount($withdraw->total_amount)); ?></span>
                                        <span class="flex-fill text-center"><?php echo e(showAmount($withdraw->total_amount * $withdraw->withdrawCurrency->rate)); ?></span>
                                    </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <li class="list-group-item text-center text-muted">
                                        <?php echo e(__($emptyMessage)); ?>

                                    </li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-none-30 mt-5">
        <div class="col-xl-4 col-lg-6 mb-30">
            <div class="card overflow-hidden">
                <div class="card-body">
                    <h5 class="card-title"><?php echo app('translator')->get('Login By Browser'); ?> (<?php echo app('translator')->get('Last 30 days'); ?>)</h5>
                    <canvas id="userBrowserChart"></canvas>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-lg-6 mb-30">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title"><?php echo app('translator')->get('Login By OS'); ?> (<?php echo app('translator')->get('Last 30 days'); ?>)</h5>
                    <canvas id="userOsChart"></canvas>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-lg-6 mb-30">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title"><?php echo app('translator')->get('Login By Country'); ?> (<?php echo app('translator')->get('Last 30 days'); ?>)</h5>
                    <canvas id="userCountryChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <?php
        $lastCron = Carbon\Carbon::parse($general->last_cron)->diffInSeconds();
    ?>

    <?php if($lastCron >= 900): ?>
        <?php echo $__env->make('admin.partials.cron_instruction', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('style'); ?>
<style>
    /* Activity Tabs Styling */
    .activity-tabs .nav-link {
        color: #6c757d;
        border: 1px solid transparent;
        border-radius: 0.375rem 0.375rem 0 0;
        padding: 0.75rem 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .activity-tabs .nav-link:hover {
        color: #dc3545;
        border-color: #e9ecef #e9ecef #dee2e6;
        background-color: #f8f9fa;
    }

    .activity-tabs .nav-link.active {
        color: #fff;
        background-color: #dc3545;
        border-color: #dee2e6 #dee2e6 #fff;
        font-weight: 600;
    }

    .activity-tabs .nav-link i {
        margin-right: 0.5rem;
        font-size: 1.1em;
    }

    /* Activity Content Styling */
    .activity-content {
        min-height: 400px;
        max-height: 500px;
        overflow-y: auto;
    }

    .activity-list {
        padding: 0;
    }

    .activity-item {
        padding: 1rem 0;
        transition: background-color 0.2s ease;
    }

    .activity-item:hover {
        background-color: #f8f9fa;
        border-radius: 0.375rem;
        margin: 0 -0.5rem;
        padding: 1rem 0.5rem;
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f8f9fa;
        border-radius: 50%;
        font-size: 1.2em;
    }

    .activity-item h6 {
        font-size: 0.95rem;
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .activity-item .text-muted {
        font-size: 0.85rem;
    }

    /* Loading Spinner */
    .spinner-border {
        width: 2rem;
        height: 2rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .activity-tabs .nav-link {
            padding: 0.5rem 0.75rem;
            font-size: 0.9rem;
        }

        .activity-tabs .nav-link i {
            margin-right: 0.25rem;
        }

        .activity-content {
            min-height: 300px;
            max-height: 400px;
        }

        .activity-item {
            padding: 0.75rem 0;
        }

        .activity-icon {
            width: 35px;
            height: 35px;
            font-size: 1.1em;
        }
    }

    /* Custom scrollbar for activity content */
    .activity-content::-webkit-scrollbar {
        width: 6px;
    }

    .activity-content::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    .activity-content::-webkit-scrollbar-thumb {
        background: #dc3545;
        border-radius: 3px;
    }

    .activity-content::-webkit-scrollbar-thumb:hover {
        background: #c82333;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script'); ?>
<script src="<?php echo e(asset('assets/admin/js/vendor/apexcharts.min.js')); ?>"></script>
<script src="<?php echo e(asset('assets/admin/js/vendor/chart.js.2.8.0.js')); ?>"></script>

<script>
    "use strict";

    $(".order-list").scroll(function() {
        if ((parseInt($(this)[0].scrollHeight) - parseInt(this.clientHeight)) == parseInt($(this).scrollTop())) {
            loadOrderList();
        }
    });

    let orderSkip = 6;
    let take      = 20;
    function loadOrderList(){
        $.ajax({
            url: "<?php echo e(route('admin.load.data')); ?>",
            type: "GET",
            dataType: 'json',
            cache: false,
            data:{
                model_name:"Order",
                skip:orderSkip,
                take:take
            },
            success: function (resp) {
                if(!resp.success){
                    return false;
                }
                orderSkip +=parseInt(take);
                let html="";
                $.each(resp.data, function (i, order) {
                    html +=`
                    <li class="list-group-item d-flex flex-wrap gap-2 align-items-center mb-2 justify-content-between">
                        <span>${order.pair.symbol}</span>
                            <span class="text-start">
                            ${getAmount(order.total_amount)} ${order.pair.coin.symbol}
                        </span>
                    </li>
                    `
                });
                $('.order-list ul').append(html);
            }
        });
    };

    $(".deposit-list").scroll(function() {
        if ((parseInt($(this)[0].scrollHeight) - parseInt(this.clientHeight)) == parseInt($(this).scrollTop())) {
            loadDepositList();
        }
    });

    let depositSkip = 6;
    function loadDepositList(){
        $.ajax({
            url: "<?php echo e(route('admin.load.data')); ?>",
            type: "GET",
            dataType: 'json',
            cache: false,
            data:{
                model_name:"Deposit",
                skip:depositSkip,
                take:take
            },
            success: function (resp) {
                if(!resp.success){
                    return false;
                }
                depositSkip +=parseInt(take);
                let html="";
                $.each(resp.data, function (i, deposit) {
                    html +=`
                    <li class="list-group-item d-flex flex-wrap gap-2 align-items-center mb-2 justify-content-between">
                        <div class="flex-fill text-start">
                            <div class="user">
                                <div class="thumb">
                                    <img src="${deposit.currency.image_url }">
                                </div>
                                <div class="text-start ms-1">
                                    <small>${deposit.currency.symbol}</small> <br>
                                    <small>${deposit.currency.name}</small>
                                </div>
                            </div>
                        </div>
                        <span class="flex-fill text-center">${getAmount(deposit.total_amount)}</span>
                        <span class="flex-fill text-center">${getAmount(parseFloat(deposit.total_amount) * parseFloat(deposit.currency.rate))}</span>
                    </li>
                    `
                });
                $('.deposit-list').append(html);
            }
        });
    };

    $(".withdraw-list").scroll(function() {
        if ((parseInt($(this)[0].scrollHeight) - parseInt(this.clientHeight)) == parseInt($(this).scrollTop())) {
            loadWithdrawList();
        }
    });

    let withdrawSkip = 6;
    function loadWithdrawList(){
        $.ajax({
            url: "<?php echo e(route('admin.load.data')); ?>",
            type: "GET",
            dataType: 'json',
            cache: false,
            data:{
                model_name:"Withdrawal",
                skip:withdrawSkip,
                take:take
            },
            success: function (resp) {
                if(!resp.success){
                    return false;
                }
                withdrawSkip +=parseInt(take);
                let html="";
                $.each(resp.data, function (i, withdraw) {
                    html +=`
                    <li class="list-group-item d-flex flex-wrap gap-2 align-items-center mb-2 justify-content-between">
                        <div class="flex-fill text-start">
                            <div class="user">
                                <div class="thumb">
                                    <img src="${withdraw.withdraw_currency.image_url }">
                                </div>
                                <div class="text-start ms-1">
                                    <small>${withdraw.withdraw_currency.symbol}</small> <br>
                                    <small>${withdraw.withdraw_currency.name}</small>
                                </div>
                            </div>
                        </div>
                        <span class="flex-fill text-center">${getAmount(withdraw.total_amount)}</span>
                        <span class="flex-fill text-center">${getAmount(parseFloat(withdraw.total_amount) * parseFloat(withdraw.withdraw_currency.rate))}</span>
                    </li>
                    `
                });
                $('.withdraw-list').append(html);
            }
        });
    };

    var ctx = document.getElementById('deposit-chart');
    if (ctx) {
        var myChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: <?php echo json_encode($widget['deposit']['currency_symbol'], 15, 512) ?>,
            datasets: [{
                data: <?php echo json_encode($widget['deposit']['currency_count'], 15, 512) ?>,
                backgroundColor: [
                    '#ff7675',
                    '#6c5ce7',
                    '#ffa62b',
                    '#ffeaa7',
                    '#D980FA',
                    '#fccbcb',
                    '#45aaf2',
                    '#05dfd7',
                    '#FF00F6',
                    '#1e90ff',
                    '#2ed573',
                    '#eccc68',
                    '#ff5200',
                    '#cd84f1',
                    '#7efff5',
                    '#7158e2',
                    '#fff200',
                    '#ff9ff3',
                    '#08ffc8',
                    '#3742fa',
                    '#1089ff',
                    '#70FF61',
                    '#bf9fee',
                    '#574b90'
                ],
                borderColor: [
                    'rgba(231, 80, 90, 0.75)'
                ],
                borderWidth: 0,
            }]
        },
        options: {
            aspectRatio: 1.3,
            responsive: true,
            elements: {
                line: {
                    tension: 0 // disables bezier curves
                }
            },
            scales: {
                xAxes: [{
                    display: false
                }],
                yAxes: [{
                    display: false
                }]
            },
            legend: {
                display: false,
            }
        }
        });
    }

    var ctx = document.getElementById('withdraw');
    if (ctx) {
        var myChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: <?php echo json_encode(@$widget['withdraw']['currency_symbol'] , 15, 512) ?>,
            datasets: [{
                data: <?php echo json_encode(@$widget['withdraw']['currency_count'] , 15, 512) ?>,
                backgroundColor: [
                    '#ff7675',
                    '#6c5ce7',
                    '#ffa62b',
                    '#ffeaa7',
                    '#D980FA',
                    '#fccbcb',
                    '#45aaf2',
                    '#05dfd7',
                    '#FF00F6',
                    '#1e90ff',
                    '#2ed573',
                    '#eccc68',
                    '#ff5200',
                    '#cd84f1',
                    '#7efff5',
                    '#7158e2',
                    '#fff200',
                    '#ff9ff3',
                    '#08ffc8',
                    '#3742fa',
                    '#1089ff',
                    '#70FF61',
                    '#bf9fee',
                    '#574b90'
                ],
                borderColor: [
                    'rgba(231, 80, 90, 0.75)'
                ],
                borderWidth: 0,
            }]
        },
        options: {
            aspectRatio: 1.3,
            responsive: true,
            elements: {
                line: {
                    tension: 0 // disables bezier curves
                }
            },
            scales: {
                xAxes: [{
                    display: false
                }],
                yAxes: [{
                    display: false
                }]
            },
            legend: {
                display: false,
            }
        }
        });
    }

    var ctx = document.getElementById('pair-chart');
    if (ctx) {
        var myChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: <?php echo json_encode($widget['order']['symbol'], 15, 512) ?>,
            datasets: [{
                data: <?php echo json_encode($widget['order']['count'], 15, 512) ?>,
                backgroundColor: [
                    '#ff7675',
                    '#6c5ce7',
                    '#ffa62b',
                    '#ffeaa7',
                    '#D980FA',
                    '#fccbcb',
                    '#45aaf2',
                    '#05dfd7',
                    '#FF00F6',
                    '#1e90ff',
                    '#2ed573',
                    '#eccc68',
                    '#ff5200',
                    '#cd84f1',
                    '#7efff5',
                    '#7158e2',
                    '#fff200',
                    '#ff9ff3',
                    '#08ffc8',
                    '#3742fa',
                    '#1089ff',
                    '#70FF61',
                    '#bf9fee',
                    '#574b90'
                ],
                borderColor: [
                    'rgba(231, 80, 90, 0.75)'
                ],
                borderWidth: 0,
            }]
        },
        options: {
            aspectRatio: 1.2,
            responsive: true,
            elements: {
                line: {
                    tension: 0 // disables bezier curves
                }
            },
            scales: {
                xAxes: [{
                    display: false
                }],
                yAxes: [{
                    display: false
                }]
            },
            legend: {
                display: false,
            }
        }
        });
    }


    var ctx = document.getElementById('userBrowserChart');
    if (ctx) {
        var myChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: <?php echo json_encode($chart['user_browser_counter']->keys(), 15, 512) ?>,
            datasets: [{
                data: <?php echo e($chart['user_browser_counter']->flatten()); ?>,
                backgroundColor: [
                    '#ff7675',
                    '#6c5ce7',
                    '#ffa62b',
                    '#ffeaa7',
                    '#D980FA',
                    '#fccbcb',
                    '#45aaf2',
                    '#05dfd7',
                    '#FF00F6',
                    '#1e90ff',
                    '#2ed573',
                    '#eccc68',
                    '#ff5200',
                    '#cd84f1',
                    '#7efff5',
                    '#7158e2',
                    '#fff200',
                    '#ff9ff3',
                    '#08ffc8',
                    '#3742fa',
                    '#1089ff',
                    '#70FF61',
                    '#bf9fee',
                    '#574b90'
                ],
                borderColor: [
                    'rgba(231, 80, 90, 0.75)'
                ],
                borderWidth: 0,

            }]
        },
        options: {
            aspectRatio: 1,
            responsive: true,
            maintainAspectRatio: true,
            elements: {
                line: {
                    tension: 0 // disables bezier curves
                }
            },
            scales: {
                xAxes: [{
                    display: false
                }],
                yAxes: [{
                    display: false
                }]
            },
            legend: {
                display: false,
            }
        }
        });
    }

    var ctx = document.getElementById('userOsChart');
    if (ctx) {
        var myChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: <?php echo json_encode($chart['user_os_counter']->keys(), 15, 512) ?>,
            datasets: [{
                data: <?php echo e($chart['user_os_counter']->flatten()); ?>,
                backgroundColor: [
                    '#ff7675',
                    '#6c5ce7',
                    '#ffa62b',
                    '#ffeaa7',
                    '#D980FA',
                    '#fccbcb',
                    '#45aaf2',
                    '#05dfd7',
                    '#FF00F6',
                    '#1e90ff',
                    '#2ed573',
                    '#eccc68',
                    '#ff5200',
                    '#cd84f1',
                    '#7efff5',
                    '#7158e2',
                    '#fff200',
                    '#ff9ff3',
                    '#08ffc8',
                    '#3742fa',
                    '#1089ff',
                    '#70FF61',
                    '#bf9fee',
                    '#574b90'
                ],
                borderColor: [
                    'rgba(0, 0, 0, 0.05)'
                ],
                borderWidth: 0,

            }]
        },
        options: {
            aspectRatio: 1,
            responsive: true,
            elements: {
                line: {
                    tension: 0 // disables bezier curves
                }
            },
            scales: {
                xAxes: [{
                    display: false
                }],
                yAxes: [{
                    display: false
                }]
            },
            legend: {
                display: false,
            }
        },
        });
    }

    // Donut chart
    var ctx = document.getElementById('userCountryChart');
    if (ctx) {
        var myChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: <?php echo json_encode($chart['user_country_counter']->keys(), 15, 512) ?>,
            datasets: [{
                data: <?php echo e($chart['user_country_counter']->flatten()); ?>,
                backgroundColor: [
                    '#ff7675',
                    '#6c5ce7',
                    '#ffa62b',
                    '#ffeaa7',
                    '#D980FA',
                    '#fccbcb',
                    '#45aaf2',
                    '#05dfd7',
                    '#FF00F6',
                    '#1e90ff',
                    '#2ed573',
                    '#eccc68',
                    '#ff5200',
                    '#cd84f1',
                    '#7efff5',
                    '#7158e2',
                    '#fff200',
                    '#ff9ff3',
                    '#08ffc8',
                    '#3742fa',
                    '#1089ff',
                    '#70FF61',
                    '#bf9fee',
                    '#574b90'
                ],
                borderColor: [
                    'rgba(231, 80, 90, 0.75)'
                ],
                borderWidth: 0,

            }]
        },
        options: {
            aspectRatio: 1,
            responsive: true,
            elements: {
                line: {
                    tension: 0 // disables bezier curves
                }
            },
            scales: {
                xAxes: [{
                    display: false
                }],
                yAxes: [{
                    display: false
                }]
            },
            legend: {
                display: false,
            }
        }
        });
    }

    // Activity Tabs Management
    let activityTabsInitialized = false;
    let currentActiveTab = 'transactions';
    let refreshInterval = null;

    // CSRF Token retrieval with multiple fallback methods
    function getCSRFToken() {
        // Method 1: Meta tag (primary)
        let token = $('meta[name="csrf-token"]').attr('content');
        if (token && token.length > 0) {
            return token;
        }

        // Method 2: Laravel global variable (fallback)
        if (typeof window.Laravel !== 'undefined' && window.Laravel.csrfToken) {
            return window.Laravel.csrfToken;
        }

        // Method 3: Hidden input field (fallback)
        token = $('input[name="_token"]').val();
        if (token && token.length > 0) {
            return token;
        }

        // Method 4: Cookie-based token (fallback)
        token = getCookie('XSRF-TOKEN');
        if (token && token.length > 0) {
            return decodeURIComponent(token);
        }

        return null;
    }

    // Helper function to get cookie value
    function getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
        return null;
    }

    // Initialize activity tabs
    function initializeActivityTabs() {
        console.log('Initializing activity tabs...');

        if (activityTabsInitialized) {
            console.log('Activity tabs already initialized');
            return;
        }

        // Check if required elements exist
        if ($('#activityTabs').length === 0) {
            console.error('Activity tabs container not found');
            return;
        }

        // Check if CSRF token is available with multiple fallback methods
        const csrfToken = getCSRFToken();
        if (!csrfToken) {
            console.error('CSRF token not found - activity tabs cannot initialize');
            showActivityError($('.activity-content[data-tab="transactions"]'), 'CSRF token missing. Please refresh the page.');
            return;
        }

        console.log('CSRF token found:', csrfToken.substring(0, 10) + '...');

        console.log('Loading initial tab content: transactions');
        // Load initial tab content
        loadActivityData('transactions');

        // Set up tab click handlers
        $('#activityTabs button[data-bs-toggle="tab"]').on('shown.bs.tab', function (e) {
            const tabId = e.target.getAttribute('aria-controls');
            console.log('Tab switched to:', tabId);
            currentActiveTab = tabId;
            loadActivityData(tabId);
        });

        // Set up auto-refresh for active tab
        refreshInterval = setInterval(function() {
            if (currentActiveTab) {
                console.log('Auto-refreshing tab:', currentActiveTab);
                loadActivityData(currentActiveTab, true);
            }
        }, 30000); // Refresh every 30 seconds

        activityTabsInitialized = true;
        console.log('Activity tabs initialized successfully');
    }

    // Load activity data for specific tab
    function loadActivityData(tab, isRefresh = false) {
        console.log('Loading activity data for tab:', tab, 'isRefresh:', isRefresh);

        const contentDiv = $(`.activity-content[data-tab="${tab}"]`);

        if (contentDiv.length === 0) {
            console.error('Content div not found for tab:', tab);
            return;
        }

        if (!isRefresh) {
            contentDiv.html(`
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading ${tab} activities...</p>
                </div>
            `);
        }

        const csrfToken = getCSRFToken();

        // Prepare headers - include CSRF token if available
        const headers = {
            'X-Requested-With': 'XMLHttpRequest'
        };

        if (csrfToken) {
            headers['X-CSRF-TOKEN'] = csrfToken;
        }

        $.ajax({
            url: "<?php echo e(route('admin.dashboard.activity')); ?>",
            type: "GET",
            data: { tab: tab, limit: 10 },
            dataType: 'json',
            headers: headers,
            success: function (response) {
                if (response.success) {
                    renderActivityContent(tab, response.data, contentDiv);
                } else {
                    console.error('Activity load failed:', response);
                    showActivityError(contentDiv, response.message || 'Failed to load activities');
                }
            },
            error: function (xhr, status, error) {
                console.error('Activity load error:', {
                    status: xhr.status,
                    statusText: xhr.statusText,
                    responseText: xhr.responseText,
                    error: error
                });

                let errorMessage = 'Error loading activities';
                if (xhr.status === 401) {
                    errorMessage = 'Authentication required. Please refresh the page.';
                } else if (xhr.status === 403) {
                    errorMessage = 'Access denied. Please check your permissions.';
                } else if (xhr.status === 404) {
                    errorMessage = 'Activity endpoint not found. Please contact support.';
                } else if (xhr.status === 419) {
                    errorMessage = 'CSRF token mismatch. Please refresh the page.';
                } else if (xhr.status === 500) {
                    errorMessage = 'Server error. Please try again later.';
                } else if (xhr.status === 0) {
                    errorMessage = 'Network error. Please check your connection.';
                }

                // Check for CSRF-related errors in response
                if (xhr.responseText && xhr.responseText.includes('CSRF')) {
                    errorMessage = 'CSRF token error. Please refresh the page.';
                }

                showActivityError(contentDiv, errorMessage);
            }
        });
    }

    // Render activity content as professional tables
    function renderActivityContent(tab, data, contentDiv) {
        if (!data || data.length === 0) {
            contentDiv.html(`
                <div class="text-center py-4">
                    <i class="las la-inbox text-muted" style="font-size: 3rem;"></i>
                    <p class="text-muted">No recent ${tab} activities found</p>
                </div>
            `);
            return;
        }

        let html = `
            <div class="table-responsive">
                <table class="table table-hover align-middle activity-table">
                    <thead class="table-dark">
                        ${getTableHeaders(tab)}
                    </thead>
                    <tbody>
        `;

        data.forEach(function(item) {
            html += getTableRow(tab, item);
        });

        html += `
                    </tbody>
                </table>
            </div>
            <div class="d-flex justify-content-between align-items-center mt-3">
                <small class="text-muted">Showing ${data.length} recent ${tab} activities</small>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn--primary w-80 h-25" onclick="loadActivityData('${tab}')">
                        <i class="las la-sync-alt"></i> Refresh
                    </button>
                    <button class="btn btn-outline-dark w-80 h-25" onclick="exportActivityData('${tab}')">
                        <i class="las la-download"></i> Export
                    </button>
                </div>
            </div>
        `;

        contentDiv.html(html);
    }

    // Get table headers for each tab
    function getTableHeaders(tab) {
        switch(tab) {
            case 'transactions':
                return `
                    <tr>
                        <th>Date</th>
                        <th>User</th>
                        <th>Type</th>
                        <th>Amount</th>
                        <th>Method</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                `;
            case 'accounts':
                return `
                    <tr>
                        <th>Date</th>
                        <th>Username</th>
                        <th>Email</th>
                        <th>Email Status</th>
                        <th>KYC Status</th>
                        <th>Actions</th>
                    </tr>
                `;
            case 'mt5':
                return `
                    <tr>
                        <th>Date</th>
                        <th>User</th>
                        <th>MT5 Login</th>
                        <th>Balance</th>
                        <th>Account Count</th>
                        <th>Actions</th>
                    </tr>
                `;
            case 'tickets':
                return `
                    <tr>
                        <th>Date</th>
                        <th>User</th>
                        <th>Subject</th>
                        <th>Priority</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                `;
            case 'kyc':
                return `
                    <tr>
                        <th>Date</th>
                        <th>User</th>
                        <th>Documents</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                `;
            case 'partnership':
                return `
                    <tr>
                        <th>Date</th>
                        <th>User</th>
                        <th>Type</th>
                        <th>Status</th>
                        <th>Details</th>
                        <th>Actions</th>
                    </tr>
                `;
            default:
                return `
                    <tr>
                        <th>Date</th>
                        <th>User</th>
                        <th>Activity</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                `;
        }
    }

    // Get table row for each tab
    function getTableRow(tab, item) {
        const timeAgo = moment(item.created_at).fromNow();
        const fullDate = moment(item.created_at).format('MMM DD, YYYY HH:mm');

        switch(tab) {
            case 'transactions':
                return `
                    <tr>
                        <td>
                            <span class="d-block fw-bold">${moment(item.created_at).format('MMM DD')}</span>
                            <small class="text-muted">${moment(item.created_at).format('HH:mm')}</small>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm me-2">
                                    <span class="avatar-title bg-light text-dark rounded-circle">
                                        ${(item.user_name || item.user).charAt(0).toUpperCase()}
                                    </span>
                                </div>
                                <div>
                                    <span class="fw-bold d-block">${item.user}</span>
                                    <small class="text-muted">${item.user_name}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge ${item.type === 'deposit' ? 'badge--success' : 'badge--danger'}">
                                <i class="las ${item.type === 'deposit' ? 'la-arrow-down' : 'la-arrow-up'}"></i>
                                ${item.type.charAt(0).toUpperCase() + item.type.slice(1)}
                            </span>
                        </td>
                        <td>
                            <span class="fw-bold ${item.type === 'deposit' ? 'text--success' : 'text--danger'}">
                                ${item.type === 'deposit' ? '+' : '-'}$${item.amount}
                            </span>
                        </td>
                        <td>
                            <span class="badge badge--dark">${item.method || 'N/A'}</span>
                        </td>
                        <td>
                            <span class="badge ${item.status_class}">${getStatusText(item.status)}</span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="${item.url}" class="btn btn-outline-primary btn-sm" title="View Details">
                                    <i class="las la-eye"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                `;

            case 'accounts':
                return `
                    <tr>
                        <td>
                            <span class="d-block fw-bold">${moment(item.created_at).format('MMM DD')}</span>
                            <small class="text-muted">${moment(item.created_at).format('HH:mm')}</small>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm me-2">
                                    <span class="avatar-title bg-light text-dark rounded-circle">
                                        ${(item.user_name || item.user).charAt(0).toUpperCase()}
                                    </span>
                                </div>
                                <div>
                                    <span class="fw-bold d-block">${item.user}</span>
                                    <small class="text-muted">${item.user_name}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="text-muted">${item.email || 'N/A'}</span>
                        </td>
                        <td>
                            <span class="badge ${item.verification_status?.email === 'verified' ? 'badge--success' : 'badge--warning'}">
                                ${item.verification_status?.email || 'Unverified'}
                            </span>
                        </td>
                        <td>
                            <span class="badge ${item.verification_status?.kyc === 'verified' ? 'badge--success' : (item.verification_status?.kyc === 'pending' ? 'badge--warning' : 'badge--danger')}">
                                ${item.verification_status?.kyc || 'Not Submitted'}
                            </span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="${item.url}" class="btn btn-outline-primary btn-sm" title="View Profile">
                                    <i class="las la-user"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                `;

            case 'mt5':
                return `
                    <tr>
                        <td>
                            <span class="d-block fw-bold">${moment(item.created_at).format('MMM DD')}</span>
                            <small class="text-muted">${moment(item.created_at).format('HH:mm')}</small>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm me-2">
                                    <span class="avatar-title bg-light text-dark rounded-circle">
                                        ${(item.user_name || item.user).charAt(0).toUpperCase()}
                                    </span>
                                </div>
                                <div>
                                    <span class="fw-bold d-block">${item.user}</span>
                                    <small class="text-muted">${item.user_name}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge badge--primary">${item.mt5_login || 'N/A'}</span>
                        </td>
                        <td>
                            <span class="fw-bold text--success">$${item.mt5_balance || '0.00'}</span>
                        </td>
                        <td>
                            <span class="badge badge--info">${item.account_count || 1} account(s)</span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="${item.url}" class="btn btn-outline-primary btn-sm" title="View MT5 Details">
                                    <i class="las la-chart-line"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                `;

            case 'tickets':
                return `
                    <tr>
                        <td>
                            <span class="d-block fw-bold">${moment(item.created_at).format('MMM DD')}</span>
                            <small class="text-muted">${moment(item.created_at).format('HH:mm')}</small>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm me-2">
                                    <span class="avatar-title bg-light text-dark rounded-circle">
                                        ${(item.user_name || item.user).charAt(0).toUpperCase()}
                                    </span>
                                </div>
                                <div>
                                    <span class="fw-bold d-block">${item.user}</span>
                                    <small class="text-muted">${item.user_name}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="text-truncate d-block" style="max-width: 200px;" title="${item.subject || item.activity}">
                                ${item.subject || item.activity || 'Support Request'}
                            </span>
                            ${item.ticket_number ? `<small class="text-muted">#${item.ticket_number}</small>` : ''}
                        </td>
                        <td>
                            <span class="badge ${item.priority_class || 'badge--info'}">
                                ${item.priority === 1 ? 'High' : item.priority === 2 ? 'Medium' : 'Low'}
                            </span>
                        </td>
                        <td>
                            <span class="badge ${item.status_class}">${getStatusText(item.status)}</span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="${item.url}" class="btn btn-outline-primary btn-sm" title="View Ticket">
                                    <i class="las la-ticket-alt"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                `;

            case 'kyc':
                return `
                    <tr>
                        <td>
                            <span class="d-block fw-bold">${moment(item.created_at).format('MMM DD')}</span>
                            <small class="text-muted">${moment(item.created_at).format('HH:mm')}</small>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm me-2">
                                    <span class="avatar-title bg-light text-dark rounded-circle">
                                        ${(item.user_name || item.user).charAt(0).toUpperCase()}
                                    </span>
                                </div>
                                <div>
                                    <span class="fw-bold d-block">${item.user}</span>
                                    <small class="text-muted">${item.user_name}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="text-muted">${item.document_types || 'Documents Submitted'}</span>
                            ${item.document_count ? `<br><small class="badge badge--info">${item.document_count} file(s)</small>` : ''}
                        </td>
                        <td>
                            <span class="badge ${item.status_class}">${getStatusText(item.status)}</span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="${item.url}" class="btn btn-outline-primary btn-sm" title="View KYC">
                                    <i class="las la-file-alt"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                `;

            case 'partnership':
                return `
                    <tr>
                        <td>
                            <span class="d-block fw-bold">${moment(item.created_at).format('MMM DD')}</span>
                            <small class="text-muted">${moment(item.created_at).format('HH:mm')}</small>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm me-2">
                                    <span class="avatar-title bg-light text-dark rounded-circle">
                                        ${(item.user_name || item.user).charAt(0).toUpperCase()}
                                    </span>
                                </div>
                                <div>
                                    <span class="fw-bold d-block">${item.user}</span>
                                    <small class="text-muted">${item.user_name}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge ${item.type === 'ib_application' ? 'badge--primary' : 'badge--info'}">
                                ${item.type === 'ib_application' ? 'IB Application' : 'IB Activity'}
                            </span>
                        </td>
                        <td>
                            <span class="badge ${item.status_class}">${getStatusText(item.status)}</span>
                        </td>
                        <td>
                            <span class="text-muted small">${item.activity || 'Partnership Activity'}</span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="${item.url}" class="btn btn-outline-primary btn-sm" title="View Details">
                                    <i class="las la-handshake"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                `;

            default:
                return `
                    <tr>
                        <td>
                            <span class="d-block fw-bold">${moment(item.created_at).format('MMM DD')}</span>
                            <small class="text-muted">${moment(item.created_at).format('HH:mm')}</small>
                        </td>
                        <td>
                            <span class="fw-bold">${item.user || 'System'}</span>
                        </td>
                        <td>
                            <span class="text-muted">${item.activity || item.title || 'Activity'}</span>
                        </td>
                        <td>
                            ${item.status ? `<span class="badge ${item.status_class || 'badge--primary'}">${item.status}</span>` : ''}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="${item.url || '#'}" class="btn btn-outline-primary btn-sm" title="View Details">
                                    <i class="las la-eye"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                `;
        }
    }

    // Export activity data
    function exportActivityData(tab) {
        // Simple CSV export functionality
        const table = document.querySelector(`.activity-content[data-tab="${tab}"] table`);
        if (!table) return;

        let csv = [];
        const rows = table.querySelectorAll('tr');

        for (let i = 0; i < rows.length; i++) {
            const row = [];
            const cols = rows[i].querySelectorAll('td, th');

            for (let j = 0; j < cols.length - 1; j++) { // Exclude actions column
                let cellText = cols[j].innerText.replace(/"/g, '""');
                row.push('"' + cellText + '"');
            }
            csv.push(row.join(','));
        }

        const csvContent = csv.join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${tab}_activities_${moment().format('YYYY-MM-DD')}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);
    }

    // Render individual activity item
    function renderActivityItem(tab, item) {
        const timeAgo = moment(item.created_at).fromNow();

        switch(tab) {
            case 'transactions':
                return `
                    <div class="activity-item border-bottom py-3">
                        <div class="d-flex align-items-center">
                            <div class="activity-icon me-3">
                                <i class="las ${item.type === 'deposit' ? 'la-arrow-down text-success' : 'la-arrow-up text-danger'}"></i>
                            </div>
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">${item.user} - ${item.type.charAt(0).toUpperCase() + item.type.slice(1)}</h6>
                                        <p class="mb-1 text-muted small">${item.user_name}</p>
                                        <small class="text-muted">${timeAgo}</small>
                                    </div>
                                    <div class="text-end">
                                        <span class="fw-bold">${item.amount}</span>
                                        <br>
                                        <span class="badge ${item.status_class}">${getStatusText(item.status)}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

            case 'accounts':
                return `
                    <div class="activity-item border-bottom py-3">
                        <div class="d-flex align-items-center">
                            <div class="activity-icon me-3">
                                <i class="las la-user-plus text-primary"></i>
                            </div>
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">${item.user}</h6>
                                        <p class="mb-1 text-muted small">${item.user_name}</p>
                                        <small class="text-muted">${timeAgo}</small>
                                    </div>
                                    <div class="text-end">
                                        <small class="d-block">Email: <span class="badge ${item.verification_status.email === 'verified' ? 'badge--success' : 'badge--warning'}">${item.verification_status.email}</span></small>
                                        <small class="d-block">KYC: <span class="badge ${item.verification_status.kyc === 'verified' ? 'badge--success' : (item.verification_status.kyc === 'pending' ? 'badge--warning' : 'badge--danger')}">${item.verification_status.kyc}</span></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

            default:
                return `
                    <div class="activity-item border-bottom py-3">
                        <div class="d-flex align-items-center">
                            <div class="activity-icon me-3">
                                <i class="las la-bell text-info"></i>
                            </div>
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">${item.user || 'System'}</h6>
                                        <p class="mb-1 text-muted small">${item.activity || item.title || 'Activity'}</p>
                                        <small class="text-muted">${timeAgo}</small>
                                    </div>
                                    <div class="text-end">
                                        ${item.status ? `<span class="badge ${item.status_class || 'badge--primary'}">${item.status}</span>` : ''}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
        }
    }

    // Show error message
    function showActivityError(contentDiv, message) {
        console.log('Showing activity error:', message);
        contentDiv.html(`
            <div class="text-center py-4">
                <i class="las la-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                <p class="text-muted">${message}</p>
                <button class="btn btn--primary btn-sm" onclick="loadActivityData('${currentActiveTab}')">
                    <i class="las la-redo-alt"></i> Retry
                </button>
                <div class="mt-3">
                    <small class="text-muted">
                        Check browser console for detailed error information.
                        <br>Route: <?php echo e(route('admin.dashboard.activity')); ?>

                    </small>
                </div>
            </div>
        `);
    }

    // Get status text
    function getStatusText(status) {
        switch(status) {
            case 1: return 'Approved';
            case 2: return 'Pending';
            case 3: return 'Rejected';
            case 'approved': return 'Approved';
            case 'pending': return 'Pending';
            case 'rejected': return 'Rejected';
            default: return 'Unknown';
        }
    }

    // Initialize when document is ready
    $(document).ready(function() {
        // Add moment.js if not already included
        if (typeof moment === 'undefined') {
            $('head').append('<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"><\/script>');
        }

        // Initialize activity tabs after charts are loaded and DOM is ready
        setTimeout(function() {
            console.log('Initializing activity tabs after DOM ready...');
            initializeActivityTabs();
        }, 1000);
    });

    // Also initialize on window load as a fallback
    $(window).on('load', function() {
        if (!activityTabsInitialized) {
            console.log('Fallback initialization on window load...');
            setTimeout(initializeActivityTabs, 500);
        }
    });

    // Clean up interval on page unload
    $(window).on('beforeunload', function() {
        if (refreshInterval) {
            clearInterval(refreshInterval);
        }
    });

</script>
<?php $__env->stopPush(); ?>


<?php $__env->startPush('style'); ?>
    <style>
        .user .thumb {
            width: 35px;
            height: 35px;
        }
        .list-group-item {

            border: 1px solid rgba(0,0,0,.045);
        }
        #deposit-chart{
            margin-left: -20px;
        }
        .order-list, .deposit-list, .withdraw-list {
            max-height: 300px;
            overflow-y: auto;
            margin-bottom: 1.5rem;
        }

        /* Professional Activity Tables Styling - Using Existing Theme Colors */
        .activity-table {
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        .activity-table thead th {
            background-color: #e3373f !important; /* Using existing --dark color */
            color: white !important;
            font-weight: 600;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: none;
            padding: 1rem 0.75rem;
        }

        .activity-table tbody tr {
            transition: all 0.2s ease;
            border-bottom: 1px solid rgba(16, 22, 58, 0.1); /* Using --dark with opacity */
        }

        .activity-table tbody tr:hover {
            background-color: rgba(227, 55, 63, 0.05); /* Using --primary with low opacity */
            transform: translateY(-1px);
            box-shadow: 0 0.25rem 0.5rem rgba(227, 55, 63, 0.1); /* Using --primary shadow */
        }

        .activity-table tbody td {
            padding: 1rem 0.75rem;
            vertical-align: middle;
            border: none;
            font-size: 0.875rem;
        }

        .activity-table .avatar-sm {
            width: 35px;
            height: 35px;
        }

        .activity-table .avatar-title {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.875rem;
            background-color: rgba(227, 55, 63, 0.1); /* Using --primary with opacity */
            color: #E3373F; /* Using --primary color */
        }

        /* Remove custom badge colors - use existing theme badge classes */
        /* All badge styling is now handled by existing badge--success, badge--warning, etc. classes */

        /* Action Buttons - Using Existing Theme Colors */
        .activity-table .btn-outline-primary {
            border-color: #E3373F; /* Using --primary */
            color: #E3373F;
        }

        .activity-table .btn-outline-primary:hover {
            background-color: #E3373F; /* Using --primary */
            border-color: #E3373F;
            color: white;
        }

        .activity-table .btn-outline-secondary {
            border-color: #868e96; /* Using existing secondary color */
            color: #868e96;
        }

        .activity-table .btn-outline-secondary:hover {
            background-color: #868e96;
            border-color: #868e96;
            color: white;
        }

        /* Activity Tab Content */
        .activity-content {
            min-height: 400px;
            max-height: 600px;
            overflow-y: auto;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .activity-table {
                font-size: 0.8rem;
            }

            .activity-table thead th,
            .activity-table tbody td {
                padding: 0.5rem 0.25rem;
            }

            .activity-table .avatar-sm {
                width: 30px;
                height: 30px;
            }

            .btn-group-sm .btn {
                padding: 0.25rem 0.5rem;
                font-size: 0.75rem;
            }
        }

        /* Table Responsive Wrapper - Using Existing Border Colors */
        .table-responsive {
            border-radius: 0.5rem;
            border: 1px solid rgba(16, 22, 58, 0.15); /* Using --dark with opacity */
        }

        /* Custom Scrollbar for Activity Content - Using Theme Colors */
        .activity-content::-webkit-scrollbar {
            width: 6px;
        }

        .activity-content::-webkit-scrollbar-track {
            background: rgba(16, 22, 58, 0.1); /* Using --dark with low opacity */
            border-radius: 3px;
        }

        .activity-content::-webkit-scrollbar-thumb {
            background: #E3373F; /* Using --primary */
            border-radius: 3px;
        }

        .activity-content::-webkit-scrollbar-thumb:hover {
            background: rgba(227, 55, 63, 0.8); /* Using --primary with opacity */
        }
        span.badge.badge--info {
            color: black;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\mbf.mybrokerforex.com-31052025\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>