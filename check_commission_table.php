<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 CHECKING COMMISSION TABLE STRUCTURE\n";
echo "=====================================\n";

try {
    // Check ib_commissions table structure
    echo "\n📊 ib_commissions table structure:\n";
    $columns = DB::select('DESCRIBE ib_commissions');
    foreach($columns as $col) {
        echo "- {$col->Field} ({$col->Type}) " . ($col->Null === 'YES' ? 'NULL' : 'NOT NULL') . "\n";
    }
    
    // Check record count
    $count = DB::table('ib_commissions')->count();
    echo "\n📈 Records in ib_commissions: {$count}\n";
    
    // Check users table for IB-related fields
    echo "\n📊 users table IB fields:\n";
    $userColumns = DB::select("DESCRIBE users");
    foreach($userColumns as $col) {
        if (stripos($col->Field, 'ib') !== false || 
            stripos($col->Field, 'commission') !== false ||
            stripos($col->Field, 'partner') !== false ||
            stripos($col->Field, 'ref') !== false) {
            echo "- {$col->Field} ({$col->Type})\n";
        }
    }
    
    // Check MT5 deals table structure
    echo "\n📊 MT5 deals table structure (sample):\n";
    $mt5Columns = DB::connection('mbf-dbmt5')->select('DESCRIBE mt5_deals_2025 LIMIT 10');
    foreach($mt5Columns as $col) {
        echo "- {$col->Field} ({$col->Type})\n";
    }
    
    // Check for real commission data in MT5
    echo "\n📊 MT5 commission data sample:\n";
    $mt5Deals = DB::connection('mbf-dbmt5')
        ->table('mt5_deals_2025')
        ->where('Profit', '>', 0)
        ->limit(5)
        ->get(['Deal', 'Login', 'Symbol', 'Volume', 'Profit', 'Commission', 'Time']);
    
    foreach($mt5Deals as $deal) {
        echo "- Deal {$deal->Deal}: Login {$deal->Login}, Profit: {$deal->Profit}, Commission: {$deal->Commission}\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n✅ Table structure check completed!\n";
