@extends($activeTemplate . 'layouts.master')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title d-flex justify-content-between align-items-center">
                <h4 class="mb-0">@lang('My Commissions')</h4>
                <div class="page-actions">
                    <a href="{{ route('user.partnership.network') }}" class="btn btn--primary btn-sm">
                        <i class="las la-sitemap"></i> @lang('View Network')
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Commission Summary Cards -->
    <div class="row gy-4 mb-4">
        <div class="col-xl-3 col-lg-4 col-sm-6">
            <div class="dashboard-widget bg-gradient-primary">
                <div class="dashboard-widget__icon">
                    <i class="las la-dollar-sign text-white"></i>
                </div>
                <div class="dashboard-widget__content">
                    <h4 class="dashboard-widget__number text-white">
                        ${{ number_format($commissionSummary['basic_stats']['total_commissions'] ?? 0, 2) }}
                    </h4>
                    <span class="dashboard-widget__caption text-white-50">@lang('Total Commissions')</span>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-4 col-sm-6">
            <div class="dashboard-widget bg-gradient-success">
                <div class="dashboard-widget__icon">
                    <i class="las la-check-circle text-white"></i>
                </div>
                <div class="dashboard-widget__content">
                    <h4 class="dashboard-widget__number text-white">
                        ${{ number_format($commissionSummary['basic_stats']['paid_commissions'] ?? 0, 2) }}
                    </h4>
                    <span class="dashboard-widget__caption text-white-50">@lang('Paid Commissions')</span>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-4 col-sm-6">
            <div class="dashboard-widget bg-gradient-warning">
                <div class="dashboard-widget__icon">
                    <i class="las la-clock text-white"></i>
                </div>
                <div class="dashboard-widget__content">
                    <h4 class="dashboard-widget__number text-white">
                        ${{ number_format($commissionSummary['basic_stats']['pending_commissions'] ?? 0, 2) }}
                    </h4>
                    <span class="dashboard-widget__caption text-white-50">@lang('Pending Commissions')</span>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-4 col-sm-6">
            <div class="dashboard-widget bg-gradient-info">
                <div class="dashboard-widget__icon">
                    <i class="las la-chart-line text-white"></i>
                </div>
                <div class="dashboard-widget__content">
                    <h4 class="dashboard-widget__number text-white">{{ number_format($commissionSummary['basic_stats']['total_trades'] ?? 0) }}</h4>
                    <span class="dashboard-widget__caption text-white-50">@lang('Total Trades')</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="las la-filter text-primary"></i>
                            @lang('Filter Commissions')
                        </h5>
                        <small class="text-muted">@lang('Real-time MT5 commission data')</small>
                    </div>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('user.ib.commissions') }}" id="commissionFilterForm">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">@lang('Start Date')</label>
                                    <input type="date" name="start_date" class="form-control form-control-sm" value="{{ $startDate }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">@lang('End Date')</label>
                                    <input type="date" name="end_date" class="form-control form-control-sm" value="{{ $endDate }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label class="form-label">@lang('Status')</label>
                                    <select name="status" class="form-control form-control-sm">
                                        <option value="">@lang('All Status')</option>
                                        <option value="pending" {{ $status == 'pending' ? 'selected' : '' }}>@lang('Pending')</option>
                                        <option value="paid" {{ $status == 'paid' ? 'selected' : '' }}>@lang('Paid')</option>
                                        <option value="cancelled" {{ $status == 'cancelled' ? 'selected' : '' }}>@lang('Cancelled')</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label class="form-label">@lang('Symbol')</label>
                                    <select name="symbol" class="form-control form-control-sm">
                                        <option value="">@lang('All Symbols')</option>
                                        @foreach($symbols as $sym)
                                            <option value="{{ $sym }}" {{ $symbol == $sym ? 'selected' : '' }}>{{ $sym }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn--primary btn-sm flex-fill">
                                            <i class="las la-search"></i> @lang('Filter')
                                        </button>
                                        <a href="{{ route('user.ib.commissions') }}" class="btn btn--secondary btn-sm">
                                            <i class="las la-redo"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Commissions Table -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="las la-history text-primary"></i>
                            @lang('Commission History')
                        </h5>
                        <div class="d-flex align-items-center gap-2">
                            <small class="text-muted">{{ $commissions->total() }} @lang('records')</small>
                            @if($commissions->count() > 0)
                            <button class="btn btn--primary btn-sm" onclick="exportCommissions()">
                                <i class="las la-download"></i> @lang('Export')
                            </button>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th class="text-white">@lang('Date')</th>
                                    <th class="text-white">@lang('Trade ID')</th>
                                    <th class="text-white">@lang('Client')</th>
                                    <th class="text-white">@lang('Symbol')</th>
                                    <th class="text-white">@lang('Volume')</th>
                                    <th class="text-white">@lang('Level')</th>
                                    <th class="text-white">@lang('Rate')</th>
                                    <th class="text-white">@lang('Commission')</th>
                                    <th class="text-white">@lang('Status')</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($commissions as $commission)
                                <tr>
                                    <td>
                                        <div class="d-flex flex-column">
                                            <span class="fw-bold">{{ \Carbon\Carbon::parse($commission->trade_closed_at)->format('M d, Y') }}</span>
                                            <small class="text-muted">{{ \Carbon\Carbon::parse($commission->trade_closed_at)->format('H:i:s') }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="fw-bold text-primary">#{{ $commission->trade_id }}</span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                <i class="las la-user text-white"></i>
                                            </div>
                                            <span>{{ $commission->fromUser->username ?? 'MT5 Client' }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info text-white">{{ $commission->symbol }}</span>
                                    </td>
                                    <td>
                                        <span class="fw-bold">{{ number_format($commission->volume, 2) }}</span>
                                        <small class="text-muted d-block">lots</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">Level {{ $commission->level }}</span>
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ number_format($commission->commission_rate, 2) }}%</span>
                                    </td>
                                    <td>
                                        <span class="fw-bold text-success fs-6">
                                            ${{ number_format($commission->commission_amount, 2) }}
                                        </span>
                                    </td>
                                    <td>
                                        @if($commission->status == 'paid')
                                            <span class="badge bg-success">@lang('Paid')</span>
                                        @elseif($commission->status == 'pending')
                                            <span class="badge bg-warning">@lang('Pending')</span>
                                        @else
                                            <span class="badge bg-danger">@lang('Cancelled')</span>
                                        @endif
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="9" class="text-center py-5">
                                        <div class="d-flex flex-column align-items-center">
                                            <i class="las la-chart-line text-muted" style="font-size: 3rem;"></i>
                                            <h6 class="text-muted mt-2">@lang('No commissions found')</h6>
                                            <p class="text-muted small">@lang('Commission data will appear here when trades generate commissions')</p>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
                @if($commissions->hasPages())
                <div class="card-footer bg-white border-top">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="text-muted small">
                            @lang('Showing') {{ $commissions->firstItem() }} @lang('to') {{ $commissions->lastItem() }}
                            @lang('of') {{ $commissions->total() }} @lang('results')
                        </div>
                        {{ $commissions->appends(request()->query())->links() }}
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Level Breakdown -->
    @if(isset($commissionSummary['level_breakdown']) && $commissionSummary['level_breakdown']->count() > 0)
    <div class="row mt-4">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">@lang('Commission by Level')</h5>
                </div>
                <div class="card-body">
                    @foreach($commissionSummary['level_breakdown'] as $breakdown)
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <h6 class="mb-0">Level {{ $breakdown->level }}</h6>
                            <small class="text-muted">{{ $breakdown->total_trades }} trades</small>
                        </div>
                        <div class="text-end">
                            <span class="fw-bold">{{ showAmount($breakdown->total_amount) }}</span>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">@lang('Top Symbols')</h5>
                </div>
                <div class="card-body">
                    @foreach($commissionSummary['symbol_breakdown']->take(5) as $breakdown)
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <h6 class="mb-0">{{ $breakdown->symbol }}</h6>
                            <small class="text-muted">{{ $breakdown->total_trades }} trades | {{ $breakdown->total_volume }} lots</small>
                        </div>
                        <div class="text-end">
                            <span class="fw-bold">{{ showAmount($breakdown->total_amount) }}</span>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
    @endif
</div>
@endsection

@push('style')
<style>
/* Commission Page Enhancements */
.bg-gradient-primary {
    background: linear-gradient(135deg, #dc3545, #c82333) !important;
}

.bg-gradient-success {
    background: linear-gradient(135deg, #28a745, #218838) !important;
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800) !important;
}

.bg-gradient-info {
    background: linear-gradient(135deg, #17a2b8, #138496) !important;
}

.dashboard-widget {
    border-radius: 12px;
    border: none;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.dashboard-widget:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 14px;
}

.table-hover tbody tr:hover {
    background-color: rgba(220, 53, 69, 0.05);
}

.card {
    border-radius: 12px;
}

.card-header {
    border-radius: 12px 12px 0 0 !important;
}

.form-control-sm {
    font-size: 12px;
    padding: 0.375rem 0.75rem;
}

.btn-sm {
    font-size: 12px;
    padding: 0.375rem 0.75rem;
}

.page-actions .btn {
    border-radius: 8px;
}

/* Loading animation */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #dc3545;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translate(-50%, -50%);
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}
</style>
@endpush

@push('script')
<script>
$(document).ready(function() {
    // Auto-submit form on filter change
    $('#commissionFilterForm select, #commissionFilterForm input[type="date"]').change(function() {
        // Add loading state
        $(this).closest('.card').addClass('loading');

        // Submit form after short delay
        setTimeout(function() {
            $('#commissionFilterForm').submit();
        }, 500);
    });

    // Export functionality
    window.exportCommissions = function() {
        const params = new URLSearchParams(window.location.search);
        params.set('export', 'csv');

        const exportUrl = '{{ route("user.ib.commissions") }}?' + params.toString();
        window.open(exportUrl, '_blank');
    };

    // Tooltip initialization
    $('[data-bs-toggle="tooltip"]').tooltip();

    // Auto-refresh every 5 minutes for real-time data
    setInterval(function() {
        if (!document.hidden) {
            location.reload();
        }
    }, 300000); // 5 minutes
});
</script>
@endpush
