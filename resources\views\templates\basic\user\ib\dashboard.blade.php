@extends($activeTemplate . 'layouts.master')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title">
                <h4>@lang('IB Dashboard')</h4>
                <div class="page-title-right">
                    <span class="badge badge--success">{{ ucfirst($user->ib_type) }} IB</span>
                    @if($user->ibGroup)
                        <span class="badge badge--primary">{{ $user->ibGroup->name }}</span>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- IB Overview Cards -->
    <div class="row gy-4">
        <div class="col-xl-3 col-lg-4 col-sm-6">
            <div class="dashboard-widget">
                <div class="dashboard-widget__icon">
                    <i class="las la-dollar-sign"></i>
                </div>
                <div class="dashboard-widget__content">
                    <h4 class="dashboard-widget__number">
                        {{ showAmount($dashboardData['commission_summary']['basic_stats']['total_commissions'] ?? 0) }}
                    </h4>
                    <span class="dashboard-widget__caption">@lang('Total Commissions')</span>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-4 col-sm-6">
            <div class="dashboard-widget">
                <div class="dashboard-widget__icon">
                    <i class="las la-users"></i>
                </div>
                <div class="dashboard-widget__content">
                    <h4 class="dashboard-widget__number">{{ $user->getTotalReferredClients() }}</h4>
                    <span class="dashboard-widget__caption">@lang('Total Clients')</span>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-4 col-sm-6">
            <div class="dashboard-widget">
                <div class="dashboard-widget__icon">
                    <i class="las la-chart-line"></i>
                </div>
                <div class="dashboard-widget__content">
                    <h4 class="dashboard-widget__number">{{ $user->getActiveReferredClients() }}</h4>
                    <span class="dashboard-widget__caption">@lang('Active Clients')</span>
                </div>
            </div>
        </div>

        @if($user->isMasterIb())
        <div class="col-xl-3 col-lg-4 col-sm-6">
            <div class="dashboard-widget">
                <div class="dashboard-widget__icon">
                    <i class="las la-sitemap"></i>
                </div>
                <div class="dashboard-widget__content">
                    <h4 class="dashboard-widget__number">{{ $dashboardData['sub_ibs']->count() }}</h4>
                    <span class="dashboard-widget__caption">@lang('Sub IBs')</span>
                </div>
            </div>
        </div>
        @endif
    </div>

    <div class="row gy-4 mt-4">
        <!-- Referral Information -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">@lang('Referral Information')</h5>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label>@lang('Your Referral Code')</label>
                        <div class="input-group">
                            <input type="text" class="form-control" value="{{ $user->referral_code }}" readonly>
                            <button class="btn btn--primary copy-btn" data-copy="{{ $user->referral_code }}">
                                <i class="las la-copy"></i> @lang('Copy')
                            </button>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>@lang('Referral Link')</label>
                        <div class="input-group">
                            <input type="text" class="form-control" value="{{ route('register') }}?ref={{ $user->referral_code }}" readonly>
                            <button class="btn btn--primary copy-btn" data-copy="{{ route('register') }}?ref={{ $user->referral_code }}">
                                <i class="las la-copy"></i> @lang('Copy')
                            </button>
                        </div>
                    </div>

                    @if($user->ibParent)
                    <div class="form-group">
                        <label>@lang('Your Parent IB')</label>
                        <input type="text" class="form-control" value="{{ $user->ibParent->fullname }} ({{ $user->ibParent->username }})" readonly>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">@lang('Performance Metrics (30 Days)')</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text--success">
                                {{ showAmount($performanceMetrics['paid_commissions'] ?? 0) }}
                            </h4>
                            <span class="caption">@lang('Paid Commissions')</span>
                        </div>
                        <div class="col-6">
                            <h4 class="text--warning">
                                {{ showAmount($performanceMetrics['pending_commissions'] ?? 0) }}
                            </h4>
                            <span class="caption">@lang('Pending Commissions')</span>
                        </div>
                    </div>
                    <div class="row text-center mt-3">
                        <div class="col-6">
                            <h4 class="text--info">{{ $performanceMetrics['total_trades'] ?? 0 }}</h4>
                            <span class="caption">@lang('Total Trades')</span>
                        </div>
                        <div class="col-6">
                            <h4 class="text--primary">{{ number_format($performanceMetrics['conversion_rate'] ?? 0, 2) }}%</h4>
                            <span class="caption">@lang('Conversion Rate')</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row mt-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title">@lang('Recent Commissions')</h5>
                    <a href="{{ route('user.ib.commissions') }}" class="btn btn--primary btn-sm">
                        @lang('View All')
                    </a>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table--light">
                            <thead>
                                <tr>
                                    <th>@lang('Date')</th>
                                    <th>@lang('Client')</th>
                                    <th>@lang('Symbol')</th>
                                    <th>@lang('Volume')</th>
                                    <th>@lang('Commission')</th>
                                    <th>@lang('Level')</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($recentCommissions as $commission)
                                <tr>
                                    <td>{{ showDateTime($commission->created_at, 'd M Y') }}</td>
                                    <td>{{ $commission->fromUser->username }}</td>
                                    <td>{{ $commission->symbol }}</td>
                                    <td>{{ $commission->volume }}</td>
                                    <td>{{ showAmount($commission->commission_amount) }}</td>
                                    <td>
                                        <span class="badge badge--info">Level {{ $commission->level }}</span>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="6" class="text-center">@lang('No commissions yet')</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        @if($user->isMasterIb() && $dashboardData['sub_ibs']->count() > 0)
        <!-- Sub IBs -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title">@lang('My Sub IBs')</h5>
                    <a href="{{ route('user.ib.sub_ibs') }}" class="btn btn--primary btn-sm">
                        @lang('View All')
                    </a>
                </div>
                <div class="card-body">
                    @foreach($dashboardData['sub_ibs']->take(5) as $subIb)
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <h6 class="mb-0">{{ $subIb->fullname }}</h6>
                            <small class="text-muted">{{ $subIb->username }}</small>
                            @if($subIb->ibGroup)
                                <br><small class="text-info">{{ $subIb->ibGroup->name }}</small>
                            @endif
                        </div>
                        <div class="text-end">
                            <span class="badge badge--{{ $subIb->ib_status == 'approved' ? 'success' : 'warning' }}">
                                {{ ucfirst($subIb->ib_status) }}
                            </span>
                            <br>
                            <small class="text-muted">
                                {{ showAmount($subIb->ibCommissionsEarned->sum('commission_amount')) }}
                            </small>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
        @endif
    </div>

    @if($user->isMasterIb())
    <!-- Master IB Network Management -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title">@lang('Network Hierarchy Management')</h5>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn--primary active" id="networkHierarchyBtn" onclick="toggleNetworkView('hierarchy')">
                                <i class="las la-sitemap"></i> @lang('Hierarchy View')
                            </button>
                            <button type="button" class="btn btn-sm btn-outline--primary" id="networkTableBtn" onclick="toggleNetworkView('table')">
                                <i class="las la-table"></i> @lang('Table View')
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Hierarchy View -->
                    <div id="networkHierarchyView">
                        <div class="network-tree">
                            @include('templates.basic.user.ib.partials.master-network-tree', [
                                'user' => $user,
                                'level' => 0,
                                'maxDepth' => 4
                            ])
                        </div>
                    </div>

                    <!-- Table View -->
                    <div id="networkTableView" style="display: none;">
                        <div class="table-responsive">
                            <table class="table table--light">
                                <thead>
                                    <tr>
                                        <th>@lang('User')</th>
                                        <th>@lang('Level')</th>
                                        <th>@lang('Type')</th>
                                        <th>@lang('Group')</th>
                                        <th>@lang('Commissions')</th>
                                        <th>@lang('Referrals')</th>
                                        <th>@lang('Status')</th>
                                        <th>@lang('Action')</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @include('templates.basic.user.ib.partials.master-network-table', [
                                        'users' => $user->getAllReferralsWithLevel(5),
                                        'masterIb' => $user
                                    ])
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">@lang('Quick Actions')</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="{{ route('user.ib.commissions') }}" class="btn btn--primary w-100">
                                <i class="las la-dollar-sign"></i> @lang('View Commissions')
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('user.ib.hierarchy') }}" class="btn btn--info w-100">
                                <i class="las la-sitemap"></i> @lang('View Hierarchy')
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('user.ib.reports') }}" class="btn btn--success w-100">
                                <i class="las la-chart-bar"></i> @lang('View Reports')
                            </a>
                        </div>
                        @if($user->isMasterIb())
                        <div class="col-md-3">
                            <a href="{{ route('user.ib.sub_ibs') }}" class="btn btn--warning w-100">
                                <i class="las la-users"></i> @lang('Manage Sub IBs')
                            </a>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('script')
<script>
(function($) {
    "use strict";

    // Copy functionality
    $('.copy-btn').on('click', function() {
        const textToCopy = $(this).data('copy');
        navigator.clipboard.writeText(textToCopy).then(function() {
            iziToast.success({
                message: 'Copied to clipboard!',
                position: "topRight"
            });
        });
    });

    // Network view toggle for Master IBs
    window.toggleNetworkView = function(viewType) {
        const hierarchyView = document.getElementById('networkHierarchyView');
        const tableView = document.getElementById('networkTableView');
        const hierarchyBtn = document.getElementById('networkHierarchyBtn');
        const tableBtn = document.getElementById('networkTableBtn');

        if (viewType === 'hierarchy') {
            hierarchyView.style.display = 'block';
            tableView.style.display = 'none';
            hierarchyBtn.classList.add('active', 'btn--primary');
            hierarchyBtn.classList.remove('btn-outline--primary');
            tableBtn.classList.remove('active', 'btn--primary');
            tableBtn.classList.add('btn-outline--primary');
        } else {
            hierarchyView.style.display = 'none';
            tableView.style.display = 'block';
            tableBtn.classList.add('active', 'btn--primary');
            tableBtn.classList.remove('btn-outline--primary');
            hierarchyBtn.classList.remove('active', 'btn--primary');
            hierarchyBtn.classList.add('btn-outline--primary');
        }
    };

})(jQuery);
</script>
@endpush
