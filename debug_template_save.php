<?php
/**
 * COMPREHENSIVE DEBUG: Template Save Functionality
 * Run this script to thoroughly investigate template save issues
 */

echo "🔧 COMPREHENSIVE TEMPLATE SAVE DEBUGGING\n";
echo "========================================\n\n";

// Check PHP version compatibility
echo "🔧 PHP Version: " . PHP_VERSION . "\n";
echo "🔧 PHP 8.4 Compatible: " . (version_compare(PHP_VERSION, '8.4.0', '>=') ? 'YES' : 'NO') . "\n";
echo "🔧 Server Environment: " . php_sapi_name() . "\n";
echo "🔧 Memory Limit: " . ini_get('memory_limit') . "\n";
echo "🔧 Max Execution Time: " . ini_get('max_execution_time') . "s\n";
echo "🔧 Post Max Size: " . ini_get('post_max_size') . "\n";
echo "🔧 Upload Max Filesize: " . ini_get('upload_max_filesize') . "\n\n";

// Test Laravel environment
if (file_exists('artisan')) {
    echo "✅ Laravel project detected\n";
    
    // Test database connection
    try {
        require_once 'vendor/autoload.php';
        $app = require_once 'bootstrap/app.php';
        $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
        $kernel->bootstrap();
        
        echo "✅ Laravel bootstrapped successfully\n";
        
        // Test notification template model
        $template = \App\Models\NotificationTemplate::first();
        if ($template) {
            echo "✅ NotificationTemplate model working\n";
            echo "📄 Sample template ID: " . $template->id . "\n";
            echo "📄 Sample template subject: " . $template->subj . "\n";
            echo "📄 Sample template content length: " . strlen($template->email_body) . " characters\n";
        } else {
            echo "❌ No notification templates found\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Laravel bootstrap failed: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ Not a Laravel project\n";
}

// Test file permissions
$files_to_check = [
    'assets/admin/js/visual-builder-email-editor.js',
    'resources/views/admin/notification/edit.blade.php',
    'app/Http/Controllers/Admin/NotificationController.php',
    'storage/logs'
];

echo "\n🔍 Checking file permissions:\n";
foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        echo "✅ {$file}: " . substr(sprintf('%o', $perms), -4) . "\n";
    } else {
        echo "❌ {$file}: NOT FOUND\n";
    }
}

// Test JavaScript file syntax
echo "\n🔍 Testing JavaScript syntax:\n";
if (file_exists('assets/admin/js/visual-builder-email-editor.js')) {
    $js_content = file_get_contents('assets/admin/js/visual-builder-email-editor.js');
    
    // Check for critical functions
    $functions_to_check = [
        'prepareFormSubmission',
        'initializeTemplateEditor',
        'getContent',
        'updateFormFields'
    ];
    
    foreach ($functions_to_check as $func) {
        if (strpos($js_content, $func) !== false) {
            echo "✅ Function {$func} found\n";
        } else {
            echo "❌ Function {$func} NOT FOUND\n";
        }
    }
    
    // Check for syntax errors (basic check)
    $lines = explode("\n", $js_content);
    $brace_count = 0;
    $paren_count = 0;
    
    foreach ($lines as $line_num => $line) {
        $brace_count += substr_count($line, '{') - substr_count($line, '}');
        $paren_count += substr_count($line, '(') - substr_count($line, ')');
    }
    
    if ($brace_count === 0) {
        echo "✅ JavaScript braces balanced\n";
    } else {
        echo "❌ JavaScript braces unbalanced: {$brace_count}\n";
    }
    
    if ($paren_count === 0) {
        echo "✅ JavaScript parentheses balanced\n";
    } else {
        echo "❌ JavaScript parentheses unbalanced: {$paren_count}\n";
    }
} else {
    echo "❌ visual-builder-email-editor.js not found\n";
}

// Test Blade template
echo "\n🔍 Testing Blade template:\n";
if (file_exists('resources/views/admin/notification/edit.blade.php')) {
    $blade_content = file_get_contents('resources/views/admin/notification/edit.blade.php');
    
    // Check for critical elements
    $elements_to_check = [
        'id="update-template-btn"',
        'name="email_body_final"',
        'name="original_email_body"',
        'visual-builder-email-editor.js'
    ];
    
    foreach ($elements_to_check as $element) {
        if (strpos($blade_content, $element) !== false) {
            echo "✅ Element {$element} found\n";
        } else {
            echo "❌ Element {$element} NOT FOUND\n";
        }
    }
} else {
    echo "❌ edit.blade.php not found\n";
}

// Generate test URLs
echo "\n🔗 Test URLs:\n";
$base_url = 'https://localhost/mbf.mybrokerforex.com-31052025';
echo "📧 Template Edit: {$base_url}/admin/notification/template/edit/1\n";
echo "📧 Global Template: {$base_url}/admin/notification/global\n";

// Generate debugging commands
echo "\n🛠️ Debugging Commands:\n";
echo "📋 Clear Laravel Cache: php artisan cache:clear\n";
echo "📋 Clear Config Cache: php artisan config:clear\n";
echo "📋 Check Laravel Logs: tail -f storage/logs/laravel.log\n";
echo "📋 Test JavaScript: Open browser console and run: typeof prepareFormSubmission\n";

// Generate browser console test script
echo "\n🌐 Browser Console Test Script:\n";
echo "```javascript\n";
echo "// Test Visual Builder functionality\n";
echo "console.log('Visual Builder Instance:', typeof visualBuilderInstance);\n";
echo "console.log('Template Data:', typeof window.templateData);\n";
echo "console.log('Prepare Form Submission:', typeof prepareFormSubmission);\n";
echo "console.log('Initialize Template Editor:', typeof initializeTemplateEditor);\n";
echo "\n";
echo "// Test form fields\n";
echo "const emailBodyField = document.querySelector('textarea[name=\"email_body\"]');\n";
echo "const emailBodyFinalField = document.getElementById('email_body_final');\n";
echo "console.log('Email Body Field:', emailBodyField ? 'FOUND' : 'NOT FOUND');\n";
echo "console.log('Email Body Final Field:', emailBodyFinalField ? 'FOUND' : 'NOT FOUND');\n";
echo "\n";
echo "// Test content sync\n";
echo "if (typeof prepareFormSubmission === 'function') {\n";
echo "    prepareFormSubmission();\n";
echo "    console.log('✅ Form submission prepared successfully');\n";
echo "} else {\n";
echo "    console.log('❌ prepareFormSubmission function not available');\n";
echo "}\n";
echo "```\n";

echo "\n✅ Debug script completed!\n";
echo "📋 Next steps:\n";
echo "1. Upload the modified files to your live server\n";
echo "2. Clear Laravel caches\n";
echo "3. Test template editing in browser\n";
echo "4. Check browser console for JavaScript errors\n";
echo "5. Monitor Laravel logs for debugging information\n";
?>
