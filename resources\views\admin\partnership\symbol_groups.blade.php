@extends('admin.layouts.app')

@section('panel')
<div class="row">
    <div class="col-lg-12">
        <div class="card b-radius--10">
            <div class="card-body p-0">
                <div class="table-responsive--md table-responsive">
                    <table class="table--light style--two table">
                        <thead>
                            <tr>
                                <th>@lang('Group Name')</th>
                                <th>@lang('Description')</th>
                                <th>@lang('Symbols Count')</th>
                                <th>@lang('Status')</th>
                                <th>@lang('Action')</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($symbolGroups as $group)
                            <tr>
                                <td>
                                    <span class="fw-bold">{{ $group->name }}</span>
                                </td>
                                <td>{{ $group->description ?? 'N/A' }}</td>
                                <td>
                                    <span class="badge badge--primary">{{ count($group->symbols_json ?? []) }} @lang('symbols')</span>
                                </td>
                                <td>
                                    @if($group->status)
                                        <span class="badge badge--success">@lang('Active')</span>
                                    @else
                                        <span class="badge badge--warning">@lang('Inactive')</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="button--group">
                                        <button type="button" class="btn btn-sm btn-outline--info viewSymbolsBtn" 
                                                data-id="{{ $group->id }}"
                                                data-name="{{ $group->name }}"
                                                data-symbols="{{ json_encode($group->symbols_json ?? []) }}">
                                            <i class="las la-eye"></i> @lang('View Symbols')
                                        </button>
                                        
                                        <button type="button" class="btn btn-sm btn-outline--primary editBtn" 
                                                data-id="{{ $group->id }}"
                                                data-name="{{ $group->name }}"
                                                data-description="{{ $group->description }}"
                                                data-symbols="{{ json_encode($group->symbols_json ?? []) }}">
                                            <i class="las la-pencil"></i> @lang('Edit')
                                        </button>
                                        
                                        <button type="button" class="btn btn-sm btn-outline--{{ $group->status ? 'warning' : 'success' }} toggleStatusBtn"
                                                data-id="{{ $group->id }}"
                                                data-status="{{ $group->status }}">
                                            @if($group->status)
                                                <i class="las la-eye-slash"></i> @lang('Disable')
                                            @else
                                                <i class="las la-eye"></i> @lang('Enable')
                                            @endif
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="5" class="text-muted text-center">@lang('No symbol groups found')</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if($symbolGroups->hasPages())
            <div class="card-footer py-4">
                {{ paginateLinks($symbolGroups) }}
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Add New Symbol Group Button -->
<div class="fab-wrapper">
    <div class="fab-container">
        <div class="fab fab-icon-holder">
            <i class="las la-plus"></i>
        </div>
        <ul class="fab-options">
            <li>
                <span class="fab-label">@lang('Add New Symbol Group')</span>
                <div class="fab-icon-holder" data-bs-toggle="modal" data-bs-target="#addSymbolGroupModal">
                    <i class="las la-plus"></i>
                </div>
            </li>
        </ul>
    </div>
</div>

<!-- Add Symbol Group Modal -->
<div class="modal fade" id="addSymbolGroupModal" tabindex="-1" role="dialog" aria-labelledby="addSymbolGroupModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addSymbolGroupModalLabel">@lang('Add New Symbol Group')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('admin.partnership.store_symbol_group') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label>@lang('Group Name') <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    
                    <div class="form-group">
                        <label>@lang('Description')</label>
                        <textarea class="form-control" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label>@lang('Select Symbols') <span class="text-danger">*</span></label>
                        <select class="form-control select2-multi" name="symbols[]" multiple required>
                            @foreach($allSymbols as $symbol)
                                <option value="{{ $symbol->id }}">{{ $symbol->symbol }} - {{ $symbol->description }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn--dark" data-bs-dismiss="modal">@lang('Close')</button>
                    <button type="submit" class="btn btn--primary">@lang('Save')</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Symbol Group Modal -->
<div class="modal fade" id="editSymbolGroupModal" tabindex="-1" role="dialog" aria-labelledby="editSymbolGroupModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editSymbolGroupModalLabel">@lang('Edit Symbol Group')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editSymbolGroupForm" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div class="form-group">
                        <label>@lang('Group Name') <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="name" id="editName" required>
                    </div>
                    
                    <div class="form-group">
                        <label>@lang('Description')</label>
                        <textarea class="form-control" name="description" id="editDescription" rows="3"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label>@lang('Select Symbols') <span class="text-danger">*</span></label>
                        <select class="form-control select2-multi" name="symbols[]" id="editSymbols" multiple required>
                            @foreach($allSymbols as $symbol)
                                <option value="{{ $symbol->id }}">{{ $symbol->symbol }} - {{ $symbol->description }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn--dark" data-bs-dismiss="modal">@lang('Close')</button>
                    <button type="submit" class="btn btn--primary">@lang('Update')</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Symbols Modal -->
<div class="modal fade" id="viewSymbolsModal" tabindex="-1" role="dialog" aria-labelledby="viewSymbolsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewSymbolsModalLabel">@lang('Symbols in Group')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="symbolsList"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn--dark" data-bs-dismiss="modal">@lang('Close')</button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('script')
<script>
    (function ($) {
        'use strict';
        
        // Initialize Select2 for multiple select
        $('.select2-multi').select2({
            placeholder: '@lang("Select symbols")',
            allowClear: true,
            width: '100%'
        });
        
        // Edit button click
        $('.editBtn').on('click', function() {
            const id = $(this).data('id');
            const name = $(this).data('name');
            const description = $(this).data('description');
            const symbols = $(this).data('symbols');
            
            $('#editName').val(name);
            $('#editDescription').val(description);
            $('#editSymbols').val(symbols).trigger('change');
            
            const updateUrl = `{{ route('admin.partnership.update_symbol_group', ':id') }}`.replace(':id', id);
            $('#editSymbolGroupForm').attr('action', updateUrl);
            
            $('#editSymbolGroupModal').modal('show');
        });
        
        // View symbols button click
        $('.viewSymbolsBtn').on('click', function() {
            const groupName = $(this).data('name');
            const symbolIds = $(this).data('symbols');
            
            $('#viewSymbolsModalLabel').text(`@lang('Symbols in Group'): ${groupName}`);
            
            // Get symbol names from the select options
            let symbolsList = '<div class="row">';
            symbolIds.forEach(function(symbolId) {
                const symbolOption = $(`#editSymbols option[value="${symbolId}"]`);
                if (symbolOption.length) {
                    const symbolText = symbolOption.text();
                    symbolsList += `<div class="col-md-6 mb-2"><span class="badge badge--primary">${symbolText}</span></div>`;
                }
            });
            symbolsList += '</div>';
            
            $('#symbolsList').html(symbolsList);
            $('#viewSymbolsModal').modal('show');
        });
        
    })(jQuery);
</script>
@endpush
