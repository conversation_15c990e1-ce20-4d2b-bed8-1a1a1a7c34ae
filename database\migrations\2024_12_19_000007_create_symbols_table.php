<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('symbols', function (Blueprint $table) {
            $table->id();
            $table->string('symbol')->unique();
            $table->string('path');
            $table->text('description');
            $table->decimal('contract_size', 15, 2);
            $table->boolean('status')->default(true)->comment('Enabled/Disabled');
            $table->timestamps();
            
            // Indexes
            $table->index('symbol');
            $table->index('status');
            $table->index('path');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('symbols');
    }
};
