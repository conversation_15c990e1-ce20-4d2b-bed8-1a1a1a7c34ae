@extends('admin.layouts.app')

@section('panel')
<div class="row">
    <div class="col-lg-12">
        <div class="card b-radius--10">
            <div class="card-header">
                <div class="row g-3 align-items-center">
                    <div class="col-md-8">
                        <h5 class="card-title">@lang('Multi IB Levels Management')</h5>
                        <p class="text-muted mb-0">@lang('Configure commission percentages for each IB level')</p>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex justify-content-end gap-2">
                            <button type="button" class="btn btn--primary btn-sm" data-bs-toggle="modal" data-bs-target="#addIbLevelModal">
                                <i class="las la-plus"></i> @lang('Add New Level')
                            </button>
                            <button type="button" class="btn btn--success btn-sm" id="resetToDefaultBtn">
                                <i class="las la-undo"></i> @lang('Reset to Default')
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive--md table-responsive">
                    <table class="table--light style--two table">
                        <thead>
                            <tr>
                                <th>@lang('Level')</th>
                                <th>@lang('Name')</th>
                                <th>@lang('Commission %')</th>
                                <th>@lang('Max Commission %')</th>
                                <th>@lang('Description')</th>
                                <th>@lang('Status')</th>
                                <th>@lang('Action')</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($ibLevels as $level)
                            <tr>
                                <td>
                                    <span class="badge badge--primary">@lang('Level') {{ $level->level }}</span>
                                </td>
                                <td>
                                    <span class="fw-bold">{{ $level->name }}</span>
                                </td>
                                <td>{{ number_format($level->commission_percent, 2) }}%</td>
                                <td>{{ number_format($level->max_commission_percent, 2) }}%</td>
                                <td>{{ $level->description ?? 'N/A' }}</td>
                                <td>
                                    @if($level->status)
                                        <span class="badge badge--success">@lang('Active')</span>
                                    @else
                                        <span class="badge badge--warning">@lang('Inactive')</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="dropdown">
                                        <button type="button" class="btn btn-sm btn-outline--primary dropdown-toggle" data-bs-toggle="dropdown">
                                            <i class="las la-ellipsis-v"></i>
                                        </button>
                                        <div class="dropdown-menu">
                                            <button type="button" class="dropdown-item editBtn"
                                                    data-id="{{ $level->id }}"
                                                    data-name="{{ $level->name }}"
                                                    data-level="{{ $level->level }}"
                                                    data-commission_percent="{{ $level->commission_percent }}"
                                                    data-max_commission_percent="{{ $level->max_commission_percent }}"
                                                    data-description="{{ $level->description }}">
                                                <i class="las la-pencil"></i> @lang('Edit')
                                            </button>

                                            <button type="button" class="dropdown-item toggleStatusBtn"
                                                    data-id="{{ $level->id }}"
                                                    data-status="{{ $level->status }}">
                                                @if($level->status)
                                                    <i class="las la-eye-slash"></i> @lang('Disable')
                                                @else
                                                    <i class="las la-eye"></i> @lang('Enable')
                                                @endif
                                            </button>

                                            <div class="dropdown-divider"></div>
                                            <button type="button" class="dropdown-item text-danger deleteBtn"
                                                    data-id="{{ $level->id }}"
                                                    data-name="{{ $level->name }}">
                                                <i class="las la-trash"></i> @lang('Delete')
                                            </button>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="7" class="text-muted text-center">@lang('No IB levels found')</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if($ibLevels->hasPages())
            <div class="card-footer py-4">
                {{ paginateLinks($ibLevels) }}
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Commission Distribution Example -->
<div class="row mt-4">
    <div class="col-lg-12">
        <div class="card b-radius--10">
            <div class="card-header">
                <h5 class="card-title">@lang('Commission Distribution Example')</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">@lang('$10 Commission Distribution:')</h6>
                        <div id="commissionExample">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="alert alert-info">
                            <h6>@lang('How it works:')</h6>
                            <ul class="mb-0">
                                <li>@lang('Client trades and generates $10 commission')</li>
                                <li>@lang('Commission is distributed to IB hierarchy')</li>
                                <li>@lang('Each level receives their percentage')</li>
                                <li>@lang('Remaining goes to company')</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add IB Level Modal -->
<div class="modal fade" id="addIbLevelModal" tabindex="-1" role="dialog" aria-labelledby="addIbLevelModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addIbLevelModalLabel">@lang('Add New IB Level')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('admin.partnership.store_ib_level') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label>@lang('Level Number') <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" name="level" min="1" required>
                    </div>
                    
                    <div class="form-group">
                        <label>@lang('Level Name') <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    
                    <div class="form-group">
                        <label>@lang('Commission Percentage') <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="number" class="form-control" name="commission_percent" step="0.01" min="0" max="100" required>
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>@lang('Max Commission Percentage') <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="number" class="form-control" name="max_commission_percent" step="0.01" min="0" max="100" required>
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>@lang('Description')</label>
                        <textarea class="form-control" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn--dark" data-bs-dismiss="modal">@lang('Close')</button>
                    <button type="submit" class="btn btn--primary">@lang('Save')</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit IB Level Modal -->
<div class="modal fade" id="editIbLevelModal" tabindex="-1" role="dialog" aria-labelledby="editIbLevelModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editIbLevelModalLabel">@lang('Edit IB Level')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editIbLevelForm" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div class="form-group">
                        <label>@lang('Level Number') <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" name="level" id="editLevel" min="1" required>
                    </div>
                    
                    <div class="form-group">
                        <label>@lang('Level Name') <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="name" id="editName" required>
                    </div>
                    
                    <div class="form-group">
                        <label>@lang('Commission Percentage') <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="number" class="form-control" name="commission_percent" id="editCommissionPercent" step="0.01" min="0" max="100" required>
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>@lang('Max Commission Percentage') <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="number" class="form-control" name="max_commission_percent" id="editMaxCommissionPercent" step="0.01" min="0" max="100" required>
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>@lang('Description')</label>
                        <textarea class="form-control" name="description" id="editDescription" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn--dark" data-bs-dismiss="modal">@lang('Close')</button>
                    <button type="submit" class="btn btn--primary">@lang('Update')</button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

@push('script')
<script>
    (function ($) {
        'use strict';
        
        // Edit button click
        $('.editBtn').on('click', function() {
            const id = $(this).data('id');
            const name = $(this).data('name');
            const level = $(this).data('level');
            const commissionPercent = $(this).data('commission_percent');
            const maxCommissionPercent = $(this).data('max_commission_percent');
            const description = $(this).data('description');
            
            $('#editName').val(name);
            $('#editLevel').val(level);
            $('#editCommissionPercent').val(commissionPercent);
            $('#editMaxCommissionPercent').val(maxCommissionPercent);
            $('#editDescription').val(description);
            
            const updateUrl = `{{ route('admin.partnership.update_ib_level', ':id') }}`.replace(':id', id);
            $('#editIbLevelForm').attr('action', updateUrl);
            
            $('#editIbLevelModal').modal('show');
        });
        
    })(jQuery);
</script>
@endpush
