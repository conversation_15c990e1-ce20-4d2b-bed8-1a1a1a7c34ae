<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use App\Models\User;
use Carbon\Carbon;

class TestMT5Improvements extends Command
{
    protected $signature = 'test:mt5-improvements';
    protected $description = 'Test all MT5 sync and admin interface improvements';

    public function handle()
    {
        $this->info('🧪 TESTING MT5 SYNC AND ADMIN INTERFACE IMPROVEMENTS');
        $this->info('=======================================================');

        $this->testBatchSizeEnforcement();
        $this->testRegistrationDateMapping();
        $this->testIBAccountFiltering();
        $this->testAutoSyncVerification();
        $this->testUserListDisplay();

        $this->info('');
        $this->info('🎉 ALL TESTS COMPLETED');
        $this->info('======================');
    }

    private function testBatchSizeEnforcement()
    {
        $this->info('');
        $this->info('📋 TEST 1: BATCH SIZE ENFORCEMENT (1000 MINIMUM)');
        $this->info('--------------------------------------------------');

        // Test that sync command enforces minimum 1000 batch size
        $testLimits = [100, 500, 1000, 2000];
        
        foreach ($testLimits as $limit) {
            $enforcedLimit = max(1000, $limit);
            $status = $enforcedLimit >= 1000 ? '✅' : '❌';
            $this->line("{$status} Limit {$limit} → Enforced: {$enforcedLimit}");
        }

        $this->info('✅ TEST 1 PASSED: Minimum 1000 batch size enforced');
    }

    private function testRegistrationDateMapping()
    {
        $this->info('');
        $this->info('📋 TEST 2: REGISTRATION DATE MAPPING (CRITICAL)');
        $this->info('------------------------------------------------');

        // Check if MT5 users have correct registration dates
        $mt5Users = User::whereNotNull('mt5_login')
                       ->whereNotNull('mt5_registration')
                       ->limit(10)
                       ->get(['id', 'email', 'created_at', 'mt5_registration', 'mt5_synced_at']);

        $correctMappings = 0;
        $totalChecked = 0;

        foreach ($mt5Users as $user) {
            $totalChecked++;
            
            // Check if created_at matches mt5_registration (allowing for small time differences)
            $createdAt = Carbon::parse($user->created_at);
            $mt5Registration = Carbon::parse($user->mt5_registration);
            
            $timeDiff = abs($createdAt->diffInMinutes($mt5Registration));
            
            if ($timeDiff <= 5) { // Allow 5 minute difference for processing time
                $correctMappings++;
                $this->line("✅ {$user->email}: Registration dates match");
            } else {
                $this->line("⚠️ {$user->email}: created_at={$user->created_at}, mt5_registration={$user->mt5_registration}");
            }
        }

        $percentage = $totalChecked > 0 ? round(($correctMappings / $totalChecked) * 100, 2) : 0;
        
        if ($percentage >= 80) {
            $this->info("✅ TEST 2 PASSED: {$percentage}% of users have correct registration dates");
        } else {
            $this->warn("⚠️ TEST 2 NEEDS IMPROVEMENT: Only {$percentage}% of users have correct registration dates");
        }
    }

    private function testIBAccountFiltering()
    {
        $this->info('');
        $this->info('📋 TEST 3: IB ACCOUNT FILTERING');
        $this->info('-------------------------------');

        // Test IB account identification
        $totalUsers = User::count();
        
        // Count IB accounts using the same logic as the filter
        $ibAccounts = User::where(function($q) {
            $q->where('partner', 1)
              ->orWhere('is_ib_account', 1)
              ->orWhere('mt5_group', 'like', '%Affiliates%')
              ->orWhere('mt5_group', 'like', '%IB%')
              ->orWhere('mt5_group', 'like', '%ib%')
              ->orWhere('mt5_group', 'like', '%affiliate%');
        })->count();

        $regularAccounts = User::where(function($q) {
            $q->where('partner', '!=', 1)
              ->where('is_ib_account', '!=', 1)
              ->where('mt5_group', 'not like', '%Affiliates%')
              ->where('mt5_group', 'not like', '%IB%')
              ->where('mt5_group', 'not like', '%ib%')
              ->where('mt5_group', 'not like', '%affiliate%');
        })->count();

        $this->table(['Account Type', 'Count', 'Percentage'], [
            ['Total Users', $totalUsers, '100%'],
            ['IB Accounts', $ibAccounts, round(($ibAccounts / $totalUsers) * 100, 2) . '%'],
            ['Regular Accounts', $regularAccounts, round(($regularAccounts / $totalUsers) * 100, 2) . '%'],
        ]);

        $this->info('✅ TEST 3 PASSED: IB account filtering logic working');
    }

    private function testAutoSyncVerification()
    {
        $this->info('');
        $this->info('📋 TEST 4: AUTO-SYNC VERIFICATION');
        $this->info('----------------------------------');

        // Check sync statistics
        $syncStats = Cache::get('mt5_sync_stats');
        
        if ($syncStats) {
            $this->table(['Metric', 'Value'], [
                ['Last Sync', $syncStats['last_sync'] ?? 'Never'],
                ['Total Users', $syncStats['total_users'] ?? 0],
                ['MT5 Users', $syncStats['mt5_users'] ?? 0],
                ['Last Processed', $syncStats['processed'] ?? 0],
                ['Last Created', $syncStats['created'] ?? 0],
                ['Last Updated', $syncStats['updated'] ?? 0],
                ['Sync Status', $syncStats['sync_status'] ?? 'Unknown'],
            ]);
            
            $this->info('✅ TEST 4 PASSED: Auto-sync statistics available');
        } else {
            $this->warn('⚠️ TEST 4 WARNING: No sync statistics found - run sync first');
        }

        // Check for recent MT5 users
        $recentMT5Users = User::whereNotNull('mt5_login')
                             ->where('created_at', '>', now()->subHours(24))
                             ->count();
        
        $this->info("📊 Recent MT5 users (last 24h): {$recentMT5Users}");
    }

    private function testUserListDisplay()
    {
        $this->info('');
        $this->info('📋 TEST 5: USER LIST DISPLAY (NEWEST FIRST)');
        $this->info('--------------------------------------------');

        // Check if users are ordered by created_at DESC
        $users = User::orderBy('created_at', 'desc')->limit(5)->get(['id', 'email', 'created_at']);
        
        $this->info('Top 5 newest users:');
        foreach ($users as $index => $user) {
            $this->line(($index + 1) . ". {$user->email} - {$user->created_at}");
        }

        // Check for duplicate consolidation
        $duplicateEmails = DB::table('users')
            ->select('email', DB::raw('COUNT(*) as count'))
            ->whereNotNull('email')
            ->where('email', '!=', '')
            ->groupBy('email')
            ->having('count', '>', 1)
            ->count();

        $this->info("📊 Remaining duplicate emails: {$duplicateEmails}");
        
        if ($duplicateEmails < 500) {
            $this->info('✅ TEST 5 PASSED: Good duplicate consolidation progress');
        } else {
            $this->warn('⚠️ TEST 5 NEEDS IMPROVEMENT: Many duplicates still exist');
        }
    }
}
