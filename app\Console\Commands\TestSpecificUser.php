<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\User;

class TestSpecificUser extends Command
{
    protected $signature = 'test:user {mt5_login}';
    protected $description = 'Test specific user data';

    public function handle()
    {
        $mt5Login = $this->argument('mt5_login');
        
        $this->info("🔍 TESTING USER WITH MT5 LOGIN: {$mt5Login}");
        $this->info('==========================================');

        // Check local user
        $localUser = User::where('mt5_login', $mt5Login)->first();
        
        if ($localUser) {
            $this->info('📊 LOCAL USER DATA:');
            $this->table(['Field', 'Value'], [
                ['Email', $localUser->email],
                ['MT5 Login', $localUser->mt5_login],
                ['created_at', $localUser->created_at],
                ['mt5_registration', $localUser->mt5_registration],
                ['mt5_synced_at', $localUser->mt5_synced_at],
                ['all_mt5_accounts_detailed', $localUser->all_mt5_accounts_detailed ?? 'NULL'],
            ]);
            
            // Check if dates match
            if ($localUser->created_at && $localUser->mt5_registration) {
                $createdAt = \Carbon\Carbon::parse($localUser->created_at);
                $mt5Registration = \Carbon\Carbon::parse($localUser->mt5_registration);
                $diffMinutes = abs($createdAt->diffInMinutes($mt5Registration));
                
                if ($diffMinutes <= 5) {
                    $this->info('✅ Registration dates match correctly');
                } else {
                    $this->warn("⚠️ Registration dates differ by {$diffMinutes} minutes");
                }
            }
        } else {
            $this->error('❌ User not found in local database');
        }

        // Check MT5 database
        try {
            $mt5User = DB::connection('mbf-dbmt5')
                        ->table('mt5_users')
                        ->where('Login', $mt5Login)
                        ->first();
            
            if ($mt5User) {
                $this->info('');
                $this->info('📊 MT5 DATABASE DATA:');
                $this->table(['Field', 'Value'], [
                    ['Login', $mt5User->Login],
                    ['Email', $mt5User->Email],
                    ['Registration', $mt5User->Registration],
                    ['Group', $mt5User->Group],
                    ['Balance', $mt5User->Balance ?? 0],
                    ['Credit', $mt5User->Credit ?? 0],
                ]);
            } else {
                $this->error('❌ User not found in MT5 database');
            }
        } catch (\Exception $e) {
            $this->error('❌ Could not connect to MT5 database: ' . $e->getMessage());
        }

        // Check for other accounts with same email
        if ($localUser) {
            $otherAccounts = User::where('email', $localUser->email)
                                ->where('mt5_login', '!=', $mt5Login)
                                ->whereNotNull('mt5_login')
                                ->get(['mt5_login', 'mt5_group', 'mt5_balance', 'mt5_credit']);
            
            if ($otherAccounts->count() > 0) {
                $this->info('');
                $this->info('📊 OTHER MT5 ACCOUNTS FOR SAME EMAIL:');
                $this->table(['MT5 Login', 'Group', 'Balance', 'Credit'], 
                    $otherAccounts->map(function($account) {
                        return [
                            $account->mt5_login,
                            $account->mt5_group ?? 'Unknown',
                            '$' . number_format($account->mt5_balance ?? 0, 2),
                            '$' . number_format($account->mt5_credit ?? 0, 2)
                        ];
                    })->toArray()
                );
            }
        }
    }
}
