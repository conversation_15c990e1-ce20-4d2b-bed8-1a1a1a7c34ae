@extends('admin.layouts.app')

@section('panel')
<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-lg-6 col-sm-6 mb-10">
        <div class="widget-two style--two box--shadow2 b-radius--5 bg--success">
            <div class="widget-two__icon b-radius--5 bg--success">
                <i class="las la-check-circle"></i>
            </div>
            <div class="widget-two__content">
                <h3 class="text-white">{{ $symbols->where('status', true)->count() }}</h3>
                <p class="text-white">@lang('Active Symbols')</p>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-6 col-sm-6 mb-10">
        <div class="widget-two style--two box--shadow2 b-radius--5 bg--warning">
            <div class="widget-two__icon b-radius--5 bg--warning">
                <i class="las la-times-circle"></i>
            </div>
            <div class="widget-two__content">
                <h3 class="text-white">{{ $symbols->where('status', false)->count() }}</h3>
                <p class="text-white">@lang('Inactive Symbols')</p>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-6 col-sm-6 mb-10">
        <div class="widget-two style--two box--shadow2 b-radius--5 bg--primary">
            <div class="widget-two__icon b-radius--5 bg--primary">
                <i class="las la-list"></i>
            </div>
            <div class="widget-two__content">
                <h3 class="text-white">{{ $symbols->total() }}</h3>
                <p class="text-white">@lang('Total Symbols')</p>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-6 col-sm-6 mb-10">
        <div class="widget-two style--two box--shadow2 b-radius--5 bg--info">
            <div class="widget-two__icon b-radius--5 bg--info">
                <i class="las la-layer-group"></i>
            </div>
            <div class="widget-two__content">
                <h3 class="text-white">{{ $paths->count() }}</h3>
                <p class="text-white">@lang('Categories')</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">
        <!-- Search and Filter Section -->
        <div class="card b-radius--10 mb-3">
            <div class="card-body">
                <form action="{{ route('admin.partnership.symbols') }}" method="GET">
                    <div class="d-flex flex-wrap gap-3">
                        <div class="flex-fill">
                            <input type="text" name="search" class="form-control" placeholder="@lang('Search symbols...')" value="{{ request('search') }}">
                        </div>
                        <div>
                            <select name="status" class="form-control">
                                <option value="">@lang('All Status')</option>
                                <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>@lang('Active')</option>
                                <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>@lang('Inactive')</option>
                            </select>
                        </div>
                        <div>
                            <select name="path" class="form-control">
                                <option value="">@lang('All Categories')</option>
                                @foreach($paths as $path)
                                    <option value="{{ $path }}" {{ request('path') == $path ? 'selected' : '' }}>
                                        {{ $path }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div>
                            <button type="submit" class="btn btn--primary">
                                <i class="las la-search"></i> @lang('Search')
                            </button>
                        </div>
                        <div>
                            <a href="{{ route('admin.partnership.symbols') }}" class="btn btn--secondary">
                                <i class="las la-undo"></i> @lang('Reset')
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Symbols Table -->
        <div class="card b-radius--10">
            <div class="card-body p-0">
                <div class="table-responsive--md table-responsive">
                    <table class="table--light style--two table">
                        <thead>
                            <tr>
                                <th>@lang('ID')</th>
                                <th>@lang('Symbol')</th>
                                <th>@lang('Description')</th>
                                <th>@lang('Category')</th>
                                <th>@lang('Contract Size')</th>
                                <th>@lang('Status')</th>
                                <th>@lang('Action')</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($symbols as $index => $symbol)
                            <tr>
                                <td>
                                    <span class="fw-bold">{{ $symbols->firstItem() + $index }}</span>
                                </td>
                                <td>
                                    <span class="fw-bold text-primary">{{ $symbol->symbol }}</span>
                                </td>
                                <td>
                                    <span class="d-block">{{ $symbol->description }}</span>
                                    <small class="text-muted">{{ $symbol->path }}</small>
                                </td>
                                <td>
                                    <span class="badge badge--info">{{ $symbol->path_category }}</span>
                                    @if($symbol->path_subcategory)
                                        <br><small class="text-muted">{{ $symbol->path_subcategory }}</small>
                                    @endif
                                </td>
                                <td>{{ $symbol->formatted_contract_size }}</td>
                                <td>
                                    @if($symbol->status)
                                        <span class="badge badge--success">@lang('Enabled')</span>
                                    @else
                                        <span class="badge badge--warning">@lang('Disabled')</span>
                                    @endif
                                </td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-outline--{{ $symbol->status ? 'warning' : 'success' }} toggleStatusBtn"
                                            data-id="{{ $symbol->id }}"
                                            data-symbol="{{ $symbol->symbol }}"
                                            data-status="{{ $symbol->status }}">
                                        @if($symbol->status)
                                            <i class="las la-eye-slash"></i> @lang('Disable')
                                        @else
                                            <i class="las la-eye"></i> @lang('Enable')
                                        @endif
                                    </button>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="7" class="text-muted text-center">@lang('No symbols found')</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if($symbols->hasPages())
            <div class="card-footer py-4">
                {{ paginateLinks($symbols) }}
            </div>
            @endif
        </div>
    </div>
</div>



<!-- Toggle Status Confirmation Modal -->
<div class="modal fade" id="toggleStatusModal" tabindex="-1" role="dialog" aria-labelledby="toggleStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="toggleStatusModalLabel">@lang('Confirm Action')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p id="toggleStatusMessage"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn--dark" data-bs-dismiss="modal">@lang('Cancel')</button>
                <button type="button" class="btn btn--primary" id="confirmToggleStatus">@lang('Confirm')</button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('script')
<script>
    (function ($) {
        'use strict';
        
        let symbolId, symbolName, currentStatus;
        
        // Handle toggle status button click
        $(document).on('click', '.toggleStatusBtn', function() {
            symbolId = $(this).data('id');
            symbolName = $(this).data('symbol');
            currentStatus = $(this).data('status');

            const action = currentStatus ? '@lang("disable")' : '@lang("enable")';
            const message = `@lang('Are you sure you want to')` + ' ' + action + ' ' + symbolName + '?';

            $('#toggleStatusMessage').text(message);
            $('#toggleStatusModal').modal('show');
        });

        // Confirm toggle status
        $('#confirmToggleStatus').on('click', function() {
            const url = `{{ route('admin.partnership.toggle_symbol', ':id') }}`.replace(':id', symbolId);

            $.ajax({
                url: url,
                type: 'POST',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    $('#toggleStatusModal').modal('hide');
                    if (response.success || response.status === 'success') {
                        notify('success', response.message || '@lang("Symbol status updated successfully")');
                        setTimeout(() => {
                            location.reload();
                        }, 1000);
                    } else {
                        notify('error', response.message || '@lang("Something went wrong")');
                    }
                },
                error: function(xhr) {
                    $('#toggleStatusModal').modal('hide');
                    let errorMessage = '@lang("Something went wrong")';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    notify('error', errorMessage);
                }
            });
        });
        
    })(jQuery);
</script>
@endpush
