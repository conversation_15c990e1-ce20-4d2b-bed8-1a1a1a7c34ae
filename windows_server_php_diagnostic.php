<?php
/**
 * Windows Server 2022/Plesk Email Editor PHP Diagnostic Script
 * 
 * This script diagnoses server-side issues that might prevent
 * the email template editor from working correctly on Windows Server.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start output buffering
ob_start();

echo "<!DOCTYPE html>\n";
echo "<html><head><title>Windows Server PHP Diagnostic</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;background:#f5f5f5;}";
echo ".panel{background:white;padding:20px;margin:10px 0;border-radius:5px;box-shadow:0 2px 5px rgba(0,0,0,0.1);}";
echo ".success{color:#28a745;}.error{color:#dc3545;}.warning{color:#ffc107;}.info{color:#17a2b8;}";
echo "pre{background:#f8f9fa;padding:10px;border-radius:3px;overflow-x:auto;}";
echo "table{width:100%;border-collapse:collapse;}th,td{padding:8px;text-align:left;border-bottom:1px solid #ddd;}";
echo "</style></head><body>";

echo "<h1>🔧 Windows Server 2022/Plesk PHP Diagnostic</h1>";

// System Information
echo "<div class='panel'>";
echo "<h2>📋 System Information</h2>";
echo "<table>";
echo "<tr><th>Property</th><th>Value</th></tr>";
echo "<tr><td>PHP Version</td><td class='info'>" . PHP_VERSION . "</td></tr>";
echo "<tr><td>Operating System</td><td class='info'>" . PHP_OS . "</td></tr>";
echo "<tr><td>Server Software</td><td class='info'>" . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</td></tr>";
echo "<tr><td>Document Root</td><td class='info'>" . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</td></tr>";
echo "<tr><td>Script Path</td><td class='info'>" . __FILE__ . "</td></tr>";
echo "<tr><td>Current Working Directory</td><td class='info'>" . getcwd() . "</td></tr>";
echo "<tr><td>Memory Limit</td><td class='info'>" . ini_get('memory_limit') . "</td></tr>";
echo "<tr><td>Max Execution Time</td><td class='info'>" . ini_get('max_execution_time') . "</td></tr>";
echo "<tr><td>Upload Max Filesize</td><td class='info'>" . ini_get('upload_max_filesize') . "</td></tr>";
echo "<tr><td>Post Max Size</td><td class='info'>" . ini_get('post_max_size') . "</td></tr>";
echo "</table>";
echo "</div>";

// File System Tests
echo "<div class='panel'>";
echo "<h2>📁 File System Tests</h2>";

$files_to_check = [
    'resources/views/admin/notification/edit.blade.php',
    'assets/admin/js/simple-email-editor.js',
    'assets/admin/css/simple-email-editor.css',
    'app/Http/Controllers/Admin/NotificationController.php',
    'storage/logs/laravel.log'
];

echo "<table>";
echo "<tr><th>File</th><th>Status</th><th>Size</th><th>Modified</th><th>Permissions</th></tr>";

foreach ($files_to_check as $file) {
    $full_path = $file;
    if (!file_exists($full_path)) {
        // Try relative to current directory
        $full_path = __DIR__ . '/' . $file;
    }
    
    if (file_exists($full_path)) {
        $size = filesize($full_path);
        $modified = date('Y-m-d H:i:s', filemtime($full_path));
        $perms = substr(sprintf('%o', fileperms($full_path)), -4);
        echo "<tr><td>$file</td><td class='success'>✅ Exists</td><td>$size bytes</td><td>$modified</td><td>$perms</td></tr>";
    } else {
        echo "<tr><td>$file</td><td class='error'>❌ Not Found</td><td>-</td><td>-</td><td>-</td></tr>";
    }
}
echo "</table>";
echo "</div>";

// Laravel Environment Tests
echo "<div class='panel'>";
echo "<h2>🚀 Laravel Environment Tests</h2>";

// Check if we're in a Laravel environment
if (file_exists('artisan')) {
    echo "<p class='success'>✅ Laravel detected (artisan file found)</p>";
    
    // Check .env file
    if (file_exists('.env')) {
        echo "<p class='success'>✅ .env file exists</p>";
        
        // Read some key environment variables
        $env_vars = [
            'APP_ENV',
            'APP_DEBUG',
            'APP_URL',
            'DB_CONNECTION',
            'MAIL_MAILER'
        ];
        
        echo "<table>";
        echo "<tr><th>Environment Variable</th><th>Value</th></tr>";
        foreach ($env_vars as $var) {
            $value = getenv($var) ?: 'Not Set';
            echo "<tr><td>$var</td><td class='info'>$value</td></tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='error'>❌ .env file not found</p>";
    }
    
    // Check storage permissions
    $storage_dirs = ['storage/logs', 'storage/framework/cache', 'storage/framework/sessions', 'storage/framework/views'];
    echo "<h3>Storage Directory Permissions</h3>";
    echo "<table>";
    echo "<tr><th>Directory</th><th>Status</th><th>Permissions</th><th>Writable</th></tr>";
    
    foreach ($storage_dirs as $dir) {
        if (is_dir($dir)) {
            $perms = substr(sprintf('%o', fileperms($dir)), -4);
            $writable = is_writable($dir) ? '✅ Yes' : '❌ No';
            $status = is_writable($dir) ? 'success' : 'error';
            echo "<tr><td>$dir</td><td class='$status'>Exists</td><td>$perms</td><td class='$status'>$writable</td></tr>";
        } else {
            echo "<tr><td>$dir</td><td class='error'>❌ Not Found</td><td>-</td><td>-</td></tr>";
        }
    }
    echo "</table>";
    
} else {
    echo "<p class='warning'>⚠️ Laravel not detected (no artisan file)</p>";
}
echo "</div>";

// PHP Extensions
echo "<div class='panel'>";
echo "<h2>🔧 PHP Extensions</h2>";

$required_extensions = [
    'mbstring',
    'openssl',
    'pdo',
    'tokenizer',
    'xml',
    'ctype',
    'json',
    'bcmath',
    'curl',
    'fileinfo',
    'gd'
];

echo "<table>";
echo "<tr><th>Extension</th><th>Status</th><th>Version</th></tr>";

foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        $version = phpversion($ext) ?: 'Unknown';
        echo "<tr><td>$ext</td><td class='success'>✅ Loaded</td><td>$version</td></tr>";
    } else {
        echo "<tr><td>$ext</td><td class='error'>❌ Not Loaded</td><td>-</td></tr>";
    }
}
echo "</table>";
echo "</div>";

// HTTP Request Test
echo "<div class='panel'>";
echo "<h2>🌐 HTTP Request Information</h2>";

echo "<table>";
echo "<tr><th>Property</th><th>Value</th></tr>";
echo "<tr><td>Request Method</td><td class='info'>" . ($_SERVER['REQUEST_METHOD'] ?? 'Unknown') . "</td></tr>";
echo "<tr><td>Request URI</td><td class='info'>" . ($_SERVER['REQUEST_URI'] ?? 'Unknown') . "</td></tr>";
echo "<tr><td>HTTP Host</td><td class='info'>" . ($_SERVER['HTTP_HOST'] ?? 'Unknown') . "</td></tr>";
echo "<tr><td>HTTPS</td><td class='info'>" . (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'Yes' : 'No') . "</td></tr>";
echo "<tr><td>User Agent</td><td class='info'>" . ($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown') . "</td></tr>";
echo "<tr><td>Remote Address</td><td class='info'>" . ($_SERVER['REMOTE_ADDR'] ?? 'Unknown') . "</td></tr>";
echo "</table>";
echo "</div>";

// Error Log Check
echo "<div class='panel'>";
echo "<h2>📝 Recent Error Logs</h2>";

$log_files = [
    'storage/logs/laravel.log',
    'storage/logs/laravel-' . date('Y-m-d') . '.log'
];

foreach ($log_files as $log_file) {
    if (file_exists($log_file)) {
        echo "<h3>$log_file</h3>";
        $log_content = file_get_contents($log_file);
        $recent_lines = array_slice(explode("\n", $log_content), -20);
        echo "<pre>" . htmlspecialchars(implode("\n", $recent_lines)) . "</pre>";
        break;
    }
}

if (!file_exists('storage/logs/laravel.log')) {
    echo "<p class='warning'>⚠️ No Laravel log files found</p>";
}
echo "</div>";

// Asset URL Test
echo "<div class='panel'>";
echo "<h2>🎯 Asset URL Tests</h2>";

$base_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'];
$assets = [
    '/assets/admin/js/simple-email-editor.js',
    '/assets/admin/css/simple-email-editor.css'
];

echo "<table>";
echo "<tr><th>Asset</th><th>Full URL</th><th>Test</th></tr>";

foreach ($assets as $asset) {
    $full_url = $base_url . $asset;
    echo "<tr><td>$asset</td><td>$full_url</td><td><a href='$full_url' target='_blank'>Test Link</a></td></tr>";
}
echo "</table>";
echo "</div>";

// Recommendations
echo "<div class='panel'>";
echo "<h2>💡 Diagnostic Summary & Recommendations</h2>";

echo "<h3>Next Steps:</h3>";
echo "<ol>";
echo "<li><strong>Upload this diagnostic file</strong> to your Windows Server and access it via browser</li>";
echo "<li><strong>Check asset loading</strong> - Click the test links above to verify JavaScript/CSS files load</li>";
echo "<li><strong>Review file permissions</strong> - Ensure all files are readable and storage directories writable</li>";
echo "<li><strong>Check Laravel logs</strong> - Look for specific errors in the log output above</li>";
echo "<li><strong>Test template editor</strong> - Access /admin/notification/template/edit/4 and check browser console</li>";
echo "</ol>";

echo "<h3>Common Windows Server Issues:</h3>";
echo "<ul>";
echo "<li><strong>File Path Issues:</strong> Windows uses backslashes, may cause asset loading problems</li>";
echo "<li><strong>Permission Issues:</strong> IIS/Plesk may have different permission requirements</li>";
echo "<li><strong>PHP Version:</strong> Ensure PHP 8.1+ compatibility</li>";
echo "<li><strong>Extension Issues:</strong> Some PHP extensions may not be enabled</li>";
echo "<li><strong>URL Rewriting:</strong> .htaccess rules may not work the same on Windows/IIS</li>";
echo "</ul>";

echo "</div>";

echo "<div class='panel'>";
echo "<h2>📊 Export Diagnostic Data</h2>";
echo "<p>Copy the following diagnostic data and send it for analysis:</p>";
echo "<textarea style='width:100%;height:200px;font-family:monospace;'>";
echo "=== WINDOWS SERVER DIAGNOSTIC REPORT ===\n";
echo "Timestamp: " . date('Y-m-d H:i:s') . "\n";
echo "PHP Version: " . PHP_VERSION . "\n";
echo "OS: " . PHP_OS . "\n";
echo "Server: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "\n";
echo "Document Root: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "\n";
echo "Memory Limit: " . ini_get('memory_limit') . "\n";
echo "Max Execution Time: " . ini_get('max_execution_time') . "\n";
echo "\n=== FILE STATUS ===\n";
foreach ($files_to_check as $file) {
    $status = file_exists($file) ? 'EXISTS' : 'MISSING';
    echo "$file: $status\n";
}
echo "\n=== EXTENSIONS ===\n";
foreach ($required_extensions as $ext) {
    $status = extension_loaded($ext) ? 'LOADED' : 'MISSING';
    echo "$ext: $status\n";
}
echo "</textarea>";
echo "</div>";

echo "</body></html>";

// Flush output
ob_end_flush();
?>
