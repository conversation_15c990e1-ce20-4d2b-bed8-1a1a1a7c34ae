<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\NotificationTemplate;
use App\Services\ShortcodeService;

class TestShortcodes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test-shortcodes {template_id : Template ID to test}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test shortcode replacement for a specific template';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $templateId = $this->argument('template_id');
        $template = NotificationTemplate::find($templateId);

        if (!$template) {
            $this->error("Template {$templateId} not found!");
            return 1;
        }

        $this->info("🧪 TESTING SHORTCODE REPLACEMENT");
        $this->line("=====================================");
        $this->line("Template: {$template->name} (ID: {$templateId})");
        $this->line("Action: {$template->act}");

        // Create test user
        $testUser = (object) [
            'id' => 1,
            'fullname' => 'Test User',
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'referral_code' => 'REF123456',
            'created_at' => now()
        ];

        // Get comprehensive shortcodes
        $shortcodes = ShortcodeService::getShortcodes($testUser, [
            'amount' => '1000.00',
            'currency' => 'USD',
            'ib_type' => 'Master IB',
            'commission_rate' => '50%',
            'approval_date' => now()->format('Y-m-d H:i:s')
        ]);

        $this->line("\n📋 AVAILABLE SHORTCODES:");
        foreach ($shortcodes as $key => $value) {
            $this->line("  {{" . $key . "}} = " . $value);
        }

        // Test replacement in email body
        $emailBody = $template->email_body;
        $subject = $template->subj;

        // Find shortcodes in template
        preg_match_all('/\{\{([^}]+)\}\}/', $emailBody . ' ' . $subject, $matches);
        $templateShortcodes = array_unique($matches[1]);

        $this->line("\n📧 SHORTCODES IN TEMPLATE:");
        foreach ($templateShortcodes as $shortcode) {
            $hasValue = isset($shortcodes[$shortcode]);
            $status = $hasValue ? '✅' : '❌';
            $value = $hasValue ? $shortcodes[$shortcode] : 'NOT IMPLEMENTED';
            $this->line("  $status {{" . $shortcode . "}} = " . $value);
        }

        // Replace shortcodes
        $processedBody = $emailBody;
        $processedSubject = $subject;

        foreach ($shortcodes as $key => $value) {
            $processedBody = str_replace('{{' . $key . '}}', $value, $processedBody);
            $processedSubject = str_replace('{{' . $key . '}}', $value, $processedSubject);
        }

        // Check for unreplaced shortcodes
        preg_match_all('/\{\{([^}]+)\}\}/', $processedBody . ' ' . $processedSubject, $unreplacedMatches);
        $unreplacedShortcodes = array_unique($unreplacedMatches[1]);

        if (empty($unreplacedShortcodes)) {
            $this->info("\n✅ ALL SHORTCODES SUCCESSFULLY REPLACED!");
        } else {
            $this->error("\n❌ UNREPLACED SHORTCODES FOUND:");
            foreach ($unreplacedShortcodes as $shortcode) {
                $this->line("  - {{" . $shortcode . "}}");
            }
        }

        $this->line("\n📄 PROCESSED SUBJECT:");
        $this->line($processedSubject);

        $this->line("\n📄 PROCESSED EMAIL BODY (first 500 chars):");
        $this->line(substr(strip_tags($processedBody), 0, 500) . "...");

        return 0;
    }
}
