<?php
/**
 * REAL-TIME REQUEST MONITORING DIAGNOSTIC
 * This script monitors actual form submissions to identify the failure point
 */

// Include Laravel bootstrap
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🔍 REAL-TIME REQUEST MONITORING DIAGNOSTIC\n";
echo "==========================================\n\n";

// 1. LOG FILE ANALYSIS
echo "1️⃣ LOG FILE ANALYSIS\n";
echo "=====================\n";

try {
    $logFile = 'storage/logs/laravel.log';
    
    if (file_exists($logFile)) {
        $logSize = filesize($logFile);
        echo "✅ Log file exists: {$logSize} bytes\n";
        
        // Get recent log entries
        $logContent = file_get_contents($logFile);
        $logLines = explode("\n", $logContent);
        $recentLines = array_slice($logLines, -50); // Last 50 lines
        
        echo "✅ Recent log entries: " . count($recentLines) . " lines\n";
        
        // Look for template update related entries
        $templateUpdateEntries = 0;
        $errorEntries = 0;
        
        foreach ($recentLines as $line) {
            if (str_contains($line, 'TEMPLATE UPDATE') || str_contains($line, 'template.update')) {
                $templateUpdateEntries++;
            }
            if (str_contains($line, 'ERROR') || str_contains($line, 'CRITICAL')) {
                $errorEntries++;
            }
        }
        
        echo "✅ Template update entries: {$templateUpdateEntries}\n";
        echo "✅ Error entries: {$errorEntries}\n";
        
        if ($templateUpdateEntries === 0) {
            echo "⚠️  POTENTIAL ISSUE: No recent template update attempts in logs\n";
        }
        
        if ($errorEntries > 0) {
            echo "⚠️  POTENTIAL ISSUE: Recent errors detected in logs\n";
        }
        
    } else {
        echo "❌ Log file not found\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Log analysis error: " . $e->getMessage() . "\n";
}

echo "\n";

// 2. REQUEST SIMULATION WITH EXACT DATA
echo "2️⃣ REQUEST SIMULATION WITH EXACT DATA\n";
echo "======================================\n";

try {
    // Simulate the exact request that would come from the form
    echo "📊 Simulating exact form submission...\n";
    
    $template = \App\Models\NotificationTemplate::find(1);
    if (!$template) {
        echo "❌ Template not found for simulation\n";
        return;
    }
    
    // Create exact request data as it would come from the form
    $formData = [
        '_token' => csrf_token(),
        'subject' => 'Test Subject [REQUEST SIMULATION]',
        'email_status' => 'on',
        'email_body' => $template->email_body, // Use current content
        'email_body_final' => $template->email_body, // Same as email_body
        'original_email_body' => htmlspecialchars($template->email_body),
        'sms_body' => $template->sms_body ?? 'Default SMS body',
        'sms_status' => 'on'
    ];
    
    echo "✅ Form data prepared with " . count($formData) . " fields\n";
    
    // Log the exact data sizes
    foreach ($formData as $key => $value) {
        $size = strlen($value);
        $preview = substr($value, 0, 50) . ($size > 50 ? '...' : '');
        echo "   - {$key}: {$size} bytes | {$preview}\n";
    }
    
    // Test the controller method directly
    echo "\n📊 Testing controller method directly...\n";
    
    // Create a proper request object
    $request = \Illuminate\Http\Request::create(
        route('admin.setting.notification.template.update', $template->id),
        'POST',
        $formData
    );
    
    // Add headers that would be present in a real request
    $request->headers->set('Content-Type', 'application/x-www-form-urlencoded');
    $request->headers->set('X-Requested-With', 'XMLHttpRequest'); // Simulate AJAX if used
    
    echo "✅ Request object created\n";
    echo "✅ Request method: " . $request->method() . "\n";
    echo "✅ Request URL: " . $request->url() . "\n";
    echo "✅ Request has CSRF: " . ($request->has('_token') ? 'YES' : 'NO') . "\n";
    
    // Store original values for comparison
    $originalSubject = $template->subj;
    $originalEmailBody = $template->email_body;
    
    try {
        // Call the controller method
        $controller = new \App\Http\Controllers\Admin\NotificationController();
        
        // Enable query logging to see what happens
        \DB::enableQueryLog();
        
        // This should trigger the same logic as the web request
        $response = $controller->templateUpdate($request, $template->id);
        
        $queries = \DB::getQueryLog();
        echo "✅ Controller method executed\n";
        echo "✅ Queries executed: " . count($queries) . "\n";
        
        foreach ($queries as $query) {
            echo "   - " . substr($query['query'], 0, 100) . "...\n";
        }
        
        // Check if template was actually updated
        $template->refresh();
        $subjectChanged = $template->subj !== $originalSubject;
        $emailBodyChanged = $template->email_body !== $originalEmailBody;
        
        echo "✅ Subject changed: " . ($subjectChanged ? 'YES' : 'NO') . "\n";
        echo "✅ Email body changed: " . ($emailBodyChanged ? 'YES' : 'NO') . "\n";
        
        if ($subjectChanged || $emailBodyChanged) {
            echo "✅ CONTROLLER WORKS: Changes were saved successfully\n";
            
            // Restore original values
            $template->update([
                'subj' => $originalSubject,
                'email_body' => $originalEmailBody
            ]);
            echo "✅ Original values restored\n";
        } else {
            echo "❌ CONTROLLER ISSUE: No changes were saved\n";
        }
        
        // Check response type
        $responseClass = get_class($response);
        echo "✅ Response type: {$responseClass}\n";
        
    } catch (\Exception $e) {
        echo "❌ Controller execution error: " . $e->getMessage() . "\n";
        echo "❌ File: " . $e->getFile() . "\n";
        echo "❌ Line: " . $e->getLine() . "\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Request simulation error: " . $e->getMessage() . "\n";
}

echo "\n";

// 3. BROWSER REQUEST ANALYSIS
echo "3️⃣ BROWSER REQUEST ANALYSIS\n";
echo "============================\n";

try {
    // Check what would happen with a real browser request
    echo "📊 Analyzing browser request requirements...\n";
    
    // Check required headers
    $requiredHeaders = [
        'Content-Type' => 'application/x-www-form-urlencoded',
        'Accept' => 'text/html,application/xhtml+xml,application/xml',
        'User-Agent' => 'Browser user agent string'
    ];
    
    foreach ($requiredHeaders as $header => $description) {
        echo "✅ Required header '{$header}': {$description}\n";
    }
    
    // Check form encoding
    echo "\n📊 Form encoding analysis...\n";
    
    $testData = [
        'simple_field' => 'Simple value',
        'special_chars' => 'Special chars: <>&"\'',
        'html_content' => '<p>HTML content with <strong>tags</strong></p>',
        'large_content' => str_repeat('Large content block. ', 100)
    ];
    
    foreach ($testData as $field => $value) {
        $encoded = urlencode($value);
        $encodedSize = strlen($encoded);
        $originalSize = strlen($value);
        
        echo "   - {$field}: {$originalSize} -> {$encodedSize} bytes (encoded)\n";
        
        if ($encodedSize > $originalSize * 3) {
            echo "     ⚠️  High encoding overhead detected\n";
        }
    }
    
    // Check for potential size limits
    $totalFormSize = 0;
    foreach ($formData as $value) {
        $totalFormSize += strlen(urlencode($value));
    }
    
    echo "\n✅ Total form size (encoded): {$totalFormSize} bytes\n";
    
    $postMaxSize = ini_get('post_max_size');
    $postMaxBytes = parseSize($postMaxSize);
    
    echo "✅ PHP post_max_size: {$postMaxSize} ({$postMaxBytes} bytes)\n";
    
    if ($totalFormSize > $postMaxBytes * 0.8) {
        echo "⚠️  POTENTIAL ISSUE: Form size approaching POST limit\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Browser request analysis error: " . $e->getMessage() . "\n";
}

echo "\n📋 REQUEST MONITORING COMPLETE\n";
echo "===============================\n";
echo "This diagnostic monitors the actual request flow:\n";
echo "1. Log file analysis for recent activity\n";
echo "2. Direct controller method testing\n";
echo "3. Browser request requirement analysis\n\n";

echo "🔍 This reveals if the issue is in the web layer or deeper.\n";

// Helper function to parse size strings like "8M" to bytes
function parseSize($size) {
    $unit = preg_replace('/[^bkmgtpezy]/i', '', $size);
    $size = preg_replace('/[^0-9\.]/', '', $size);

    if ($unit) {
        return round($size * pow(1024, stripos('bkmgtpezy', $unit[0])));
    }

    return round($size);
}

?>
