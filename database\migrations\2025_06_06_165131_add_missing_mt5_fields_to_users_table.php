<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add missing MT5 fields that weren't added in the previous migration

            // Core MT5 Fields
            if (!Schema::hasColumn('users', 'mt5_timestamp')) {
                $table->bigInteger('mt5_timestamp')->nullable()->after('mt5_login');
            }
            if (!Schema::hasColumn('users', 'mt5_cert_serial_number')) {
                $table->bigInteger('mt5_cert_serial_number')->nullable()->after('mt5_group');
            }
            if (!Schema::hasColumn('users', 'mt5_rights')) {
                $table->bigInteger('mt5_rights')->nullable()->after('mt5_cert_serial_number');
            }
            if (!Schema::hasColumn('users', 'mt5_last_pass_change')) {
                $table->timestamp('mt5_last_pass_change')->nullable()->after('mt5_last_access');
            }

            // Personal Information
            if (!Schema::hasColumn('users', 'mt5_first_name')) {
                $table->string('mt5_first_name')->nullable()->after('mt5_last_pass_change');
            }
            if (!Schema::hasColumn('users', 'mt5_last_name')) {
                $table->string('mt5_last_name')->nullable()->after('mt5_first_name');
            }
            if (!Schema::hasColumn('users', 'mt5_middle_name')) {
                $table->string('mt5_middle_name')->nullable()->after('mt5_last_name');
            }
            if (!Schema::hasColumn('users', 'mt5_account')) {
                $table->string('mt5_account')->nullable()->after('mt5_company');
            }
            if (!Schema::hasColumn('users', 'mt5_language')) {
                $table->integer('mt5_language')->nullable()->after('mt5_country');
            }
            if (!Schema::hasColumn('users', 'mt5_client_id')) {
                $table->bigInteger('mt5_client_id')->nullable()->after('mt5_language');
            }

            // Address Information
            if (!Schema::hasColumn('users', 'mt5_city')) {
                $table->string('mt5_city')->nullable()->after('mt5_client_id');
            }
            if (!Schema::hasColumn('users', 'mt5_state')) {
                $table->string('mt5_state')->nullable()->after('mt5_city');
            }
            if (!Schema::hasColumn('users', 'mt5_zip_code')) {
                $table->string('mt5_zip_code')->nullable()->after('mt5_state');
            }
            if (!Schema::hasColumn('users', 'mt5_address')) {
                $table->string('mt5_address')->nullable()->after('mt5_zip_code');
            }
            if (!Schema::hasColumn('users', 'mt5_phone')) {
                $table->string('mt5_phone')->nullable()->after('mt5_address');
            }
            if (!Schema::hasColumn('users', 'mt5_email')) {
                $table->string('mt5_email')->nullable()->after('mt5_phone');
            }
            if (!Schema::hasColumn('users', 'mt5_id')) {
                $table->string('mt5_id')->nullable()->after('mt5_email');
            }
            if (!Schema::hasColumn('users', 'mt5_status')) {
                $table->string('mt5_status')->nullable()->after('mt5_id');
            }
            if (!Schema::hasColumn('users', 'mt5_color')) {
                $table->integer('mt5_color')->nullable()->after('mt5_comment');
            }
            if (!Schema::hasColumn('users', 'mt5_phone_password')) {
                $table->string('mt5_phone_password')->nullable()->after('mt5_color');
            }

            // Trading Information
            if (!Schema::hasColumn('users', 'mt5_agent')) {
                $table->bigInteger('mt5_agent')->nullable()->after('mt5_leverage');
            }
            if (!Schema::hasColumn('users', 'mt5_trade_accounts')) {
                $table->string('mt5_trade_accounts')->nullable()->after('mt5_agent');
            }
            if (!Schema::hasColumn('users', 'mt5_limit_positions')) {
                $table->double('mt5_limit_positions')->nullable()->after('mt5_trade_accounts');
            }
            if (!Schema::hasColumn('users', 'mt5_limit_orders')) {
                $table->integer('mt5_limit_orders')->nullable()->after('mt5_limit_positions');
            }
            if (!Schema::hasColumn('users', 'mt5_lead_campaign')) {
                $table->string('mt5_lead_campaign')->nullable()->after('mt5_limit_orders');
            }
            if (!Schema::hasColumn('users', 'mt5_lead_source')) {
                $table->string('mt5_lead_source')->nullable()->after('mt5_lead_campaign');
            }
            if (!Schema::hasColumn('users', 'mt5_timestamp_trade')) {
                $table->bigInteger('mt5_timestamp_trade')->nullable()->after('mt5_lead_source');
            }

            // Financial Information
            if (!Schema::hasColumn('users', 'mt5_interest_rate')) {
                $table->double('mt5_interest_rate')->nullable()->after('mt5_equity');
            }
            if (!Schema::hasColumn('users', 'mt5_commission_daily')) {
                $table->double('mt5_commission_daily')->nullable()->after('mt5_interest_rate');
            }
            if (!Schema::hasColumn('users', 'mt5_commission_monthly')) {
                $table->double('mt5_commission_monthly')->nullable()->after('mt5_commission_daily');
            }
            if (!Schema::hasColumn('users', 'mt5_balance_prev_day')) {
                $table->double('mt5_balance_prev_day')->nullable()->after('mt5_commission_monthly');
            }
            if (!Schema::hasColumn('users', 'mt5_balance_prev_month')) {
                $table->double('mt5_balance_prev_month')->nullable()->after('mt5_balance_prev_day');
            }
            if (!Schema::hasColumn('users', 'mt5_equity_prev_day')) {
                $table->double('mt5_equity_prev_day')->nullable()->after('mt5_balance_prev_month');
            }
            if (!Schema::hasColumn('users', 'mt5_equity_prev_month')) {
                $table->double('mt5_equity_prev_month')->nullable()->after('mt5_equity_prev_day');
            }

            // Additional Information
            if (!Schema::hasColumn('users', 'mt5_name')) {
                $table->string('mt5_name')->nullable()->after('mt5_equity_prev_month');
            }
            if (!Schema::hasColumn('users', 'mt5_api_data')) {
                $table->text('mt5_api_data')->nullable()->after('mt5_last_ip');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop the columns we added
            $columnsToRemove = [
                'mt5_timestamp',
                'mt5_cert_serial_number',
                'mt5_rights',
                'mt5_last_pass_change',
                'mt5_first_name',
                'mt5_last_name',
                'mt5_middle_name',
                'mt5_account',
                'mt5_language',
                'mt5_client_id',
                'mt5_city',
                'mt5_state',
                'mt5_zip_code',
                'mt5_address',
                'mt5_phone',
                'mt5_email',
                'mt5_id',
                'mt5_status',
                'mt5_color',
                'mt5_phone_password',
                'mt5_agent',
                'mt5_trade_accounts',
                'mt5_limit_positions',
                'mt5_limit_orders',
                'mt5_lead_campaign',
                'mt5_lead_source',
                'mt5_timestamp_trade',
                'mt5_interest_rate',
                'mt5_commission_daily',
                'mt5_commission_monthly',
                'mt5_balance_prev_day',
                'mt5_balance_prev_month',
                'mt5_equity_prev_day',
                'mt5_equity_prev_month',
                'mt5_name',
                'mt5_api_data'
            ];

            foreach ($columnsToRemove as $column) {
                if (Schema::hasColumn('users', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
