<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct Referral AJAX Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <h1>🔧 Direct Referral AJAX Test</h1>
        <p class="text-muted">Testing AJAX endpoints independently</p>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test 1: MT5 Accounts Loading</h5>
                    </div>
                    <div class="card-body">
                        <button id="test-mt5-accounts" class="btn btn-primary">Load MT5 Accounts</button>
                        <div id="mt5-accounts-result" class="mt-3"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test 2: MT5 Search</h5>
                    </div>
                    <div class="card-body">
                        <input type="text" id="mt5-search-input" class="form-control mb-2" placeholder="Enter MT5 ID (e.g., 878046)">
                        <button id="test-mt5-search" class="btn btn-success">Search MT5</button>
                        <div id="mt5-search-result" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Test 3: Form Submission Simulation</h5>
                    </div>
                    <div class="card-body">
                        <form id="test-form">
                            <input type="hidden" name="_token" value="test-token">
                            <input type="hidden" name="referral_user" id="referral_user" value="">
                            
                            <div class="mb-3">
                                <label>Select Test User ID:</label>
                                <select id="test-user-select" class="form-control">
                                    <option value="">Choose a user</option>
                                    <option value="1">User ID 1</option>
                                    <option value="10921">User ID 10921</option>
                                    <option value="11178">User ID 11178</option>
                                </select>
                            </div>
                            
                            <button type="button" id="test-form-submit" class="btn btn-warning">Test Form Validation</button>
                        </form>
                        <div id="form-test-result" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-info">
                    <h6>🎯 Testing Instructions:</h6>
                    <ol>
                        <li>Click "Load MT5 Accounts" to test the AJAX endpoint</li>
                        <li>Enter an MT5 ID and click "Search MT5" to test search functionality</li>
                        <li>Select a user and test form validation</li>
                        <li>Check browser console for detailed logs</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            console.log('🔧 Direct Referral AJAX Test Started');
            
            // Test 1: MT5 Accounts Loading
            $('#test-mt5-accounts').click(function() {
                $('#mt5-accounts-result').html('<i class="fas fa-spinner fa-spin"></i> Loading...');
                
                $.ajax({
                    url: '/admin/users/get-all-mt5-accounts',
                    type: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    success: function(response) {
                        console.log('✅ MT5 Accounts Response:', response);
                        
                        if (response.success && response.accounts) {
                            let html = `<div class="alert alert-success">
                                <strong>✅ Success!</strong><br>
                                Found ${response.accounts.length} MT5 accounts<br>
                                <small>Check console for full response</small>
                            </div>`;
                            
                            // Show first 3 accounts
                            html += '<div class="mt-2"><strong>Sample accounts:</strong><ul>';
                            response.accounts.slice(0, 3).forEach(function(account) {
                                html += `<li>MT5: ${account.mt5_login} (User: ${account.user_name})</li>`;
                            });
                            html += '</ul></div>';
                            
                            $('#mt5-accounts-result').html(html);
                        } else {
                            $('#mt5-accounts-result').html('<div class="alert alert-warning">❌ No accounts found</div>');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('❌ MT5 Accounts Error:', error);
                        $('#mt5-accounts-result').html(`<div class="alert alert-danger">
                            ❌ Error: ${error}<br>
                            Status: ${xhr.status}<br>
                            Response: ${xhr.responseText}
                        </div>`);
                    }
                });
            });
            
            // Test 2: MT5 Search
            $('#test-mt5-search').click(function() {
                const searchTerm = $('#mt5-search-input').val().trim();
                if (!searchTerm) {
                    alert('Please enter an MT5 ID');
                    return;
                }
                
                $('#mt5-search-result').html('<i class="fas fa-spinner fa-spin"></i> Searching...');
                
                $.ajax({
                    url: '/admin/users/search-by-mt5',
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': 'test-token'
                    },
                    data: {
                        mt5_id: searchTerm,
                        _token: 'test-token'
                    },
                    success: function(response) {
                        console.log('✅ MT5 Search Response:', response);
                        
                        if (response.success && response.users && response.users.length > 0) {
                            let html = `<div class="alert alert-success">
                                <strong>✅ Found ${response.users.length} users!</strong>
                            </div>`;
                            
                            html += '<div class="mt-2"><ul>';
                            response.users.forEach(function(user) {
                                html += `<li>MT5: ${user.mt5_login} - ${user.user_name} (ID: ${user.id})</li>`;
                            });
                            html += '</ul></div>';
                            
                            $('#mt5-search-result').html(html);
                        } else {
                            $('#mt5-search-result').html('<div class="alert alert-warning">❌ No users found</div>');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('❌ MT5 Search Error:', error);
                        $('#mt5-search-result').html(`<div class="alert alert-danger">
                            ❌ Error: ${error}<br>
                            Status: ${xhr.status}<br>
                            Response: ${xhr.responseText}
                        </div>`);
                    }
                });
            });
            
            // Test 3: Form Validation
            $('#test-user-select').change(function() {
                const userId = $(this).val();
                $('#referral_user').val(userId);
                console.log('🔧 User selected:', userId);
                console.log('🔧 Hidden field value:', $('#referral_user').val());
            });
            
            $('#test-form-submit').click(function() {
                const selectedUserId = $('#referral_user').val();
                const formData = $('#test-form').serialize();
                
                console.log('🔧 Form submission test:');
                console.log('  - Selected User ID:', selectedUserId);
                console.log('  - Form data:', formData);
                
                if (!selectedUserId || selectedUserId === '') {
                    $('#form-test-result').html('<div class="alert alert-danger">❌ Validation failed: No user selected</div>');
                } else {
                    $('#form-test-result').html(`<div class="alert alert-success">
                        ✅ Validation passed!<br>
                        Selected User ID: ${selectedUserId}<br>
                        Form data: ${formData}
                    </div>`);
                }
            });
            
            console.log('🔧 All test handlers initialized');
        });
    </script>
</body>
</html>
