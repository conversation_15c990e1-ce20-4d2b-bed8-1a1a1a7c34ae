<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 UPDATING REAL COMMISSION-EARNING IBs\n";
echo "=======================================\n";

// Real commission-earning IBs identified from MT5
$realIBs = [
    '<EMAIL>',
    '<EMAIL>', 
    '<EMAIL>'
];

foreach ($realIBs as $email) {
    echo "\n🔍 Processing: {$email}\n";
    
    try {
        $user = \App\Models\User::where('email', $email)->first();
        
        if (!$user) {
            echo "❌ User not found\n";
            continue;
        }
        
        echo "   Current Status: Partner={$user->partner}, IB Status={$user->ib_status}\n";
        echo "   MT5 Login: {$user->mt5_login}\n";
        
        // Update to approved Master IB
        $user->update([
            'partner' => 1,
            'ib_status' => 'approved',
            'ib_type' => 'master',
            'ib_approved_at' => now(),
            'is_ib_account' => true,
            'ib_group_id' => 1, // Standard IB Group
            'ib_commission_rate' => 50.00, // 50% commission rate
            'updated_at' => now()
        ]);
        
        echo "   ✅ Updated to approved Master IB\n";
        echo "   New Status: Partner={$user->partner}, IB Status={$user->ib_status}\n";
        
    } catch (Exception $e) {
        echo "   ❌ Error: " . $e->getMessage() . "\n";
    }
}

// Verify the updates
echo "\n📊 VERIFICATION OF UPDATES\n";
echo "==========================\n";

$approvedIBs = \App\Models\User::where('partner', 1)->get();
echo "✅ Total Approved IBs: {$approvedIBs->count()}\n";

foreach ($realIBs as $email) {
    $user = \App\Models\User::where('email', $email)->first();
    if ($user && $user->partner == 1) {
        echo "   ✅ {$email}: Successfully approved (ID: {$user->id})\n";
    } else {
        echo "   ❌ {$email}: Update failed\n";
    }
}

// Test commission calculation for these IBs
echo "\n💰 COMMISSION CALCULATION TEST\n";
echo "==============================\n";

foreach ($realIBs as $email) {
    $user = \App\Models\User::where('email', $email)->first();
    if ($user && $user->partner == 1) {
        echo "\n🔍 Testing commission for: {$email}\n";
        
        // Simulate a $10 commission
        $brokerCommission = 10.00;
        $ibCommissionRate = 50.00; // 50%
        $ibCommission = ($brokerCommission * $ibCommissionRate) / 100;
        
        echo "   Broker Commission: $" . number_format($brokerCommission, 2) . "\n";
        echo "   IB Rate: {$ibCommissionRate}%\n";
        echo "   IB Commission: $" . number_format($ibCommission, 2) . "\n";
        
        // Check if they have referrals
        $referrals = $user->referrals()->count();
        echo "   Referrals: {$referrals} users\n";
    }
}

echo "\n✅ Real IB data integration completed!\n";
