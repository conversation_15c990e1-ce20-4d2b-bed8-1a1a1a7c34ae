<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\MT5CommissionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class MT5WebhookController extends Controller
{
    protected $mt5CommissionService;

    public function __construct()
    {
        $this->mt5CommissionService = new MT5CommissionService();
    }

    /**
     * Handle MT5 trade closure webhook
     */
    public function handleTradeClose(Request $request)
    {
        return $this->mt5CommissionService->handleMT5Webhook($request);
    }

    /**
     * Handle MT5 account update webhook
     */
    public function handleAccountUpdate(Request $request)
    {
        try {
            Log::info("MT5 account update webhook received", ['data' => $request->all()]);

            // Process account update
            $accountData = $request->all();
            
            // Find and update user account
            $userAccount = \App\Models\UserAccounts::where('login', $accountData['login'])->first();
            
            if ($userAccount) {
                $userAccount->update([
                    'balance' => $accountData['balance'] ?? $userAccount->balance,
                    'equity' => $accountData['equity'] ?? $userAccount->equity,
                    'margin' => $accountData['margin'] ?? $userAccount->margin,
                    'free_margin' => $accountData['margin_free'] ?? $userAccount->free_margin,
                    'last_sync' => now()
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Account updated successfully'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Account not found'
            ], 404);

        } catch (\Exception $e) {
            Log::error("MT5 account update webhook error: " . $e->getMessage());
            return response()->json(['error' => 'Internal server error'], 500);
        }
    }

    /**
     * Test webhook endpoint
     */
    public function test(Request $request)
    {
        Log::info("MT5 webhook test received", ['data' => $request->all()]);
        
        return response()->json([
            'success' => true,
            'message' => 'Webhook test successful',
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * Get webhook status and statistics
     */
    public function status()
    {
        try {
            $stats = $this->mt5CommissionService->getCommissionStats();
            
            return response()->json([
                'success' => true,
                'webhook_status' => 'active',
                'statistics' => $stats,
                'last_processed' => \App\Models\IbCommission::latest()->first()?->created_at
            ]);

        } catch (\Exception $e) {
            Log::error("Webhook status error: " . $e->getMessage());
            return response()->json(['error' => 'Internal server error'], 500);
        }
    }
}
