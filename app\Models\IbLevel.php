<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class IbLevel extends Model
{
    use HasFactory;

    protected $fillable = [
        'level',
        'name',
        'commission_percent',
        'max_commission_percent',
        'description',
        'status'
    ];

    protected $casts = [
        'commission_percent' => 'decimal:2',
        'max_commission_percent' => 'decimal:2',
        'status' => 'boolean'
    ];

    /**
     * Scope for active levels
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Scope for ordering by level
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('level', 'asc');
    }

    /**
     * Get all active levels ordered by level number
     */
    public static function getActiveLevels()
    {
        return self::active()->ordered()->get();
    }

    /**
     * Get level by number
     */
    public static function getByLevel($levelNumber)
    {
        return self::where('level', $levelNumber)->first();
    }

    /**
     * Get maximum level number
     */
    public static function getMaxLevel()
    {
        return self::active()->max('level') ?? 0;
    }
}
