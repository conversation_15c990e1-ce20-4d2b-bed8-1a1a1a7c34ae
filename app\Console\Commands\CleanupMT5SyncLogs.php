<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class CleanupMT5SyncLogs extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'mt5:cleanup-sync-logs 
                            {--days=7 : Number of days to keep logs}
                            {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     */
    protected $description = 'Clean up old MT5 sync logs and optimize log files';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧹 Starting MT5 Sync Log Cleanup');
        
        $days = $this->option('days');
        $dryRun = $this->option('dry-run');
        
        if ($dryRun) {
            $this->warn('🧪 DRY RUN MODE - No files will be deleted');
        }
        
        $cutoffDate = Carbon::now()->subDays($days);
        $this->info("📅 Cleaning logs older than: {$cutoffDate->format('Y-m-d H:i:s')}");
        
        $logPaths = [
            storage_path('logs/mt5-sync.log'),
            storage_path('logs/mt5-commission-sync.log'),
            storage_path('logs/mt5-ib-sync.log'),
            storage_path('logs/laravel.log')
        ];
        
        $totalSizeBefore = 0;
        $totalSizeAfter = 0;
        $filesProcessed = 0;
        
        foreach ($logPaths as $logPath) {
            if (File::exists($logPath)) {
                $sizeBefore = File::size($logPath);
                $totalSizeBefore += $sizeBefore;
                
                $this->info("📄 Processing: " . basename($logPath) . " (" . $this->formatBytes($sizeBefore) . ")");
                
                if (!$dryRun) {
                    $this->cleanLogFile($logPath, $cutoffDate);
                }
                
                $sizeAfter = File::exists($logPath) ? File::size($logPath) : 0;
                $totalSizeAfter += $sizeAfter;
                $filesProcessed++;
                
                $saved = $sizeBefore - $sizeAfter;
                if ($saved > 0) {
                    $this->info("✅ Cleaned: " . $this->formatBytes($saved) . " saved");
                } else {
                    $this->info("ℹ️  No cleanup needed");
                }
            } else {
                $this->warn("⚠️  Log file not found: " . basename($logPath));
            }
        }
        
        // Summary
        $totalSaved = $totalSizeBefore - $totalSizeAfter;
        $this->info('');
        $this->info('🎉 Cleanup Summary:');
        $this->table(['Metric', 'Value'], [
            ['Files Processed', $filesProcessed],
            ['Total Size Before', $this->formatBytes($totalSizeBefore)],
            ['Total Size After', $this->formatBytes($totalSizeAfter)],
            ['Space Saved', $this->formatBytes($totalSaved)],
            ['Cleanup Date', $cutoffDate->format('Y-m-d H:i:s')]
        ]);
        
        if (!$dryRun) {
            Log::info('MT5 Sync Log Cleanup Completed', [
                'files_processed' => $filesProcessed,
                'space_saved_bytes' => $totalSaved,
                'cutoff_date' => $cutoffDate
            ]);
        }
        
        return 0;
    }

    /**
     * Clean individual log file by removing old entries
     */
    private function cleanLogFile($logPath, $cutoffDate)
    {
        try {
            if (!File::exists($logPath)) {
                return;
            }
            
            $tempPath = $logPath . '.tmp';
            $inputHandle = fopen($logPath, 'r');
            $outputHandle = fopen($tempPath, 'w');
            
            if (!$inputHandle || !$outputHandle) {
                $this->error("❌ Failed to open file handles for: " . basename($logPath));
                return;
            }
            
            $linesKept = 0;
            $linesRemoved = 0;
            
            while (($line = fgets($inputHandle)) !== false) {
                // Extract timestamp from Laravel log format
                if (preg_match('/^\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]/', $line, $matches)) {
                    try {
                        $logDate = Carbon::parse($matches[1]);
                        
                        if ($logDate->gte($cutoffDate)) {
                            fwrite($outputHandle, $line);
                            $linesKept++;
                        } else {
                            $linesRemoved++;
                        }
                    } catch (\Exception $e) {
                        // If date parsing fails, keep the line
                        fwrite($outputHandle, $line);
                        $linesKept++;
                    }
                } else {
                    // If no timestamp found, keep the line (might be continuation)
                    fwrite($outputHandle, $line);
                    $linesKept++;
                }
            }
            
            fclose($inputHandle);
            fclose($outputHandle);
            
            // Replace original file with cleaned version
            File::move($tempPath, $logPath);
            
            $this->line("  📊 Lines kept: {$linesKept}, Lines removed: {$linesRemoved}");
            
        } catch (\Exception $e) {
            $this->error("❌ Error cleaning " . basename($logPath) . ": " . $e->getMessage());
            
            // Clean up temp file if it exists
            if (File::exists($tempPath)) {
                File::delete($tempPath);
            }
        }
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
