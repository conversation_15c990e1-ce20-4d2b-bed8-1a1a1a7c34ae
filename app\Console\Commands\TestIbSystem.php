<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class TestIbSystem extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:ib-system';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the complete IB system functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 Testing Complete IB System');
        $this->info('============================');

        // Test 1: Check IB User
        $this->info('📋 Test 1: Checking IB User Data');
        $ibUser = User::where('email', '<EMAIL>')->first();
        
        if ($ibUser) {
            $this->info("✅ IB User Found:");
            $this->line("   ID: {$ibUser->id}");
            $this->line("   Email: {$ibUser->email}");
            $this->line("   MT5 Login: {$ibUser->mt5_login}");
            $this->line("   MT5 Group: {$ibUser->mt5_group}");
            $this->line("   IB Status: {$ibUser->ib_status}");
            $this->line("   IB Type: {$ibUser->ib_type}");
            $this->line("   Partner: {$ibUser->partner}");
        } else {
            $this->error("❌ IB User not found!");
            return 1;
        }

        // Test 2: Check MT5 Commission Data
        $this->info("\n📊 Test 2: Checking MT5 Commission Data");
        try {
            $commissions = DB::connection('mbf-dbmt5')
                ->table('mt5_deals_2025')
                ->where('Login', $ibUser->mt5_login)
                ->where('Action', 18)
                ->get();

            $totalCommission = $commissions->sum('Profit');
            $commissionCount = $commissions->count();

            $this->info("✅ MT5 Commission Data:");
            $this->line("   Total Commission: $" . number_format($totalCommission, 2));
            $this->line("   Commission Deals: {$commissionCount}");
            
            if ($commissionCount > 0) {
                $recentCommission = $commissions->first();
                $this->line("   Latest Deal: {$recentCommission->Deal}");
                $this->line("   Latest Amount: $" . number_format($recentCommission->Profit, 2));
                $this->line("   Latest Date: {$recentCommission->Time}");
            }
        } catch (\Exception $e) {
            $this->error("❌ Failed to get MT5 commission data: " . $e->getMessage());
        }

        // Test 3: Check User Detail Page Data
        $this->info("\n🔍 Test 3: Testing User Detail Controller");
        try {
            $controller = new \App\Http\Controllers\Admin\ManageUsersController();
            $reflection = new \ReflectionClass($controller);
            $method = $reflection->getMethod('getMT5CommissionData');
            $method->setAccessible(true);
            
            $mt5Data = $method->invoke($controller, $ibUser->mt5_login, 30);
            
            $this->info("✅ Controller MT5 Data:");
            $this->line("   Total Commission: $" . number_format($mt5Data['total_commission'], 2));
            $this->line("   Commission Count: {$mt5Data['commission_count']}");
            $this->line("   Period Days: {$mt5Data['period_days']}");
        } catch (\Exception $e) {
            $this->error("❌ Failed to test controller method: " . $e->getMessage());
        }

        // Test 4: Check isIb() Method
        $this->info("\n👤 Test 4: Testing isIb() Method");
        if (method_exists($ibUser, 'isIb')) {
            $isIb = $ibUser->isIb();
            if ($isIb) {
                $this->info("✅ isIb() method returns true");
            } else {
                $this->warn("⚠️  isIb() method returns false - check implementation");
            }
        } else {
            $this->error("❌ isIb() method not found on User model");
        }

        // Test 5: Check Database Structure
        $this->info("\n🗄️  Test 5: Checking Database Structure");
        $userColumns = DB::getSchemaBuilder()->getColumnListing('users');
        $requiredColumns = ['mt5_login', 'mt5_group', 'ib_status', 'ib_type', 'partner'];
        
        foreach ($requiredColumns as $column) {
            if (in_array($column, $userColumns)) {
                $this->info("✅ Column '{$column}' exists");
            } else {
                $this->error("❌ Column '{$column}' missing");
            }
        }

        // Test 6: Test URLs
        $this->info("\n🌐 Test 6: Testing URLs");
        $testUrls = [
            "User Detail: http://localhost/mbf.mybrokerforex.com-31052025/admin/users/detail/{$ibUser->id}",
            "Admin Dashboard: http://localhost/mbf.mybrokerforex.com-31052025/admin/dashboard",
        ];

        foreach ($testUrls as $url) {
            $this->line("   📎 {$url}");
        }

        // Summary
        $this->info("\n🎉 IB System Test Summary");
        $this->info("========================");
        $this->info("✅ IB User: {$ibUser->email} (ID: {$ibUser->id})");
        $this->info("✅ MT5 Login: {$ibUser->mt5_login}");
        $this->info("✅ Total Commission: $" . number_format($totalCommission ?? 0, 2));
        $this->info("✅ Commission Deals: " . ($commissionCount ?? 0));
        $this->info("✅ IB Status: {$ibUser->ib_status}");
        $this->info("✅ System Ready for Testing!");

        return 0;
    }
}
