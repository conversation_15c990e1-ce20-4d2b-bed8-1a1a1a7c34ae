'use strict';

$(function(){
  $('#sidebar__menuWrapper').slimScroll({
    height: 'calc(100vh - 86.75px)',
    railVisible: true,
		alwaysVisible: true
  });
});

$(function(){
  $('.dropdown-menu__body').slimScroll({
    height: '270px'
  });
});

// activity-list 
$(function(){
  $('.activity-list').slimScroll({
    height: '385px'
  });
});

// recent ticket list 
$(function(){
  $('.recent-ticket-list__body').slimScroll({
    height: '295px'
  });
});

$('.navbar-search-field').on('input', function () {
    var search = $(this).val().toLowerCase();
    var search_result_pane = $('.search-list');
    $(search_result_pane).html('');
    if (search.length == 0) {
      $('.search-list').addClass('d-none');
        return;
    }
    $('.search-list').removeClass('d-none');

    // search
    var match = $('.sidebar__menu-wrapper .nav-link').filter(function (idx, elem) {
        return $(elem).text().trim().toLowerCase().indexOf(search) >= 0 ? elem : null;
    }).sort();



    // search not found
    if (match.length == 0) {
        $(search_result_pane).append('<li class="text-muted pl-5">No search result found.</li>');
        return;
    }

    // search found
    match.each(function (idx, elem) {
      var parent = $(elem).parents('.sidebar-menu-item.sidebar-dropdown').find('.menu-title').first().text();
      if (!parent) {
        parent = `Main Menu`
      }
      parent = `<small class="d-block">${parent}</small>`
      var item_url = $(elem).attr('href') || $(elem).data('default-url');
      var item_text = $(elem).text().replace(/(\d+)/g, '').trim();
      $(search_result_pane).append(`
        <li>
          ${parent}
          <a href="${item_url}" class="fw-bold text-color--3 d-block">${item_text}</a>
        </li>
      `);
    });

});


  $(function () {
    $('[data-bs-toggle="tooltip"]').tooltip()
  })

  // responsive sidebar expand js 
  $('.res-sidebar-open-btn').on('click', function (){
    $('.sidebar').addClass('open');
  }); 

  $('.res-sidebar-close-btn').on('click', function (){
    $('.sidebar').removeClass('open');
  }); 

/* Get the documentElement (<html>) to display the page in fullscreen */
let elem = document.documentElement;

$('.sidebar-dropdown > a').on('click', function () {
  if ($(this).parent().find('.sidebar-submenu').length) {
    if ($(this).parent().find('.sidebar-submenu').first().is(':visible')) {
      $(this).find('.side-menu__sub-icon').removeClass('transform rotate-180');
      $(this).removeClass('side-menu--open');
      $(this).parent().find('.sidebar-submenu').first().slideUp({
        done: function done() {
          $(this).removeClass('sidebar-submenu__open');
        }
      });
    } else {
      $(this).find('.side-menu__sub-icon').addClass('transform rotate-180');
      $(this).addClass('side-menu--open');
      $(this).parent().find('.sidebar-submenu').first().slideDown({
        done: function done() {
          $(this).addClass('sidebar-submenu__open');
        }
      });
    }
  }
});

// select-2 init
$('.select2-basic').select2();
$('.select2-multi-select').select2();
$(".select2-auto-tokenize").select2({
  tags: true,
  tokenSeparators: [',']
});


function proPicURL(input) {
  if (input.files && input.files[0]) {
    
    var reader = new FileReader();
    reader.onload = function (e) {
      var preview = $(input).closest('.image-upload-wrapper').find('.image-upload-preview');
      $(preview).css('background-image', 'url(' + e.target.result + ')');
      $(preview).addClass('has-image');
      $(preview).hide();
      $(preview).fadeIn(650);
    }
    reader.readAsDataURL(input.files[0]);
  }
}
$(".image-upload-input").on('change', function () {
  proPicURL(this);
});
$(".remove-image").on('click', function () {
  $(this).parents(".image-upload-preview").css('background-image', 'none');
  $(this).parents(".image-upload-preview").removeClass('has-image');
  $(this).parents(".image-upload-wrapper").find('input[type=file]').val('');
});
$("form").on("change", ".file-upload-field", function () {
  $(this).parent(".file-upload-wrapper").attr("data-text", $(this).val().replace(/.*(\/|\\)/, ''));
});



var inputElements = $('input,select,textarea');

$.each(inputElements, function (index, element) {
    element = $(element);
    if (!element.hasClass('profilePicUpload') && (!element.attr('id')) && element.attr('type') != 'hidden') {
      element.closest('.form-group').find('label').attr('for',element.attr('name'));
      element.attr('id',element.attr('name'))
    }
});



var tooltipTriggerList = [].slice.call(document.querySelectorAll('[title], [data-title], [data-bs-title]'))
tooltipTriggerList.map(function (tooltipTriggerEl) {
  return new bootstrap.Tooltip(tooltipTriggerEl)
});

$.each($('input, select, textarea'), function (i, element) {
  
  if (element.hasAttribute('required')) {
    $(element).closest('.form-group').find('label').first().addClass('required');
  }

});


//Custom Data Table
$('.custom-data-table').closest('.card').find('.card-body').attr('style','padding-top:0px');
var tr_elements = $('.custom-data-table tbody tr');
$(document).on('input','input[name=search_table]',function(){
  var search = $(this).val().toUpperCase();
  var match = tr_elements.filter(function (idx, elem) {
    return $(elem).text().trim().toUpperCase().indexOf(search) >= 0 ? elem : null;
  }).sort();
  var table_content = $('.custom-data-table tbody');
  if (match.length == 0) {
    table_content.html('<tr><td colspan="100%" class="text-center">Data Not Found</td></tr>');
  }else{
    table_content.html(match);
  }
});

$('.pagination').closest('nav').addClass('d-flex justify-content-end');

$('.showFilterBtn').on('click',function(){
  $('.responsive-filter-card').slideToggle();
});

$(document).on('click','.short-codes',function () {
  var text = $(this).text();
  var vInput = document.createElement("input");
  vInput.value = text;
  document.body.appendChild(vInput);
  vInput.select();
  document.execCommand("copy");
  document.body.removeChild(vInput);
  $(this).addClass('copied');
  setTimeout(() => {
      $(this).removeClass('copied');
  }, 1000);
});

Array.from(document.querySelectorAll('table')).forEach(table => {
  let heading = table.querySelectorAll('thead tr th');
  Array.from(table.querySelectorAll('tbody tr')).forEach((row) => {
      Array.from(row.querySelectorAll('td')).forEach((colum, i) => {
          colum.setAttribute('data-label', heading[i].innerText)
      });
  });
});

var len = 0;
var clickLink = 0;
var search = null;
var process = false;
$('#searchInput').on('keydown', function(e){
  var length = $('.search-list li').length;
  if(search != $(this).val() && process){
      len = 0;
      clickLink = 0;
      $(`.search-list li:eq(${len}) a`).focus();
      $(`#searchInput`).focus();
  }
  //Down
  if(e.keyCode == 40 && length){
      process = true;
      var contra = false;
      if(len < clickLink && clickLink < length){
          len += 2;
      }
      $(`.search-list li[class="bg--dark"]`).removeClass('bg--dark');
      $(`.search-list li a[class="text--white"]`).removeClass('text--white');
      $(`.search-list li:eq(${len}) a`).focus().addClass('text--white');
      $(`.search-list li:eq(${len})`).addClass('bg--dark');
      $(`#searchInput`).focus();
      clickLink = len;
      if(!$(`.search-list li:eq(${clickLink}) a`).length){
          $(`.search-list li:eq(${len})`).addClass('text--white');
      }
      len += 1;
      if(length == Math.abs(clickLink)){
          len = 0;
      }
  }
  //Up
  else if(e.keyCode == 38 && length){
      process = true;
      if(len > clickLink && len != 0){
          len -= 2;
      }
      $(`.search-list li[class="bg--dark"]`).removeClass('bg--dark');
      $(`.search-list li a[class="text--white"]`).removeClass('text--white');
      $(`.search-list li:eq(${len}) a`).focus().addClass('text--white');
      $(`.search-list li:eq(${len})`).addClass('bg--dark');
      $(`#searchInput`).focus();
      clickLink = len;
      if(!$(`.search-list li:eq(${clickLink}) a`).length){
          $(`.search-list li:eq(${len})`).addClass('text--white');
      }
      len -= 1;
      if(length == Math.abs(clickLink)){
          len = 0;
      }
  }
  //Enter
  else if(e.keyCode == 13){
      e.preventDefault();
      if($(`.search-list li:eq(${clickLink}) a`).length && process){
          $(`.search-list li:eq(${clickLink}) a`)[0].click();
      }
  }
  //Retry
  else if(e.keyCode == 8){
      len = 0;
      clickLink = 0;
      $(`.search-list li:eq(${len}) a`).focus();
      $(`#searchInput`).focus();
  }
  search = $(this).val();
});

// ============================================================================
// EMAIL TEMPLATE EDITOR FUNCTIONALITY
// ============================================================================

// ============================================================================
// GLOBAL COPYTOCLIPBOARD FUNCTION - ACCESSIBLE EVERYWHERE
// ============================================================================

// Global copyToClipboard function - accessible from anywhere
window.copyToClipboard = function(text) {
    console.log('copyToClipboard called with:', text);

    if (navigator.clipboard && window.isSecureContext) {
        // Use modern clipboard API
        navigator.clipboard.writeText(text).then(function() {
            showCopySuccess(text);
        }).catch(function(err) {
            console.error('Clipboard API failed:', err);
            fallbackCopyToClipboard(text);
        });
    } else {
        // Fallback for older browsers or non-secure contexts
        fallbackCopyToClipboard(text);
    }
};

// Fallback copy method
function fallbackCopyToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        document.execCommand('copy');
        showCopySuccess(text);
    } catch (err) {
        console.error('Failed to copy text: ', err);
        alert('Failed to copy shortcode. Please copy manually: ' + text);
    }

    document.body.removeChild(textArea);
}

// Show success message
function showCopySuccess(text) {
    if (typeof toastr !== 'undefined') {
        toastr.success('Shortcode copied to clipboard: ' + text);
    } else if (typeof Swal !== 'undefined') {
        Swal.fire({
            icon: 'success',
            title: 'Copied!',
            text: 'Shortcode copied to clipboard: ' + text,
            timer: 2000,
            showConfirmButton: false
        });
    } else {
        // Simple alert fallback
        alert('Shortcode copied to clipboard: ' + text);
    }
}

$(document).ready(function() {
    console.log('Document ready - initializing email template editors...');

    // Initialize email template editor for edit pages
    if ($('.email-builder-interface').length > 0 || $('.visual-editor-panel').length > 0) {
        console.log('Initializing email template editor...');
        initEmailTemplateEditor();
    }

    // Initialize global template editor
    if ($('.global-template-editor-container').length > 0) {
        console.log('Initializing global template editor...');
        initGlobalTemplateEditor();
    }

    // Initialize modal template editor for templates list page
    if ($('#addTemplateModal').length > 0) {
        console.log('Initializing modal template editor...');
        initModalTemplateEditor();
    }

    console.log('All email template editors initialized');
});

// Copy shortcode functionality - CENTRALIZED IN APP.JS
function initShortcodeCopyFunctionality() {
    // Global function for copying shortcodes
    window.copyToClipboard = function(text) {
        if (navigator.clipboard && window.isSecureContext) {
            // Use modern clipboard API
            navigator.clipboard.writeText(text).then(function() {
                showCopySuccess(text);
            }).catch(function(err) {
                fallbackCopyToClipboard(text);
            });
        } else {
            // Fallback for older browsers or non-secure contexts
            fallbackCopyToClipboard(text);
        }
    };

    // Fallback copy method
    function fallbackCopyToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            document.execCommand('copy');
            showCopySuccess(text);
        } catch (err) {
            console.error('Failed to copy text: ', err);
            alert('Failed to copy shortcode. Please copy manually: ' + text);
        }

        document.body.removeChild(textArea);
    }

    // Show success message
    function showCopySuccess(text) {
        if (typeof toastr !== 'undefined') {
            toastr.success('Shortcode copied to clipboard: ' + text);
        } else if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'success',
                title: 'Copied!',
                text: 'Shortcode copied to clipboard: ' + text,
                timer: 2000,
                showConfirmButton: false
            });
        } else {
            // Simple alert fallback
            alert('Shortcode copied to clipboard: ' + text);
        }
    }
}

function initEmailTemplateEditor() {
    console.log('Initializing Email Template Editor...');

    let currentMode = 'visual'; // 'visual' or 'html'
    let visualEditorInitialized = false;
    let visualEditor = null;

    // Get DOM elements
    const visualBtn = $('#visual-editor-btn');
    const htmlBtn = $('#html-editor-btn');
    const visualPanel = $('#visual-email-builder');
    const htmlPanel = $('#html-editor-panel');
    const container = document.getElementById('email-builder-container');

    console.log('DOM Elements found:', {
        visualBtn: visualBtn.length,
        htmlBtn: htmlBtn.length,
        visualPanel: visualPanel.length,
        htmlPanel: htmlPanel.length,
        container: container ? 'found' : 'not found'
    });

    // Initialize the professional visual editor
    function initVisualEditor() {
        if (visualEditorInitialized) {
            console.log('Visual editor already initialized');
            return;
        }

        if (!container) {
            console.error('Email builder container not found - cannot initialize visual editor');
            return;
        }

        console.log('Initializing visual editor...');

        // Initialize the advanced visual email editor if available
        if (typeof VisualEmailEditor !== 'undefined') {
            console.log('Advanced VisualEmailEditor found, initializing...');
            visualEditor = new VisualEmailEditor(container, {
                primaryColor: '#dc3545',
                secondaryColor: '#c82333',
                fontFamily: 'Arial, sans-serif'
            });
            visualEditorInitialized = true;
        } else {
            console.log('Advanced VisualEmailEditor not found, using basic editor...');
            // Fallback to basic editor functionality
            initBasicVisualEditor();
        }
    }

    // Fallback basic visual editor
    function initBasicVisualEditor() {
        if (!container) {
            console.error('Container not found for basic visual editor');
            return;
        }

        console.log('Initializing basic visual editor...');

        // Add basic component functionality
        $('.component-btn').off('click').on('click', function(e) {
            e.preventDefault();
            const componentType = $(this).data('component');
            console.log('Component button clicked:', componentType);
            addBasicComponent(componentType);
        });

        // Preview functionality
        $('#preview-email').off('click').on('click', function(e) {
            e.preventDefault();
            console.log('Preview button clicked');
            previewEmail();
        });

        // Reset functionality
        $('#reset-template').off('click').on('click', function(e) {
            e.preventDefault();
            console.log('Reset button clicked');
            if (confirm('Are you sure you want to reset the template?')) {
                resetTemplate();
            }
        });

        // Desktop/Mobile view toggle
        $('#desktop-view').off('click').on('click', function(e) {
            e.preventDefault();
            console.log('Desktop view clicked');
            $(this).addClass('active');
            $('#mobile-view').removeClass('active');
            $('.email-preview-container').removeClass('mobile-view');
        });

        $('#mobile-view').off('click').on('click', function(e) {
            e.preventDefault();
            console.log('Mobile view clicked');
            $(this).addClass('active');
            $('#desktop-view').removeClass('active');
            $('.email-preview-container').addClass('mobile-view');
        });

        visualEditorInitialized = true;
        console.log('Basic visual editor initialized successfully');
    }

    // Add basic component
    function addBasicComponent(type) {
        const placeholder = container.querySelector('.canvas-placeholder');
        if (placeholder) {
            placeholder.style.display = 'none';
        }

        let componentHTML = '';
        switch(type) {
            case 'header':
                componentHTML = '<div class="email-component" style="padding: 20px; text-align: center; background: #f8f9fa; border-bottom: 2px solid #dc3545;"><h2 style="color: #dc3545; margin: 0;">Your Header Here</h2></div>';
                break;
            case 'text':
                componentHTML = '<div class="email-component" style="padding: 20px;"><p style="color: #333; line-height: 1.6; margin: 0;">Your text content here. You can edit this in HTML mode.</p></div>';
                break;
            case 'button':
                componentHTML = '<div class="email-component" style="padding: 20px; text-align: center;"><a href="#" style="display: inline-block; background: #dc3545; color: #fff; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600;">Click Here</a></div>';
                break;
            case 'image':
                componentHTML = '<div class="email-component" style="padding: 20px; text-align: center;"><img src="https://via.placeholder.com/400x200/dc3545/ffffff?text=Your+Image" alt="Image" style="max-width: 100%; height: auto; border-radius: 6px;"></div>';
                break;
            case 'footer':
                componentHTML = '<div class="email-component" style="padding: 20px; text-align: center; background: #f8f9fa; border-top: 2px solid #dc3545; color: #6c757d; font-size: 14px;"><p style="margin: 0;">© 2025 MBFX. All rights reserved.</p></div>';
                break;
        }

        container.insertAdjacentHTML('beforeend', componentHTML);
        updateFormData();
    }

    // Preview email
    function previewEmail() {
        const htmlContent = generateEmailHTML();
        const previewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');

        const fullHTML = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Preview</title>
    <style>
        body { margin: 0; padding: 20px; background: #f5f5f5; font-family: Arial, sans-serif; }
        .email-container { max-width: 600px; margin: 0 auto; background: #ffffff; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <div class="email-container">
        ${htmlContent}
    </div>
</body>
</html>`;

        previewWindow.document.write(fullHTML);
        previewWindow.document.close();
    }

    // Reset template
    function resetTemplate() {
        container.innerHTML = `
            <div class="canvas-placeholder" style="display: flex; align-items: center; justify-content: center; height: 100%; min-height: 450px; text-align: center; color: #6c757d; background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); border: 3px dashed #dee2e6; border-radius: 8px;">
                <div>
                    <i class="las la-plus-circle" style="font-size: 48px; color: #dc3545; margin-bottom: 15px;"></i>
                    <h5 style="color: #333; margin-bottom: 10px;">Start Building Your Email</h5>
                    <p style="margin: 0;">Click components from the left panel to start building your professional email template</p>
                </div>
            </div>
        `;
        updateFormData();
    }

    // Generate email HTML
    function generateEmailHTML() {
        const placeholder = container.querySelector('.canvas-placeholder');
        if (placeholder && placeholder.style.display !== 'none') {
            return '<p>No content added yet. Please add some components to your email.</p>';
        }

        const components = container.querySelectorAll('.email-component');
        let html = '';

        components.forEach(component => {
            html += component.outerHTML;
        });

        return html || '<p>No content available.</p>';
    }

    // Update form data
    function updateFormData() {
        const htmlContent = generateEmailHTML();
        $('textarea[name="email_body"]').val(htmlContent);
        $('#email_body_final').val(htmlContent);
    }

    // Editor mode switching
    if (visualBtn.length && htmlBtn.length) {
        console.log('Setting up editor mode switching...');

        visualBtn.on('click', function(e) {
            e.preventDefault();
            console.log('Visual editor button clicked');

            if (currentMode === 'visual') {
                console.log('Already in visual mode');
                return;
            }

            currentMode = 'visual';

            // Update button states
            visualBtn.addClass('active').removeClass('btn-outline--primary').addClass('btn--primary');
            htmlBtn.removeClass('active').removeClass('btn--primary').addClass('btn-outline--secondary');

            // Show/hide panels
            visualPanel.show();
            htmlPanel.hide();

            console.log('Switched to visual mode');

            // Sync content from HTML to visual
            const htmlContent = $('textarea[name="email_body"]').val();
            if (htmlContent && htmlContent.trim() && container) {
                container.innerHTML = htmlContent;
                console.log('Synced HTML content to visual editor');
            }
        });

        htmlBtn.on('click', function(e) {
            e.preventDefault();
            console.log('HTML editor button clicked');

            if (currentMode === 'html') {
                console.log('Already in HTML mode');
                return;
            }

            currentMode = 'html';

            // Update button states
            htmlBtn.addClass('active').removeClass('btn-outline--secondary').addClass('btn--primary');
            visualBtn.removeClass('active').removeClass('btn--primary').addClass('btn-outline--primary');

            // Show/hide panels
            htmlPanel.show();
            visualPanel.hide();

            console.log('Switched to HTML mode');

            // Sync content from visual to HTML
            updateFormData();
        });
    } else {
        console.error('Editor toggle buttons not found');
    }

    // Initialize the editor
    console.log('Initializing editor in mode:', currentMode);
    if (currentMode === 'visual') {
        initVisualEditor();
    }

    // Sync textarea changes
    $('textarea[name="email_body"]').on('input', function() {
        const content = $(this).val();
        $('#email_body_final').val(content);
        console.log('Textarea content synced to hidden field');
    });

    // Initialize immediately
    console.log('Email template editor initialization complete');
}

        // Create a professional visual editor interface
        const editorHTML = `
            <div class="email-builder-interface">
                <div class="builder-toolbar">
                    <div class="toolbar-section">
                        <h6><i class="las la-palette"></i> Email Components</h6>
                        <div class="component-buttons">
                            <button type="button" class="component-btn" data-component="text">
                                <i class="las la-font"></i>
                                <span>Text Block</span>
                            </button>
                            <button type="button" class="component-btn" data-component="button">
                                <i class="las la-mouse-pointer"></i>
                                <span>Action Button</span>
                            </button>
                            <button type="button" class="component-btn" data-component="image">
                                <i class="las la-image"></i>
                                <span>Image</span>
                            </button>
                            <button type="button" class="component-btn" data-component="divider">
                                <i class="las la-minus"></i>
                                <span>Divider</span>
                            </button>
                        </div>
                    </div>
                    <div class="toolbar-section">
                        <h6><i class="las la-tools"></i> Actions</h6>
                        <div class="canvas-actions">
                            <button type="button" class="btn btn-sm btn-outline--success" id="preview-email">
                                <i class="las la-eye"></i> Preview
                            </button>
                            <button type="button" class="btn btn-sm btn-outline--info" id="reset-template">
                                <i class="las la-redo"></i> Reset
                            </button>
                        </div>
                    </div>
                </div>
                <div class="builder-canvas">
                    <div class="canvas-header">
                        <h6><i class="las la-eye"></i> Professional Email Preview</h6>
                    </div>
                    <div class="email-canvas" id="email-canvas">
                        <div class="email-preview-container">
                            <iframe id="email-preview-frame" style="width: 100%; height: 100%; border: none; border-radius: 8px;"></iframe>
                        </div>
                    </div>
                </div>
            </div>
        `;

        container.html(editorHTML);

        // Load current template into preview
        updateEmailPreview(currentTemplate);

        // Bind component button events
        bindComponentEvents();

        visualEditorInitialized = true;
    }

    // Bind component button events
    function bindComponentEvents() {
        $('.component-btn').on('click', function() {
            const component = $(this).data('component');
            addComponent(component);
        });

        $('#preview-email').on('click', function() {
            previewEmail();
        });

        $('#reset-template').on('click', function() {
            if (confirm('Are you sure you want to reset the template?')) {
                resetToDefaultTemplate();
            }
        });
    }

    // Add component to email template
    function addComponent(componentType) {
        let componentHTML = '';

        switch(componentType) {
            case 'text':
                componentHTML = `
                    <!-- Professional Text Block Component -->
                    <tr>
                        <td style="padding: 30px;">
                            <div style="background-color: #ffffff; border-radius: 8px; padding: 25px; border-left: 4px solid #dc3545; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
                                <h3 style="color: #333333; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">Custom Text Block</h3>
                                <p style="color: #555555; line-height: 1.6; font-size: 16px; margin: 0 0 15px 0;">
                                    Dear @{{fullname}}, this is a professional text block component. You can customize this content to include any message you want to send to your users.
                                </p>
                                <p style="color: #666666; line-height: 1.6; font-size: 14px; margin: 0; font-style: italic;">
                                    This component maintains professional styling and responsive design.
                                </p>
                            </div>
                        </td>
                    </tr>
                `;
                break;
            case 'button':
                componentHTML = `
                    <!-- Professional Button Component -->
                    <tr>
                        <td style="padding: 30px; text-align: center;">
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="margin: 0 auto;">
                                <tr>
                                    <td style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); border-radius: 8px; box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);">
                                        <a href="#" style="display: inline-block; color: #ffffff; text-decoration: none; padding: 16px 32px; font-weight: 600; font-size: 16px; border-radius: 8px; transition: all 0.3s ease; text-transform: uppercase; letter-spacing: 0.5px;">
                                            Take Action Now
                                        </a>
                                    </td>
                                </tr>
                            </table>
                            <p style="color: #666666; font-size: 12px; margin: 10px 0 0 0; font-style: italic;">Click the button above to proceed</p>
                        </td>
                    </tr>
                `;
                break;
            case 'image':
                componentHTML = `
                    <!-- Professional Image Component -->
                    <tr>
                        <td style="padding: 30px; text-align: center;">
                            <div style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 12px; padding: 25px; border: 2px solid #dee2e6;">
                                <img src="https://i.imgur.com/KxKgSk2.png" alt="Professional Image" style="max-width: 200px; height: auto; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
                                <h4 style="color: #333333; font-size: 16px; margin: 15px 0 5px 0; font-weight: 600;">Professional Image</h4>
                                <p style="color: #6c757d; font-size: 14px; margin: 0; font-style: italic;">High-quality image placeholder with professional styling</p>
                            </div>
                        </td>
                    </tr>
                `;
                break;
            case 'divider':
                componentHTML = `
                    <!-- Professional Divider Component -->
                    <tr>
                        <td style="padding: 25px 30px;">
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                <tr>
                                    <td style="width: 30%; border-top: 2px solid #dc3545;"></td>
                                    <td style="width: 40%; text-align: center; padding: 0 20px;">
                                        <div style="background-color: #dc3545; width: 8px; height: 8px; border-radius: 50%; margin: 0 auto; transform: translateY(-4px);"></div>
                                    </td>
                                    <td style="width: 30%; border-top: 2px solid #dc3545;"></td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                `;
                break;
        }

        // Get current template and insert component
        let currentTemplate = $('textarea[name="email_body"]').val() || getDefaultTemplate();

        // Insert component before the footer section for table-based templates
        if (currentTemplate.includes('<!-- Footer Section -->')) {
            const footerStart = currentTemplate.indexOf('<!-- Footer Section -->');
            currentTemplate = currentTemplate.substring(0, footerStart) +
                            componentHTML + '\n                    ' +
                            currentTemplate.substring(footerStart);
        } else if (currentTemplate.includes('</table>')) {
            // Fallback: insert before last closing table tag
            const lastTableClose = currentTemplate.lastIndexOf('</table>');
            if (lastTableClose !== -1) {
                currentTemplate = currentTemplate.substring(0, lastTableClose) +
                                componentHTML + '\n                ' +
                                currentTemplate.substring(lastTableClose);
            }
        } else {
            // Simple append for non-table templates
            currentTemplate += componentHTML;
        }

        // Update template
        $('textarea[name="email_body"]').val(currentTemplate);
        $('#email_body_final').val(currentTemplate);

        // Update preview
        updateEmailPreview(currentTemplate);

        // Show success message
        showNotification('Professional component added successfully!', 'success');
    }

    // Update email preview
    function updateEmailPreview(htmlContent) {
        const iframe = document.getElementById('email-preview-frame');
        if (!iframe) return;

        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

        // Add responsive meta tags and basic styling
        const fullHTML = '<!DOCTYPE html><html><head>' +
                         '<meta charset="utf-8">' +
                         '<meta name="viewport" content="width=device-width, initial-scale=1.0">' +
                         '<title>Email Preview</title>' +
                         '<style>body { margin: 0; padding: 10px; background: #f5f5f5; font-family: Arial, sans-serif; }' +
                         '.email-container { max-width: 100%; margin: 0 auto; background: #ffffff; }</style>' +
                         '</head><body><div class="email-container">' + htmlContent + '</div></body></html>';

        iframeDoc.open();
        iframeDoc.write(fullHTML);
        iframeDoc.close();
    }

    // Preview email in new window
    function previewEmail() {
        const htmlContent = $('textarea[name="email_body"]').val();
        const previewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');

        const fullHTML = '<!DOCTYPE html><html><head>' +
                         '<meta charset="utf-8">' +
                         '<meta name="viewport" content="width=device-width, initial-scale=1.0">' +
                         '<title>Email Template Preview</title>' +
                         '<style>body { margin: 0; padding: 20px; background: #f5f5f5; font-family: Arial, sans-serif; }' +
                         '.email-container { max-width: 600px; margin: 0 auto; background: #ffffff; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }</style>' +
                         '</head><body><div class="email-container">' + htmlContent + '</div></body></html>';

        previewWindow.document.write(fullHTML);
        previewWindow.document.close();
    }

    // Reset to default template
    function resetToDefaultTemplate() {
        const defaultTemplate = getDefaultTemplate();
        $('textarea[name="email_body"]').val(defaultTemplate);
        $('#email_body_final').val(defaultTemplate);
        updateEmailPreview(defaultTemplate);
        showNotification('Template reset to default successfully!', 'info');
    }

    // Get default email template structure
    function getDefaultTemplate() {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@{{site_name}} - Notification</title>
    <style>
        @media only screen and (max-width: 600px) {
            .container { width: 100% !important; }
            .header-logo { max-width: 150px !important; }
            .content-padding { padding: 20px !important; }
            .footer-text { font-size: 12px !important; }
        }
    </style>
</head>
<body style="margin: 0; padding: 0; background-color: #f4f4f4; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
    <!-- Full-width container -->
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #f4f4f4;">
        <tr>
            <td align="center" style="padding: 20px 0;">
                <!-- Main email container -->
                <table class="container" role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" style="background-color: #ffffff; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">

                    <!-- Header Section -->
                    <tr>
                        <td style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); padding: 30px; text-align: center; border-radius: 8px 8px 0 0;">
                            <img src="https://i.imgur.com/KxKgSk2.png" alt="@{{site_name}}" class="header-logo" style="max-width: 180px; height: auto; margin-bottom: 15px;">
                            <h1 style="color: #ffffff; margin: 0; font-size: 24px; font-weight: 600; text-shadow: 0 1px 2px rgba(0,0,0,0.1);">@{{site_name}}</h1>
                            <p style="color: #ffffff; margin: 8px 0 0 0; font-size: 14px; opacity: 0.9;">Professional Trading Platform</p>
                        </td>
                    </tr>

                    <!-- Content Section -->
                    <tr>
                        <td class="content-padding" style="padding: 40px 30px;">
                            <h2 style="color: #333333; margin: 0 0 20px 0; font-size: 20px; font-weight: 600;">Dear @{{fullname}},</h2>
                            <div style="color: #555555; line-height: 1.6; font-size: 16px; margin-bottom: 30px;">
                                @{{message}}
                            </div>
                            <div style="background-color: #f8f9fa; border-left: 4px solid #dc3545; padding: 20px; margin: 20px 0; border-radius: 0 4px 4px 0;">
                                <p style="margin: 0; color: #666666; font-size: 14px; font-style: italic;">
                                    This is an automated notification from @{{site_name}}. Please do not reply to this email.
                                </p>
                            </div>
                        </td>
                    </tr>

                    <!-- Footer Section -->
                    <tr>
                        <td style="background-color: #2c3e50; padding: 25px 30px; text-align: center; border-radius: 0 0 8px 8px;">
                            <p class="footer-text" style="color: #ecf0f1; margin: 0 0 10px 0; font-size: 14px;">
                                Best regards,<br>
                                <strong>@{{site_name}} Team</strong>
                            </p>
                            <p class="footer-text" style="color: #95a5a6; margin: 0; font-size: 12px;">
                                © 2025 @{{site_name}}. All rights reserved.
                            </p>
                        </td>
                    </tr>

                </table>
            </td>
        </tr>
    </table>
</body>
</html>`;
    }

    // Show notification
    function showNotification(message, type = 'success') {
        const alertClass = type === 'success' ? 'alert-success' : type === 'error' ? 'alert-danger' : 'alert-info';
        const notification = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert" ' +
                            'style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">' +
                            message + '<button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>';

        $('body').append(notification);

        // Auto-dismiss after 3 seconds
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 3000);
    }

    // Switch to visual editor
    function switchToVisualEditor() {
        currentMode = 'visual';
        $('#visual-editor-btn').addClass('active').removeClass('btn-outline--primary').addClass('btn--primary');
        $('#html-editor-btn').removeClass('active').removeClass('btn--primary').addClass('btn-outline--secondary');

        $('#visual-email-builder').show();
        $('#html-editor-panel').hide();

        // Initialize visual editor if not already done
        if (!visualEditorInitialized) {
            initVisualEditor();
        }
    }

    // Switch to HTML editor
    function switchToHtmlEditor() {
        currentMode = 'html';
        $('#html-editor-btn').addClass('active').removeClass('btn-outline--secondary').addClass('btn--primary');
        $('#visual-editor-btn').removeClass('active').removeClass('btn--primary').addClass('btn-outline--primary');

        $('#html-editor-panel').show();
        $('#visual-email-builder').hide();
    }

    // Event handlers
    $('#visual-editor-btn').on('click', function(e) {
        e.preventDefault();
        switchToVisualEditor();
    });

    $('#html-editor-btn').on('click', function(e) {
        e.preventDefault();
        switchToHtmlEditor();
    });

    // Form submission handler
    $('form').on('submit', function(e) {
        // Always use the content from the textarea as the final template
        const html = $('textarea[name="email_body"]').val();
        $('#email_body_final').val(html);
    });

    // Initialize with visual editor by default
    switchToVisualEditor();

    // Add shortcode helper
    addShortcodeHelper();

    // Add shortcode helper panel
    function addShortcodeHelper() {
        // Get shortcodes from server-side data using the safe accessor
        var shortcodes = ['fullname', 'username', 'message', 'site_name'];

        // Create shortcode helper HTML
        var shortcodeHelper = document.createElement('div');
        shortcodeHelper.className = 'shortcode-helper';

        var headerHTML = '<h6><i class="las la-tags"></i> Available Shortcodes (Click to copy)</h6>';
        var tagsHTML = '<div class="shortcode-tags">';

        // Add shortcode tags
        shortcodes.forEach(function(shortcode) {
            var shortcodeText = '{{' + shortcode + '}}';
            tagsHTML += '<span class="shortcode-tag" data-shortcode="' + shortcodeText + '">' + shortcodeText + '</span>';
        });

        tagsHTML += '</div>';
        var footerHTML = '<small class="text-muted">Click any shortcode to copy it to clipboard</small>';

        shortcodeHelper.innerHTML = headerHTML + tagsHTML + footerHTML;

        // Prepend to container
        var container = document.querySelector('.email-template-editor-container');
        if (container) {
            container.insertBefore(shortcodeHelper, container.firstChild);
        }

        // Handle shortcode clicks
        $(document).on('click', '.shortcode-tag', function() {
            const shortcode = $(this).data('shortcode');

            // Copy to clipboard
            if (navigator.clipboard) {
                navigator.clipboard.writeText(shortcode).then(function() {
                    showNotification('Shortcode copied to clipboard!', 'success');
                });
            } else {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = shortcode;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showNotification('Shortcode copied to clipboard!', 'success');
            }
        });
    }
}

// ============================================================================
// GLOBAL TEMPLATE EDITOR FUNCTIONALITY
// ============================================================================

function initGlobalTemplateEditor() {
    let currentMode = 'visual'; // 'visual' or 'html'
    let visualEditorInitialized = false;

    // Initialize the visual editor for global template
    function initVisualEditor() {
        if (visualEditorInitialized) {
            return;
        }

        const container = $('#email-builder-container');
        const currentTemplate = $('textarea[name="email_template"]').val() || getDefaultTemplate();

        // Create a professional global template visual editor interface
        const editorHTML = `
            <div class="email-builder-interface">
                <div class="builder-toolbar">
                    <div class="toolbar-section">
                        <h6><i class="las la-palette"></i> Global Components</h6>
                        <div class="component-buttons">
                            <button type="button" class="component-btn" data-component="header">
                                <i class="las la-heading"></i>
                                <span>Header</span>
                            </button>
                            <button type="button" class="component-btn" data-component="text">
                                <i class="las la-font"></i>
                                <span>Text Block</span>
                            </button>
                            <button type="button" class="component-btn" data-component="button">
                                <i class="las la-mouse-pointer"></i>
                                <span>Action Button</span>
                            </button>
                            <button type="button" class="component-btn" data-component="image">
                                <i class="las la-image"></i>
                                <span>Image</span>
                            </button>
                            <button type="button" class="component-btn" data-component="footer">
                                <i class="las la-align-center"></i>
                                <span>Footer</span>
                            </button>
                        </div>
                    </div>
                    <div class="toolbar-section">
                        <h6><i class="las la-tools"></i> Global Actions</h6>
                        <div class="canvas-actions">
                            <button type="button" class="btn btn-sm btn-outline--success" id="preview-email">
                                <i class="las la-eye"></i> Preview
                            </button>
                            <button type="button" class="btn btn-sm btn-outline--info" id="reset-template">
                                <i class="las la-redo"></i> Reset
                            </button>
                        </div>
                    </div>
                </div>
                <div class="builder-canvas">
                    <div class="canvas-header">
                        <h6><i class="las la-globe"></i> Global Email Template Preview</h6>
                    </div>
                    <div class="email-canvas" id="email-canvas">
                        <div class="email-preview-container">
                            <iframe id="email-preview-frame" style="width: 100%; height: 100%; border: none; border-radius: 8px;"></iframe>
                        </div>
                    </div>
                </div>
            </div>
        `;

        container.html(editorHTML);

        // Load current template into preview
        updateEmailPreview(currentTemplate);

        // Bind component button events
        bindComponentEvents();

        visualEditorInitialized = true;
    }

    // Bind component button events
    function bindComponentEvents() {
        $('.component-btn').on('click', function() {
            const component = $(this).data('component');
            addComponent(component);
        });

        $('#preview-email').on('click', function() {
            previewEmail();
        });

        $('#reset-template').on('click', function() {
            if (confirm('Are you sure you want to reset the template? This will restore the default template.')) {
                resetToDefaultTemplate();
            }
        });
    }

    // Add component to email template
    function addComponent(componentType) {
        let componentHTML = '';

        switch(componentType) {
            case 'header':
                componentHTML = `
                    <!-- Professional Header Component -->
                    <tr>
                        <td style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); padding: 25px; text-align: center; border-radius: 8px 8px 0 0;">
                            <h2 style="color: #ffffff; margin: 0; font-size: 22px; font-weight: 600; text-shadow: 0 1px 2px rgba(0,0,0,0.1);">@{{site_name}}</h2>
                            <p style="color: #ffffff; margin: 8px 0 0 0; font-size: 14px; opacity: 0.9;">Professional Global Notification</p>
                        </td>
                    </tr>
                `;
                break;
            case 'text':
                componentHTML = `
                    <!-- Professional Text Component -->
                    <tr>
                        <td style="padding: 30px;">
                            <div style="background-color: #f8f9fa; border-radius: 8px; padding: 25px; border-left: 4px solid #dc3545;">
                                <h3 style="color: #333333; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">Global Announcement</h3>
                                <p style="color: #555555; line-height: 1.6; font-size: 16px; margin: 0 0 15px 0;">
                                    Dear @{{fullname}}, this is an important global notification that has been sent to all users of @{{site_name}}.
                                </p>
                                <p style="color: #666666; line-height: 1.6; font-size: 14px; margin: 0;">
                                    @{{message}}
                                </p>
                            </div>
                        </td>
                    </tr>
                `;
                break;
            case 'button':
                componentHTML = `
                    <!-- Professional Action Button -->
                    <tr>
                        <td style="padding: 30px; text-align: center;">
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="margin: 0 auto;">
                                <tr>
                                    <td style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); border-radius: 8px; box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);">
                                        <a href="#" style="display: inline-block; color: #ffffff; text-decoration: none; padding: 16px 32px; font-weight: 600; font-size: 16px; border-radius: 8px; text-transform: uppercase; letter-spacing: 0.5px;">
                                            View Details
                                        </a>
                                    </td>
                                </tr>
                            </table>
                            <p style="color: #666666; font-size: 12px; margin: 10px 0 0 0; font-style: italic;">Click to access your account</p>
                        </td>
                    </tr>
                `;
                break;
            case 'image':
                componentHTML = `
                    <!-- Professional Image Component -->
                    <tr>
                        <td style="padding: 30px; text-align: center;">
                            <div style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 12px; padding: 25px;">
                                <img src="https://i.imgur.com/KxKgSk2.png" alt="@{{site_name}} Logo" style="max-width: 150px; height: auto; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
                                <h4 style="color: #333333; font-size: 16px; margin: 15px 0 5px 0; font-weight: 600;">@{{site_name}}</h4>
                                <p style="color: #6c757d; font-size: 14px; margin: 0; font-style: italic;">Professional Trading Platform</p>
                            </div>
                        </td>
                    </tr>
                `;
                break;
            case 'footer':
                componentHTML = `
                    <!-- Professional Footer Component -->
                    <tr>
                        <td style="background-color: #2c3e50; padding: 25px 30px; text-align: center; border-radius: 0 0 8px 8px;">
                            <p style="color: #ecf0f1; margin: 0 0 10px 0; font-size: 14px;">
                                Best regards,<br>
                                <strong>@{{site_name}} Team</strong>
                            </p>
                            <p style="color: #95a5a6; margin: 0; font-size: 12px;">
                                © 2025 @{{site_name}}. All rights reserved.
                            </p>
                        </td>
                    </tr>
                `;
                break;
        }

        // Get current template and insert component
        let currentTemplate = $('textarea[name="email_template"]').val() || getDefaultTemplate();

        // Insert component before the footer section for table-based templates
        if (currentTemplate.includes('<!-- Footer Section -->')) {
            const footerStart = currentTemplate.indexOf('<!-- Footer Section -->');
            currentTemplate = currentTemplate.substring(0, footerStart) +
                            componentHTML + '\n                    ' +
                            currentTemplate.substring(footerStart);
        } else if (currentTemplate.includes('</table>')) {
            // Fallback: insert before last closing table tag
            const lastTableClose = currentTemplate.lastIndexOf('</table>');
            if (lastTableClose !== -1) {
                currentTemplate = currentTemplate.substring(0, lastTableClose) +
                                componentHTML + '\n                ' +
                                currentTemplate.substring(lastTableClose);
            }
        } else {
            // Simple append for non-table templates
            currentTemplate += componentHTML;
        }

        // Update template
        $('textarea[name="email_template"]').val(currentTemplate);
        $('#email_template_final').val(currentTemplate);

        // Update preview
        updateEmailPreview(currentTemplate);

        // Show success message
        showGlobalNotification('Professional component added successfully!', 'success');
    }

    // Update email preview
    function updateEmailPreview(htmlContent) {
        const iframe = document.getElementById('email-preview-frame');
        if (!iframe) return;

        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

        // Add responsive meta tags and basic styling
        const fullHTML = '<!DOCTYPE html><html><head>' +
                         '<meta charset="utf-8">' +
                         '<meta name="viewport" content="width=device-width, initial-scale=1.0">' +
                         '<title>Email Preview</title>' +
                         '<style>body { margin: 0; padding: 10px; background: #f5f5f5; font-family: Arial, sans-serif; }' +
                         '.email-container { max-width: 100%; margin: 0 auto; background: #ffffff; }</style>' +
                         '</head><body><div class="email-container">' + htmlContent + '</div></body></html>';

        iframeDoc.open();
        iframeDoc.write(fullHTML);
        iframeDoc.close();
    }

    // Preview email in new window
    function previewEmail() {
        const htmlContent = $('textarea[name="email_template"]').val();
        const previewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');

        const fullHTML = '<!DOCTYPE html><html><head>' +
                         '<meta charset="utf-8">' +
                         '<meta name="viewport" content="width=device-width, initial-scale=1.0">' +
                         '<title>Global Email Template Preview</title>' +
                         '<style>body { margin: 0; padding: 20px; background: #f5f5f5; font-family: Arial, sans-serif; }' +
                         '.email-container { max-width: 600px; margin: 0 auto; background: #ffffff; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }</style>' +
                         '</head><body><div class="email-container">' + htmlContent + '</div></body></html>';

        previewWindow.document.write(fullHTML);
        previewWindow.document.close();
    }

    // Reset to default template
    function resetToDefaultTemplate() {
        const defaultTemplate = getDefaultTemplate();
        $('textarea[name="email_template"]').val(defaultTemplate);
        $('#email_template_final').val(defaultTemplate);
        updateEmailPreview(defaultTemplate);
        showGlobalNotification('Template reset to default successfully!', 'info');
    }

    // Get default email template structure
    function getDefaultTemplate() {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@{{site_name}} - Global Notification</title>
    <style>
        @media only screen and (max-width: 600px) {
            .container { width: 100% !important; }
            .header-logo { max-width: 120px !important; }
            .content-padding { padding: 20px !important; }
            .footer-text { font-size: 12px !important; }
        }
    </style>
</head>
<body style="margin: 0; padding: 0; background-color: #f4f4f4; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
    <!-- Full-width container -->
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #f4f4f4;">
        <tr>
            <td align="center" style="padding: 20px 0;">
                <!-- Main email container -->
                <table class="container" role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" style="background-color: #ffffff; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">

                    <!-- Header Section -->
                    <tr>
                        <td style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); padding: 30px; text-align: center; border-radius: 8px 8px 0 0;">
                            <img src="https://i.imgur.com/KxKgSk2.png" alt="@{{site_name}}" class="header-logo" style="max-width: 150px; height: auto; margin-bottom: 15px;">
                            <h1 style="color: #ffffff; margin: 0; font-size: 24px; font-weight: 600; text-shadow: 0 1px 2px rgba(0,0,0,0.1);">@{{site_name}}</h1>
                            <p style="color: #ffffff; margin: 8px 0 0 0; font-size: 14px; opacity: 0.9;">Global System Notification</p>
                        </td>
                    </tr>

                    <!-- Content Section -->
                    <tr>
                        <td class="content-padding" style="padding: 40px 30px;">
                            <h2 style="color: #333333; margin: 0 0 20px 0; font-size: 20px; font-weight: 600;">Dear @{{fullname}},</h2>
                            <div style="color: #555555; line-height: 1.6; font-size: 16px; margin-bottom: 30px;">
                                @{{message}}
                            </div>
                            <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-left: 4px solid #f39c12; padding: 20px; margin: 20px 0; border-radius: 0 4px 4px 0;">
                                <p style="margin: 0; color: #856404; font-size: 14px; font-weight: 600;">
                                    <i class="fas fa-info-circle"></i> Important Global Notification
                                </p>
                                <p style="margin: 10px 0 0 0; color: #856404; font-size: 14px;">
                                    This message has been sent to all users of @{{site_name}}.
                                </p>
                            </div>
                        </td>
                    </tr>

                    <!-- Footer Section -->
                    <tr>
                        <td style="background-color: #2c3e50; padding: 25px 30px; text-align: center; border-radius: 0 0 8px 8px;">
                            <p class="footer-text" style="color: #ecf0f1; margin: 0 0 10px 0; font-size: 14px;">
                                Best regards,<br>
                                <strong>@{{site_name}} Team</strong>
                            </p>
                            <p class="footer-text" style="color: #95a5a6; margin: 0; font-size: 12px;">
                                © 2025 @{{site_name}}. All rights reserved.
                            </p>
                        </td>
                    </tr>

                </table>
            </td>
        </tr>
    </table>
</body>
</html>`;
    }

    // Show notification
    function showGlobalNotification(message, type = 'success') {
        const alertClass = type === 'success' ? 'alert-success' : type === 'error' ? 'alert-danger' : 'alert-info';
        const notification = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert" ' +
                            'style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">' +
                            message + '<button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>';

        $('body').append(notification);

        // Auto-dismiss after 3 seconds
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 3000);
    }

    // Switch to visual editor
    function switchToVisualEditor() {
        currentMode = 'visual';
        $('#visual-editor-btn').addClass('active').removeClass('btn-outline--primary').addClass('btn--primary');
        $('#html-editor-btn').removeClass('active').removeClass('btn--primary').addClass('btn-outline--secondary');

        $('#visual-email-builder').show();
        $('#html-editor-panel').hide();

        // Initialize visual editor if not already done
        if (!visualEditorInitialized) {
            initVisualEditor();
        }
    }

    // Switch to HTML editor
    function switchToHtmlEditor() {
        currentMode = 'html';
        $('#html-editor-btn').addClass('active').removeClass('btn-outline--secondary').addClass('btn--primary');
        $('#visual-editor-btn').removeClass('active').removeClass('btn--primary').addClass('btn-outline--primary');

        $('#html-editor-panel').show();
        $('#visual-email-builder').hide();
    }

    // Event handlers
    $('#visual-editor-btn').on('click', function(e) {
        e.preventDefault();
        switchToVisualEditor();
    });

    $('#html-editor-btn').on('click', function(e) {
        e.preventDefault();
        switchToHtmlEditor();
    });

    // Form submission handler
    $('form').on('submit', function(e) {
        // Always use the content from the textarea as the final template
        const html = $('textarea[name="email_template"]').val();
        $('#email_template_final').val(html);
    });

    // Initialize with visual editor by default
    switchToVisualEditor();

    // Add shortcode helper
    addGlobalShortcodeHelper();

    // Add shortcode helper panel for global template
    function addGlobalShortcodeHelper() {
        const shortcodeHelper = '<div class="shortcode-helper">' +
            '<h6><i class="las la-tags"></i> Available Shortcodes (Click to copy)</h6>' +
            '<div class="shortcode-tags">' +
            '<span class="shortcode-tag" data-shortcode="@{{fullname}}">@{{fullname}}</span>' +
            '<span class="shortcode-tag" data-shortcode="@{{username}}">@{{username}}</span>' +
            '<span class="shortcode-tag" data-shortcode="@{{message}}">@{{message}}</span>' +
            '<span class="shortcode-tag" data-shortcode="@{{site_name}}">@{{site_name}}</span>' +
            '</div>' +
            '<small class="text-muted">Click any shortcode to copy it to clipboard</small>' +
            '</div>';

        // Prepend to container
        var container = document.querySelector('.global-template-editor-container');
        if (container) {
            container.insertAdjacentHTML('afterbegin', shortcodeHelper);
        }

        // Handle shortcode clicks
        $(document).on('click', '.shortcode-tag', function() {
            const shortcode = $(this).data('shortcode');

            // Copy to clipboard
            if (navigator.clipboard) {
                navigator.clipboard.writeText(shortcode).then(function() {
                    showGlobalNotification('Shortcode copied to clipboard!', 'success');
                });
            } else {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = shortcode;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showGlobalNotification('Shortcode copied to clipboard!', 'success');
            }
        });
    }
}

// ============================================================================
// MODAL TEMPLATE EDITOR FUNCTIONALITY (for templates list page)
// ============================================================================

function initModalTemplateEditor() {
    let modalCurrentMode = 'visual';
    let modalVisualEditorInitialized = false;

    // Initialize modal visual editor
    function initModalVisualEditor() {
        if (modalVisualEditorInitialized) {
            return;
        }

        const container = $('#modal-email-builder-container');
        const defaultTemplate = getModalDefaultTemplate();

        // Create a simplified visual editor interface for modal
        const editorHTML = `
            <div class="email-builder-interface">
                <div class="builder-toolbar">
                    <div class="toolbar-section">
                        <h6><i class="las la-palette"></i> Components</h6>
                        <div class="component-buttons">
                            <button type="button" class="btn btn-sm btn-outline--primary component-btn" data-component="text">
                                <i class="las la-font"></i> Text Block
                            </button>
                            <button type="button" class="btn btn-sm btn-outline--primary component-btn" data-component="button">
                                <i class="las la-mouse-pointer"></i> Button
                            </button>
                            <button type="button" class="btn btn-sm btn-outline--primary component-btn" data-component="image">
                                <i class="las la-image"></i> Image
                            </button>
                            <button type="button" class="btn btn-sm btn-outline--primary component-btn" data-component="divider">
                                <i class="las la-minus"></i> Divider
                            </button>
                        </div>
                    </div>
                </div>
                <div class="builder-canvas">
                    <div class="canvas-header">
                        <h6><i class="las la-eye"></i> Email Preview</h6>
                        <div class="canvas-actions">
                            <button type="button" class="btn btn-sm btn-outline--success" id="modal-preview-email">
                                <i class="las la-eye"></i> Preview
                            </button>
                        </div>
                    </div>
                    <div class="email-canvas" id="modal-email-canvas">
                        <div class="email-preview-container">
                            <iframe id="modal-email-preview-frame" style="width: 100%; height: 300px; border: 1px solid #ddd; border-radius: 5px;"></iframe>
                        </div>
                    </div>
                </div>
            </div>
        `;

        container.html(editorHTML);

        // Load default template into preview
        updateModalEmailPreview(defaultTemplate);
        $('textarea[name="email_body"]').val(defaultTemplate);

        // Bind component button events
        bindModalComponentEvents();

        modalVisualEditorInitialized = true;
    }

    // Bind modal component events
    function bindModalComponentEvents() {
        $('.component-btn').on('click', function() {
            const component = $(this).data('component');
            addModalComponent(component);
        });

        $('#modal-preview-email').on('click', function() {
            previewModalEmail();
        });
    }

    // Add component to modal email template
    function addModalComponent(componentType) {
        let componentHTML = '';

        switch(componentType) {
            case 'text':
                componentHTML = '<div style="padding: 20px; font-family: Arial, sans-serif;">' +
                               '<p style="margin: 0; color: #333; line-height: 1.6;">Dear @{{fullname}},</p>' +
                               '<div style="margin: 20px 0; color: #333; line-height: 1.6;">Your custom notification message goes here.</div>' +
                               '</div>';
                break;
            case 'button':
                componentHTML = '<div style="padding: 20px; text-align: center;">' +
                               '<a href="#" style="display: inline-block; background: #dc3545; color: #ffffff; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold;">' +
                               'Click Here</a></div>';
                break;
            case 'image':
                componentHTML = '<div style="padding: 20px; text-align: center;">' +
                               '<img src="https://i.imgur.com/KxKgSk2.png" alt="Logo" style="max-width: 300px; height: auto;">' +
                               '</div>';
                break;
            case 'divider':
                componentHTML = '<div style="padding: 10px 20px;">' +
                               '<hr style="border: none; border-top: 1px solid #ddd; margin: 0;">' +
                               '</div>';
                break;
        }

        // Get current template and append component
        let currentTemplate = $('textarea[name="email_body"]').val() || getModalDefaultTemplate();
        currentTemplate += componentHTML;

        // Update template
        $('textarea[name="email_body"]').val(currentTemplate);
        $('#modal_email_body_final').val(currentTemplate);

        // Update preview
        updateModalEmailPreview(currentTemplate);
    }

    // Update modal email preview
    function updateModalEmailPreview(htmlContent) {
        const iframe = document.getElementById('modal-email-preview-frame');
        if (!iframe) return;

        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

        // Add responsive meta tags and basic styling
        const fullHTML = '<!DOCTYPE html><html><head>' +
                         '<meta charset="utf-8">' +
                         '<meta name="viewport" content="width=device-width, initial-scale=1.0">' +
                         '<title>Email Preview</title>' +
                         '<style>body { margin: 0; padding: 10px; background: #f5f5f5; font-family: Arial, sans-serif; }' +
                         '.email-container { max-width: 100%; margin: 0 auto; background: #ffffff; }</style>' +
                         '</head><body><div class="email-container">' + htmlContent + '</div></body></html>';

        iframeDoc.open();
        iframeDoc.write(fullHTML);
        iframeDoc.close();
    }

    // Preview modal email in new window
    function previewModalEmail() {
        const htmlContent = $('textarea[name="email_body"]').val();
        const previewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');

        const fullHTML = '<!DOCTYPE html><html><head>' +
                         '<meta charset="utf-8">' +
                         '<meta name="viewport" content="width=device-width, initial-scale=1.0">' +
                         '<title>New Template Preview</title>' +
                         '<style>body { margin: 0; padding: 20px; background: #f5f5f5; font-family: Arial, sans-serif; }' +
                         '.email-container { max-width: 600px; margin: 0 auto; background: #ffffff; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }</style>' +
                         '</head><body><div class="email-container">' + htmlContent + '</div></body></html>';

        previewWindow.document.write(fullHTML);
        previewWindow.document.close();
    }

    // Get default modal template
    function getModalDefaultTemplate() {
        return '<div style="padding: 20px; font-family: Arial, sans-serif;">' +
               '<h3 style="color: #dc3545; margin: 0 0 20px 0;">Custom Notification</h3>' +
               '<p style="margin: 0; color: #333; line-height: 1.6;">Dear @{{fullname}},</p>' +
               '<div style="margin: 20px 0; color: #333; line-height: 1.6;">Your custom notification message goes here.</div>' +
               '<p style="margin: 20px 0 0 0; color: #666; font-size: 14px;">Best regards,<br>@{{site_name}} Team</p>' +
               '</div>';
    }

    // Modal editor toggle functions
    function switchToModalVisualEditor() {
        modalCurrentMode = 'visual';
        $('#modal-visual-editor-btn').addClass('active').removeClass('btn-outline--primary').addClass('btn--primary');
        $('#modal-html-editor-btn').removeClass('active').removeClass('btn--primary').addClass('btn-outline--secondary');

        $('#modal-visual-email-builder').show();
        $('#modal-html-editor-panel').hide();

        // Initialize visual editor if not already done
        if (!modalVisualEditorInitialized) {
            initModalVisualEditor();
        }
    }

    function switchToModalHtmlEditor() {
        modalCurrentMode = 'html';
        $('#modal-html-editor-btn').addClass('active').removeClass('btn-outline--secondary').addClass('btn--primary');
        $('#modal-visual-editor-btn').removeClass('active').removeClass('btn--primary').addClass('btn-outline--primary');

        $('#modal-html-editor-panel').show();
        $('#modal-visual-email-builder').hide();
    }

    // Modal event handlers
    $('#modal-visual-editor-btn').on('click', function(e) {
        e.preventDefault();
        switchToModalVisualEditor();
    });

    $('#modal-html-editor-btn').on('click', function(e) {
        e.preventDefault();
        switchToModalHtmlEditor();
    });

    // Modal form submission handler
    $('#addTemplateModal form').on('submit', function(e) {
        const html = $('textarea[name="email_body"]').val();
        $('#modal_email_body_final').val(html);
    });

    // Initialize modal with visual editor when opened
    $('#addTemplateModal').on('shown.bs.modal', function() {
        switchToModalVisualEditor();
    });

    // Reset modal when closed
    $('#addTemplateModal').on('hidden.bs.modal', function() {
        modalVisualEditorInitialized = false;
        $(this).find('form')[0].reset();
        $('textarea[name="email_body"]').val('');
        $('#modal_email_body_final').val('');
    });
}


// ============================================================================
// EMAIL TEMPLATE EDITOR FUNCTIONALITY - ADDED ENHANCEMENT
// ============================================================================

// Enhanced copyToClipboard function - preserves existing functionality
window.copyToClipboard = function(text) {
    console.log('Enhanced copyToClipboard called with:', text);

    if (navigator.clipboard && window.isSecureContext) {
        // Use modern clipboard API
        navigator.clipboard.writeText(text).then(function() {
            showCopySuccess(text);
        }).catch(function(err) {
            console.error('Clipboard API failed:', err);
            fallbackCopyToClipboard(text);
        });
    } else {
        // Fallback for older browsers or non-secure contexts
        fallbackCopyToClipboard(text);
    }
};

// Fallback copy method
function fallbackCopyToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        document.execCommand('copy');
        showCopySuccess(text);
    } catch (err) {
        console.error('Failed to copy text: ', err);
        alert('Failed to copy shortcode. Please copy manually: ' + text);
    }

    document.body.removeChild(textArea);
}

// Show success message
function showCopySuccess(text) {
    if (typeof toastr !== 'undefined') {
        toastr.success('Shortcode copied to clipboard: ' + text);
    } else {
        alert('Shortcode copied to clipboard: ' + text);
    }
}

// Enhanced email template editor initialization - COMPLETELY REWRITTEN
$(document).ready(function() {
    console.log('🚀 Enhanced email template editor initializing...');

    // Initialize email template editor for edit pages - DETECT BOTH TYPES
    if ($('.email-builder-interface').length > 0 || $('.visual-editor-panel').length > 0 || $('#email-builder-container').length > 0) {
        console.log('✅ Email template editor elements found, initializing enhanced editor...');
        initEnhancedEmailTemplateEditor();
    } else {
        console.log('⚠️ No email template editor elements found - preserving existing functionality');
    }
});

// Enhanced email template editor function - COMPLETELY REWRITTEN FOR BOTH TYPES
function initEnhancedEmailTemplateEditor() {
    console.log('🔧 Initializing Enhanced Email Template Editor...');

    let currentMode = 'visual';
    let visualEditorInitialized = false;
    let editorType = 'unknown';

    // Get DOM elements
    const visualBtn = $('#visual-editor-btn');
    const htmlBtn = $('#html-editor-btn');
    const visualPanel = $('#visual-email-builder');
    const htmlPanel = $('#html-editor-panel');
    const container = document.getElementById('email-builder-container');

    // Detect editor type
    if ($('.email-builder-interface').length > 0) {
        editorType = 'individual_template';
        console.log('📝 Detected: Individual Template Editor (with component buttons)');
    } else if ($('#email-builder-container').length > 0) {
        editorType = 'global_template';
        console.log('🌐 Detected: Global Template Editor (with drag-and-drop)');
    }

    console.log('🔍 Enhanced DOM Elements found:', {
        visualBtn: visualBtn.length,
        htmlBtn: htmlBtn.length,
        visualPanel: visualPanel.length,
        htmlPanel: htmlPanel.length,
        container: container ? 'found' : 'not found',
        editorType: editorType
    });

    // Initialize the appropriate editor based on type
    if (editorType === 'individual_template') {
        initIndividualTemplateEditor();
    } else if (editorType === 'global_template') {
        initGlobalTemplateEditor();
    }

    // Common functionality for both editor types
    initCommonEditorFeatures();
}

// Initialize Individual Template Editor (with component buttons)
function initIndividualTemplateEditor() {
    console.log('🔧 Initializing Individual Template Editor...');

    const container = document.getElementById('email-builder-container');
    if (!container) {
        console.error('❌ Container not found for individual template editor');
        return;
    }

    // Load existing template content
    loadExistingTemplateContent();

    // Initialize component buttons
    initComponentButtons();

    // Initialize editor mode switching
    initEditorModeSwitch();

    console.log('✅ Individual Template Editor initialized');
}

// Initialize Global Template Editor (with drag-and-drop)
function initGlobalTemplateEditor() {
    console.log('🔧 Initializing Global Template Editor...');

    const container = document.getElementById('email-builder-container');
    if (!container) {
        console.error('❌ Container not found for global template editor');
        return;
    }

    // Initialize the advanced visual email editor
    if (typeof VisualEmailEditor !== 'undefined') {
        const editor = new VisualEmailEditor(container);
        console.log('✅ Advanced Visual Email Editor initialized');
    } else {
        console.log('⚠️ VisualEmailEditor class not found, using fallback');
        loadExistingTemplateContent();
    }

    console.log('✅ Global Template Editor initialized');
}

// Initialize Component Buttons for Individual Template Editor
function initComponentButtons() {
    console.log('🔘 Initializing component buttons...');

    $('.component-btn').off('click').on('click', function(e) {
        e.preventDefault();
        const componentType = $(this).data('component');
        console.log('🔘 Component button clicked:', componentType);
        addComponentToCanvas(componentType);
    });

    console.log('✅ Component buttons initialized');
}

// Initialize Editor Mode Switch (Visual/HTML)
function initEditorModeSwitch() {
    console.log('🔄 Initializing editor mode switch...');

    const visualBtn = $('#visual-editor-btn');
    const htmlBtn = $('#html-editor-btn');
    const visualPanel = $('#visual-email-builder');
    const htmlPanel = $('#html-editor-panel');

    // Visual editor button
    visualBtn.off('click').on('click', function(e) {
        e.preventDefault();
        console.log('👁️ Switching to visual editor');

        // Update button states
        visualBtn.addClass('active').removeClass('btn-outline--primary').addClass('btn--primary');
        htmlBtn.removeClass('active').removeClass('btn--primary').addClass('btn-outline--secondary');

        // Show/hide panels
        visualPanel.show();
        htmlPanel.hide();

        // Sync content from HTML to visual
        syncHTMLToVisual();
    });

    // HTML editor button
    htmlBtn.off('click').on('click', function(e) {
        e.preventDefault();
        console.log('📝 Switching to HTML editor');

        // Update button states
        htmlBtn.addClass('active').removeClass('btn-outline--secondary').addClass('btn--primary');
        visualBtn.removeClass('active').removeClass('btn--primary').addClass('btn-outline--primary');

        // Show/hide panels
        htmlPanel.show();
        visualPanel.hide();

        // Sync content from visual to HTML
        syncVisualToHTML();
    });

    console.log('✅ Editor mode switch initialized');
}

// Initialize Common Editor Features
function initCommonEditorFeatures() {
    console.log('🔧 Initializing common editor features...');

    // Preview functionality
    $('#preview-email').off('click').on('click', function(e) {
        e.preventDefault();
        console.log('👁️ Preview button clicked');
        previewEmail();
    });

    // Reset functionality
    $('#reset-template').off('click').on('click', function(e) {
        e.preventDefault();
        console.log('🔄 Reset button clicked');
        if (confirm('Are you sure you want to reset the template?')) {
            resetTemplate();
        }
    });

    console.log('✅ Common editor features initialized');
}

// Add Component to Canvas (for Individual Template Editor)
function addComponentToCanvas(componentType) {
    console.log('➕ Adding component to canvas:', componentType);

    const container = document.getElementById('email-builder-container');
    if (!container) {
        console.error('❌ Canvas container not found');
        return;
    }

    // Remove placeholder if it exists
    const placeholder = container.querySelector('.canvas-placeholder');
    if (placeholder) {
        placeholder.remove();
    }

    // Create component HTML
    let componentHTML = '';
    const componentId = 'component_' + Date.now();

    switch(componentType) {
        case 'header':
            componentHTML = `<div class="email-component" id="${componentId}" style="padding: 30px 20px; text-align: center; background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; margin-bottom: 20px;">
                <h1 style="margin: 0; font-size: 28px; font-weight: 700;" contenteditable="true">Your Header Title</h1>
                <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;" contenteditable="true">Subtitle or description</p>
            </div>`;
            break;
        case 'text':
            componentHTML = `<div class="email-component" id="${componentId}" style="padding: 20px; margin-bottom: 15px; background: #ffffff; border: 1px solid #e9ecef; border-radius: 8px;">
                <p style="margin: 0; font-size: 16px; line-height: 1.6; color: #333;" contenteditable="true">Click here to edit this text block. You can add your content, format it, and customize the styling as needed.</p>
            </div>`;
            break;
        case 'button':
            componentHTML = `<div class="email-component" id="${componentId}" style="padding: 20px; text-align: center; margin-bottom: 15px;">
                <a href="#" style="display: inline-block; background: #dc3545; color: #fff; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; transition: all 0.3s ease;" contenteditable="true">Click Here</a>
            </div>`;
            break;
        case 'image':
            componentHTML = `<div class="email-component" id="${componentId}" style="padding: 20px; text-align: center; margin-bottom: 15px;">
                <img src="https://via.placeholder.com/500x250/dc3545/ffffff?text=Your+Image" alt="Email Image" style="max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
                <p style="font-size: 12px; color: #666; margin-top: 10px; font-style: italic;">Click to edit image source</p>
            </div>`;
            break;
        case 'footer':
            componentHTML = `<div class="email-component" id="${componentId}" style="padding: 30px 20px; text-align: center; background: #f8f9fa; border-top: 3px solid #dc3545; color: #6c757d; font-size: 14px; margin-top: 20px;">
                <p style="margin: 0 0 10px 0;" contenteditable="true">© 2025 Your Company. All rights reserved.</p>
                <p style="margin: 0; font-size: 12px;" contenteditable="true">You received this email because you subscribed to our newsletter.</p>
            </div>`;
            break;
    }

    // Add component to container
    container.insertAdjacentHTML('beforeend', componentHTML);

    // Make the new component interactive
    makeComponentInteractive(componentId);

    // Sync content to form
    syncVisualToHTML();

    console.log('✅ Component added successfully:', componentType);
}

// Make Component Interactive
function makeComponentInteractive(componentId) {
    const component = document.getElementById(componentId);
    if (!component) return;

    // Add hover effects
    component.addEventListener('mouseenter', function() {
        this.style.boxShadow = '0 4px 12px rgba(220, 53, 69, 0.3)';
        this.style.transform = 'translateY(-2px)';
    });

    component.addEventListener('mouseleave', function() {
        this.style.boxShadow = '';
        this.style.transform = '';
    });

    // Add click to focus for editable elements
    const editableElements = component.querySelectorAll('[contenteditable="true"]');
    editableElements.forEach(element => {
        element.addEventListener('focus', function() {
            this.style.outline = '2px solid #dc3545';
            this.style.outlineOffset = '2px';
        });

        element.addEventListener('blur', function() {
            this.style.outline = '';
            this.style.outlineOffset = '';
            syncVisualToHTML(); // Sync changes
        });
    });

    console.log('🎯 Component made interactive:', componentId);
}

// Sync Visual Editor Content to HTML Textarea
function syncVisualToHTML() {
    const container = document.getElementById('email-builder-container');
    const textarea = $('textarea[name="email_body"]');
    const hiddenField = $('#email_body_final');

    if (container && textarea.length) {
        const content = container.innerHTML;
        textarea.val(content);
        if (hiddenField.length) {
            hiddenField.val(content);
        }
        console.log('📤 Content synced from visual to HTML');
    }
}

// Sync HTML Textarea Content to Visual Editor
function syncHTMLToVisual() {
    const container = document.getElementById('email-builder-container');
    const textarea = $('textarea[name="email_body"]');

    if (container && textarea.length) {
        const content = textarea.val();
        if (content && content.trim()) {
            parseHTMLToVisualComponents(content);
        } else {
            showEmptyState();
        }
        console.log('📥 Content synced from HTML to visual');
    }
}

// CRITICAL FIX: Load existing template content
function loadExistingTemplateContent() {
    console.log('📂 Loading existing template content...');

    const container = document.getElementById('email-builder-container');
    if (!container) return;

    // Get existing content from textarea
    const existingContent = $('textarea[name="email_body"]').val() || $('textarea[name="email_template"]').val();
    console.log('📄 Existing content found:', existingContent ? 'Yes (' + existingContent.length + ' chars)' : 'No');

    if (existingContent && existingContent.trim()) {
        // Parse and display existing HTML content in visual editor
        parseHTMLToVisualComponents(existingContent);
        console.log('✅ Existing template content loaded into visual editor');
    } else {
        console.log('📝 No existing content found, showing empty state');
        showEmptyState();
    }
}

// Parse HTML content into visual components
function parseHTMLToVisualComponents(htmlContent) {
    const container = document.getElementById('email-builder-container');
    if (!container) return;

    console.log('📋 Parsing HTML content into visual components...');

    // Clear the container
    container.innerHTML = '';

    // Create a wrapper for the existing content
    const contentWrapper = document.createElement('div');
    contentWrapper.className = 'existing-content-wrapper';
    contentWrapper.style.cssText = `
        background: #ffffff;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        padding: 20px;
        margin: 10px 0;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        position: relative;
    `;

    // Add a header to indicate this is existing content
    const contentHeader = document.createElement('div');
    contentHeader.className = 'content-status-header';
    contentHeader.style.cssText = `
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 10px 15px;
        border-radius: 8px;
        margin-bottom: 15px;
        text-align: center;
        font-weight: 600;
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    `;
    contentHeader.innerHTML = '✅ Existing Template Content - Click to Edit';

    // Create the editable content area
    const editableArea = document.createElement('div');
    editableArea.className = 'editable-template-content';
    editableArea.contentEditable = true;
    editableArea.innerHTML = htmlContent;
    editableArea.style.cssText = `
        min-height: 200px;
        padding: 20px;
        border: 1px dashed #dee2e6;
        border-radius: 8px;
        background: #fafafa;
        font-family: Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        cursor: text;
    `;

    // Add event listeners for real-time editing
    editableArea.addEventListener('input', function() {
        syncVisualToHTML();
        console.log('📝 Content updated in real-time');
    });

    editableArea.addEventListener('focus', function() {
        this.style.borderColor = '#dc3545';
        this.style.boxShadow = '0 0 0 3px rgba(220, 53, 69, 0.1)';
    });

    editableArea.addEventListener('blur', function() {
        this.style.borderColor = '#dee2e6';
        this.style.boxShadow = '';
    });

    // Assemble the content
    contentWrapper.appendChild(contentHeader);
    contentWrapper.appendChild(editableArea);
    container.appendChild(contentWrapper);

    console.log('✅ HTML content parsed and made editable');
}

// Show empty state when no content exists
function showEmptyState() {
    const container = document.getElementById('email-builder-container');
    if (!container) return;

    container.innerHTML = `
        <div class="canvas-placeholder" style="display: flex; align-items: center; justify-content: center; height: 100%; min-height: 450px; text-align: center; color: #6c757d; background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); border: 3px dashed #dee2e6; border-radius: 8px;">
            <div>
                <i class="las la-plus-circle" style="font-size: 48px; color: #dc3545; margin-bottom: 15px;"></i>
                <h5 style="color: #333; margin-bottom: 10px;">Start Building Your Email Template</h5>
                <p style="margin: 0; max-width: 300px;">Click the component buttons above to add headers, text blocks, buttons, images, and footers to your email template.</p>
            </div>
        </div>
    `;

    console.log('📝 Empty state displayed');
}

// Enhanced Preview Email Function
function previewEmail() {
    console.log('👁️ Opening email preview...');

    let content = '';
    const editableContent = document.querySelector('.editable-template-content');
    const textarea = $('textarea[name="email_body"]');

    // Get content from visual editor or textarea
    if (editableContent && $('#visual-email-builder').is(':visible')) {
        content = editableContent.innerHTML;
    } else if (textarea.length) {
        content = textarea.val();
    }

    if (!content || content.trim() === '') {
        alert('⚠️ No content to preview. Please add content to the email template first.');
        return;
    }

    // Create and show preview modal
    const previewModal = `
        <div class="modal fade" id="emailPreviewModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header bg--primary">
                        <h5 class="modal-title text-white">
                            <i class="las la-eye"></i> Email Template Preview
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body p-0">
                        <div class="email-preview-container" style="max-height: 70vh; overflow-y: auto; background: #f5f5f5; padding: 30px;">
                            <div style="max-width: 600px; margin: 0 auto; background: #ffffff; box-shadow: 0 4px 20px rgba(0,0,0,0.1); border-radius: 8px; overflow: hidden;">
                                ${content}
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn--secondary" data-bs-dismiss="modal">
                            <i class="las la-times"></i> Close Preview
                        </button>
                        <button type="button" class="btn btn--primary" onclick="openPreviewInNewWindow()">
                            <i class="las la-external-link-alt"></i> Open in New Window
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if present
    $('#emailPreviewModal').remove();

    // Add modal to page and show it
    $('body').append(previewModal);
    $('#emailPreviewModal').modal('show');

    console.log('✅ Email preview modal opened');
}

// Open preview in new window
function openPreviewInNewWindow() {
    let content = '';
    const editableContent = document.querySelector('.editable-template-content');
    const textarea = $('textarea[name="email_body"]');

    if (editableContent && $('#visual-email-builder').is(':visible')) {
        content = editableContent.innerHTML;
    } else if (textarea.length) {
        content = textarea.val();
    }

    const previewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');
    const fullHTML = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Template Preview</title>
    <style>
        body { margin: 0; padding: 20px; background: #f5f5f5; font-family: Arial, sans-serif; }
        .email-container { max-width: 600px; margin: 0 auto; background: #ffffff; box-shadow: 0 2px 10px rgba(0,0,0,0.1); border-radius: 8px; overflow: hidden; }
    </style>
</head>
<body>
    <div class="email-container">
        ${content}
    </div>
</body>
</html>`;

    previewWindow.document.write(fullHTML);
    previewWindow.document.close();

    console.log('🔗 Preview opened in new window');
}

// Reset Template Function
function resetTemplate() {
    console.log('🔄 Resetting template...');

    const container = document.getElementById('email-builder-container');
    const textarea = $('textarea[name="email_body"]');
    const hiddenField = $('#email_body_final');

    if (container) {
        showEmptyState();
    }

    if (textarea.length) {
        textarea.val('');
    }

    if (hiddenField.length) {
        hiddenField.val('');
    }

    console.log('✅ Template reset successfully');
}

    // Initialize the fully functional editor with all interactive features
    function initializeFunctionalEditor() {
        console.log('Initializing fully functional editor...');

        // Make content editable and add event listeners
        const editableContent = document.getElementById('editable-content');
        const widthSlider = document.getElementById('canvas-width');
        const widthDisplay = document.getElementById('width-display');
        const bgColorPicker = document.getElementById('bg-color-picker');
        const textColorPicker = document.getElementById('text-color-picker');
        const fontSizeSelector = document.getElementById('font-size-selector');
        const applyBtn = document.getElementById('apply-global-styles');
        const resetBtn = document.getElementById('reset-global-styles');

        if (editableContent) {
            // Add real-time editing capabilities
            editableContent.addEventListener('input', function() {
                console.log('Content changed - syncing to form');
                syncContentToForm();
            });

            // Add click-to-edit functionality for specific elements
            editableContent.addEventListener('click', function(e) {
                const target = e.target;
                if (target.tagName === 'H1' || target.tagName === 'H2' || target.tagName === 'H3' ||
                    target.tagName === 'P' || target.tagName === 'SPAN' || target.tagName === 'DIV') {
                    target.focus();
                    console.log('Element focused for editing:', target.tagName);
                }
            });
        }

        // Width slider functionality
        if (widthSlider && widthDisplay) {
            widthSlider.addEventListener('input', function() {
                const width = this.value + 'px';
                widthDisplay.textContent = width;
                if (editableContent) {
                    editableContent.style.maxWidth = width;
                    editableContent.style.margin = '0 auto';
                }
            });
        }

        // Color picker functionality
        if (bgColorPicker) {
            bgColorPicker.addEventListener('change', function() {
                if (editableContent) {
                    editableContent.style.backgroundColor = this.value;
                }
            });
        }

        if (textColorPicker) {
            textColorPicker.addEventListener('change', function() {
                if (editableContent) {
                    editableContent.style.color = this.value;
                }
            });
        }

        // Font size functionality
        if (fontSizeSelector) {
            fontSizeSelector.addEventListener('change', function() {
                if (editableContent) {
                    editableContent.style.fontSize = this.value;
                }
            });
        }

        // Apply styles button
        if (applyBtn) {
            applyBtn.addEventListener('click', function() {
                applyAllStyles();
                syncContentToForm();
            });
        }

        // Reset styles button
        if (resetBtn) {
            resetBtn.addEventListener('click', function() {
                resetAllStyles();
                syncContentToForm();
            });
        }

        console.log('Functional editor initialized with all interactive features');
    }

    // Sync visual editor content to form fields
    function syncContentToForm() {
        const editableContent = document.getElementById('editable-content');
        if (editableContent) {
            const content = editableContent.innerHTML;
            $('textarea[name="email_body"]').val(content);
            $('#email_body_final').val(content);
            console.log('Content synced to form fields');
        }
    }

    // Apply all current style settings
    function applyAllStyles() {
        const editableContent = document.getElementById('editable-content');
        const widthSlider = document.getElementById('canvas-width');
        const bgColorPicker = document.getElementById('bg-color-picker');
        const textColorPicker = document.getElementById('text-color-picker');
        const fontSizeSelector = document.getElementById('font-size-selector');

        if (editableContent) {
            if (widthSlider) editableContent.style.maxWidth = widthSlider.value + 'px';
            if (bgColorPicker) editableContent.style.backgroundColor = bgColorPicker.value;
            if (textColorPicker) editableContent.style.color = textColorPicker.value;
            if (fontSizeSelector) editableContent.style.fontSize = fontSizeSelector.value;

            console.log('All styles applied to content');
        }
    }

    // Reset all styles to defaults
    function resetAllStyles() {
        const editableContent = document.getElementById('editable-content');
        const widthSlider = document.getElementById('canvas-width');
        const widthDisplay = document.getElementById('width-display');
        const bgColorPicker = document.getElementById('bg-color-picker');
        const textColorPicker = document.getElementById('text-color-picker');
        const fontSizeSelector = document.getElementById('font-size-selector');

        if (editableContent) {
            editableContent.style.maxWidth = '600px';
            editableContent.style.backgroundColor = '#ffffff';
            editableContent.style.color = '#333333';
            editableContent.style.fontSize = '14px';
        }

        if (widthSlider) widthSlider.value = 600;
        if (widthDisplay) widthDisplay.textContent = '600px';
        if (bgColorPicker) bgColorPicker.value = '#ffffff';
        if (textColorPicker) textColorPicker.value = '#333333';
        if (fontSizeSelector) fontSizeSelector.value = '14px';

        console.log('All styles reset to defaults');
    }

    // Global functions for component actions (accessible from onclick)
    window.addNewComponent = function(type) {
        const editableContent = document.getElementById('editable-content');
        if (!editableContent) return;

        let componentHTML = '';
        switch(type) {
            case 'text':
                componentHTML = '<div style="padding: 15px; margin: 10px 0; border: 1px dashed #ccc; background: #f9f9f9;"><p contenteditable="true">Click here to edit this text block. You can type directly to modify the content.</p></div>';
                break;
            case 'button':
                componentHTML = '<div style="padding: 15px; margin: 10px 0; text-align: center;"><a href="#" style="display: inline-block; background: #dc3545; color: #fff; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600;" contenteditable="true">Edit Button Text</a></div>';
                break;
            case 'image':
                componentHTML = '<div style="padding: 15px; margin: 10px 0; text-align: center; border: 1px dashed #ccc;"><img src="https://via.placeholder.com/400x200/dc3545/ffffff?text=Click+to+Edit" alt="Editable Image" style="max-width: 100%; height: auto; border-radius: 6px;"><p style="font-size: 12px; color: #666; margin-top: 10px;">Click image to change source</p></div>';
                break;
        }

        editableContent.insertAdjacentHTML('beforeend', componentHTML);
        syncContentToForm();
        console.log('New component added:', type);
    };

    // Global function to save current content
    window.saveCurrentContent = function() {
        syncContentToForm();

        // Show save confirmation
        const statusIndicator = document.querySelector('.status-indicator');
        if (statusIndicator) {
            const originalText = statusIndicator.textContent;
            statusIndicator.textContent = '💾 CONTENT SAVED TO FORM - Ready for submission';
            statusIndicator.style.background = '#28a745';

            setTimeout(() => {
                statusIndicator.textContent = originalText;
                statusIndicator.style.background = '';
            }, 3000);
        }

        console.log('Content saved and ready for form submission');
    };

    // Create customization controls for visual editing (LEGACY - keeping for compatibility)
    function createCustomizationControls() {
        const controlsContainer = document.createElement('div');
        controlsContainer.className = 'visual-customization-controls';
        controlsContainer.style.cssText = `
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
        `;

        controlsContainer.innerHTML = `
            <div style="display: flex; flex-wrap: wrap; gap: 15px; align-items: center;">
                <div style="display: flex; align-items: center; gap: 8px;">
                    <label style="font-size: 12px; font-weight: 600; color: #495057;">Width:</label>
                    <input type="range" id="content-width" min="300" max="800" value="600" style="width: 100px;">
                    <span id="width-value" style="font-size: 12px; color: #6c757d;">600px</span>
                </div>
                <div style="display: flex; align-items: center; gap: 8px;">
                    <label style="font-size: 12px; font-weight: 600; color: #495057;">Background:</label>
                    <input type="color" id="bg-color" value="#ffffff" style="width: 40px; height: 30px; border: none; border-radius: 4px;">
                </div>
                <div style="display: flex; align-items: center; gap: 8px;">
                    <label style="font-size: 12px; font-weight: 600; color: #495057;">Text Color:</label>
                    <input type="color" id="text-color" value="#333333" style="width: 40px; height: 30px; border: none; border-radius: 4px;">
                </div>
                <div style="display: flex; align-items: center; gap: 8px;">
                    <label style="font-size: 12px; font-weight: 600; color: #495057;">Font Size:</label>
                    <select id="font-size" style="padding: 4px 8px; border: 1px solid #ced4da; border-radius: 4px; font-size: 12px;">
                        <option value="12px">12px</option>
                        <option value="14px" selected>14px</option>
                        <option value="16px">16px</option>
                        <option value="18px">18px</option>
                        <option value="20px">20px</option>
                        <option value="24px">24px</option>
                    </select>
                </div>
                <button type="button" id="apply-styles" style="background: #dc3545; color: white; border: none; padding: 6px 12px; border-radius: 4px; font-size: 12px; font-weight: 600; cursor: pointer;">Apply Styles</button>
                <button type="button" id="reset-styles" style="background: #6c757d; color: white; border: none; padding: 6px 12px; border-radius: 4px; font-size: 12px; font-weight: 600; cursor: pointer;">Reset</button>
            </div>
        `;

        // Add event listeners for customization controls
        setTimeout(() => {
            const widthSlider = document.getElementById('content-width');
            const widthValue = document.getElementById('width-value');
            const bgColor = document.getElementById('bg-color');
            const textColor = document.getElementById('text-color');
            const fontSize = document.getElementById('font-size');
            const applyBtn = document.getElementById('apply-styles');
            const resetBtn = document.getElementById('reset-styles');

            if (widthSlider && widthValue) {
                widthSlider.addEventListener('input', function() {
                    widthValue.textContent = this.value + 'px';
                });
            }

            if (applyBtn) {
                applyBtn.addEventListener('click', function() {
                    applyCustomStyles();
                });
            }

            if (resetBtn) {
                resetBtn.addEventListener('click', function() {
                    resetCustomStyles();
                });
            }
        }, 100);

        return controlsContainer;
    }

    // Apply custom styles to the content
    function applyCustomStyles() {
        const contentPreview = container.querySelector('.existing-template-content div:last-child');
        const widthSlider = document.getElementById('content-width');
        const bgColor = document.getElementById('bg-color');
        const textColor = document.getElementById('text-color');
        const fontSize = document.getElementById('font-size');

        if (contentPreview && widthSlider && bgColor && textColor && fontSize) {
            contentPreview.style.maxWidth = widthSlider.value + 'px';
            contentPreview.style.backgroundColor = bgColor.value;
            contentPreview.style.color = textColor.value;
            contentPreview.style.fontSize = fontSize.value;

            // Update the textarea with modified content
            updateFormDataWithStyles();

            console.log('Custom styles applied');
        }
    }

    // Reset styles to default
    function resetCustomStyles() {
        const contentPreview = container.querySelector('.existing-template-content div:last-child');

        if (contentPreview) {
            contentPreview.style.maxWidth = '600px';
            contentPreview.style.backgroundColor = '#ffffff';
            contentPreview.style.color = '#333333';
            contentPreview.style.fontSize = '14px';

            // Reset form controls
            const widthSlider = document.getElementById('content-width');
            const bgColor = document.getElementById('bg-color');
            const textColor = document.getElementById('text-color');
            const fontSize = document.getElementById('font-size');

            if (widthSlider) widthSlider.value = 600;
            if (bgColor) bgColor.value = '#ffffff';
            if (textColor) textColor.value = '#333333';
            if (fontSize) fontSize.value = '14px';

            document.getElementById('width-value').textContent = '600px';

            updateFormDataWithStyles();

            console.log('Styles reset to default');
        }
    }

    // Update form data with current styles
    function updateFormDataWithStyles() {
        const contentPreview = container.querySelector('.existing-template-content div:last-child');
        if (contentPreview) {
            const styledContent = contentPreview.innerHTML;
            $('textarea[name="email_body"]').val(styledContent);
            $('#email_body_final').val(styledContent);
            console.log('Form data updated with styled content');
        }
    }

    // Show empty state when no content exists
    function showEmptyState() {
        if (!container) return;

        container.innerHTML = `
            <div class="canvas-placeholder" style="display: flex; align-items: center; justify-content: center; height: 100%; min-height: 450px; text-align: center; color: #6c757d; background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); border: 3px dashed #dee2e6; border-radius: 8px;">
                <div>
                    <i class="las la-plus-circle" style="font-size: 48px; color: #dc3545; margin-bottom: 15px;"></i>
                    <h5 style="color: #333; margin-bottom: 10px;">Start Building Your Enhanced Email</h5>
                    <p style="margin: 0;">Click components from the left panel to start building your professional email template</p>
                </div>
            </div>
        `;
    }

    // Initialize basic visual editor
    function initBasicVisualEditor() {
        if (!container) {
            console.error('Container not found for enhanced visual editor');
            return;
        }

        console.log('Initializing enhanced basic visual editor...');

        // CRITICAL: Load existing content first
        loadExistingTemplateContent();

        // Add basic component functionality
        $('.component-btn').off('click').on('click', function(e) {
            e.preventDefault();
            const componentType = $(this).data('component');
            console.log('Enhanced component button clicked:', componentType);
            addBasicComponent(componentType);
        });

        // Preview functionality
        $('#preview-email').off('click').on('click', function(e) {
            e.preventDefault();
            console.log('Enhanced preview button clicked');
            previewEmail();
        });

        // Reset functionality
        $('#reset-template').off('click').on('click', function(e) {
            e.preventDefault();
            console.log('Enhanced reset button clicked');
            if (confirm('Are you sure you want to reset the template?')) {
                resetTemplate();
            }
        });

        // Desktop/Mobile view toggle
        $('#desktop-view').off('click').on('click', function(e) {
            e.preventDefault();
            console.log('Enhanced desktop view clicked');
            $(this).addClass('active');
            $('#mobile-view').removeClass('active');
            $('.email-preview-container').removeClass('mobile-view');
        });

        $('#mobile-view').off('click').on('click', function(e) {
            e.preventDefault();
            console.log('Enhanced mobile view clicked');
            $(this).addClass('active');
            $('#desktop-view').removeClass('active');
            $('.email-preview-container').addClass('mobile-view');
        });

        visualEditorInitialized = true;
        console.log('Enhanced basic visual editor initialized successfully');
    }

    // Add basic component
    function addBasicComponent(type) {
        const placeholder = container.querySelector('.canvas-placeholder');
        if (placeholder) {
            placeholder.style.display = 'none';
        }

        let componentHTML = '';
        switch(type) {
            case 'header':
                componentHTML = '<div class="email-component" style="padding: 20px; text-align: center; background: #f8f9fa; border-bottom: 2px solid #dc3545;"><h2 style="color: #dc3545; margin: 0;">Your Header Here</h2></div>';
                break;
            case 'text':
                componentHTML = '<div class="email-component" style="padding: 20px;"><p style="color: #333; line-height: 1.6; margin: 0;">Your text content here. You can edit this in HTML mode.</p></div>';
                break;
            case 'button':
                componentHTML = '<div class="email-component" style="padding: 20px; text-align: center;"><a href="#" style="display: inline-block; background: #dc3545; color: #fff; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600;">Click Here</a></div>';
                break;
            case 'image':
                componentHTML = '<div class="email-component" style="padding: 20px; text-align: center;"><img src="https://via.placeholder.com/400x200/dc3545/ffffff?text=Your+Image" alt="Image" style="max-width: 100%; height: auto; border-radius: 6px;"></div>';
                break;
            case 'footer':
                componentHTML = '<div class="email-component" style="padding: 20px; text-align: center; background: #f8f9fa; border-top: 2px solid #dc3545; color: #6c757d; font-size: 14px;"><p style="margin: 0;">© 2025 MBFX. All rights reserved.</p></div>';
                break;
        }

        container.insertAdjacentHTML('beforeend', componentHTML);
        updateFormData();
        console.log('Enhanced component added:', type);
    }

    // Preview email - FIXED to show existing content
    function previewEmail() {
        // Get content from textarea (existing template content)
        let htmlContent = $('textarea[name="email_body"]').val();

        // If no content in textarea, try to get from visual editor
        if (!htmlContent || htmlContent.trim() === '') {
            htmlContent = generateEmailHTML();
        }

        // If still no content, show a message
        if (!htmlContent || htmlContent.trim() === '' || htmlContent.includes('No content added yet')) {
            htmlContent = '<div style="padding: 40px; text-align: center; color: #6c757d;"><h3>No Content Available</h3><p>Please add content to your email template first.</p></div>';
        }

        console.log('Preview content length:', htmlContent.length);

        const previewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');

        const fullHTML = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Email Template Preview</title>
    <style>
        body { margin: 0; padding: 20px; background: #f5f5f5; font-family: Arial, sans-serif; }
        .email-container { max-width: 600px; margin: 0 auto; background: #ffffff; box-shadow: 0 2px 10px rgba(0,0,0,0.1); border-radius: 8px; overflow: hidden; }
        .preview-header { background: #dc3545; color: white; padding: 10px 20px; font-size: 14px; font-weight: 600; }
    </style>
</head>
<body>
    <div class="preview-header">📧 Email Template Preview - Enhanced Editor</div>
    <div class="email-container">
        ${htmlContent}
    </div>
</body>
</html>`;

        previewWindow.document.write(fullHTML);
        previewWindow.document.close();
        console.log('Enhanced email preview opened with existing content');
    }

    // Reset template
    function resetTemplate() {
        container.innerHTML = `
            <div class="canvas-placeholder" style="display: flex; align-items: center; justify-content: center; height: 100%; min-height: 450px; text-align: center; color: #6c757d; background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); border: 3px dashed #dee2e6; border-radius: 8px;">
                <div>
                    <i class="las la-plus-circle" style="font-size: 48px; color: #dc3545; margin-bottom: 15px;"></i>
                    <h5 style="color: #333; margin-bottom: 10px;">Start Building Your Enhanced Email</h5>
                    <p style="margin: 0;">Click components from the left panel to start building your professional email template</p>
                </div>
            </div>
        `;
        updateFormData();
        console.log('Enhanced template reset');
    }

    // Generate email HTML - FIXED to handle existing content
    function generateEmailHTML() {
        // First, try to get content from textarea (existing template)
        const textareaContent = $('textarea[name="email_body"]').val();
        if (textareaContent && textareaContent.trim() !== '') {
            console.log('Using existing textarea content for HTML generation');
            return textareaContent;
        }

        // If no textarea content, check for visual components
        const placeholder = container.querySelector('.canvas-placeholder');
        if (placeholder && placeholder.style.display !== 'none') {
            return '<div style="padding: 40px; text-align: center; color: #6c757d;"><h3>No Content Added Yet</h3><p>Please add some components to your email template or switch to HTML editor to add content.</p></div>';
        }

        // Check for existing template content in visual editor
        const existingContent = container.querySelector('.existing-template-content div:last-child');
        if (existingContent) {
            console.log('Using existing visual content for HTML generation');
            return existingContent.innerHTML;
        }

        // Check for visual components
        const components = container.querySelectorAll('.email-component');
        let html = '';

        components.forEach(component => {
            html += component.outerHTML;
        });

        return html || '<div style="padding: 40px; text-align: center; color: #6c757d;"><h3>No Content Available</h3><p>Please add content to your email template.</p></div>';
    }

    // Update form data - ENHANCED to preserve existing content
    function updateFormData() {
        const htmlContent = generateEmailHTML();

        // Only update if we have meaningful content
        if (htmlContent && !htmlContent.includes('No Content Added Yet') && !htmlContent.includes('No Content Available')) {
            $('textarea[name="email_body"]').val(htmlContent);
            $('#email_body_final').val(htmlContent);
            console.log('Enhanced form data updated with content length:', htmlContent.length);
        } else {
            console.log('Skipping form update - no meaningful content to save');
        }
    }

    // Editor mode switching
    if (visualBtn.length && htmlBtn.length) {
        console.log('Setting up enhanced editor mode switching...');

        visualBtn.on('click', function(e) {
            e.preventDefault();
            console.log('Enhanced visual editor button clicked');

            if (currentMode === 'visual') {
                console.log('Already in enhanced visual mode');
                return;
            }

            currentMode = 'visual';

            // Update button states
            visualBtn.addClass('active').removeClass('btn-outline--primary').addClass('btn--primary');
            htmlBtn.removeClass('active').removeClass('btn--primary').addClass('btn-outline--secondary');

            // Show/hide panels
            visualPanel.show();
            htmlPanel.hide();

            console.log('Switched to enhanced visual mode');

            // Initialize visual editor if not done
            if (!visualEditorInitialized) {
                initBasicVisualEditor();
            }
        });

        htmlBtn.on('click', function(e) {
            e.preventDefault();
            console.log('Enhanced HTML editor button clicked');

            if (currentMode === 'html') {
                console.log('Already in enhanced HTML mode');
                return;
            }

            currentMode = 'html';

            // Update button states
            htmlBtn.addClass('active').removeClass('btn-outline--secondary').addClass('btn--primary');
            visualBtn.removeClass('active').removeClass('btn--primary').addClass('btn-outline--primary');

            // Show/hide panels
            htmlPanel.show();
            visualPanel.hide();

            console.log('Switched to enhanced HTML mode');

            // Sync content from visual to HTML
            updateFormData();
        });
    } else {
        console.error('Enhanced editor toggle buttons not found');
    }

    // Initialize the enhanced editor
    console.log('Initializing enhanced editor in mode:', currentMode);
    if (currentMode === 'visual') {
        initBasicVisualEditor();
    }

    // Sync textarea changes - ENHANCED to handle existing content
    $('textarea[name="email_body"]').on('input', function() {
        const content = $(this).val();
        $('#email_body_final').val(content);
        console.log('Enhanced textarea content synced to hidden field');

        // If in visual mode and content changed, reload visual editor
        if (currentMode === 'visual' && content && content.trim() !== '') {
            console.log('Reloading visual editor due to textarea change');
            loadExistingTemplateContent();
        }
    });

    // CRITICAL: Load existing content immediately when editor initializes
    console.log('Loading existing template content on initialization...');
    setTimeout(() => {
        if (currentMode === 'visual') {
            loadExistingTemplateContent();
        }
    }, 500); // Small delay to ensure DOM is ready

    console.log('Enhanced email template editor initialization complete');
}
}
