<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ib_commissions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('from_user_id')->comment('User who generated the trade');
            $table->unsignedBigInteger('to_ib_user_id')->comment('IB who receives the commission');
            $table->string('trade_id')->comment('MT5 trade ID');
            $table->string('symbol')->comment('Trading symbol');
            $table->decimal('volume', 10, 2)->comment('Trade volume in lots');
            $table->decimal('commission_amount', 10, 2)->comment('Calculated commission amount');
            $table->decimal('commission_rate', 5, 2)->comment('Commission rate used for calculation');
            $table->integer('level')->comment('Commission level: 1, 2, 3, etc.');
            $table->enum('status', ['pending', 'paid', 'cancelled'])->default('pending');
            $table->timestamp('trade_closed_at')->nullable()->comment('When the MT5 trade was closed');
            $table->timestamp('paid_at')->nullable()->comment('When commission was paid');
            $table->text('notes')->nullable();
            $table->timestamps();
            
            // Foreign keys
            $table->foreign('from_user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('to_ib_user_id')->references('id')->on('users')->onDelete('cascade');
            
            // Indexes
            $table->index(['from_user_id', 'trade_id']);
            $table->index(['to_ib_user_id', 'status']);
            $table->index(['symbol', 'trade_closed_at']);
            $table->index('level');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ib_commissions');
    }
};
