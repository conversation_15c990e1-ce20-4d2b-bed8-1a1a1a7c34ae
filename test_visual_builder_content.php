<?php
/**
 * Test Visual Builder Content Loading
 * This script tests if the Visual Builder is properly loading template content
 */

echo "🔧 TESTING VISUAL BUILDER CONTENT LOADING\n";
echo str_repeat('=', 50) . "\n\n";

// Test template ID 44 (Account Verification Required)
$templateId = 44;
$url = "https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/edit/{$templateId}";

echo "📧 TESTING TEMPLATE ID: {$templateId}\n";
echo str_repeat('-', 40) . "\n";

$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => 'User-Agent: Mozilla/5.0',
        'timeout' => 30
    ],
    'ssl' => [
        'verify_peer' => false,
        'verify_peer_name' => false
    ]
]);

$content = file_get_contents($url, false, $context);

if ($content) {
    echo "✅ Page loaded successfully (" . strlen($content) . " chars)\n";
    
    // Check for PHP errors
    if (strpos($content, 'Fatal error') !== false || strpos($content, 'Parse error') !== false) {
        echo "❌ PHP errors detected\n";
    } else {
        echo "✅ No PHP errors detected\n";
    }
    
    // Check for Visual Builder elements
    if (strpos($content, 'visual-builder-container') !== false) {
        echo "✅ Visual Builder container found\n";
    } else {
        echo "❌ Visual Builder container missing\n";
    }
    
    if (strpos($content, 'visual-editor-btn') !== false) {
        echo "✅ Visual Editor button found\n";
    } else {
        echo "❌ Visual Editor button missing\n";
    }
    
    if (strpos($content, 'html-editor-btn') !== false) {
        echo "✅ HTML Editor button found\n";
    } else {
        echo "❌ HTML Editor button missing\n";
    }
    
    // Check for template content
    if (strpos($content, 'Account Verification Required') !== false) {
        echo "✅ Template content found\n";
    } else {
        echo "❌ Template content missing\n";
    }
    
    // Check for Visual Builder JavaScript
    if (strpos($content, 'visual-builder-email-editor.js') !== false) {
        echo "✅ Visual Builder JS included\n";
    } else {
        echo "❌ Visual Builder JS missing\n";
    }
    
    // Check for template data initialization
    if (strpos($content, 'templateData') !== false) {
        echo "✅ Template data initialization found\n";
    } else {
        echo "❌ Template data initialization missing\n";
    }
    
    // Extract template content if possible
    if (preg_match('/content:\s*"([^"]*)"/', $content, $matches)) {
        $templateContent = $matches[1];
        echo "✅ Template content extracted: " . substr($templateContent, 0, 100) . "...\n";
    } else {
        echo "❌ Could not extract template content\n";
    }
    
} else {
    echo "❌ Failed to load page\n";
}

echo "\n" . str_repeat('=', 50) . "\n";
echo "🎯 EXPECTED BEHAVIOR:\n";
echo "✅ Visual Builder should load with existing template content\n";
echo "✅ Template content should be editable in visual mode\n";
echo "✅ HTML editor should show raw HTML correctly\n";
echo "✅ Preview should display actual template content\n";
echo "✅ No duplicate buttons or controls\n";
