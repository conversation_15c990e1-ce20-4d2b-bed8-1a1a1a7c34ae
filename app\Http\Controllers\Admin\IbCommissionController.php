<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\IbCommission;
use App\Models\User;
use App\Services\IbCommissionIntegrationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class IbCommissionController extends Controller
{
    protected $commissionService;

    public function __construct(IbCommissionIntegrationService $commissionService)
    {
        $this->commissionService = $commissionService;
    }

    /**
     * Display commission overview
     */
    public function index(Request $request)
    {
        $pageTitle = 'IB Commission Management';
        
        $query = IbCommission::with(['fromUser', 'toIbUser'])
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('ib_user')) {
            $query->where('to_ib_user_id', $request->ib_user);
        }

        if ($request->filled('symbol')) {
            $query->where('symbol', 'like', '%' . $request->symbol . '%');
        }

        if ($request->filled('date_from')) {
            $query->where('deal_time', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('deal_time', '<=', $request->date_to);
        }

        $commissions = $query->paginate(20);

        // Get summary statistics
        $stats = $this->getCommissionStats($request);

        // Get IB users for filter dropdown
        $ibUsers = User::where('ib_status', 'approved')
            ->orderBy('firstname')
            ->get(['id', 'firstname', 'lastname', 'username']);

        return view('admin.ib.commissions.index', compact(
            'pageTitle', 'commissions', 'stats', 'ibUsers'
        ));
    }

    /**
     * Show commission details for specific IB
     */
    public function show($ibUserId, Request $request)
    {
        $ib = User::findOrFail($ibUserId);
        
        if (!$ib->isIb()) {
            $notify[] = ['error', 'User is not an approved IB'];
            return redirect()->route('admin.ib.commissions.index')->withNotify($notify);
        }

        $pageTitle = "Commission Details - {$ib->fullname}";

        $period = $request->get('period', '30days');
        
        // Get commission statistics
        $stats = $this->commissionService->getIbCommissionStats($ibUserId, $period);
        
        // Get commission breakdown
        $symbolBreakdown = IbCommission::getSymbolBreakdown($ibUserId, $period);
        $levelBreakdown = IbCommission::getLevelBreakdown($ibUserId, $period);
        
        // Get recent commissions
        $recentCommissions = IbCommission::forIb($ibUserId)
            ->with(['fromUser'])
            ->orderBy('deal_time', 'desc')
            ->limit(50)
            ->get();

        // Get network commission flow
        $networkFlow = $this->commissionService->getNetworkCommissionFlow($ibUserId, $period);

        return view('admin.ib.commissions.show', compact(
            'pageTitle', 'ib', 'stats', 'symbolBreakdown', 'levelBreakdown', 
            'recentCommissions', 'networkFlow', 'period'
        ));
    }

    /**
     * Sync commission data manually
     */
    public function syncCommissions(Request $request)
    {
        try {
            $limit = $request->get('limit', 1000);
            $result = $this->commissionService->syncCommissionData($limit);

            if ($result['success']) {
                $notify[] = ['success', "Successfully synced {$result['processed']} commission records"];
            } else {
                $notify[] = ['error', 'Sync failed: ' . $result['error']];
            }

        } catch (\Exception $e) {
            $notify[] = ['error', 'Sync error: ' . $e->getMessage()];
        }

        return back()->withNotify($notify);
    }

    /**
     * Get commission statistics
     */
    private function getCommissionStats($request)
    {
        $query = IbCommission::query();

        // Apply same filters as main query
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('ib_user')) {
            $query->where('to_ib_user_id', $request->ib_user);
        }

        if ($request->filled('symbol')) {
            $query->where('symbol', 'like', '%' . $request->symbol . '%');
        }

        if ($request->filled('date_from')) {
            $query->where('deal_time', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('deal_time', '<=', $request->date_to);
        }

        return [
            'total_commission' => $query->sum('commission_amount'),
            'total_trades' => $query->count(),
            'total_volume' => $query->sum('volume'),
            'pending_commission' => $query->where('status', 'pending')->sum('commission_amount'),
            'processed_commission' => $query->where('status', 'processed')->sum('commission_amount'),
            'unique_ibs' => $query->distinct('to_ib_user_id')->count('to_ib_user_id'),
            'unique_traders' => $query->distinct('from_user_id')->count('from_user_id')
        ];
    }

    /**
     * Process pending commissions
     */
    public function processPending(Request $request)
    {
        try {
            $commissionIds = $request->get('commission_ids', []);
            
            if (empty($commissionIds)) {
                $notify[] = ['error', 'No commissions selected'];
                return back()->withNotify($notify);
            }

            $processed = 0;
            foreach ($commissionIds as $id) {
                $commission = IbCommission::find($id);
                if ($commission && $commission->status === 'pending') {
                    $commission->markAsProcessed('Bulk processed by admin');
                    $processed++;
                }
            }

            $notify[] = ['success', "Successfully processed {$processed} commission records"];

        } catch (\Exception $e) {
            $notify[] = ['error', 'Processing error: ' . $e->getMessage()];
        }

        return back()->withNotify($notify);
    }

    /**
     * Get commission chart data for AJAX
     */
    public function getChartData(Request $request)
    {
        $ibUserId = $request->get('ib_user_id');
        $days = $request->get('days', 30);

        if (!$ibUserId) {
            return response()->json(['error' => 'IB user ID required'], 400);
        }

        $chartData = IbCommission::where('to_ib_user_id', $ibUserId)
            ->where('deal_time', '>=', Carbon::now()->subDays($days))
            ->selectRaw('DATE(deal_time) as date, SUM(commission_amount) as daily_commission, COUNT(*) as daily_trades')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->map(function ($item) {
                return [
                    'date' => $item->date,
                    'commission' => (float) $item->daily_commission,
                    'trades' => (int) $item->daily_trades
                ];
            });

        return response()->json($chartData);
    }

    /**
     * Export commission data
     */
    public function export(Request $request)
    {
        // Implementation for exporting commission data to CSV/Excel
        // This would be implemented based on specific requirements
        
        $notify[] = ['info', 'Export functionality will be implemented'];
        return back()->withNotify($notify);
    }
}
