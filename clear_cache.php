<?php
/**
 * CLEAR LARAVEL CACHE VIA WEB INTERFACE
 * Upload this to your live server and run via browser
 * Delete after use for security
 */

echo "<h1>Laravel Cache Cleaner</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;}</style>";

// Change to Laravel root directory
$laravelRoot = __DIR__;
chdir($laravelRoot);

echo "<h2>Clearing Laravel Caches...</h2>";

try {
    // Clear application cache
    if (function_exists('exec')) {
        // Try to find PHP executable
        $phpPaths = [
            'php',
            'C:\\Program Files\\PHP\\php.exe',
            'C:\\Program Files (x86)\\Plesk\\Additional\\PleskPHP81\\php.exe',
            'C:\\Program Files (x86)\\Plesk\\Additional\\PleskPHP82\\php.exe',
            'C:\\xampp\\php\\php.exe',
            'C:\\inetpub\\PHP\\php.exe'
        ];
        
        $phpExe = null;
        foreach ($phpPaths as $path) {
            if (file_exists($path) || $path === 'php') {
                // Test if this PHP works
                $test = shell_exec("\"$path\" --version 2>&1");
                if ($test && strpos($test, 'PHP') !== false) {
                    $phpExe = $path;
                    echo "<span class='success'>✓ Found PHP at: $path</span><br>";
                    break;
                }
            }
        }
        
        if ($phpExe) {
            echo "<br><strong>Executing cache clear commands...</strong><br>";
            
            // Clear different caches
            $commands = [
                'cache:clear' => 'Application Cache',
                'config:clear' => 'Configuration Cache',
                'route:clear' => 'Route Cache',
                'view:clear' => 'View Cache'
            ];
            
            foreach ($commands as $cmd => $description) {
                echo "<br>Clearing $description...<br>";
                $command = "\"$phpExe\" artisan $cmd 2>&1";
                $output = shell_exec($command);
                
                if ($output) {
                    if (strpos($output, 'cleared') !== false || strpos($output, 'Cache cleared') !== false) {
                        echo "<span class='success'>✓ $description cleared successfully</span><br>";
                    } else {
                        echo "<span class='error'>⚠ $description: " . htmlspecialchars($output) . "</span><br>";
                    }
                } else {
                    echo "<span class='error'>✗ Failed to clear $description</span><br>";
                }
            }
            
        } else {
            echo "<span class='error'>✗ Could not find PHP executable</span><br>";
            echo "<p>Manual cache clearing options:</p>";
        }
        
    } else {
        echo "<span class='error'>✗ exec() function is disabled</span><br>";
    }
    
    // Manual cache clearing by deleting files
    echo "<br><h3>Manual Cache Clearing (File Deletion)</h3>";
    
    $cacheDirectories = [
        'bootstrap/cache' => 'Bootstrap Cache',
        'storage/framework/cache' => 'Framework Cache',
        'storage/framework/sessions' => 'Sessions',
        'storage/framework/views' => 'Compiled Views',
        'storage/logs' => 'Log Files (optional)'
    ];
    
    foreach ($cacheDirectories as $dir => $description) {
        $fullPath = $laravelRoot . DIRECTORY_SEPARATOR . $dir;
        if (is_dir($fullPath)) {
            echo "<br>Clearing $description ($dir)...<br>";
            
            // Get files in directory
            $files = glob($fullPath . DIRECTORY_SEPARATOR . '*');
            $cleared = 0;
            
            foreach ($files as $file) {
                if (is_file($file) && basename($file) !== '.gitignore') {
                    if (unlink($file)) {
                        $cleared++;
                    }
                }
            }
            
            echo "<span class='success'>✓ Cleared $cleared files from $description</span><br>";
        } else {
            echo "<span class='error'>✗ Directory not found: $dir</span><br>";
        }
    }
    
} catch (Exception $e) {
    echo "<span class='error'>Error: " . $e->getMessage() . "</span><br>";
}

echo "<br><h3>Environment Check</h3>";
echo "<strong>Current Directory:</strong> " . getcwd() . "<br>";
echo "<strong>Laravel Root:</strong> " . $laravelRoot . "<br>";
echo "<strong>PHP Version:</strong> " . PHP_VERSION . "<br>";
echo "<strong>Server Software:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "<br>";

// Check if .env file exists and is readable
if (file_exists('.env')) {
    echo "<span class='success'>✓ .env file found</span><br>";
    
    // Check PYTHON paths in .env
    $envContent = file_get_contents('.env');
    if (strpos($envContent, 'PYTHON_EXE') !== false) {
        echo "<span class='success'>✓ PYTHON_EXE found in .env</span><br>";
    } else {
        echo "<span class='error'>✗ PYTHON_EXE not found in .env</span><br>";
    }
    
    if (strpos($envContent, 'PYTHON_SCRIPT') !== false) {
        echo "<span class='success'>✓ PYTHON_SCRIPT found in .env</span><br>";
    } else {
        echo "<span class='error'>✗ PYTHON_SCRIPT not found in .env</span><br>";
    }
} else {
    echo "<span class='error'>✗ .env file not found</span><br>";
}

echo "<br><p><strong>Cache clearing completed!</strong></p>";
echo "<p><em>Delete this file after use for security.</em></p>";
echo "<p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>
