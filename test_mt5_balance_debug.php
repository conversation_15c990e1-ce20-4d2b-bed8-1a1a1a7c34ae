<?php

/**
 * Debug script to test MT5 balance deduction functionality
 * Run this script to verify MT5 integration
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== MT5 Balance Deduction Debug ===\n\n";

// Test 1: Check MT5 database connection
echo "TEST 1: MT5 Database Connection\n";
echo "===============================\n";

try {
    $mt5Connection = DB::connection('mbf-dbmt5');
    $mt5Users = $mt5Connection->table('mt5_users')->count();
    echo "✅ MT5 database connected successfully\n";
    echo "Total MT5 users: {$mt5Users}\n";
} catch (\Exception $e) {
    echo "❌ MT5 database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 2: Find a test user with MT5 accounts
echo "\nTEST 2: Finding test user with MT5 accounts\n";
echo "==========================================\n";

$testUser = \App\Models\User::whereHas('wallets')
    ->whereNotNull('email')
    ->where('email', 'like', '%@gmail.com')
    ->first();

if (!$testUser) {
    echo "❌ No suitable test user found\n";
    exit(1);
}

echo "Test user: {$testUser->email} (ID: {$testUser->id})\n";

// Get MT5 accounts for this user
$mt5Accounts = \App\Models\Mt5Users::getAccounts($testUser->email);
echo "Total MT5 accounts found: " . $mt5Accounts->count() . "\n";

if ($mt5Accounts->isEmpty()) {
    echo "❌ No MT5 accounts found for test user\n";
    exit(1);
}

echo "\nMT5 Accounts for {$testUser->email}:\n";
foreach ($mt5Accounts as $account) {
    echo "  Login: {$account->Login} | Group: {$account->Group} | Balance: \${$account->Balance}\n";
}

// Test 3: Filter for real accounts
echo "\nTEST 3: Filtering for real accounts\n";
echo "===================================\n";

$realAccounts = $mt5Accounts->filter(function ($account) {
    $isReal = stripos($account->Group, 'real') !== false || 
             stripos($account->Group, 'Real') !== false ||
             (stripos($account->Group, 'demo') === false && stripos($account->Group, 'Demo') === false);
    return $isReal;
});

echo "Real accounts found: " . $realAccounts->count() . "\n";

if ($realAccounts->isEmpty()) {
    echo "❌ No real accounts found for withdrawal testing\n";
    
    // Show what accounts are available
    echo "\nAvailable accounts (all types):\n";
    foreach ($mt5Accounts as $account) {
        $isDemo = stripos($account->Group, 'demo') !== false || stripos($account->Group, 'Demo') !== false;
        $isReal = stripos($account->Group, 'real') !== false || stripos($account->Group, 'Real') !== false;
        $accountType = $isDemo ? 'DEMO' : ($isReal ? 'REAL' : 'OTHER');
        echo "  {$account->Login} | {$account->Group} | Type: {$accountType} | Balance: \${$account->Balance}\n";
    }
    exit(1);
}

echo "\nReal accounts for withdrawal:\n";
foreach ($realAccounts as $account) {
    echo "  Login: {$account->Login} | Group: {$account->Group} | Balance: \${$account->Balance}\n";
}

// Test 4: Test MT5Service initialization
echo "\nTEST 4: MT5Service initialization\n";
echo "=================================\n";

try {
    $mt5Service = new \App\Services\MT5Service();
    echo "✅ MT5Service instantiated successfully\n";
    
    // Check environment variables
    $pythonExe = env('PYTHON_EXE');
    $pythonScript = env('PYTHON_SCRIPT');
    
    echo "Python executable: " . ($pythonExe ?: 'NOT SET') . "\n";
    echo "Python script: " . ($pythonScript ?: 'NOT SET') . "\n";
    
    if (!$pythonExe || !$pythonScript) {
        echo "❌ Python environment variables not properly configured\n";
        echo "Please check .env file for PYTHON_EXE and PYTHON_SCRIPT settings\n";
    }
    
} catch (\Exception $e) {
    echo "❌ MT5Service initialization failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 5: Test small balance deduction (if real account has sufficient balance)
echo "\nTEST 5: Test balance deduction\n";
echo "==============================\n";

$accountWithBalance = $realAccounts->filter(function ($account) {
    return $account->Balance > 1.00; // At least $1
})->first();

if (!$accountWithBalance) {
    echo "❌ No real accounts with sufficient balance for testing\n";
    echo "All real account balances:\n";
    foreach ($realAccounts as $account) {
        echo "  {$account->Login}: \${$account->Balance}\n";
    }
} else {
    echo "Testing with account: {$accountWithBalance->Login} (Balance: \${$accountWithBalance->Balance})\n";
    
    // Test with very small amount
    $testAmount = 0.01; // 1 cent
    echo "Attempting to deduct \${$testAmount} from account {$accountWithBalance->Login}\n";
    
    try {
        $result = $mt5Service->deductBalanceFromUserAccounts($testUser, $testAmount, "Test deduction - Debug script");
        
        echo "\nMT5 Deduction Result:\n";
        echo "Success: " . ($result['success'] ? 'YES' : 'NO') . "\n";
        echo "Message: " . $result['message'] . "\n";
        echo "Accounts updated: " . $result['accounts_updated'] . "\n";
        echo "Total accounts: " . $result['total_accounts'] . "\n";
        
        if (isset($result['amount_deducted'])) {
            echo "Amount deducted: \${$result['amount_deducted']}\n";
        }
        
        if (isset($result['errors']) && !empty($result['errors'])) {
            echo "Errors:\n";
            foreach ($result['errors'] as $error) {
                echo "  - {$error}\n";
            }
        }
        
        if ($result['success']) {
            echo "✅ MT5 balance deduction test SUCCESSFUL\n";
            
            // Add the balance back
            echo "\nAdding balance back...\n";
            $addResult = $mt5Service->addBalanceToUserAccounts($testUser, $testAmount, "Test refund - Debug script");
            
            if ($addResult['success']) {
                echo "✅ Balance refunded successfully\n";
            } else {
                echo "❌ Balance refund failed: " . $addResult['message'] . "\n";
            }
        } else {
            echo "❌ MT5 balance deduction test FAILED\n";
        }
        
    } catch (\Exception $e) {
        echo "❌ Exception during MT5 balance deduction test: " . $e->getMessage() . "\n";
        echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    }
}

// Test 6: Check Python script execution directly
echo "\nTEST 6: Direct Python script test\n";
echo "=================================\n";

if ($pythonExe && $pythonScript && $accountWithBalance) {
    echo "Testing direct Python script execution...\n";
    
    $command = sprintf(
        '%s %s get_balance --login %d',
        escapeshellarg($pythonExe),
        escapeshellarg($pythonScript),
        $accountWithBalance->Login
    );
    
    echo "Command: {$command}\n";
    
    try {
        $output = shell_exec($command . ' 2>&1');
        echo "Python script output:\n";
        echo $output . "\n";
        
        if (strpos($output, 'success') !== false) {
            echo "✅ Python script execution appears successful\n";
        } else {
            echo "❌ Python script execution may have issues\n";
        }
        
    } catch (\Exception $e) {
        echo "❌ Python script execution failed: " . $e->getMessage() . "\n";
    }
} else {
    echo "Skipping Python script test - missing configuration or test account\n";
}

echo "\n=== MT5 Debug Complete ===\n";
