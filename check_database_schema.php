<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 CHECKING DATABASE SCHEMA AND IB STATUS\n";
echo "=========================================\n";

// Check the users table schema
echo "\n📊 Users table schema for IB-related fields:\n";
$columns = \DB::select("DESCRIBE users");
$ibColumns = array_filter($columns, function($col) {
    return strpos(strtolower($col->Field), 'ib') !== false || 
           strpos(strtolower($col->Field), 'partner') !== false;
});

foreach ($ibColumns as $col) {
    echo "   - {$col->Field}: {$col->Type} (Default: {$col->Default}, Null: {$col->Null})\n";
}

// Check actual data types and values
echo "\n🔍 Checking actual ib_status values in database:\n";
$statusValues = \DB::select("SELECT DISTINCT ib_status, COUNT(*) as count FROM users GROUP BY ib_status ORDER BY count DESC");
foreach ($statusValues as $status) {
    echo "   - ib_status = '{$status->ib_status}' ({$status->count} users)\n";
}

// Check partner values
echo "\n🔍 Checking partner values in database:\n";
$partnerValues = \DB::select("SELECT DISTINCT partner, COUNT(*) as count FROM users GROUP BY partner ORDER BY count DESC");
foreach ($partnerValues as $partner) {
    echo "   - partner = '{$partner->partner}' ({$partner->count} users)\n";
}

// Check specific combinations
echo "\n🔍 Checking partner=1 users with different ib_status:\n";
$combinations = \DB::select("
    SELECT partner, ib_status, COUNT(*) as count 
    FROM users 
    WHERE partner = 1 
    GROUP BY partner, ib_status
");

foreach ($combinations as $combo) {
    echo "   - partner={$combo->partner}, ib_status='{$combo->ib_status}' ({$combo->count} users)\n";
}

// Try to update with explicit values
echo "\n🔧 Attempting explicit update:\n";

// First, let's see what the current values are for hammedali
$user = \DB::select("SELECT id, email, partner, ib_status FROM users WHERE email = '<EMAIL>'")[0];
echo "Before update - ID: {$user->id}, partner: {$user->partner}, ib_status: '{$user->ib_status}'\n";

// Try updating just this one user
$affected = \DB::update("UPDATE users SET ib_status = ? WHERE email = ?", ['approved', '<EMAIL>']);
echo "Update affected {$affected} rows\n";

// Check the result
$user = \DB::select("SELECT id, email, partner, ib_status FROM users WHERE email = '<EMAIL>'")[0];
echo "After update - ID: {$user->id}, partner: {$user->partner}, ib_status: '{$user->ib_status}'\n";

// Test the isIb method now
$eloquentUser = \App\Models\User::where('email', '<EMAIL>')->first();
echo "isIb() result: " . ($eloquentUser->isIb() ? 'true' : 'false') . "\n";

// Update all partner=1 users
echo "\n🔧 Updating all partner=1 users:\n";
$affected = \DB::update("UPDATE users SET ib_status = ? WHERE partner = ?", ['approved', 1]);
echo "Updated {$affected} users with partner=1\n";

// Verify the updates
echo "\n✅ Final verification:\n";
$approvedUsers = \App\Models\User::where('partner', 1)->where('ib_status', 'approved')->take(5)->get();
foreach ($approvedUsers as $user) {
    echo "   - {$user->email}: partner={$user->partner}, ib_status='{$user->ib_status}', isIb()=" . ($user->isIb() ? 'true' : 'false') . "\n";
}

echo "\n✅ Database schema check and fix completed!\n";
