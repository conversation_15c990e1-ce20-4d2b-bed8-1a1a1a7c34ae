<?php $__env->startSection('panel'); ?>
<div class="row">
    <div class="col-lg-12">
        <div class="card b-radius--10">
            <div class="card-header">
                <div class="row g-3 align-items-center">
                    <div class="col-md-6">
                        <h5 class="card-title"><?php echo app('translator')->get('Pending Commissions'); ?></h5>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="<?php echo e(route('admin.commissions.index')); ?>" class="btn btn--primary btn-sm">
                                <i class="las la-arrow-left"></i> <?php echo app('translator')->get('Back to Overview'); ?>
                            </a>
                            <button type="button" class="btn btn--success btn-sm" id="bulkApproveBtn" disabled>
                                <i class="las la-check"></i> <?php echo app('translator')->get('Bulk Approve'); ?>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive--sm table-responsive">
                    <table class="table table--light style--two">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th><?php echo app('translator')->get('IB User'); ?></th>
                                <th><?php echo app('translator')->get('MT5 Login'); ?></th>
                                <th><?php echo app('translator')->get('Deal ID'); ?></th>
                                <th><?php echo app('translator')->get('Amount'); ?></th>
                                <th><?php echo app('translator')->get('Symbol'); ?></th>
                                <th><?php echo app('translator')->get('Date'); ?></th>
                                <th><?php echo app('translator')->get('Action'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $commissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $commission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>
                                    <input type="checkbox" class="form-check-input commission-checkbox" 
                                           value="<?php echo e($commission->id); ?>">
                                </td>
                                <td>
                                    <div>
                                        <strong><?php echo e($commission->firstname); ?> <?php echo e($commission->lastname); ?></strong><br>
                                        <small class="text-muted"><?php echo e($commission->email); ?></small>
                                    </div>
                                </td>
                                <td>
                                    <span class="fw-bold"><?php echo e($commission->mt5_login); ?></span>
                                </td>
                                <td>
                                    <span class="badge badge--dark"><?php echo e($commission->mt5_deal_id); ?></span>
                                </td>
                                <td>
                                    <span class="fw-bold text-success">$<?php echo e(number_format($commission->commission_amount, 2)); ?></span>
                                    <br><small class="text-muted">Level <?php echo e($commission->level); ?></small>
                                </td>
                                <td>
                                    <span class="badge badge--info"><?php echo e($commission->symbol ?: 'N/A'); ?></span>
                                    <br><small class="text-muted"><?php echo e(number_format($commission->volume, 2)); ?> lots</small>
                                </td>
                                <td>
                                    <div>
                                        <strong><?php echo e(showDateTime($commission->deal_time)); ?></strong><br>
                                        <small class="text-muted"><?php echo e(\Carbon\Carbon::parse($commission->deal_time)->diffForHumans()); ?></small>
                                    </div>
                                </td>
                                <td>
                                    <div class="button--group">
                                        <button type="button" class="btn btn-sm btn-outline--success approve-btn" 
                                                data-id="<?php echo e($commission->id); ?>">
                                            <i class="las la-check"></i> <?php echo app('translator')->get('Approve'); ?>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline--danger reject-btn" 
                                                data-id="<?php echo e($commission->id); ?>">
                                            <i class="las la-times"></i> <?php echo app('translator')->get('Reject'); ?>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="8" class="text-center"><?php echo app('translator')->get('No pending commissions found'); ?></td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <?php if($commissions->hasPages()): ?>
            <div class="card-footer py-4">
                <?php echo e($commissions->links()); ?>

            </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script'); ?>
<script>
$(document).ready(function() {
    // Select all functionality
    $('#selectAll').on('change', function() {
        $('.commission-checkbox').prop('checked', this.checked);
        toggleBulkApproveBtn();
    });

    $('.commission-checkbox').on('change', function() {
        toggleBulkApproveBtn();
    });

    function toggleBulkApproveBtn() {
        const checkedCount = $('.commission-checkbox:checked').length;
        $('#bulkApproveBtn').prop('disabled', checkedCount === 0);
    }

    // Bulk approve
    $('#bulkApproveBtn').on('click', function() {
        const selectedIds = $('.commission-checkbox:checked').map(function() {
            return this.value;
        }).get();

        if (selectedIds.length === 0) return;

        if (confirm(`Are you sure you want to approve ${selectedIds.length} commissions?`)) {
            $.post('<?php echo e(route("admin.commissions.bulk.approve")); ?>', {
                _token: '<?php echo e(csrf_token()); ?>',
                commission_ids: selectedIds
            }).done(function(response) {
                location.reload();
            }).fail(function() {
                alert('Failed to approve commissions');
            });
        }
    });

    // Individual approve
    $('.approve-btn').on('click', function() {
        const id = $(this).data('id');
        if (confirm('Are you sure you want to approve this commission?')) {
            $.post(`<?php echo e(route('admin.commissions.approve', '')); ?>/${id}`, {
                _token: '<?php echo e(csrf_token()); ?>'
            }).done(function(response) {
                location.reload();
            }).fail(function() {
                alert('Failed to approve commission');
            });
        }
    });

    // Individual reject
    $('.reject-btn').on('click', function() {
        const id = $(this).data('id');
        const reason = prompt('Enter rejection reason (optional):');
        if (reason !== null) {
            $.post(`<?php echo e(route('admin.commissions.reject', '')); ?>/${id}`, {
                _token: '<?php echo e(csrf_token()); ?>',
                reason: reason
            }).done(function(response) {
                location.reload();
            }).fail(function() {
                alert('Failed to reject commission');
            });
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\mbf.mybrokerforex.com-31052025\resources\views/admin/commissions/pending.blade.php ENDPATH**/ ?>