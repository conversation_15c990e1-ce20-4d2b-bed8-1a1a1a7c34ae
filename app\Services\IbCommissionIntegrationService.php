<?php

namespace App\Services;

use App\Models\User;
use App\Models\IbCommission;
use App\Models\IbLevel;
use App\Models\RebateRule;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class IbCommissionIntegrationService
{
    /**
     * Sync real-time commission data from MT5 deals table
     */
    public function syncCommissionData($limit = 1000)
    {
        try {
            Log::info("Starting MT5 commission data sync");

            // Get recent deals from MT5 database
            $recentDeals = $this->getRecentMT5Deals($limit);
            
            $processedCount = 0;
            $commissionTotal = 0;

            foreach ($recentDeals as $deal) {
                if ($this->processDealCommission($deal)) {
                    $processedCount++;
                    $commissionTotal += $deal->Commission ?? 0;
                }
            }

            Log::info("MT5 commission sync completed", [
                'processed_deals' => $processedCount,
                'total_commission' => $commissionTotal
            ]);

            return [
                'success' => true,
                'processed' => $processedCount,
                'commission_total' => $commissionTotal
            ];

        } catch (\Exception $e) {
            Log::error("MT5 commission sync error: " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Get recent deals from MT5 database
     */
    private function getRecentMT5Deals($limit = 1000)
    {
        return DB::connection('mbf-dbmt5')
            ->table('mt5_deals_2025')
            ->select([
                'Deal',
                'Login',
                'Time',
                'Symbol',
                'Action',
                'VolumeExt as Volume', // MT5 uses VolumeExt
                'Price',
                'Commission',
                'Storage', // MT5 uses Storage instead of Swap
                'Profit',
                'Comment'
            ])
            ->where('Time', '>=', Carbon::now()->subDays(7))
            ->where('Action', '!=', 2) // Exclude balance operations
            ->whereNotNull('Commission')
            ->where('Commission', '!=', 0)
            ->orderBy('Time', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Process individual deal for commission calculation
     */
    private function processDealCommission($deal)
    {
        try {
            // Find the trader user by MT5 login
            $trader = User::where('mt5_login', $deal->Login)->first();
            
            if (!$trader || !$trader->ref_by) {
                return false; // No referrer, no commission
            }

            // Get the IB (referrer)
            $ib = User::find($trader->ref_by);
            
            if (!$ib || !$ib->isIb()) {
                return false; // Referrer is not an IB
            }

            // Calculate commission based on deal volume and rebate rules
            $commission = $this->calculateCommissionAmount($deal, $ib);
            
            if ($commission <= 0) {
                return false;
            }

            // Create commission record
            $this->createCommissionRecord($deal, $trader, $ib, $commission);

            // Process multi-level commissions if applicable
            $this->processMultiLevelCommissions($deal, $ib, $commission);

            return true;

        } catch (\Exception $e) {
            Log::error("Error processing deal commission", [
                'deal_id' => $deal->Deal,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Calculate commission amount based on rebate rules
     */
    private function calculateCommissionAmount($deal, $ib)
    {
        // Get rebate rule for this symbol and IB group
        $rebateRule = RebateRule::getRebateForSymbol($deal->Symbol, $ib->ib_group_id);
        
        if (!$rebateRule) {
            return 0;
        }

        // Convert volume to lots (MT5 volume is in 0.01 lots)
        $volumeInLots = $deal->Volume / 100;

        // Check if volume meets minimum requirements
        if (!$rebateRule->isVolumeValid($volumeInLots)) {
            return 0;
        }

        // Calculate base commission
        $baseCommission = $rebateRule->calculateRebate($volumeInLots);

        // Apply IB level multiplier if applicable
        if ($ib->ib_group_id) {
            $ibGroup = $ib->ibGroup;
            if ($ibGroup) {
                $baseCommission *= $ibGroup->commission_multiplier;
            }
        }

        return round($baseCommission, 2);
    }

    /**
     * Create commission record in database
     */
    private function createCommissionRecord($deal, $trader, $ib, $commission)
    {
        // Check if commission already exists for this deal
        $existingCommission = IbCommission::where('mt5_deal_id', $deal->Deal)->first();
        
        if ($existingCommission) {
            return $existingCommission;
        }

        return IbCommission::create([
            'mt5_deal_id' => $deal->Deal,
            'from_user_id' => $trader->id,
            'to_ib_user_id' => $ib->id,
            'symbol' => $deal->Symbol,
            'volume' => $deal->Volume / 100, // Convert to lots
            'commission_amount' => $commission,
            'deal_time' => Carbon::parse($deal->Time),
            'status' => 'pending',
            'level' => 1,
            'mt5_login' => $deal->Login,
            'deal_profit' => $deal->Profit ?? 0,
            'deal_commission' => $deal->Commission ?? 0
        ]);
    }

    /**
     * Process multi-level commissions for IB hierarchy
     */
    private function processMultiLevelCommissions($deal, $primaryIb, $baseCommission)
    {
        $currentIb = $primaryIb;
        $level = 2;
        $maxLevels = 5; // Configurable maximum levels

        while ($currentIb->ib_parent_id && $level <= $maxLevels) {
            $parentIb = User::find($currentIb->ib_parent_id);
            
            if (!$parentIb || !$parentIb->isIb()) {
                break;
            }

            // Get level-specific commission percentage
            $ibLevel = IbLevel::getByLevel($level);
            if (!$ibLevel) {
                break;
            }

            // Calculate commission for this level
            $levelCommission = $baseCommission * ($ibLevel->commission_percent / 100);
            
            if ($levelCommission > 0) {
                IbCommission::create([
                    'mt5_deal_id' => $deal->Deal,
                    'from_user_id' => $deal->Login, // Original trader
                    'to_ib_user_id' => $parentIb->id,
                    'symbol' => $deal->Symbol,
                    'volume' => $deal->Volume / 100,
                    'commission_amount' => $levelCommission,
                    'deal_time' => Carbon::parse($deal->Time),
                    'status' => 'pending',
                    'level' => $level,
                    'mt5_login' => $deal->Login,
                    'parent_commission_id' => null // Could link to primary commission
                ]);
            }

            $currentIb = $parentIb;
            $level++;
        }
    }

    /**
     * Get real-time commission statistics for IB dashboard
     */
    public function getIbCommissionStats($ibUserId, $period = '30days')
    {
        $startDate = match($period) {
            'today' => Carbon::today(),
            '7days' => Carbon::now()->subDays(7),
            '30days' => Carbon::now()->subDays(30),
            '90days' => Carbon::now()->subDays(90),
            default => Carbon::now()->subDays(30)
        };

        return [
            'total_commission' => IbCommission::where('to_ib_user_id', $ibUserId)
                ->where('created_at', '>=', $startDate)
                ->sum('commission_amount'),
            
            'total_trades' => IbCommission::where('to_ib_user_id', $ibUserId)
                ->where('created_at', '>=', $startDate)
                ->count(),
            
            'total_volume' => IbCommission::where('to_ib_user_id', $ibUserId)
                ->where('created_at', '>=', $startDate)
                ->sum('volume'),
            
            'active_traders' => IbCommission::where('to_ib_user_id', $ibUserId)
                ->where('created_at', '>=', $startDate)
                ->distinct('from_user_id')
                ->count('from_user_id')
        ];
    }

    /**
     * Get network commission flow for hierarchical display
     */
    public function getNetworkCommissionFlow($ibUserId, $period = '30days')
    {
        $startDate = Carbon::now()->subDays(30);

        // Get direct commissions
        $directCommissions = IbCommission::where('to_ib_user_id', $ibUserId)
            ->where('level', 1)
            ->where('created_at', '>=', $startDate)
            ->with(['fromUser', 'toIbUser'])
            ->get();

        // Get sub-IB commissions
        $subIbCommissions = IbCommission::where('to_ib_user_id', $ibUserId)
            ->where('level', '>', 1)
            ->where('created_at', '>=', $startDate)
            ->with(['fromUser', 'toIbUser'])
            ->get();

        return [
            'direct_commissions' => $directCommissions,
            'sub_ib_commissions' => $subIbCommissions,
            'total_network_commission' => $directCommissions->sum('commission_amount') + $subIbCommissions->sum('commission_amount')
        ];
    }

    /**
     * Sync existing MT5 IB users with local database
     */
    public function syncMT5IbUsers()
    {
        try {
            Log::info("Starting MT5 IB users sync");

            $ibGroups = [
                'real\\Affiliates',
                'real\\IB\\IB MAIN',
                'real\\IB\\IB SUB',
                'real\\Multi-IB\\Default',
                'real\\Multi-IB\\Level1',
                'real\\Multi-IB\\Level2',
                'real\\Multi-IB\\Level3',
                'real\\Multi-IB\\Level4',
                'real\\Multi-IB\\Level5'
            ];

            $mt5IbUsers = DB::connection('mbf-dbmt5')
                ->table('mt5_users')
                ->whereIn('Group', $ibGroups)
                ->select('Login', 'FirstName', 'LastName', 'Email', 'Group', 'Agent', 'Balance', 'Registration')
                ->get();

            $syncedCount = 0;
            $updatedCount = 0;

            foreach ($mt5IbUsers as $mt5User) {
                if (empty($mt5User->Email)) continue;

                // Find or create user in local database
                $localUser = User::where('email', $mt5User->Email)->first();

                if ($localUser) {
                    // Update existing user with IB status
                    $wasUpdated = $this->updateUserIbStatus($localUser, $mt5User);
                    if ($wasUpdated) $updatedCount++;
                } else {
                    // Create new user from MT5 data
                    $localUser = $this->createUserFromMT5($mt5User);
                    if ($localUser) $syncedCount++;
                }
            }

            Log::info("MT5 IB users sync completed", [
                'synced_new' => $syncedCount,
                'updated_existing' => $updatedCount,
                'total_processed' => $mt5IbUsers->count()
            ]);

            return [
                'success' => true,
                'synced_new' => $syncedCount,
                'updated_existing' => $updatedCount,
                'total_processed' => $mt5IbUsers->count()
            ];

        } catch (\Exception $e) {
            Log::error("MT5 IB users sync error: " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Update existing user with IB status from MT5
     */
    private function updateUserIbStatus($user, $mt5User)
    {
        $updated = false;

        // Update MT5 login if not set
        if (!$user->mt5_login && $mt5User->Login) {
            $user->mt5_login = $mt5User->Login;
            $updated = true;
        }

        // Set IB status based on MT5 group
        $ibType = $this->determineIbTypeFromGroup($mt5User->Group);
        if ($ibType && $user->ib_status !== 'approved') {
            $user->ib_status = 'approved';
            $user->ib_type = $ibType;
            $user->partner = 1; // Approved IB
            $user->ib_approved_at = now();
            $updated = true;
        }

        // Update agent relationship
        if ($mt5User->Agent && $mt5User->Agent != 0) {
            $parentIb = User::where('mt5_login', $mt5User->Agent)->first();
            if ($parentIb && !$user->ref_by) {
                $user->ref_by = $parentIb->id;
                $updated = true;
            }
        }

        if ($updated) {
            $user->save();
        }

        return $updated;
    }

    /**
     * Create new user from MT5 data
     */
    private function createUserFromMT5($mt5User)
    {
        try {
            $ibType = $this->determineIbTypeFromGroup($mt5User->Group);

            $user = User::create([
                'firstname' => $mt5User->FirstName ?: 'MT5',
                'lastname' => $mt5User->LastName ?: 'User',
                'username' => 'mt5_' . $mt5User->Login,
                'email' => $mt5User->Email,
                'country_code' => 'US',
                'mobile' => '+1234567890',
                'password' => bcrypt('password123'),
                'mt5_login' => $mt5User->Login,
                'ib_status' => $ibType ? 'approved' : null,
                'ib_type' => $ibType,
                'partner' => $ibType ? 1 : 0,
                'ib_approved_at' => $ibType ? now() : null,
                'kv' => 1, // Verified
                'ev' => 1, // Email verified
                'sv' => 1, // SMS verified
                'ts' => 0,
                'tv' => 1
            ]);

            // Set agent relationship
            if ($mt5User->Agent && $mt5User->Agent != 0) {
                $parentIb = User::where('mt5_login', $mt5User->Agent)->first();
                if ($parentIb) {
                    $user->ref_by = $parentIb->id;
                    $user->save();
                }
            }

            return $user;

        } catch (\Exception $e) {
            Log::error("Failed to create user from MT5 data", [
                'mt5_login' => $mt5User->Login,
                'email' => $mt5User->Email,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Determine IB type from MT5 group
     */
    private function determineIbTypeFromGroup($group)
    {
        $ibGroupMappings = [
            'real\\Affiliates' => 'master',
            'real\\IB\\IB MAIN' => 'master',
            'real\\IB\\IB SUB' => 'sub',
            'real\\Multi-IB\\Default' => 'master',
            'real\\Multi-IB\\Level1' => 'sub',
            'real\\Multi-IB\\Level2' => 'sub',
            'real\\Multi-IB\\Level3' => 'sub',
            'real\\Multi-IB\\Level4' => 'sub',
            'real\\Multi-IB\\Level5' => 'sub'
        ];

        return $ibGroupMappings[$group] ?? null;
    }

    /**
     * Get MT5 commission data for specific IB
     */
    public function getMT5CommissionData($mt5Login, $days = 30)
    {
        try {
            $startDate = Carbon::now()->subDays($days);

            $commissionDeals = DB::connection('mbf-dbmt5')
                ->table('mt5_deals_2025')
                ->where('Login', $mt5Login)
                ->where('Action', 18) // Commission action
                ->where('Time', '>=', $startDate)
                ->select('Deal', 'Login', 'Time', 'Profit as Commission', 'Comment')
                ->orderBy('Time', 'desc')
                ->get();

            $totalCommission = $commissionDeals->sum('Commission');
            $commissionCount = $commissionDeals->count();

            return [
                'total_commission' => $totalCommission,
                'commission_count' => $commissionCount,
                'recent_commissions' => $commissionDeals->take(10),
                'period_days' => $days
            ];

        } catch (\Exception $e) {
            Log::error("Failed to get MT5 commission data", [
                'mt5_login' => $mt5Login,
                'error' => $e->getMessage()
            ]);

            return [
                'total_commission' => 0,
                'commission_count' => 0,
                'recent_commissions' => collect(),
                'period_days' => $days
            ];
        }
    }
}
