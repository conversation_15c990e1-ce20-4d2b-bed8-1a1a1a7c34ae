<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\IbCommission;
use App\Services\MultiLevelIbCommissionService;
use App\Services\IbCommissionIntegrationService;
use App\Services\MT5CommissionSyncService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class MultiLevelIbTestController extends Controller
{
    protected $multiLevelService;
    protected $integrationService;
    protected $mt5SyncService;

    public function __construct(
        MultiLevelIbCommissionService $multiLevelService,
        IbCommissionIntegrationService $integrationService,
        MT5CommissionSyncService $mt5SyncService
    ) {
        $this->multiLevelService = $multiLevelService;
        $this->integrationService = $integrationService;
        $this->mt5SyncService = $mt5SyncService;
    }

    /**
     * PART 1 & 2: Comprehensive Multi-Level IB System Testing Dashboard
     */
    public function testDashboard()
    {
        $pageTitle = 'Multi-Level IB System Testing';

        // Get test hierarchy data
        $masterIB = User::where('mt5_login', '878046')->first();
        $subIB = User::where('mt5_login', '878010')->first();
        $client878023 = User::where('mt5_login', '878023')->first();
        $client878012 = User::where('mt5_login', '878012')->first();

        // Build hierarchy structure
        $hierarchyData = $this->buildTestHierarchy($masterIB);

        // Get commission summary
        $commissionSummary = $masterIB ? $this->multiLevelService->getHierarchyCommissionSummary($masterIB->id) : null;

        // Get recent MT5 deals for testing
        $recentDeals = $this->getRecentMT5Deals();

        return view('admin.users.multi_level_ib_test', compact(
            'pageTitle',
            'masterIB',
            'subIB',
            'client878023',
            'client878012',
            'hierarchyData',
            'commissionSummary',
            'recentDeals'
        ));
    }

    /**
     * Build test hierarchy structure for display
     */
    private function buildTestHierarchy($masterIB)
    {
        if (!$masterIB) {
            return null;
        }

        $hierarchy = [
            'id' => $masterIB->id,
            'name' => $masterIB->fullname,
            'mt5_login' => $masterIB->mt5_login,
            'ib_type' => $masterIB->ib_type ?? 'master',
            'level' => 0,
            'children' => []
        ];

        // Get direct referrals
        $directReferrals = User::where('ref_by', $masterIB->id)->get();

        foreach ($directReferrals as $referral) {
            $child = [
                'id' => $referral->id,
                'name' => $referral->fullname,
                'mt5_login' => $referral->mt5_login,
                'ib_type' => $referral->ib_status == 1 ? ($referral->ib_type ?? 'sub') : 'client',
                'level' => 1,
                'children' => []
            ];

            // Get sub-referrals if this is a Sub-IB
            if ($referral->ib_status == 1) {
                $subReferrals = User::where('ref_by', $referral->id)->get();
                
                foreach ($subReferrals as $subRef) {
                    // Get recent trades count for this user
                    $recentTradesCount = 0;
                    try {
                        $recentTradesCount = DB::connection('mbf-dbmt5')
                            ->table('mt5_deals_2025')
                            ->where('Login', $subRef->mt5_login)
                            ->where('Time', '>=', \Carbon\Carbon::now()->subDays(7))
                            ->count();
                    } catch (\Exception $e) {
                        // Ignore MT5 connection errors
                    }

                    $child['children'][] = [
                        'id' => $subRef->id,
                        'name' => $subRef->fullname,
                        'mt5_login' => $subRef->mt5_login,
                        'ib_type' => $subRef->ib_status == 1 ? ($subRef->ib_type ?? 'sub') : 'client',
                        'level' => 2,
                        'mt5_balance' => $subRef->mt5_balance ?? 0,
                        'recent_trades' => $recentTradesCount,
                        'children' => []
                    ];
                }
            }

            $hierarchy['children'][] = $child;
        }

        return $hierarchy;
    }

    /**
     * Get recent MT5 deals for testing
     */
    private function getRecentMT5Deals($limit = 10)
    {
        try {
            return DB::connection('mbf-dbmt5')
                ->table('mt5_deals_2025')
                ->whereIn('Login', ['878046', '878010', '878023', '878012'])
                ->where('Time', '>=', Carbon::now()->subDays(7))
                ->orderBy('Time', 'desc')
                ->limit($limit)
                ->get();
        } catch (\Exception $e) {
            return collect();
        }
    }

    /**
     * PART 4: Test real-time commission calculation
     */
    public function testRealTimeCommission(Request $request)
    {
        $mt5Login = $request->get('mt5_login', '878012'); // Default to Client 878012

        // Use 878012's actual profitable trade for testing
        $testTradeData = [
            'deal_id' => 'REAL_TIME_' . time(),
            'mt5_login' => $mt5Login,
            'symbol' => 'GOLDUSD.p',
            'volume' => 2.0, // 2 lots
            'profit' => 102.00,
            'commission' => 0,
            'time' => now()->toISOString()
        ];

        $result = $this->multiLevelService->processMultiLevelCommission($testTradeData);

        // Get the created commission records
        $commissions = [];
        if ($result) {
            $commissionRecords = \App\Models\IbCommission::where('trade_id', $testTradeData['deal_id'])->get();
            foreach ($commissionRecords as $commission) {
                $ib = \App\Models\User::find($commission->to_ib_user_id);
                $commissions[] = [
                    'level' => $commission->level,
                    'ib_name' => $ib->fullname,
                    'amount' => $commission->commission_amount,
                    'rate' => $commission->commission_rate
                ];
            }
        }

        return response()->json([
            'success' => $result,
            'message' => $result ? 'Commission processed successfully for 878012\'s trade' : 'Commission processing failed',
            'trade_data' => $testTradeData,
            'commission_records' => $commissions,
            'commission_breakdown' => [
                'base_commission' => '$10.00 (2 lots × $5)',
                'sub_ib_commission' => '$3.00 (30% to Hayat hayat)',
                'master_ib_commission' => '$5.00 (50% to Hameed Ali)',
                'total_distributed' => '$8.00'
            ]
        ]);
    }

    /**
     * Process commission for specific trade
     */
    public function processTradeCommission(Request $request)
    {
        $request->validate([
            'mt5_login' => 'required',
            'symbol' => 'required',
            'volume' => 'required|numeric',
            'profit' => 'required|numeric'
        ]);

        $tradeData = [
            'mt5_login' => $request->mt5_login,
            'symbol' => $request->symbol,
            'volume' => $request->volume,
            'profit' => $request->profit,
            'commission' => $request->commission ?? 0,
            'time' => now(),
            'deal_id' => 'TEST_' . time()
        ];

        $result = $this->multiLevelService->processMultiLevelCommission($tradeData);

        return response()->json([
            'success' => $result,
            'message' => $result ? 'Commission processed successfully' : 'Commission processing failed',
            'trade_data' => $tradeData
        ]);
    }

    /**
     * Sync MT5 commission data
     */
    public function syncMT5Commissions(Request $request)
    {
        $days = $request->get('days', 7);
        
        try {
            $result = $this->integrationService->syncCommissionData(1000);
            
            return response()->json([
                'success' => true,
                'message' => 'MT5 commission sync completed',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'MT5 sync failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get commission breakdown for hierarchy
     */
    public function getCommissionBreakdown(Request $request)
    {
        $masterIbId = $request->get('master_ib_id');
        
        if (!$masterIbId) {
            return response()->json([
                'success' => false,
                'message' => 'Master IB ID required'
            ]);
        }

        $summary = $this->multiLevelService->getHierarchyCommissionSummary($masterIbId);

        return response()->json([
            'success' => true,
            'data' => $summary
        ]);
    }

    /**
     * Create test trade for commission testing
     */
    public function createTestTrade(Request $request)
    {
        $request->validate([
            'mt5_login' => 'required',
            'symbol' => 'required',
            'volume' => 'required|numeric|min:0.01',
            'profit' => 'required|numeric'
        ]);

        // Simulate a trade
        $tradeData = [
            'deal_id' => 'TEST_' . time() . '_' . $request->mt5_login,
            'mt5_login' => $request->mt5_login,
            'symbol' => $request->symbol,
            'volume' => $request->volume,
            'profit' => $request->profit,
            'commission' => $request->commission ?? 0,
            'time' => now()->toISOString()
        ];

        // Process commission
        $result = $this->multiLevelService->processMultiLevelCommission($tradeData);

        // Get updated commission summary
        $user = User::where('mt5_login', $request->mt5_login)->first();
        $masterIB = $this->findMasterIB($user);
        
        $commissionSummary = $masterIB ? $this->multiLevelService->getHierarchyCommissionSummary($masterIB->id) : null;

        return response()->json([
            'success' => $result,
            'message' => $result ? 'Test trade processed successfully' : 'Test trade processing failed',
            'trade_data' => $tradeData,
            'commission_summary' => $commissionSummary
        ]);
    }

    /**
     * Find the Master IB for a given user
     */
    private function findMasterIB($user)
    {
        if (!$user) {
            return null;
        }

        $current = $user;
        
        // Walk up the hierarchy to find the Master IB
        while ($current->ref_by) {
            $parent = User::find($current->ref_by);
            if (!$parent) {
                break;
            }
            
            if ($parent->ib_status == 1 && ($parent->ib_type == 'master' || !$parent->ref_by)) {
                return $parent;
            }
            
            $current = $parent;
        }

        // If current user is a Master IB
        if ($current->ib_status == 1 && ($current->ib_type == 'master' || !$current->ref_by)) {
            return $current;
        }

        return null;
    }

    /**
     * Get MT5 database connection status
     */
    public function getMT5ConnectionStatus()
    {
        try {
            $connection = DB::connection('mbf-dbmt5');
            $result = $connection->select('SELECT COUNT(*) as count FROM mt5_deals_2025 LIMIT 1');
            
            return response()->json([
                'success' => true,
                'message' => 'MT5 database connection successful',
                'deals_count' => $result[0]->count ?? 0
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'MT5 database connection failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get live MT5 data for specific user
     */
    public function getLiveMT5Data(Request $request)
    {
        $mt5Login = $request->get('mt5_login');
        
        if (!$mt5Login) {
            return response()->json([
                'success' => false,
                'message' => 'MT5 login required'
            ]);
        }

        try {
            $deals = DB::connection('mbf-dbmt5')
                ->table('mt5_deals_2025')
                ->where('Login', $mt5Login)
                ->where('Time', '>=', Carbon::now()->subDays(30))
                ->orderBy('Time', 'desc')
                ->limit(20)
                ->get();

            return response()->json([
                'success' => true,
                'data' => $deals,
                'count' => $deals->count()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get MT5 data: ' . $e->getMessage()
            ]);
        }
    }
}
