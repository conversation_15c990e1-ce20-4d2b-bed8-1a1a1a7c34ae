<?php

namespace App\Services;

use App\Models\User;
use App\Models\IbCommission;
use App\Models\IbLevel;
use App\Models\RebateRule;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class MultiLevelIbCommissionService
{
    /**
     * PART 2: Enhanced Multi-Level Commission Distribution
     * Process commission for the test hierarchy: 878046 → 878010 → 878012
     */
    public function processMultiLevelCommission($tradeData)
    {
        try {
            // TASK 3: Check for duplicate commission processing
            if ($this->isDuplicateCommission($tradeData['deal_id'])) {
                Log::info("Commission already processed for deal: " . $tradeData['deal_id']);
                return false;
            }

            // Find the trader who made the trade
            $trader = User::where('mt5_login', $tradeData['mt5_login'])->first();

            if (!$trader) {
                Log::warning("Trader not found for MT5 login: " . $tradeData['mt5_login']);
                return false;
            }

            Log::info("Processing multi-level commission", [
                'trader' => $trader->fullname,
                'mt5_login' => $trader->mt5_login,
                'trade_data' => $tradeData
            ]);

            // Calculate base commission amount
            $baseCommission = $this->calculateBaseCommission($tradeData, $trader);

            if ($baseCommission <= 0) {
                Log::info("No commission calculated for trade");
                return false;
            }

            // Process commission distribution through the hierarchy
            $result = $this->distributeCommissionThroughHierarchy($trader, $tradeData, $baseCommission);

            Log::info("Multi-level commission processing completed successfully");
            return $result;

        } catch (\Exception $e) {
            Log::error("Multi-level commission processing failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Distribute commission through the IB hierarchy
     */
    private function distributeCommissionThroughHierarchy($trader, $tradeData, $baseCommission)
    {
        $currentUser = $trader;
        $level = 1;
        $remainingCommission = $baseCommission;
        $commissionsCreated = 0;

        // Walk up the referral hierarchy
        while ($currentUser->ref_by && $level <= 5) { // Max 5 levels
            $ib = User::find($currentUser->ref_by);

            if (!$ib || !$ib->isIb()) {
                Log::info("Breaking hierarchy walk - IB not found or not active", [
                    'ib_id' => $currentUser->ref_by,
                    'ib_found' => $ib ? 'yes' : 'no',
                    'is_ib' => $ib ? ($ib->isIb() ? 'yes' : 'no') : 'n/a'
                ]);
                break;
            }

            // Get commission percentage for this level
            $commissionPercent = $this->getCommissionPercentForLevel($ib, $level);

            if ($commissionPercent > 0) {
                $levelCommission = $baseCommission * ($commissionPercent / 100);

                try {
                    // Create commission record
                    $commission = IbCommission::create([
                        'trade_id' => $tradeData['deal_id'] ?? 'TRADE_' . time(),
                        'mt5_deal_id' => $tradeData['deal_id'] ?? null,
                        'from_user_id' => $trader->id,
                        'to_ib_user_id' => $ib->id,
                        'mt5_login' => $trader->mt5_login,
                        'symbol' => $tradeData['symbol'],
                        'volume' => $tradeData['volume'],
                        'commission_amount' => $levelCommission,
                        'commission_rate' => $commissionPercent,
                        'level' => $level,
                        'status' => 'paid', // TASK 1 FIX: Auto-approve and pay commission for real-time updates
                        'deal_time' => Carbon::parse($tradeData['time'] ?? now()),
                        'deal_profit' => $tradeData['profit'] ?? 0,
                        'deal_commission' => $tradeData['commission'] ?? 0,
                        'paid_at' => now(), // TASK 1 FIX: Mark as paid immediately
                        'processed_at' => now(), // TASK 1 FIX: Mark as processed immediately
                        'notes' => "Level {$level} commission for {$ib->ib_type} IB - Auto-paid"
                    ]);

                    // TASK 1 FIX: Real-time MT5 balance update
                    $this->updateMT5BalanceRealTime($ib, $levelCommission, "Commission - Deal {$tradeData['deal_id']}");

                    $commissionsCreated++;

                    Log::info("Commission distributed", [
                        'level' => $level,
                        'ib' => $ib->fullname,
                        'ib_type' => $ib->ib_type,
                        'commission_amount' => $levelCommission,
                        'commission_percent' => $commissionPercent,
                        'commission_id' => $commission->id
                    ]);

                    $remainingCommission -= $levelCommission;

                } catch (\Exception $e) {
                    Log::error("Failed to create commission record", [
                        'level' => $level,
                        'ib_id' => $ib->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            $currentUser = $ib;
            $level++;
        }

        Log::info("Commission distribution completed", [
            'commissions_created' => $commissionsCreated,
            'remaining_commission' => $remainingCommission
        ]);

        return $commissionsCreated > 0;
    }

    /**
     * Calculate base commission amount from trade data
     */
    private function calculateBaseCommission($tradeData, $trader)
    {
        // Get rebate rule for this symbol
        $rebateRule = RebateRule::where('symbol', $tradeData['symbol'])
            ->where('status', 1)
            ->first();

        if (!$rebateRule) {
            // Default commission calculation if no specific rule
            $volumeInLots = $tradeData['volume'];
            return $volumeInLots * 5; // $5 per lot default
        }

        return $rebateRule->calculateRebate($tradeData['volume']);
    }

    /**
     * Get commission percentage for specific level and IB type
     */
    private function getCommissionPercentForLevel($ib, $level)
    {
        // PART 2: Commission structure based on IB type and hierarchy level
        // Level 1 = Sub-IB (direct parent), Level 2 = Master IB (grandparent)

        if ($level == 1) {
            // First level - Sub-IB gets 30%
            return 30;
        } elseif ($level == 2) {
            // Second level - Master IB gets 50%
            return 50;
        } elseif ($level == 3) {
            // Third level - gets 20%
            return 20;
        }

        return 0; // No commission for levels beyond 3
    }

    /**
     * Pay commission to IB account
     */
    private function payCommissionToIb($commission)
    {
        try {
            $ib = $commission->toIbUser;
            
            // Add commission to IB's balance
            $ib->balance += $commission->commission_amount;
            $ib->save();

            // Mark commission as processed
            $commission->update([
                'status' => 'processed',
                'paid_at' => now(),
                'processed_at' => now()
            ]);

            // Create transaction record
            $this->createCommissionTransaction($commission);

            Log::info("Commission paid to IB", [
                'ib_id' => $ib->id,
                'ib_name' => $ib->fullname,
                'commission_amount' => $commission->commission_amount,
                'new_balance' => $ib->balance
            ]);

        } catch (\Exception $e) {
            Log::error("Failed to pay commission to IB: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Create transaction record for commission payment
     */
    private function createCommissionTransaction($commission)
    {
        // This would create a transaction record in your transactions table
        // Implementation depends on your transaction model structure
    }

    /**
     * PART 4: Real-time commission testing for user 878012
     */
    public function testRealTimeCommission($mt5Login = '878012')
    {
        try {
            // Get recent trade data from MT5 for testing
            $recentTrade = $this->getRecentMT5Trade($mt5Login);
            
            if (!$recentTrade) {
                return [
                    'success' => false,
                    'message' => 'No recent trades found for MT5 login: ' . $mt5Login
                ];
            }

            // Process the commission
            $result = $this->processMultiLevelCommission($recentTrade);

            return [
                'success' => $result,
                'trade_data' => $recentTrade,
                'message' => $result ? 'Commission processed successfully' : 'Commission processing failed'
            ];

        } catch (\Exception $e) {
            Log::error("Real-time commission test failed: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Test failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get recent trade from MT5 database for testing
     */
    private function getRecentMT5Trade($mt5Login)
    {
        try {
            // For John Doe (877753), use the profitable trade we identified
            if ($mt5Login == '877753') {
                return [
                    'deal_id' => '3125048',
                    'mt5_login' => '877753',
                    'symbol' => 'GOLDUSD.p',
                    'volume' => 2.0, // 200/100 = 2 lots
                    'profit' => 102.00,
                    'commission' => 0,
                    'time' => '2025-06-13 21:31:58'
                ];
            }

            // For other users, get from database
            $trade = DB::connection('mbf-dbmt5')
                ->table('mt5_deals_2025')
                ->where('Login', $mt5Login)
                ->where('Profit', '>', 0) // Only profitable trades
                ->orderBy('Time', 'desc')
                ->first();

            if (!$trade) {
                return null;
            }

            return [
                'deal_id' => $trade->Deal,
                'mt5_login' => $trade->Login,
                'symbol' => $trade->Symbol,
                'volume' => $trade->Volume / 100, // Convert to lots
                'profit' => $trade->Profit,
                'commission' => $trade->Commission,
                'time' => $trade->Time
            ];

        } catch (\Exception $e) {
            Log::error("Failed to get recent MT5 trade: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get commission summary for hierarchy analysis
     */
    public function getHierarchyCommissionSummary($masterIbId)
    {
        $masterIb = User::findOrFail($masterIbId);
        
        // Get all commissions in the hierarchy
        $directCommissions = IbCommission::where('to_ib_user_id', $masterIbId)
            ->where('level', 1)
            ->sum('commission_amount');

        $multiLevelCommissions = IbCommission::where('to_ib_user_id', $masterIbId)
            ->where('level', '>', 1)
            ->sum('commission_amount');

        // Get Sub-IB commissions
        $subIbs = User::where('ref_by', $masterIbId)
            ->where('ib_status', 1)
            ->get();

        $subIbCommissions = 0;
        foreach ($subIbs as $subIb) {
            $subIbCommissions += IbCommission::where('to_ib_user_id', $subIb->id)->sum('commission_amount');
        }

        return [
            'master_ib' => $masterIb->fullname,
            'direct_commissions' => $directCommissions,
            'multi_level_commissions' => $multiLevelCommissions,
            'sub_ib_commissions' => $subIbCommissions,
            'total_commissions' => $directCommissions + $multiLevelCommissions,
            'hierarchy_total' => $directCommissions + $multiLevelCommissions + $subIbCommissions
        ];
    }

    /**
     * CRITICAL BUG FIX: Check if commission has already been processed for this deal
     */
    private function isDuplicateCommission($dealId)
    {
        // CRITICAL BUG FIX: Only check trade_id to avoid false positives with mt5_deal_id = 0
        return IbCommission::where('trade_id', $dealId)
            ->whereNotIn('status', ['cancelled']) // Ignore cancelled commissions
            ->exists();
    }

    /**
     * TASK 3: Get commission history for a specific deal
     */
    public function getCommissionHistory($dealId)
    {
        return IbCommission::where('trade_id', $dealId)
            ->orWhere('mt5_deal_id', $dealId)
            ->with(['fromUser', 'toIbUser'])
            ->orderBy('level')
            ->get();
    }

    /**
     * TASK 3: Validate commission processing eligibility
     */
    public function validateCommissionEligibility($tradeData)
    {
        $validations = [
            'duplicate_check' => !$this->isDuplicateCommission($tradeData['deal_id']),
            'trader_exists' => User::where('mt5_login', $tradeData['mt5_login'])->exists(),
            'profitable_trade' => $tradeData['profit'] > 0,
            'valid_volume' => $tradeData['volume'] > 0,
            'valid_symbol' => !empty($tradeData['symbol'])
        ];

        return [
            'eligible' => !in_array(false, $validations),
            'validations' => $validations,
            'reasons' => array_keys(array_filter($validations, function($v) { return !$v; }))
        ];
    }

    /**
     * TASK 3: Force reprocess commission (admin override)
     */
    public function forceReprocessCommission($dealId, $adminUserId = null)
    {
        try {
            // Mark existing commissions as cancelled
            IbCommission::where('trade_id', $dealId)
                ->orWhere('mt5_deal_id', $dealId)
                ->update([
                    'status' => 'cancelled',
                    'notes' => 'Cancelled for reprocessing by admin' . ($adminUserId ? " (ID: {$adminUserId})" : ''),
                    'updated_at' => now()
                ]);

            // Get original trade data from MT5
            $tradeData = $this->getTradeDataFromMT5($dealId);

            if (!$tradeData) {
                Log::error("Cannot reprocess commission - trade data not found for deal: {$dealId}");
                return false;
            }

            // Process commission again
            return $this->processMultiLevelCommission($tradeData);

        } catch (\Exception $e) {
            Log::error("Force reprocess commission failed", [
                'deal_id' => $dealId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * TASK 3: Get trade data from MT5 database
     */
    private function getTradeDataFromMT5($dealId)
    {
        try {
            $trade = DB::connection('mbf-dbmt5')
                ->table('mt5_deals_2025')
                ->where('Deal', $dealId)
                ->first();

            if (!$trade) {
                return null;
            }

            return [
                'deal_id' => $trade->Deal,
                'mt5_login' => $trade->Login,
                'symbol' => $trade->Symbol,
                'volume' => $trade->Volume / 100, // Convert to lots
                'profit' => $trade->Profit,
                'commission' => $trade->Commission ?? 0,
                'time' => $trade->Time
            ];

        } catch (\Exception $e) {
            Log::error("Failed to get trade data from MT5", [
                'deal_id' => $dealId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get commission statistics
     */
    public function getCommissionStats($userId = null)
    {
        $query = IbCommission::query();

        if ($userId) {
            $query->where('to_ib_user_id', $userId);
        }

        return [
            'total_commissions' => $query->sum('commission_amount'),
            'pending_commissions' => $query->where('status', 'pending')->sum('commission_amount'),
            'paid_commissions' => $query->where('status', 'paid')->sum('commission_amount'),
            'total_trades' => $query->count(),
            'this_month' => $query->whereMonth('created_at', now()->month)->sum('commission_amount')
        ];
    }

    /**
     * TASK 1 FIX: Real-time MT5 balance update for commission earnings
     */
    private function updateMT5BalanceRealTime($ib, $commissionAmount, $comment)
    {
        try {
            if (!$ib->mt5_login) {
                Log::warning("IB has no MT5 login for balance update", [
                    'ib_id' => $ib->id,
                    'ib_name' => $ib->fullname
                ]);
                return false;
            }

            // Update local commission earnings first
            $totalCommission = IbCommission::where('to_ib_user_id', $ib->id)
                ->where('status', 'paid')
                ->sum('commission_amount');

            $ib->update(['commission_earnings' => $totalCommission]);

            // CRITICAL BUG FIX: Use the correct MT5 Python integration script from environment
            $pythonExe = env('PYTHON_EXE', 'python');
            $pythonScript = env('PYTHON_SCRIPT', base_path('python/mt5manager.py'));

            // CRITICAL BUG FIX: Use correct command format for mt5manager.py add_balance
            $command = sprintf(
                '%s %s add_balance --login %s --amount %s --comment %s',
                escapeshellarg($pythonExe),
                escapeshellarg($pythonScript),
                escapeshellarg($ib->mt5_login),
                escapeshellarg($commissionAmount),
                escapeshellarg($comment)
            );

            // Execute the Python script
            $output = shell_exec($command . ' 2>&1');

            Log::info("Real-time MT5 balance update executed", [
                'ib_id' => $ib->id,
                'ib_name' => $ib->fullname,
                'mt5_login' => $ib->mt5_login,
                'commission_amount' => $commissionAmount,
                'total_commission_earnings' => $totalCommission,
                'output' => $output
            ]);

            // Update local MT5 balance as well
            $ib->increment('mt5_balance', $commissionAmount);

            return true;

        } catch (\Exception $e) {
            Log::error("Failed to update MT5 balance in real-time", [
                'ib_id' => $ib->id,
                'ib_name' => $ib->fullname,
                'mt5_login' => $ib->mt5_login,
                'commission_amount' => $commissionAmount,
                'error' => $e->getMessage()
            ]);

            // Still update local balance and commission earnings even if MT5 update fails
            $totalCommission = IbCommission::where('to_ib_user_id', $ib->id)
                ->where('status', 'paid')
                ->sum('commission_amount');

            $ib->update([
                'commission_earnings' => $totalCommission,
                'mt5_balance' => $ib->mt5_balance + $commissionAmount
            ]);

            return false;
        }
    }
}
