<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\NotificationTemplate;

class TestAllTemplateDelivery extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test-all-delivery {--email=<EMAIL>}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test email delivery for all 45 templates individually';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $testEmail = $this->option('email');
        
        $this->info('🧪 COMPREHENSIVE EMAIL DELIVERY TESTING');
        $this->line('==========================================');
        $this->line("Test Email: {$testEmail}");
        $this->line('');

        $templates = NotificationTemplate::orderBy('id')->get();
        $successCount = 0;
        $failureCount = 0;
        $failedTemplates = [];

        foreach ($templates as $template) {
            $this->line("Testing Template {$template->id}: {$template->name}");
            
            try {
                // Create test user
                $testUser = (object) [
                    'id' => 1,
                    'fullname' => 'Test User',
                    'username' => 'testuser',
                    'email' => $testEmail,
                    'referral_code' => 'REF123456',
                    'created_at' => now()
                ];

                // Get comprehensive shortcodes for this template
                $shortcodes = \App\Services\ShortcodeService::getShortcodes($testUser, [
                    'amount' => '1000.00',
                    'currency' => 'USD',
                    'new_balance' => '5000.00',
                    'transaction_id' => 'TXN' . time(),
                    'mt5_login' => '12345678',
                    'ib_type' => 'Master IB',
                    'commission_rate' => '50%',
                    'approval_date' => now()->format('Y-m-d H:i:s'),
                    'template_id' => $template->id
                ]);

                // Send test email using the template's action code
                notify($testUser, $template->act, $shortcodes, ['email'], false);
                
                $this->info("  ✅ SUCCESS: Email sent for {$template->act}");
                $successCount++;
                
                // Small delay to prevent overwhelming the email server
                sleep(1);
                
            } catch (\Exception $e) {
                $this->error("  ❌ FAILED: {$e->getMessage()}");
                $failureCount++;
                $failedTemplates[] = [
                    'id' => $template->id,
                    'name' => $template->name,
                    'act' => $template->act,
                    'error' => $e->getMessage()
                ];
            }
        }

        $this->line('');
        $this->info('📊 DELIVERY TEST SUMMARY');
        $this->line('========================');
        $this->line("Total Templates: " . count($templates));
        $this->line("Successful: {$successCount}");
        $this->line("Failed: {$failureCount}");

        if (!empty($failedTemplates)) {
            $this->line('');
            $this->error('❌ FAILED TEMPLATES:');
            foreach ($failedTemplates as $failed) {
                $this->line("  - Template {$failed['id']}: {$failed['name']} ({$failed['act']})");
                $this->line("    Error: {$failed['error']}");
            }
        }

        if ($failureCount === 0) {
            $this->info('🎉 ALL TEMPLATES DELIVERED SUCCESSFULLY!');
        } else {
            $this->warn("⚠️  {$failureCount} templates failed delivery. Check errors above.");
        }

        return 0;
    }
}
