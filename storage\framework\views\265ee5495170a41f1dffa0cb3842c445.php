<div class="card mt-30">
    <div class="card-header">
      <h5 class="card-title mb-0"><?php echo app('translator')->get('Information of'); ?> <?php echo e($user->fullname); ?></h5>
    </div>
    <div class="card-body">
      <form action="<?php echo e(route('admin.users.update', $user->id )); ?>" method="POST" enctype="multipart/form-data">
        <?php echo csrf_field(); ?>

        <div class="row">
          <div class="col-md-2 d-flex flex-column">
            <!-- Profile Picture Section -->
            <div class="text-center">
              <label style="font-size: 12px; font-weight: 600;"><?php echo app('translator')->get('Profile Picture'); ?></label>
              <div class="profile-picture-container" style="position: relative; display: inline-block;">
                <img src="<?php echo e(getImage(getFilePath('userProfile') . '/' . $user->image, getFileSize('userProfile'), true)); ?>"
                     alt="<?php echo app('translator')->get('Profile Picture'); ?>"
                     class="profile-picture"
                     style="width: 70px; height: 70px; border-radius: 50%; object-fit: cover; border: 2px solid #dc3545; box-shadow: 0 2px 6px rgba(0,0,0,0.1);">
              </div>
            </div>

            <div class="form-group checkbox-group">
              <label style="font-size: 11px; font-weight: 500;"><?php echo app('translator')->get('Email Verification'); ?></label>
              <input class="btn-xs" type="checkbox" data-width="90%" data-size="mini" data-onstyle="-success" data-offstyle="-danger"
                data-bs-toggle="toggle" data-on="<?php echo app('translator')->get('Verified'); ?>" data-off="<?php echo app('translator')->get('Unverified'); ?>" name="ev"
                <?php if($user->ev ): ?> checked <?php endif; ?>>
            </div>
            <div class="form-group checkbox-group">
              <label style="font-size: 11px; font-weight: 500;"><?php echo app('translator')->get('Mobile Verification'); ?></label>
              <input type="checkbox" data-width="90%" data-size="mini" data-onstyle="-success" data-offstyle="-danger"
                data-bs-toggle="toggle" data-on="<?php echo app('translator')->get('Verified'); ?>" data-off="<?php echo app('translator')->get('Unverified'); ?>" name="sv"
                <?php if($user->sv ): ?> checked <?php endif; ?>>
            </div>
            <div class="form-group checkbox-group">
              <label style="font-size: 11px; font-weight: 500;"><?php echo app('translator')->get('2FA Verification'); ?></label>
              <input type="checkbox" data-width="90%" data-size="mini" data-onstyle="-success" data-offstyle="-danger"
                data-bs-toggle="toggle" data-on="<?php echo app('translator')->get('Enable'); ?>" data-off="<?php echo app('translator')->get('Disable'); ?>" name="ts"
                <?php if($user->ts ): ?> checked <?php endif; ?>>
            </div>
            <div class="form-group checkbox-group">
              <label style="font-size: 11px; font-weight: 500;"><?php echo app('translator')->get('KYC'); ?></label>
              <input type="checkbox" data-width="90%" data-size="mini" data-onstyle="-success"
                  data-offstyle="-danger" data-bs-toggle="toggle" data-on="<?php echo app('translator')->get('Verified'); ?>"
                  data-off="<?php echo app('translator')->get('Unverified'); ?>" name="kv"
                  <?php if($user->kv ): ?> checked <?php endif; ?>>
          </div>
            <div class="form-group checkbox-group">
              <label style="font-size: 11px; font-weight: 500;"><?php echo app('translator')->get('Deposit Status'); ?></label>
              <input type="checkbox" data-width="90%" data-size="mini" data-onstyle="-success"
                data-offstyle="-danger" data-bs-toggle="toggle" data-on="<?php echo app('translator')->get('Verified'); ?>"
                data-off="<?php echo app('translator')->get('Unverified'); ?>" name="deposit_status" <?php if($user->deposit_status ): ?> checked <?php endif; ?>>
            </div>
            <div class="form-group checkbox-group">
              <label style="font-size: 11px; font-weight: 500;"><?php echo app('translator')->get('Withdraw Status'); ?></label>
              <input type="checkbox" data-width="90%" data-size="mini" data-onstyle="-success"
                data-offstyle="-danger" data-bs-toggle="toggle" data-on="<?php echo app('translator')->get('Verified'); ?>"
                data-off="<?php echo app('translator')->get('Unverified'); ?>" name="withdraw_status" <?php if($user->withdraw_status ): ?> checked <?php endif; ?>>
            </div>
            <div class="form-group checkbox-group">
              <label style="font-size: 11px; font-weight: 500;"><?php echo app('translator')->get('Send Money Status'); ?></label>
              <input type="checkbox" data-width="90%" data-size="mini" data-onstyle="-success"
                data-offstyle="-danger" data-bs-toggle="toggle" data-on="<?php echo app('translator')->get('Verified'); ?>"
                data-off="<?php echo app('translator')->get('Unverified'); ?>" name="send_money_status" <?php if($user->send_money_status ): ?> checked <?php endif; ?>>
            </div>
          </div>

          <!-- Main Form Section on the Right -->
          <div class="col-md-10">
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label><?php echo app('translator')->get('First Name'); ?></label>
                  <input class="form-control" type="text" name="firstname" required value="<?php echo e($user->firstname); ?>">
                </div>
              </div>

              <div class="col-md-6">
                <div class="form-group">
                  <label class="form-control-label"><?php echo app('translator')->get('Last Name'); ?></label>
                  <input class="form-control" type="text" name="lastname" required value="<?php echo e($user->lastname); ?>">
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label><?php echo app('translator')->get('Email'); ?></label>
                  <input class="form-control" type="email" name="email" value="<?php echo e($user->email); ?>" required>
                </div>
              </div>

              <div class="col-md-6">
                <div class="form-group">
                  <label><?php echo app('translator')->get('Mobile Number'); ?></label>
                  <div class="input-group">
                    <span class="input-group-text mobile-code"></span>
                    <?php
                      // ISSUE 1 FIX: Comprehensive mobile fallback logic
                      $mobileValue = $user->mobile;
                      if (empty($mobileValue) || $mobileValue === null) {
                          $mobileValue = (!empty($user->mt5_phone) && $user->mt5_phone !== 'N/A') ? $user->mt5_phone : '';
                      }
                    ?>
                    <input type="number" name="mobile" value="<?php echo e($mobileValue); ?>" id="mobile"
                      class="form-control checkUser" required>
                  </div>
                </div>
              </div>
            </div>

            <div class="row mt-4">
              <div class="col-md-12">
                <div class="form-group ">
                  <label><?php echo app('translator')->get('Address'); ?></label>
                  <?php
                    // ISSUE 1 FIX: Comprehensive address fallback logic
                    $addressValue = '';
                    if (is_object($user->address) && isset($user->address->address)) {
                        $addressValue = $user->address->address;
                    }
                    // If address is empty, N/A, or null, try MT5 address
                    if (empty($addressValue) || $addressValue === 'N/A' || $addressValue === null) {
                        $addressValue = (!empty($user->mt5_address) && $user->mt5_address !== 'N/A') ? $user->mt5_address : '';
                    }
                  ?>
                  <input class="form-control" type="text" name="address" value="<?php echo e($addressValue); ?>">
                </div>
              </div>

              <div class="col-xl-4 col-md-6">
                <div class="form-group">
                  <label><?php echo app('translator')->get('City'); ?></label>
                  <?php
                    $cityValue = '';
                    if (is_object($user->address) && isset($user->address->city)) {
                        $cityValue = $user->address->city;
                    }
                    if (empty($cityValue) || $cityValue === 'N/A' || $cityValue === null) {
                        $cityValue = (!empty($user->mt5_city) && $user->mt5_city !== 'N/A') ? $user->mt5_city : '';
                    }
                  ?>
                  <input class="form-control" type="text" name="city" value="<?php echo e($cityValue); ?>">
                </div>
              </div>

              <div class="col-xl-4 col-md-6">
                <div class="form-group ">
                  <label><?php echo app('translator')->get('State'); ?></label>
                  <?php
                    $stateValue = '';
                    if (is_object($user->address) && isset($user->address->state)) {
                        $stateValue = $user->address->state;
                    }
                    if (empty($stateValue) || $stateValue === 'N/A' || $stateValue === null) {
                        $stateValue = (!empty($user->mt5_state) && $user->mt5_state !== 'N/A') ? $user->mt5_state : '';
                    }
                  ?>
                  <input class="form-control" type="text" name="state" value="<?php echo e($stateValue); ?>">
                </div>
              </div>

              <div class="col-xl-4 col-md-6">
                <div class="form-group ">
                  <label><?php echo app('translator')->get('Zip/Postal'); ?></label>
                  <?php
                    $zipValue = '';
                    if (is_object($user->address) && isset($user->address->zip)) {
                        $zipValue = $user->address->zip;
                    }
                    if (empty($zipValue) || $zipValue === 'N/A' || $zipValue === null) {
                        $zipValue = (!empty($user->mt5_zip_code) && $user->mt5_zip_code !== 'N/A') ? $user->mt5_zip_code : '';
                    }
                  ?>
                  <input class="form-control" type="text" name="zip" value="<?php echo e($zipValue); ?>">
                </div>
              </div>


              <div class="col-md-6">
                <div class="form-group ">
                  <label><?php echo app('translator')->get('Account Types'); ?></label>
                  <input class="form-control" type="text" name="account_type" value="<?php echo e(@$user->account_type ?? "N/A"); ?>">
                </div>
              </div>

              <div class="col-md-6">
                <div class="form-group ">
                  <label><?php echo app('translator')->get('Country'); ?></label>
                  <select name="country" class="form-control" required>
                    <?php $__currentLoopData = $countries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                      <?php
                        // PHASE 1 FIX: Handle both country codes and country names
                        $isSelected = ($user->country_code == $key) ||
                                     (strtolower($user->country_code) == strtolower($country->country)) ||
                                     ($user->country_code == $country->country);
                      ?>
                      <option data-mobile_code="<?php echo e($country->dial_code); ?>" value="<?php echo e($key); ?>"
                        <?php echo e($isSelected ? 'selected' : ''); ?>>
                        <?php echo e(__($country->country)); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                  </select>
                </div>
              </div>
            </div>



            <!-- Submit Button -->
            <div class="row mt-4">
              <div class="col-md-12">
                <div class="form-group">
                  <button type="submit" class="btn btn--primary w-100 h-45"><?php echo app('translator')->get('Submit'); ?></button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <style>
    .checkbox-group {
      width: 100%;
      margin: 0px 0 3px 0;
    }

    .checkbox-group input[type="checkbox"] {
      width: 100%;
    }

    /* Profile Picture Column Optimization */
    .col-md-2 {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: stretch;
      padding-right: 25px;
    }

   

    /* Toggle Button Optimizations */
    .toggle.btn-mini {
      min-height: 28px !important;
      font-size: 10px !important;
    }

    .toggle-group .btn {
      padding: 4px 8px !important;
      font-size: 10px !important;
    }

    /* Profile Picture Styling */
    .profile-picture-container {
      margin-bottom: 15px;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
      .col-md-2 {
        min-height: auto;
        margin-bottom: 25px;
      }

      .col-md-10 {
        min-height: auto;
      }
    }
  </style>
<?php /**PATH C:\xampp\htdocs\mbf.mybrokerforex.com-31052025\resources\views/components/user-detail/detail.blade.php ENDPATH**/ ?>