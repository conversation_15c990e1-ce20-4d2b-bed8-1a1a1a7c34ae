<?php

require_once 'vendor/autoload.php';
require_once 'bootstrap/app.php';

$general = gs();

echo "Global Email Template:\n";
echo "=====================\n";
echo "Length: " . strlen($general->email_template) . " characters\n";
echo "First 500 characters:\n";
echo substr($general->email_template, 0, 500) . "\n";
echo "...\n\n";

echo "Contains {{message}}: " . (strpos($general->email_template, '{{message}}') !== false ? 'YES' : 'NO') . "\n";
echo "Contains MBFX: " . (strpos($general->email_template, 'MBFX') !== false ? 'YES' : 'NO') . "\n";
echo "Contains Laravel: " . (strpos($general->email_template, 'Laravel') !== false ? 'YES' : 'NO') . "\n";
