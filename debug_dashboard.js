// Debug script to inject into the user dashboard page
// Copy and paste this into the browser console on the user dashboard page

console.log('🔧 Dashboard Debug Script Loaded');

// Test the real-time functionality
function testDashboardRealTime() {
    console.log('🧪 Testing dashboard real-time functionality...');
    
    // Get current login from URL
    const urlParams = new URLSearchParams(window.location.search);
    const currentLogin = urlParams.get('login') || '873475';
    
    console.log('📍 Current login:', currentLogin);
    console.log('📍 Current URL:', window.location.href);
    
    // Test the endpoint directly
    fetch(`/mbf.mybrokerforex.com-********/test-mt5-realtime/${currentLogin}`)
        .then(response => {
            console.log('📡 Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('📥 API Response:', data);
            
            if (data.success && data.data && data.data.success) {
                const orders = data.data.data || [];
                console.log(`✅ Found ${orders.length} positions for account ${currentLogin}`);
                
                orders.forEach((order, index) => {
                    console.log(`📊 Position ${index + 1}:`, {
                        Symbol: order.Symbol,
                        Profit: order.Profit,
                        Volume: order.Volume,
                        Price: `${order.PriceOpen} → ${order.PriceCurrent}`
                    });
                });
                
                // Try to update the table if it exists
                const tbody = document.querySelector('#ordersTable tbody');
                if (tbody) {
                    console.log('📋 Table found, updating...');
                    updateTableWithData(orders);
                } else {
                    console.warn('⚠️ Table not found on page');
                }
                
            } else {
                console.error('❌ API returned error:', data.error || data.message);
            }
        })
        .catch(error => {
            console.error('❌ Fetch error:', error);
        });
}

function updateTableWithData(orders) {
    const tbody = document.querySelector('#ordersTable tbody');
    if (!tbody) {
        console.error('❌ Table tbody not found');
        return;
    }
    
    if (!orders || orders.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="10" class="text-center py-4">
                    <i class="las la-chart-line text-muted" style="font-size: 2rem;"></i>
                    <p class="text-muted mt-2">No open positions found</p>
                    <small class="text-muted">Real-time data via MT5 Manager API</small>
                </td>
            </tr>
        `;
        return;
    }
    
    let html = '';
    orders.forEach((order, index) => {
        // Format timestamp
        let timeDisplay = 'N/A';
        if (order.TimeSetup && order.TimeSetup > 0) {
            const timestamp = order.TimeSetup > 1000000000000 ? order.TimeSetup : order.TimeSetup * 1000;
            timeDisplay = new Date(timestamp).toLocaleString();
        }
        
        // Format values
        const profit = parseFloat(order.Profit || 0).toFixed(2);
        const volume = parseFloat(order.Volume || 0);
        const volumeDisplay = volume > 1000 ? (volume / 100000).toFixed(3) + ' lot' : volume.toFixed(2);
        const commission = parseFloat(order.Commission || 0).toFixed(2);
        const fee = parseFloat(order.Fee || 0).toFixed(2);
        const ticket = order.Ticket || order.Order || 'N/A';
        
        html += `
            <tr style="animation: fadeIn 0.5s ease-in; background-color: rgba(40, 167, 69, 0.1);">
                <td>${timeDisplay}</td>
                <td><strong>${order.Symbol || 'N/A'}</strong></td>
                <td class="${profit >= 0 ? 'text-success' : 'text-danger'}"><strong>$${profit}</strong></td>
                <td>$${commission}</td>
                <td>$${fee}</td>
                <td>${ticket}</td>
                <td>-</td>
                <td>-</td>
                <td>${volumeDisplay}</td>
                <td>${order.PriceOpen || 0} → ${order.PriceCurrent || 0}</td>
            </tr>
        `;
    });
    
    tbody.innerHTML = html;
    console.log('✅ Table updated with', orders.length, 'rows');
    
    // Add visual indicator
    const indicator = document.querySelector('.real-time-indicator');
    if (indicator) {
        indicator.style.backgroundColor = '#28a745';
    }
}

// Add debug indicator to page
function addDebugIndicator() {
    const debugDiv = document.createElement('div');
    debugDiv.id = 'debug-indicator';
    debugDiv.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 5px 10px;
        border-radius: 3px;
        z-index: 9999;
        font-size: 11px;
        cursor: pointer;
    `;
    debugDiv.textContent = 'Debug Active - Click to Test';
    debugDiv.onclick = testDashboardRealTime;
    document.body.appendChild(debugDiv);
    
    console.log('🔧 Debug indicator added to page');
}

// Auto-run when script is loaded
console.log('🚀 Starting dashboard debug...');
addDebugIndicator();

// Test immediately
setTimeout(() => {
    console.log('🔄 Running initial test...');
    testDashboardRealTime();
}, 2000);

// Set up auto-refresh every 30 seconds
setInterval(() => {
    console.log('🔄 Auto-refresh test...');
    testDashboardRealTime();
}, 30000);

console.log('✅ Dashboard debug script ready!');
console.log('📋 Available functions:');
console.log('  - testDashboardRealTime() - Test the real-time functionality');
console.log('  - updateTableWithData(orders) - Update table with order data');
