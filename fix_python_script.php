<?php
/**
 * Python Script Fix and Verification Tool
 * Upload this to your live server and run it to fix Python script issues
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Python Script Fix and Verification</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .section{margin:20px 0;padding:15px;border:1px solid #ddd;} .success{color:green;} .error{color:red;} .warning{color:orange;} pre{background:#f5f5f5;padding:10px;overflow-x:auto;}</style>";

// Load environment variables
$envVars = [];
if (file_exists('.env')) {
    $envContent = file_get_contents('.env');
    $envLines = explode("\n", $envContent);
    
    foreach ($envLines as $line) {
        if (strpos($line, '=') !== false && !str_starts_with(trim($line), '#')) {
            list($key, $value) = explode('=', $line, 2);
            $envVars[trim($key)] = trim($value, '"');
        }
    }
}

echo "<div class='section'>";
echo "<h2>1. Current Configuration</h2>";

$pythonExe = $envVars['PYTHON_EXE'] ?? 'python';
$pythonScript = $envVars['PYTHON_SCRIPT'] ?? '';

echo "<strong>Python Executable:</strong> {$pythonExe}<br>";
echo "<strong>Python Script Path:</strong> {$pythonScript}<br>";
echo "<strong>Current Directory:</strong> " . getcwd() . "<br>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>2. Path Verification</h2>";

// Check different possible paths
$possiblePaths = [
    $pythonScript,
    getcwd() . '/python/mt5manager.py',
    getcwd() . '\\python\\mt5manager.py',
    __DIR__ . '/python/mt5manager.py',
    __DIR__ . '\\python\\mt5manager.py',
    'python/mt5manager.py',
    'python\\mt5manager.py'
];

$foundPath = null;
foreach ($possiblePaths as $path) {
    if (file_exists($path)) {
        echo "<span class='success'>✓ Found script at: {$path}</span><br>";
        $foundPath = $path;
        break;
    } else {
        echo "<span class='error'>✗ Not found: {$path}</span><br>";
    }
}

if (!$foundPath) {
    echo "<div class='error'>";
    echo "<h3>❌ Python Script Not Found!</h3>";
    echo "<p>Please upload the python/mt5manager.py file to your server.</p>";
    echo "</div>";
} else {
    echo "<div class='success'>";
    echo "<h3>✅ Python Script Found!</h3>";
    echo "<p>Correct path: {$foundPath}</p>";
    echo "</div>";
}
echo "</div>";

if ($foundPath) {
    echo "<div class='section'>";
    echo "<h2>3. Script Content Verification</h2>";
    
    $scriptContent = file_get_contents($foundPath);
    $scriptSize = filesize($foundPath);
    
    echo "<strong>Script Size:</strong> {$scriptSize} bytes<br>";
    echo "<strong>Script Permissions:</strong> " . substr(sprintf('%o', fileperms($foundPath)), -4) . "<br>";
    
    // Check if script contains required functions
    $requiredFunctions = [
        'create_account',
        'change_password', 
        'change_leverage',
        'test_connection'
    ];
    
    foreach ($requiredFunctions as $func) {
        if (strpos($scriptContent, $func) !== false) {
            echo "<span class='success'>✓ Function '{$func}' found</span><br>";
        } else {
            echo "<span class='error'>✗ Function '{$func}' missing</span><br>";
        }
    }
    
    // Show first few lines of script
    $lines = explode("\n", $scriptContent);
    echo "<br><strong>Script Preview (first 10 lines):</strong><br>";
    echo "<pre>";
    for ($i = 0; $i < min(10, count($lines)); $i++) {
        echo htmlspecialchars($lines[$i]) . "\n";
    }
    echo "</pre>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>4. Python Execution Test</h2>";
    
    // Test Python version
    $pythonCmd = escapeshellarg($pythonExe) . " --version 2>&1";
    $pythonVersion = shell_exec($pythonCmd);
    
    if ($pythonVersion) {
        echo "<span class='success'>✓ Python Version: " . trim($pythonVersion) . "</span><br>";
    } else {
        echo "<span class='error'>✗ Python not accessible</span><br>";
    }
    
    // Test script execution
    echo "<br><strong>Testing script execution...</strong><br>";
    $testCmd = escapeshellarg($pythonExe) . " " . escapeshellarg($foundPath) . " --help 2>&1";
    echo "<strong>Command:</strong> {$testCmd}<br>";
    
    $output = shell_exec($testCmd);
    if ($output) {
        echo "<span class='success'>✓ Script executed successfully</span><br>";
        echo "<strong>Output:</strong><pre>" . htmlspecialchars($output) . "</pre>";
    } else {
        echo "<span class='error'>✗ Script execution failed</span><br>";
    }
    
    // Test MT5 connection
    echo "<br><strong>Testing MT5 connection...</strong><br>";
    $mt5TestCmd = escapeshellarg($pythonExe) . " " . escapeshellarg($foundPath) . " test_connection 2>&1";
    echo "<strong>Command:</strong> {$mt5TestCmd}<br>";
    
    $mt5Output = shell_exec($mt5TestCmd);
    if ($mt5Output) {
        echo "<strong>MT5 Test Output:</strong><pre>" . htmlspecialchars($mt5Output) . "</pre>";
        
        if (strpos(strtolower($mt5Output), 'success') !== false || strpos(strtolower($mt5Output), 'connected') !== false) {
            echo "<span class='success'>✓ MT5 connection test successful</span><br>";
        } else {
            echo "<span class='warning'>⚠ MT5 connection test completed but check output above</span><br>";
        }
    } else {
        echo "<span class='error'>✗ No output from MT5 test command</span><br>";
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>5. Python Dependencies Check</h2>";
    
    // Check if MetaTrader5 package is installed
    $mt5CheckCmd = escapeshellarg($pythonExe) . " -c \"import MetaTrader5; print('MetaTrader5 version:', MetaTrader5.__version__)\" 2>&1";
    echo "<strong>Checking MetaTrader5 package...</strong><br>";
    echo "<strong>Command:</strong> {$mt5CheckCmd}<br>";
    
    $mt5PackageOutput = shell_exec($mt5CheckCmd);
    if ($mt5PackageOutput && strpos($mt5PackageOutput, 'version') !== false) {
        echo "<span class='success'>✓ MetaTrader5 package installed: " . trim($mt5PackageOutput) . "</span><br>";
    } else {
        echo "<span class='error'>✗ MetaTrader5 package not installed or error occurred</span><br>";
        echo "<strong>Output:</strong><pre>" . htmlspecialchars($mt5PackageOutput) . "</pre>";
        echo "<p><strong>To fix:</strong> Run <code>pip install MetaTrader5</code> on your server</p>";
    }
    
    // Check other required packages
    $requiredPackages = ['requests', 'json', 'sys', 'argparse'];
    foreach ($requiredPackages as $package) {
        $packageCheckCmd = escapeshellarg($pythonExe) . " -c \"import {$package}; print('{$package} OK')\" 2>&1";
        $packageOutput = shell_exec($packageCheckCmd);
        
        if ($packageOutput && strpos($packageOutput, 'OK') !== false) {
            echo "<span class='success'>✓ {$package} package available</span><br>";
        } else {
            echo "<span class='error'>✗ {$package} package missing or error</span><br>";
        }
    }
    echo "</div>";
}

echo "<div class='section'>";
echo "<h2>6. Environment File Fix</h2>";

if ($foundPath && $foundPath !== $pythonScript) {
    echo "<div class='warning'>";
    echo "<h3>⚠ Path Mismatch Detected!</h3>";
    echo "<p>Your .env file has: <code>{$pythonScript}</code></p>";
    echo "<p>But script found at: <code>{$foundPath}</code></p>";
    echo "<p><strong>Recommended fix:</strong> Update your .env file with the correct path.</p>";
    echo "</div>";
    
    // Suggest the correct .env configuration
    echo "<h4>Suggested .env Configuration:</h4>";
    echo "<pre>";
    echo "PYTHON_EXE=\"{$pythonExe}\"\n";
    echo "PYTHON_SCRIPT=\"{$foundPath}\"\n";
    echo "</pre>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>7. Laravel Integration Test</h2>";

// Test if Laravel can find the script
echo "<strong>Testing Laravel MT5Service integration...</strong><br>";

// Check if MT5Service exists
$mt5ServicePath = 'app/Services/MT5Service.php';
if (file_exists($mt5ServicePath)) {
    echo "<span class='success'>✓ MT5Service.php found</span><br>";
    
    // Check if the service uses the correct paths
    $serviceContent = file_get_contents($mt5ServicePath);
    if (strpos($serviceContent, 'PYTHON_EXE') !== false) {
        echo "<span class='success'>✓ MT5Service uses PYTHON_EXE environment variable</span><br>";
    } else {
        echo "<span class='warning'>⚠ MT5Service may not be using environment variables</span><br>";
    }
} else {
    echo "<span class='error'>✗ MT5Service.php not found</span><br>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>8. Next Steps</h2>";
echo "<ol>";

if (!$foundPath) {
    echo "<li><strong>Upload the Python script:</strong>";
    echo "<ul>";
    echo "<li>Upload <code>python/mt5manager.py</code> to your server</li>";
    echo "<li>Ensure the file is in the correct location</li>";
    echo "<li>Set proper file permissions (755)</li>";
    echo "</ul></li>";
}

echo "<li><strong>Install Python dependencies:</strong>";
echo "<pre>pip install MetaTrader5</pre></li>";

if ($foundPath && $foundPath !== $pythonScript) {
    echo "<li><strong>Update .env file:</strong>";
    echo "<pre>PYTHON_SCRIPT=\"{$foundPath}\"</pre></li>";
}

echo "<li><strong>Clear Laravel caches:</strong>";
echo "<pre>";
echo "php artisan cache:clear\n";
echo "php artisan config:clear\n";
echo "</pre></li>";

echo "<li><strong>Test the functionality:</strong>";
echo "<ul>";
echo "<li>Try changing leverage on an MT5 account</li>";
echo "<li>Try changing password on an MT5 account</li>";
echo "<li>Check Laravel logs for any errors</li>";
echo "</ul></li>";

echo "</ol>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>9. Manual Testing Commands</h2>";
echo "<p>You can test these commands directly on your server:</p>";

if ($foundPath) {
    echo "<h4>Test MT5 Connection:</h4>";
    echo "<pre>" . escapeshellarg($pythonExe) . " " . escapeshellarg($foundPath) . " test_connection</pre>";
    
    echo "<h4>Test Account Creation:</h4>";
    echo "<pre>" . escapeshellarg($pythonExe) . " " . escapeshellarg($foundPath) . " create_account --first_name \"Test\" --last_name \"User\" --email \"<EMAIL>\" --password \"TestPass123\" --group \"demo\\\\MBFX\\\\PREMIUM_200_USD_B\" --leverage 200</pre>";
    
    echo "<h4>Test Leverage Change:</h4>";
    echo "<pre>" . escapeshellarg($pythonExe) . " " . escapeshellarg($foundPath) . " change_leverage --login YOUR_MT5_LOGIN --leverage 300</pre>";
    
    echo "<h4>Test Password Change:</h4>";
    echo "<pre>" . escapeshellarg($pythonExe) . " " . escapeshellarg($foundPath) . " change_password --login YOUR_MT5_LOGIN --new_password \"NewPass123\" --password_type main</pre>";
}
echo "</div>";

echo "<p><em>After fixing the issues, delete this diagnostic file for security.</em></p>";
?>
