<?php

require 'vendor/autoload.php';
$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 CHECKING MT5 DATABASE STRUCTURE\n";
echo "==================================\n\n";

try {
    // Check MT5 database connection
    echo "Testing MT5 database connection...\n";
    $columns = DB::connection('mbf-dbmt5')->select('DESCRIBE mt5_users');
    
    echo "✅ MT5 database connected successfully!\n\n";
    echo "MT5_USERS table columns:\n";
    echo "------------------------\n";
    
    foreach($columns as $col) {
        echo sprintf("%-25s %s\n", $col->Field, $col->Type);
    }
    
    echo "\n";
    
    // Get sample data
    echo "Sample MT5 users (first 5):\n";
    echo "----------------------------\n";
    
    $sampleUsers = DB::connection('mbf-dbmt5')->table('mt5_users')
        ->whereNotNull('Email')
        ->where('Email', '!=', '')
        ->limit(5)
        ->get();
    
    foreach($sampleUsers as $user) {
        echo "Login: {$user->Login}, Email: {$user->Email}, Name: {$user->FirstName} {$user->LastName}\n";
    }
    
    echo "\n";
    
    // Count total users
    $totalUsers = DB::connection('mbf-dbmt5')->table('mt5_users')
        ->whereNotNull('Email')
        ->where('Email', '!=', '')
        ->count();
    
    echo "Total MT5 users with email: {$totalUsers}\n";
    
    // Check for date columns
    echo "\nDate-related columns:\n";
    foreach($columns as $col) {
        if (stripos($col->Field, 'date') !== false || stripos($col->Field, 'time') !== false || stripos($col->Type, 'timestamp') !== false || stripos($col->Type, 'datetime') !== false) {
            echo "- {$col->Field} ({$col->Type})\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
