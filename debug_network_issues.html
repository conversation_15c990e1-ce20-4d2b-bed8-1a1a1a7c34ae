<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Network Debug Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error { color: red; }
        .success { color: green; }
        .warning { color: orange; }
        .code { background: #f5f5f5; padding: 10px; border-radius: 3px; font-family: monospace; }
    </style>
</head>
<body>
    <h1>Network Visualization Debug Tool</h1>
    
    <div class="debug-section">
        <h2>1. Test User Partnership Network Page</h2>
        <p><strong>URL:</strong> <a href="https://localhost/mbf.mybrokerforex.com-31052025/user/partnership/network" target="_blank">User Network Page</a></p>
        
        <h3>JavaScript Console Tests:</h3>
        <div class="code">
            // Copy and paste these commands in browser console:
            
            // 1. Check if BACKEND_DATA is defined
            console.log('BACKEND_DATA:', typeof BACKEND_DATA !== 'undefined' ? BACKEND_DATA : 'UNDEFINED');
            
            // 2. Check if OrgChart.js is loaded
            console.log('OrgChart.js:', typeof $.fn.orgchart !== 'undefined' ? 'LOADED' : 'NOT LOADED');
            
            // 3. Check for global variables
            console.log('Global vars:', {
                orgChart: typeof orgChart,
                treeData: typeof treeData,
                currentPage: typeof currentPage
            });
            
            // 4. Test tree initialization
            if (typeof initializeOrgChart === 'function') {
                console.log('initializeOrgChart function: AVAILABLE');
            } else {
                console.error('initializeOrgChart function: MISSING');
            }
        </div>
    </div>
    
    <div class="debug-section">
        <h2>2. Test Admin User Detail Page</h2>
        <p><strong>URL:</strong> <a href="https://localhost/mbf.mybrokerforex.com-31052025/admin/users/detail/6902" target="_blank">Admin Detail Page</a></p>
        <p><em>Click on "Network" tab after opening</em></p>
        
        <h3>JavaScript Console Tests:</h3>
        <div class="code">
            // Copy and paste these commands in browser console:
            
            // 1. Check if ADMIN_BACKEND_DATA is defined
            console.log('ADMIN_BACKEND_DATA:', typeof ADMIN_BACKEND_DATA !== 'undefined' ? ADMIN_BACKEND_DATA : 'UNDEFINED');
            
            // 2. Check admin-specific variables
            console.log('Admin vars:', {
                adminOrgChart: typeof adminOrgChart,
                adminTreeData: typeof adminTreeData,
                adminCurrentPage: typeof adminCurrentPage
            });
            
            // 3. Test admin functions
            if (typeof initializeAdminOrgChart === 'function') {
                console.log('initializeAdminOrgChart function: AVAILABLE');
            } else {
                console.error('initializeAdminOrgChart function: MISSING');
            }
            
            // 4. Check for conflicts
            console.log('Function conflicts check:', {
                userInit: typeof initializeOrgChart,
                adminInit: typeof initializeAdminOrgChart,
                bothDefined: typeof initializeOrgChart !== 'undefined' && typeof initializeAdminOrgChart !== 'undefined'
            });
        </div>
    </div>
    
    <div class="debug-section">
        <h2>3. Common Error Patterns</h2>
        <h3>Look for these errors in console:</h3>
        <ul>
            <li class="error">ReferenceError: BACKEND_DATA is not defined</li>
            <li class="error">ReferenceError: ADMIN_BACKEND_DATA is not defined</li>
            <li class="error">TypeError: Cannot read property 'networkData' of undefined</li>
            <li class="error">TypeError: $.fn.orgchart is not a function</li>
            <li class="warning">Mixed Content: The page was loaded over HTTPS</li>
            <li class="error">SyntaxError: Unexpected token</li>
        </ul>
    </div>
    
    <div class="debug-section">
        <h2>4. Visual Checks</h2>
        <h3>User Page Should Show:</h3>
        <ul>
            <li>Professional network tree OR fallback tree</li>
            <li>Control buttons (Expand All, Collapse All, Export)</li>
            <li>Debug info section with user details</li>
            <li>No raw JavaScript code visible</li>
        </ul>
        
        <h3>Admin Page Should Show:</h3>
        <ul>
            <li>Network tab in user detail tabs</li>
            <li>Professional admin network tree</li>
            <li>Admin-specific controls</li>
            <li>Debug info with admin context</li>
            <li>No function conflicts with user page</li>
        </ul>
    </div>
    
    <div class="debug-section">
        <h2>5. Quick Fix Commands</h2>
        <p>If you find issues, try these in browser console:</p>
        <div class="code">
            // Force reload OrgChart.js
            var script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/orgchart/4.0.1/js/jquery.orgchart.min.js';
            document.head.appendChild(script);
            
            // Check jQuery
            console.log('jQuery:', typeof $ !== 'undefined' ? 'LOADED' : 'NOT LOADED');
            
            // Force fallback tree (user page)
            if (typeof showFallbackTree === 'function' && typeof treeData !== 'undefined') {
                showFallbackTree(treeData);
            }
            
            // Force admin fallback tree (admin page)
            if (typeof showAdminFallbackTree === 'function' && typeof adminTreeData !== 'undefined') {
                showAdminFallbackTree(adminTreeData);
            }
        </div>
    </div>
    
    <script>
        // Auto-open both pages for testing
        setTimeout(() => {
            console.log('Debug tool loaded. Use the commands above to test both pages.');
        }, 1000);
    </script>
</body>
</html>
