@echo off
echo ========================================
echo  Installing Python Dependencies for MT5
echo ========================================
echo.

REM Check if Python is installed
echo [1/4] Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH!
    echo Please install Python from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

python --version
echo ✓ Python is installed and accessible
echo.

REM Upgrade pip
echo [2/4] Upgrading pip...
python -m pip install --upgrade pip
if %errorlevel% neq 0 (
    echo WARNING: Failed to upgrade pip, continuing anyway...
)
echo ✓ Pip upgraded
echo.

REM Install MetaTrader5 package
echo [3/4] Installing MetaTrader5 package...
python -m pip install MetaTrader5
if %errorlevel% neq 0 (
    echo ERROR: Failed to install MetaTrader5 package!
    echo This is required for MT5 API functionality
    pause
    exit /b 1
)
echo ✓ MetaTrader5 package installed
echo.

REM Install other required packages
echo [4/4] Installing additional packages...
python -m pip install requests
python -m pip install configparser
echo ✓ Additional packages installed
echo.

REM Verify installations
echo ========================================
echo  Verifying Installations
echo ========================================
echo.

echo Testing MetaTrader5 import...
python -c "import MetaTrader5; print('✓ MetaTrader5 version:', MetaTrader5.__version__)"
if %errorlevel% neq 0 (
    echo ERROR: MetaTrader5 import failed!
    pause
    exit /b 1
)

echo Testing requests import...
python -c "import requests; print('✓ requests version:', requests.__version__)"
if %errorlevel% neq 0 (
    echo ERROR: requests import failed!
    pause
    exit /b 1
)

echo Testing other modules...
python -c "import json, sys, argparse, configparser; print('✓ Standard modules OK')"
if %errorlevel% neq 0 (
    echo ERROR: Standard modules import failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo  Installation Complete!
echo ========================================
echo.
echo All Python dependencies have been installed successfully.
echo.
echo Next steps:
echo 1. Upload fix_python_script.php to your domain root
echo 2. Visit https://yourdomain.com/fix_python_script.php
echo 3. Follow the instructions to fix any remaining issues
echo 4. Test your MT5 functionality
echo.
pause
