<?php

namespace App\Services;

use App\Models\User;
use App\Models\IbCommission;
use App\Models\IbLevel;
use App\Models\RebateRule;
use App\Models\Transaction;
use App\Models\Wallet;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class IbCommissionService
{
    /**
     * Calculate and distribute commissions for a trade
     */
    public function calculateAndDistributeCommissions($tradeData)
    {
        try {
            DB::beginTransaction();

            $trader = User::where('email', $tradeData['email'])->first();
            if (!$trader) {
                Log::warning("Trader not found for commission calculation", ['email' => $tradeData['email']]);
                return false;
            }

            // Get IB hierarchy for the trader
            $ibHierarchy = $trader->getIbHierarchy();
            if ($ibHierarchy->isEmpty()) {
                Log::info("No IB hierarchy found for trader", ['trader_id' => $trader->id]);
                return true; // Not an error, just no commissions to calculate
            }

            // Get base rebate for the symbol
            $baseRebate = $this->getBaseRebate($tradeData['symbol'], $ibHierarchy->first());
            if (!$baseRebate) {
                Log::info("No rebate rule found for symbol", ['symbol' => $tradeData['symbol']]);
                return true;
            }

            // Calculate total rebate amount
            $totalRebateAmount = $baseRebate * $tradeData['volume'];

            // Get commission levels
            $commissionLevels = IbLevel::getActiveLevels();
            if ($commissionLevels->isEmpty()) {
                Log::warning("No commission levels configured");
                return false;
            }

            // Distribute commissions through hierarchy
            $level = 1;
            foreach ($ibHierarchy as $ib) {
                $commissionLevel = $commissionLevels->where('level', $level)->first();
                if (!$commissionLevel) {
                    break; // No more levels configured
                }

                $commissionAmount = ($totalRebateAmount * $commissionLevel->commission_percent) / 100;
                
                // Apply group multiplier if exists
                if ($ib->ibGroup) {
                    $commissionAmount *= $ib->ibGroup->commission_multiplier;
                }

                // Create commission record
                $commission = IbCommission::create([
                    'from_user_id' => $trader->id,
                    'to_ib_user_id' => $ib->id,
                    'trade_id' => $tradeData['trade_id'],
                    'symbol' => $tradeData['symbol'],
                    'volume' => $tradeData['volume'],
                    'commission_amount' => $commissionAmount,
                    'commission_rate' => $commissionLevel->commission_percent,
                    'level' => $level,
                    'status' => 'pending',
                    'trade_closed_at' => $tradeData['closed_at'] ?? now()
                ]);

                // Pay commission to IB
                $this->payCommissionToIb($commission);

                $level++;
            }

            DB::commit();

            Log::info("Commissions calculated and distributed", [
                'trader_id' => $trader->id,
                'trade_id' => $tradeData['trade_id'],
                'total_rebate' => $totalRebateAmount,
                'levels_processed' => $level - 1
            ]);

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to calculate commissions: " . $e->getMessage(), [
                'trade_data' => $tradeData,
                'error' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Get base rebate for symbol and IB
     */
    private function getBaseRebate($symbol, $ib)
    {
        if (!$ib->ibGroup) {
            return 0;
        }

        $rebateRule = RebateRule::getRebateForSymbol($symbol, $ib->ibGroup->id);
        return $rebateRule ? $rebateRule->rebate_per_lot : 0;
    }

    /**
     * Pay commission to IB wallet
     */
    private function payCommissionToIb($commission)
    {
        try {
            $ib = $commission->toIbUser;
            
            // Get or create USD wallet for IB (assuming commissions are paid in USD)
            $usdCurrency = \App\Models\Currency::where('symbol', 'USD')->first();
            if (!$usdCurrency) {
                Log::error("USD currency not found for commission payment");
                return false;
            }

            $wallet = Wallet::firstOrCreate([
                'user_id' => $ib->id,
                'currency_id' => $usdCurrency->id
            ], [
                'amount' => 0,
                'total_amount' => 0
            ]);

            // Update wallet balance
            $wallet->amount += $commission->commission_amount;
            $wallet->total_amount += $commission->commission_amount;
            $wallet->save();

            // Create transaction record
            Transaction::create([
                'user_id' => $ib->id,
                'wallet_id' => $wallet->id,
                'amount' => $commission->commission_amount,
                'post_balance' => $wallet->amount,
                'charge' => 0,
                'trx_type' => '+',
                'details' => "IB Commission - Level {$commission->level} - Trade #{$commission->trade_id}",
                'trx' => getTrx(),
                'remark' => 'ib_commission'
            ]);

            // Mark commission as paid
            $commission->markAsPaid();

            // Send notification to IB
            notify($ib, 'IB_COMMISSION_EARNED', [
                'amount' => showAmount($commission->commission_amount),
                'symbol' => $commission->symbol,
                'volume' => $commission->volume,
                'level' => $commission->level,
                'trade_id' => $commission->trade_id,
                'balance' => showAmount($wallet->amount)
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error("Failed to pay commission to IB: " . $e->getMessage(), [
                'commission_id' => $commission->id
            ]);
            return false;
        }
    }

    /**
     * Recalculate commissions for a specific trade
     */
    public function recalculateCommissions($tradeId)
    {
        try {
            DB::beginTransaction();

            // Cancel existing commissions
            $existingCommissions = IbCommission::where('trade_id', $tradeId)->get();
            foreach ($existingCommissions as $commission) {
                if ($commission->status === 'paid') {
                    // Reverse the payment
                    $this->reverseCommissionPayment($commission);
                }
                $commission->markAsCancelled('Recalculation');
            }

            // Get trade data and recalculate
            // This would need to be implemented based on your MT5 integration
            // For now, we'll just mark as completed
            
            DB::commit();
            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to recalculate commissions: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Reverse commission payment
     */
    private function reverseCommissionPayment($commission)
    {
        try {
            $ib = $commission->toIbUser;
            $wallet = Wallet::where('user_id', $ib->id)
                ->whereHas('currency', function($q) {
                    $q->where('symbol', 'USD');
                })
                ->first();

            if ($wallet && $wallet->amount >= $commission->commission_amount) {
                $wallet->amount -= $commission->commission_amount;
                $wallet->save();

                // Create reversal transaction
                Transaction::create([
                    'user_id' => $ib->id,
                    'wallet_id' => $wallet->id,
                    'amount' => $commission->commission_amount,
                    'post_balance' => $wallet->amount,
                    'charge' => 0,
                    'trx_type' => '-',
                    'details' => "IB Commission Reversal - Trade #{$commission->trade_id}",
                    'trx' => getTrx(),
                    'remark' => 'ib_commission_reversal'
                ]);
            }

        } catch (\Exception $e) {
            Log::error("Failed to reverse commission payment: " . $e->getMessage());
        }
    }

    /**
     * Get commission summary for IB
     */
    public function getIbCommissionSummary($ibId, $startDate = null, $endDate = null)
    {
        $ib = User::findOrFail($ibId);
        
        if (!$ib->isIb()) {
            return null;
        }

        $stats = $ib->getIbStats($startDate, $endDate);
        $levelBreakdown = IbCommission::getLevelBreakdown($ibId, $startDate, $endDate);
        $symbolBreakdown = IbCommission::getSymbolBreakdown($ibId, $startDate, $endDate);

        return [
            'basic_stats' => $stats,
            'level_breakdown' => $levelBreakdown,
            'symbol_breakdown' => $symbolBreakdown,
            'hierarchy_stats' => app(IbHierarchyService::class)->getHierarchyStats($ibId)
        ];
    }

    /**
     * Process pending commissions
     */
    public function processPendingCommissions()
    {
        $pendingCommissions = IbCommission::pending()
            ->where('created_at', '<=', now()->subMinutes(5)) // Process after 5 minutes
            ->limit(100)
            ->get();

        $processed = 0;
        foreach ($pendingCommissions as $commission) {
            if ($this->payCommissionToIb($commission)) {
                $processed++;
            }
        }

        Log::info("Processed pending commissions", ['count' => $processed]);
        return $processed;
    }
}
