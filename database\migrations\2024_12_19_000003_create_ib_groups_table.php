<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ib_groups', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->text('description')->nullable();
            $table->decimal('commission_multiplier', 5, 2)->default(1.00)->comment('Multiplier for base commission rates');
            $table->integer('max_levels')->default(3)->comment('Maximum levels allowed in this group');
            $table->json('rules_json')->nullable()->comment('Additional rules and settings');
            $table->boolean('status')->default(true)->comment('Active/Inactive');
            $table->timestamps();
            
            // Indexes
            $table->index('name');
            $table->index('status');
        });
        
        // Add foreign key to users table
        Schema::table('users', function (Blueprint $table) {
            if (Schema::hasColumn('users', 'ib_group_id') && !Schema::hasColumn('users', 'ib_group_id_foreign')) {
                $table->foreign('ib_group_id')->references('id')->on('ib_groups')->onDelete('set null');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Drop foreign key first
        Schema::table('users', function (Blueprint $table) {
            if (Schema::hasColumn('users', 'ib_group_id')) {
                $table->dropForeign(['ib_group_id']);
            }
        });
        
        Schema::dropIfExists('ib_groups');
    }
};
