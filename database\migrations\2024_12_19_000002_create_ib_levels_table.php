<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ib_levels', function (Blueprint $table) {
            $table->id();
            $table->integer('level')->unique()->comment('Level number: 1, 2, 3, etc.');
            $table->string('name')->comment('Level name: Level 1, Level 2, etc.');
            $table->decimal('commission_percent', 5, 2)->default(0)->comment('Commission percentage for this level');
            $table->decimal('max_commission_percent', 5, 2)->default(0)->comment('Maximum commission percentage IBs can set for sub-IBs');
            $table->text('description')->nullable();
            $table->boolean('status')->default(true)->comment('Active/Inactive');
            $table->timestamps();
            
            // Indexes
            $table->index('level');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ib_levels');
    }
};
