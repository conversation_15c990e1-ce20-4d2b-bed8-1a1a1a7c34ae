@extends('admin.layouts.app')

@section('panel')
<div class="row">
    <div class="col-lg-12">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h4 class="page-title">@lang('IB Groups')</h4>
            <div class="d-flex gap-2">
                <!-- Search Bar -->
                <div class="search-form">
                    <form action="" method="GET">
                        <div class="input-group">
                            <input type="text" name="search" class="form-control" placeholder="@lang('Search IB groups...')" value="{{ request()->search }}">
                            <button class="btn btn--primary" type="submit"><i class="las la-search"></i></button>
                        </div>
                    </form>
                </div>
                <!-- Add Button -->
                <button type="button" class="btn btn--primary" data-bs-toggle="modal" data-bs-target="#addIbGroupModal">
                    <i class="las la-plus"></i> @lang('Add New IB Group')
                </button>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row gy-4 mb-4">
            <div class="col-xl-3 col-lg-6 col-sm-6">
                <div class="widget-two style--two box--shadow2 b-radius--5 bg--success">
                    <div class="widget-two__icon b-radius--5 bg--success">
                        <i class="las la-users"></i>
                    </div>
                    <div class="widget-two__content">
                        <h3 class="text-white">{{ $ibGroups->where('status', true)->count() }}</h3>
                        <p class="text-white">@lang('Active Groups')</p>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-lg-6 col-sm-6">
                <div class="widget-two style--two box--shadow2 b-radius--5 bg--warning">
                    <div class="widget-two__icon b-radius--5 bg--warning">
                        <i class="las la-user-slash"></i>
                    </div>
                    <div class="widget-two__content">
                        <h3 class="text-white">{{ $ibGroups->where('status', false)->count() }}</h3>
                        <p class="text-white">@lang('Inactive Groups')</p>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-lg-6 col-sm-6">
                <div class="widget-two style--two box--shadow2 b-radius--5 bg--primary">
                    <div class="widget-two__icon b-radius--5 bg--primary">
                        <i class="las la-layer-group"></i>
                    </div>
                    <div class="widget-two__content">
                        <h3 class="text-white">{{ $ibGroups->sum(function($group) { return $group->users->count(); }) }}</h3>
                        <p class="text-white">@lang('Total Members')</p>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-lg-6 col-sm-6">
                <div class="widget-two style--two box--shadow2 b-radius--5 bg--info">
                    <div class="widget-two__icon b-radius--5 bg--info">
                        <i class="las la-cogs"></i>
                    </div>
                    <div class="widget-two__content">
                        <h3 class="text-white">{{ $ibGroups->sum(function($group) { return $group->rebateRules->count(); }) }}</h3>
                        <p class="text-white">@lang('Total Rules')</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="card b-radius--10">
            <div class="card-body p-0">
                <div class="table-responsive--md table-responsive">
                    <table class="table--light style--two table">
                        <thead>
                            <tr>
                                <th>@lang('Group Name')</th>
                                <th>@lang('Description')</th>
                                <th>@lang('Members')</th>
                                <th>@lang('Rebate Rules')</th>
                                <th>@lang('Status')</th>
                                <th>@lang('Action')</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($ibGroups as $group)
                            <tr>
                                <td>
                                    <span class="fw-bold">{{ $group->name }}</span>
                                </td>
                                <td>{{ $group->description ?? 'N/A' }}</td>
                                <td>
                                    <span class="badge badge--primary">{{ $group->users->count() }} @lang('members')</span>
                                </td>
                                <td>
                                    <span class="badge badge--info">{{ $group->rebateRules->count() }} @lang('rules')</span>
                                </td>
                                <td>
                                    @if($group->status)
                                        <span class="badge badge--success">@lang('Active')</span>
                                    @else
                                        <span class="badge badge--warning">@lang('Inactive')</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="button--group">
                                        <button type="button" class="btn btn-sm btn-outline--info viewMembersBtn" 
                                                data-id="{{ $group->id }}"
                                                data-name="{{ $group->name }}">
                                            <i class="las la-users"></i> @lang('View Members')
                                        </button>
                                        
                                        <button type="button" class="btn btn-sm btn-outline--primary editBtn" 
                                                data-id="{{ $group->id }}"
                                                data-name="{{ $group->name }}"
                                                data-description="{{ $group->description }}">
                                            <i class="las la-pencil"></i> @lang('Edit')
                                        </button>
                                        
                                        <button type="button" class="btn btn-sm btn-outline--{{ $group->status ? 'warning' : 'success' }} toggleStatusBtn"
                                                data-id="{{ $group->id }}"
                                                data-status="{{ $group->status }}">
                                            @if($group->status)
                                                <i class="las la-eye-slash"></i> @lang('Disable')
                                            @else
                                                <i class="las la-eye"></i> @lang('Enable')
                                            @endif
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="6" class="text-muted text-center">@lang('No IB groups found')</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if($ibGroups->hasPages())
            <div class="card-footer py-4">
                {{ paginateLinks($ibGroups) }}
            </div>
            @endif
        </div>
    </div>
</div>



<!-- Add IB Group Modal -->
<div class="modal fade" id="addIbGroupModal" tabindex="-1" role="dialog" aria-labelledby="addIbGroupModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addIbGroupModalLabel">@lang('Add New IB Group')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('admin.partnership.store_ib_group') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label>@lang('Group Name') <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    
                    <div class="form-group">
                        <label>@lang('Description')</label>
                        <textarea class="form-control" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn--dark" data-bs-dismiss="modal">@lang('Close')</button>
                    <button type="submit" class="btn btn--primary">@lang('Save')</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit IB Group Modal -->
<div class="modal fade" id="editIbGroupModal" tabindex="-1" role="dialog" aria-labelledby="editIbGroupModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editIbGroupModalLabel">@lang('Edit IB Group')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editIbGroupForm" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div class="form-group">
                        <label>@lang('Group Name') <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="name" id="editName" required>
                    </div>
                    
                    <div class="form-group">
                        <label>@lang('Description')</label>
                        <textarea class="form-control" name="description" id="editDescription" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn--dark" data-bs-dismiss="modal">@lang('Close')</button>
                    <button type="submit" class="btn btn--primary">@lang('Update')</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Members Modal -->
<div class="modal fade" id="viewMembersModal" tabindex="-1" role="dialog" aria-labelledby="viewMembersModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewMembersModalLabel">@lang('Group Members')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="membersList">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">@lang('Loading...')</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn--dark" data-bs-dismiss="modal">@lang('Close')</button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('script')
<script>
    (function ($) {
        'use strict';
        
        // Edit button click
        $('.editBtn').on('click', function() {
            const id = $(this).data('id');
            const name = $(this).data('name');
            const description = $(this).data('description');
            
            $('#editName').val(name);
            $('#editDescription').val(description);
            
            const updateUrl = `{{ route('admin.partnership.update_ib_group', ':id') }}`.replace(':id', id);
            $('#editIbGroupForm').attr('action', updateUrl);
            
            $('#editIbGroupModal').modal('show');
        });
        
        // View members button click
        $('.viewMembersBtn').on('click', function() {
            const groupId = $(this).data('id');
            const groupName = $(this).data('name');
            
            $('#viewMembersModalLabel').text(`@lang('Members of'): ${groupName}`);
            $('#viewMembersModal').modal('show');
            
            // Load members via AJAX (you can implement this endpoint)
            $('#membersList').html('<div class="alert alert-info">@lang("Member list functionality can be implemented as needed")</div>');
        });
        
    })(jQuery);
</script>
@endpush
