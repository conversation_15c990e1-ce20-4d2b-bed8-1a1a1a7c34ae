<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserAccounts;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class MT5CompleteMigrationService
{
    /**
     * Complete migration strategy: All MT5 accounts as separate entries
     * Show only most recent per email in admin list
     */
    public function migrateAllMT5AccountsComplete($deleteExisting = false)
    {
        try {
            Log::info("Starting complete MT5 accounts migration - ALL ACCOUNTS STRATEGY");

            if ($deleteExisting) {
                $this->clearExistingData();
            }

            // Get all MT5 users ordered by email and registration date
            $mt5Users = DB::connection('mbf-dbmt5')
                ->table('mt5_users')
                ->whereNotNull('Email')
                ->where('Email', '!=', '')
                ->orderBy('Email')
                ->orderBy('Registration', 'desc') // Most recent first
                ->get();

            $totalProcessed = 0;
            $usersCreated = 0;
            $accountsCreated = 0;
            $errors = 0;
            $currentEmail = null;
            $primaryUserForEmail = null;

            foreach ($mt5Users as $mt5User) {
                try {
                    // Check if this is a new email
                    if ($currentEmail !== $mt5User->Email) {
                        $currentEmail = $mt5User->Email;
                        
                        // Create primary user for this email (using most recent/best data)
                        $primaryUserForEmail = $this->createPrimaryUserFromMT5($mt5User);
                        $usersCreated++;
                        
                        Log::info("Created primary user for {$mt5User->Email}", [
                            'user_id' => $primaryUserForEmail->id,
                            'mt5_login' => $mt5User->Login
                        ]);
                    }

                    // Create MT5 account record for this login
                    $this->createMT5AccountRecord($primaryUserForEmail, $mt5User);
                    $accountsCreated++;
                    
                    $totalProcessed++;

                    if ($totalProcessed % 100 == 0) {
                        Log::info("Migration progress: {$totalProcessed} accounts processed");
                    }

                } catch (\Exception $e) {
                    $errors++;
                    Log::error("Failed to migrate MT5 account {$mt5User->Login}", [
                        'error' => $e->getMessage(),
                        'mt5_data' => $mt5User
                    ]);
                }
            }

            // Setup referral relationships
            $this->setupReferralRelationships();

            Log::info("Complete MT5 migration finished", [
                'total_processed' => $totalProcessed,
                'users_created' => $usersCreated,
                'accounts_created' => $accountsCreated,
                'errors' => $errors
            ]);

            return [
                'success' => true,
                'total_processed' => $totalProcessed,
                'users_created' => $usersCreated,
                'accounts_created' => $accountsCreated,
                'errors' => $errors
            ];

        } catch (\Exception $e) {
            Log::error("Complete MT5 migration failed: " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Create primary user from MT5 data (using best available data)
     */
    private function createPrimaryUserFromMT5($mt5User)
    {
        // Get all MT5 accounts for this email to find best data
        $allAccountsForEmail = DB::connection('mbf-dbmt5')
            ->table('mt5_users')
            ->where('Email', $mt5User->Email)
            ->orderByRaw("CASE 
                WHEN `Group` LIKE '%Affiliates%' THEN 1 
                WHEN Phone IS NOT NULL AND Phone != '' AND LENGTH(Phone) > 4 THEN 2
                WHEN Country IS NOT NULL AND Country != '' AND Country != 'Aland Islands' THEN 3
                ELSE 4 
            END")
            ->orderBy('Registration', 'desc')
            ->get();

        // Use the best account data for user creation
        $bestAccount = $allAccountsForEmail->first();
        
        // Determine IB status from any IB account
        $ibAccount = $allAccountsForEmail->where('Group', 'like', '%Affiliates%')->first();
        $ibStatus = $ibAccount ? $this->determineIbStatusFromGroup($ibAccount->Group) : ['status' => 0, 'type' => null, 'partner' => 0];
        
        // Parse names properly
        $names = $this->parseNames($bestAccount);
        
        // Generate username
        $username = $this->generateUsername($bestAccount->Email, $bestAccount->Login);

        $userData = [
            // Basic user data with best available information
            'firstname' => $names['firstname'],
            'lastname' => $names['lastname'],
            'username' => $username,
            'email' => $bestAccount->Email,
            'country_code' => $this->getBestCountry($allAccountsForEmail),
            'mobile' => $this->getBestPhone($allAccountsForEmail),
            'password' => bcrypt('password123'), // Default password
            'address' => $this->getBestAddress($allAccountsForEmail),

            // Account status
            'status' => 1, // Active
            'kv' => 1, 'ev' => 1, 'sv' => 1, 'ts' => 0, 'tv' => 1,

            // IB data from best IB account
            'ib_status' => $ibStatus['status'],
            'ib_type' => $ibStatus['type'],
            'partner' => $ibStatus['partner'],
            'ib_approved_at' => $ibStatus['status'] === 1 ? now() : null,

            // Primary MT5 account data (most recent)
            'mt5_login' => $bestAccount->Login,
            'mt5_group' => $bestAccount->Group,
            'mt5_balance' => $bestAccount->Balance ?: 0,
            'mt5_agent' => $bestAccount->Agent,
            'mt5_synced_at' => now(),

            'created_at' => now(),
            'updated_at' => now()
        ];

        return User::create($userData);
    }

    /**
     * Create MT5 account records in batches for performance
     */
    public function createMT5AccountsBatch($batchSize = 1000)
    {
        try {
            Log::info("Creating MT5 account records in batches of {$batchSize}");

            $users = User::whereNotNull('mt5_login')
                ->whereDoesntHave('userAccounts')
                ->get();

            $totalUsers = $users->count();
            $processed = 0;
            $created = 0;

            Log::info("Found {$totalUsers} users without MT5 account records");

            foreach ($users->chunk($batchSize) as $userChunk) {
                $accountsToInsert = [];

                foreach ($userChunk as $user) {
                    $mt5User = DB::connection('mbf-dbmt5')
                        ->table('mt5_users')
                        ->where('Login', $user->mt5_login)
                        ->first();

                    if ($mt5User) {
                        $accountsToInsert[] = [
                            'User_Id' => $user->id,
                            'Account' => $mt5User->Login,
                            'Master_Password' => 'migrated',
                            'created_at' => $mt5User->Registration ? Carbon::parse($mt5User->Registration) : now(),
                            'updated_at' => now()
                        ];
                    }
                    $processed++;
                }

                if (!empty($accountsToInsert)) {
                    DB::table('user_accounts')->insert($accountsToInsert);
                    $created += count($accountsToInsert);
                }

                Log::info("Batch processed: {$processed}/{$totalUsers} users, {$created} accounts created");
            }

            Log::info("MT5 account batch creation completed", [
                'total_users' => $totalUsers,
                'accounts_created' => $created
            ]);

            return [
                'success' => true,
                'total_users' => $totalUsers,
                'accounts_created' => $created
            ];

        } catch (\Exception $e) {
            Log::error("MT5 account batch creation failed: " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Get best country from all accounts
     */
    private function getBestCountry($accounts)
    {
        foreach ($accounts as $account) {
            if (!empty($account->Country) && $account->Country !== 'Aland Islands') {
                return $account->Country;
            }
        }
        return '';
    }

    /**
     * Get best phone from all accounts
     */
    private function getBestPhone($accounts)
    {
        foreach ($accounts as $account) {
            if (!empty($account->Phone) && strlen($account->Phone) > 4) {
                return $account->Phone;
            }
        }
        return '';
    }

    /**
     * Get best address from all accounts
     */
    private function getBestAddress($accounts)
    {
        $bestAddress = ['address' => '', 'city' => '', 'state' => '', 'zip' => '', 'country' => ''];
        
        foreach ($accounts as $account) {
            if (!empty($account->Address) && empty($bestAddress['address'])) {
                $bestAddress['address'] = $account->Address;
            }
            if (!empty($account->City) && empty($bestAddress['city'])) {
                $bestAddress['city'] = $account->City;
            }
            if (!empty($account->State) && empty($bestAddress['state'])) {
                $bestAddress['state'] = $account->State;
            }
            if (!empty($account->ZipCode) && empty($bestAddress['zip'])) {
                $bestAddress['zip'] = $account->ZipCode;
            }
            if (!empty($account->Country) && empty($bestAddress['country'])) {
                $bestAddress['country'] = $account->Country;
            }
        }
        
        return $bestAddress;
    }

    /**
     * Parse names properly
     */
    private function parseNames($mt5User)
    {
        $firstName = '';
        $lastName = '';

        if (!empty(trim($mt5User->FirstName)) && !empty(trim($mt5User->LastName))) {
            $firstName = trim($mt5User->FirstName);
            $lastName = trim($mt5User->LastName);
        } elseif (!empty(trim($mt5User->FirstName))) {
            $fullName = trim($mt5User->FirstName);
            $nameParts = explode(' ', $fullName, 2);
            $firstName = $nameParts[0];
            $lastName = isset($nameParts[1]) && !empty(trim($nameParts[1])) ? trim($nameParts[1]) : '';
        } elseif (!empty(trim($mt5User->Name))) {
            $fullName = trim($mt5User->Name);
            $nameParts = explode(' ', $fullName, 2);
            $firstName = $nameParts[0];
            $lastName = isset($nameParts[1]) && !empty(trim($nameParts[1])) ? trim($nameParts[1]) : '';
        } else {
            $emailParts = explode('@', $mt5User->Email);
            $firstName = $emailParts[0] ?: 'User';
            $lastName = '';
        }

        if (empty($firstName)) {
            $firstName = 'User';
        }

        return ['firstname' => $firstName, 'lastname' => $lastName];
    }

    /**
     * Determine IB status from MT5 group
     */
    private function determineIbStatusFromGroup($group)
    {
        $ibGroups = [
            'real\\Affiliates' => ['status' => 1, 'type' => 'master', 'partner' => 1],
            'real\\IB\\IB MAIN' => ['status' => 1, 'type' => 'master', 'partner' => 1],
            'real\\IB\\IB SUB' => ['status' => 1, 'type' => 'sub', 'partner' => 1],
        ];

        return $ibGroups[$group] ?? ['status' => 0, 'type' => null, 'partner' => 0];
    }

    /**
     * Determine MT5 account type
     */
    private function determineMT5AccountType($group)
    {
        if (strpos($group, 'demo') !== false || strpos($group, 'Demo') !== false) {
            return 'demo';
        }
        return 'live';
    }

    /**
     * Generate unique username
     */
    private function generateUsername($email, $mt5Login)
    {
        $baseUsername = str_replace(['@', '.', '+'], '', $email);
        $baseUsername = substr($baseUsername, 0, 15);
        
        $counter = 1;
        $username = $baseUsername;
        
        while (User::where('username', $username)->exists()) {
            $username = $baseUsername . $counter;
            $counter++;
        }
        
        return $username;
    }

    /**
     * Clear existing data
     */
    private function clearExistingData()
    {
        Log::info("Clearing existing users and accounts data");

        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        DB::table('user_accounts')->truncate();
        DB::table('ib_commissions')->truncate();
        DB::table('transactions')->truncate();
        DB::table('deposits')->truncate();
        DB::table('withdrawals')->truncate();
        DB::table('users')->truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        Log::info("Data cleared successfully");
    }

    /**
     * Setup referral relationships
     */
    private function setupReferralRelationships()
    {
        Log::info("Setting up referral relationships");
        
        $users = User::whereNotNull('mt5_agent')
            ->where('mt5_agent', '!=', 0)
            ->whereNull('ref_by')
            ->get();

        $updated = 0;
        
        foreach ($users as $user) {
            $parentUser = User::where('mt5_login', $user->mt5_agent)->first();
            
            if ($parentUser) {
                $user->update(['ref_by' => $parentUser->id]);
                $updated++;
            }
        }

        Log::info("Referral relationships setup completed", ['updated' => $updated]);
        return $updated;
    }
}
