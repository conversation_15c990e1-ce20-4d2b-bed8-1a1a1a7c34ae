<?php

namespace App\Services;

use App\Models\User;
use App\Models\IbCommission;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class MT5CommissionSyncService
{
    /**
     * Sync commission data from MT5 deals for all IBs
     */
    public function syncAllIbCommissions($days = 30)
    {
        try {
            Log::info("Starting MT5 commission sync for all IBs");

            // Get all approved IBs
            $ibs = User::where('ib_status', 'approved')
                ->whereNotNull('mt5_login')
                ->get();

            $totalCommissions = 0;
            $totalDeals = 0;

            foreach ($ibs as $ib) {
                $result = $this->syncIbCommissions($ib, $days);
                $totalCommissions += $result['commission_count'];
                $totalDeals += $result['deal_count'];
            }

            Log::info("MT5 commission sync completed", [
                'total_ibs' => $ibs->count(),
                'total_commissions' => $totalCommissions,
                'total_deals' => $totalDeals
            ]);

            return [
                'success' => true,
                'total_ibs' => $ibs->count(),
                'total_commissions' => $totalCommissions,
                'total_deals' => $totalDeals
            ];

        } catch (\Exception $e) {
            Log::error("MT5 commission sync error: " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Sync commission data for specific IB
     */
    public function syncIbCommissions($ib, $days = 30)
    {
        try {
            $startDate = Carbon::now()->subDays($days);

            // Get commission deals from MT5 for this IB
            $commissionDeals = DB::connection('mbf-dbmt5')
                ->table('mt5_deals_2025')
                ->where('Login', $ib->mt5_login)
                ->where('Action', 18) // Commission action
                ->where('Time', '>=', $startDate)
                ->select('Deal', 'Login', 'Time', 'Profit as Commission', 'Comment', 'VolumeExt as Volume')
                ->orderBy('Time', 'desc')
                ->get();

            $commissionCount = 0;
            $totalCommission = 0;

            foreach ($commissionDeals as $deal) {
                // Check if commission already exists
                $existingCommission = IbCommission::where('mt5_deal_id', $deal->Deal)->first();
                
                if (!$existingCommission) {
                    // Create new commission record
                    IbCommission::create([
                        'mt5_deal_id' => $deal->Deal,
                        'from_user_id' => null, // Will be set when we identify the trader
                        'to_ib_user_id' => $ib->id,
                        'mt5_login' => $deal->Login,
                        'symbol' => 'COMMISSION', // Commission deals don't have symbols
                        'volume' => $deal->Volume / 100, // Convert to lots
                        'commission_amount' => abs($deal->Commission), // Use absolute value
                        'deal_time' => Carbon::parse($deal->Time),
                        'status' => 'processed',
                        'level' => 1,
                        'deal_commission' => $deal->Commission,
                        'notes' => $deal->Comment ?: 'MT5 Commission Payment'
                    ]);

                    $commissionCount++;
                    $totalCommission += abs($deal->Commission);
                }
            }

            return [
                'ib_id' => $ib->id,
                'commission_count' => $commissionCount,
                'deal_count' => $commissionDeals->count(),
                'total_commission' => $totalCommission
            ];

        } catch (\Exception $e) {
            Log::error("Failed to sync commissions for IB {$ib->id}", [
                'error' => $e->getMessage(),
                'ib_login' => $ib->mt5_login
            ]);
            
            return [
                'ib_id' => $ib->id,
                'commission_count' => 0,
                'deal_count' => 0,
                'total_commission' => 0,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get IB commission statistics
     */
    public function getIbCommissionStats($ibUserId, $period = '30days')
    {
        $startDate = match($period) {
            'today' => Carbon::today(),
            '7days' => Carbon::now()->subDays(7),
            '30days' => Carbon::now()->subDays(30),
            '90days' => Carbon::now()->subDays(90),
            default => Carbon::now()->subDays(30)
        };

        $commissions = IbCommission::where('to_ib_user_id', $ibUserId)
            ->where('deal_time', '>=', $startDate)
            ->get();

        return [
            'total_commission' => $commissions->sum('commission_amount'),
            'total_trades' => $commissions->count(),
            'total_volume' => $commissions->sum('volume'),
            'average_commission' => $commissions->count() > 0 ? $commissions->avg('commission_amount') : 0,
            'recent_commissions' => $commissions->sortByDesc('deal_time')->take(10)
        ];
    }

    /**
     * Get MT5 commission data directly from deals table
     */
    public function getMT5CommissionData($mt5Login, $days = 30)
    {
        try {
            $startDate = Carbon::now()->subDays($days);

            $commissionDeals = DB::connection('mbf-dbmt5')
                ->table('mt5_deals_2025')
                ->where('Login', $mt5Login)
                ->where('Action', 18) // Commission action
                ->where('Time', '>=', $startDate)
                ->select('Deal', 'Login', 'Time', 'Profit as Commission', 'Comment')
                ->orderBy('Time', 'desc')
                ->get();

            $totalCommission = $commissionDeals->sum('Commission');
            $commissionCount = $commissionDeals->count();

            return [
                'total_commission' => $totalCommission,
                'commission_count' => $commissionCount,
                'recent_commissions' => $commissionDeals->take(10),
                'period_days' => $days
            ];

        } catch (\Exception $e) {
            Log::error("Failed to get MT5 commission data", [
                'mt5_login' => $mt5Login,
                'error' => $e->getMessage()
            ]);
            
            return [
                'total_commission' => 0,
                'commission_count' => 0,
                'recent_commissions' => collect(),
                'period_days' => $days
            ];
        }
    }

    /**
     * Get referral network for IB
     */
    public function getIbReferralNetwork($ibUserId, $maxDepth = 5)
    {
        $ib = User::find($ibUserId);
        if (!$ib) {
            return [];
        }

        return $this->buildReferralTree($ib, 0, $maxDepth);
    }

    /**
     * Build referral tree recursively
     */
    private function buildReferralTree($user, $currentDepth = 0, $maxDepth = 5)
    {
        if ($currentDepth >= $maxDepth) {
            return [];
        }

        $tree = [];
        $referrals = User::where('ref_by', $user->id)->get();

        foreach ($referrals as $referral) {
            $commissionStats = $this->getIbCommissionStats($referral->id, '30days');
            
            $tree[] = [
                'user' => $referral,
                'level' => $currentDepth + 1,
                'is_ib' => $referral->isIb(),
                'ib_type' => $referral->ib_type,
                'mt5_login' => $referral->mt5_login,
                'mt5_balance' => $referral->mt5_balance ?? 0,
                'commission_stats' => $commissionStats,
                'children' => $this->buildReferralTree($referral, $currentDepth + 1, $maxDepth)
            ];
        }

        return $tree;
    }

    /**
     * Get network statistics
     */
    public function getNetworkStatistics($ibUserId)
    {
        $ib = User::find($ibUserId);
        if (!$ib) {
            return [];
        }

        $network = $this->getIbReferralNetwork($ibUserId);
        $allReferrals = $this->flattenReferralTree($network);

        $stats = [
            'direct_referrals' => count($network),
            'total_referrals' => count($allReferrals),
            'total_ibs' => count(array_filter($allReferrals, fn($ref) => $ref['is_ib'])),
            'total_commission' => array_sum(array_column($allReferrals, 'commission_stats.total_commission')),
            'levels' => []
        ];

        // Group by levels
        foreach ($allReferrals as $referral) {
            $level = $referral['level'];
            if (!isset($stats['levels'][$level])) {
                $stats['levels'][$level] = ['count' => 0, 'ibs' => 0, 'commission' => 0];
            }
            $stats['levels'][$level]['count']++;
            if ($referral['is_ib']) {
                $stats['levels'][$level]['ibs']++;
            }
            $stats['levels'][$level]['commission'] += $referral['commission_stats']['total_commission'] ?? 0;
        }

        return $stats;
    }

    /**
     * Flatten referral tree for statistics
     */
    private function flattenReferralTree($tree)
    {
        $flattened = [];
        
        foreach ($tree as $node) {
            $flattened[] = $node;
            if (!empty($node['children'])) {
                $flattened = array_merge($flattened, $this->flattenReferralTree($node['children']));
            }
        }
        
        return $flattened;
    }
}
