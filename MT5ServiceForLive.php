<?php

namespace App\Services;

use App\Models\Mt5Users;
use Illuminate\Support\Facades\Log;

class MT5Service
{
    private $pythonScript;
    private $pythonExe;
    private $mt5Server;
    private $mt5Port;
    private $managerLogin;
    private $managerPassword;
    private $build;
    private $agent;

    public function __construct()
    {
        $this->pythonExe = env('PYTHON_EXE', 'python');
        $this->pythonScript = env('PYTHON_SCRIPT', 'python/mt5manager.py');

        // MT5 Web API Configuration
        $this->mt5Server = env('MT5_SERVER', '**************');
        $this->mt5Port = env('MT5_PORT', 443);
        $this->managerLogin = env('MT5_MANAGER_LOGIN', '877966');
        $this->managerPassword = env('MT5_MANAGER_PASSWORD', 'ElVi!tL7');
        $this->build = env('MT5_BUILD', '484');
        $this->agent = env('MT5_AGENT', 'WebAPI');
    }

    /**
     * Change account password using Python Manager API
     */
    public function changeAccountPasswordWebAPI($accountLogin, $newPassword, $passwordType = 'main')
    {
        try {
            Log::info("Password change request for account: {$accountLogin}, type: {$passwordType}");

            // Validate password requirements (MT5 compliant)
            if (!$this->validateMT5Password($newPassword)) {
                return [
                    'success' => false,
                    'message' => 'Password must be 8-16 characters with uppercase, lowercase, numbers, and special characters',
                    'account_id' => $accountLogin
                ];
            }

            Log::info("Using Python Manager API for password changes");

            // Build Python command using environment variables
            $command = escapeshellarg($this->pythonExe) . " " . escapeshellarg($this->pythonScript) . " change_password --login {$accountLogin} --new_password " . escapeshellarg($newPassword) . " --password_type {$passwordType}";

            Log::info("Executing Python command: {$command}");

            // Execute command with timeout
            $output = [];
            $returnCode = 0;
            exec($command . ' 2>&1', $output, $returnCode);

            $outputString = implode("\n", $output);
            Log::info("Python script output: {$outputString}");

            if ($returnCode === 0) {
                // Parse JSON response from Python script
                $lines = explode("\n", $outputString);
                $jsonLine = null;

                foreach ($lines as $line) {
                    $line = trim($line);
                    if (strpos($line, '{"status":') === 0) {
                        $jsonLine = $line;
                        break;
                    }
                }

                if ($jsonLine) {
                    $result = json_decode($jsonLine, true);
                    if ($result && $result['status'] === 'success') {
                        Log::info("Password changed successfully for account {$accountLogin}");
                        return [
                            'success' => true,
                            'message' => "Password changed successfully for account {$accountLogin}",
                            'account_id' => $accountLogin,
                            'password_type' => $passwordType,
                            'method' => 'python_manager_api'
                        ];
                    } else {
                        Log::error("Password change failed for account {$accountLogin}: " . ($result['error_message'] ?? 'Unknown error'));
                        return [
                            'success' => false,
                            'message' => 'Password change failed: ' . ($result['error_message'] ?? 'Unknown error'),
                            'account_id' => $accountLogin
                        ];
                    }
                }

                // If no JSON found, assume success based on return code
                Log::info("Password change completed for account {$accountLogin} (no JSON response)");
                return [
                    'success' => true,
                    'message' => "Password changed successfully for account {$accountLogin}",
                    'account_id' => $accountLogin,
                    'password_type' => $passwordType,
                    'method' => 'python_manager_api'
                ];
            } else {
                Log::error("Python script failed for account {$accountLogin}: {$outputString}");
                return [
                    'success' => false,
                    'message' => 'Password change failed: ' . $outputString,
                    'account_id' => $accountLogin
                ];
            }

        } catch (\Exception $e) {
            Log::error("Exception during password change for account {$accountLogin}: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Password change failed: ' . $e->getMessage(),
                'account_id' => $accountLogin
            ];
        }
    }

    /**
     * Validate MT5 password requirements
     */
    private function validateMT5Password($password)
    {
        // MT5 password requirements: 8-16 characters, uppercase, lowercase, numbers, special characters
        if (strlen($password) < 8 || strlen($password) > 16) {
            return false;
        }

        $hasLower = preg_match('/[a-z]/', $password);
        $hasUpper = preg_match('/[A-Z]/', $password);
        $hasNumber = preg_match('/[0-9]/', $password);
        $hasSpecial = preg_match('/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/', $password);

        return $hasLower && $hasUpper && $hasNumber && $hasSpecial;
    }

    /**
     * Change account leverage using Python Manager API
     */
    public function changeAccountLeverageWebAPI($accountLogin, $newLeverage)
    {
        try {
            Log::info("Changing leverage for account: {$accountLogin} to {$newLeverage} via Python Manager API");

            // Validate leverage value (must match Python script LEVERAGE_OPTIONS)
            $validLeverages = [100, 200, 300, 400, 500];
            if (!in_array(intval($newLeverage), $validLeverages)) {
                return [
                    'success' => false,
                    'message' => 'Invalid leverage value. Must be one of: ' . implode(', ', $validLeverages),
                    'account_id' => $accountLogin
                ];
            }

            Log::info("Using Python Manager API for leverage changes");

            // Build Python command using environment variables
            $command = escapeshellarg($this->pythonExe) . " " . escapeshellarg($this->pythonScript) . " change_leverage --login {$accountLogin} --leverage {$newLeverage}";

            Log::info("Executing Python command: {$command}");

            // Execute command with timeout
            $output = [];
            $returnCode = 0;
            exec($command . ' 2>&1', $output, $returnCode);

            $outputString = implode("\n", $output);
            Log::info("Python script output: {$outputString}");

            if ($returnCode === 0) {
                // Parse JSON response from Python script
                $lines = explode("\n", $outputString);
                $jsonLine = null;

                foreach ($lines as $line) {
                    $line = trim($line);
                    if (strpos($line, '{"status":') === 0) {
                        $jsonLine = $line;
                        break;
                    }
                }

                if ($jsonLine) {
                    $result = json_decode($jsonLine, true);
                    if ($result && $result['status'] === 'success') {
                        Log::info("Leverage updated successfully for account {$accountLogin} to {$newLeverage}");
                        return [
                            'success' => true,
                            'message' => "Leverage updated to {$newLeverage} via Python Manager API",
                            'account_id' => $accountLogin,
                            'old_leverage' => $result['old_leverage'] ?? 'unknown',
                            'new_leverage' => $result['new_leverage'] ?? $newLeverage,
                            'method' => 'python_manager_api'
                        ];
                    }
                }

                // If no JSON found, assume success based on return code
                Log::info("Leverage change completed for account {$accountLogin} (no JSON response)");
                return [
                    'success' => true,
                    'message' => "Leverage updated to {$newLeverage} via Python Manager API",
                    'account_id' => $accountLogin,
                    'new_leverage' => $newLeverage,
                    'method' => 'python_manager_api'
                ];
            } else {
                Log::error("Python script failed for account {$accountLogin}: {$outputString}");
                return [
                    'success' => false,
                    'message' => 'Leverage change failed: ' . $outputString,
                    'account_id' => $accountLogin
                ];
            }

        } catch (\Exception $e) {
            Log::error("Exception during Manager API leverage change for account {$accountLogin}: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Leverage change failed: ' . $e->getMessage(),
                'account_id' => $accountLogin
            ];
        }
    }

    /**
     * Add balance to user's MT5 accounts
     */
    public function addBalanceToUserAccounts($user, $amount, $comment = "Deposit")
    {
        try {
            // Get user's MT5 accounts from the database
            $mt5Accounts = Mt5Users::getAccounts($user->email);
            
            if ($mt5Accounts->isEmpty()) {
                Log::warning("No MT5 accounts found for user: " . $user->email);
                return [
                    'success' => false,
                    'message' => 'No MT5 accounts found for user',
                    'accounts_updated' => 0,
                    'total_accounts' => 0
                ];
            }

            $successCount = 0;
            $totalAccounts = $mt5Accounts->count();
            $errors = [];

            foreach ($mt5Accounts as $account) {
                $result = $this->addBalanceToAccount($account->Login, $amount, $comment);
                
                if ($result['success']) {
                    $successCount++;
                    Log::info("Successfully added balance to MT5 account: " . $account->Login . " Amount: " . $amount);
                } else {
                    $errors[] = "Account {$account->Login}: " . $result['message'];
                    Log::error("Failed to add balance to MT5 account: " . $account->Login . " Error: " . $result['message']);
                }
            }

            $success = $successCount > 0;
            $message = $success 
                ? "Successfully updated {$successCount}/{$totalAccounts} MT5 accounts"
                : "Failed to update any MT5 accounts";

            Log::info("MT5 Balance Addition Summary: {$successCount}/{$totalAccounts} accounts updated successfully for user: " . $user->email);

            return [
                'success' => $success,
                'message' => $message,
                'accounts_updated' => $successCount,
                'total_accounts' => $totalAccounts,
                'errors' => $errors
            ];

        } catch (\Exception $e) {
            Log::error("Error in addBalanceToUserAccounts: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Exception: ' . $e->getMessage(),
                'accounts_updated' => 0,
                'total_accounts' => 0
            ];
        }
    }

    /**
     * Add balance to a specific MT5 account
     */
    public function addBalanceToAccount($login, $amount, $comment = "Deposit")
    {
        try {
            // Build the command to add balance to MT5 account
            $command = sprintf(
                '%s %s add_balance --login %d --amount %.2f --comment %s',
                escapeshellarg($this->pythonExe),
                escapeshellarg($this->pythonScript),
                $login,
                $amount,
                escapeshellarg($comment)
            );

            // Execute the command
            $output = shell_exec($command . ' 2>&1');
            $result = $this->extractJsonFromOutput($output);

            if ($result && isset($result['status']) && $result['status'] === 'success') {
                return [
                    'success' => true,
                    'message' => 'Balance added successfully',
                    'data' => $result
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $output ?: 'Unknown error occurred',
                    'data' => $result
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Exception: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Deduct balance from user's MT5 accounts
     * If specificAccount is provided, deduct only from that account
     */
    public function deductBalanceFromUserAccounts($user, $amount, $comment = "Withdrawal", $specificAccount = null)
    {
        try {
            // If specific account is provided, use only that account
            if ($specificAccount) {
                Log::info("Deducting from specific MT5 account: {$specificAccount} for user: {$user->email}");

                // Find the specific account
                $account = Mt5Users::getAccounts($user->email)->filter(function ($acc) use ($specificAccount) {
                    return $acc->Login == $specificAccount && stripos($acc->Group, 'real') !== false;
                })->first();

                if (!$account) {
                    Log::error("Specific MT5 account {$specificAccount} not found or not real for user: {$user->email}");
                    return [
                        'success' => false,
                        'message' => "MT5 account {$specificAccount} not found or not accessible",
                        'accounts_updated' => 0,
                        'total_accounts' => 0
                    ];
                }

                // Check if account has sufficient balance
                $currentBalance = $account->Balance ?? 0;
                if ($currentBalance < $amount) {
                    Log::error("Insufficient balance in MT5 account {$specificAccount}. Required: {$amount}, Available: {$currentBalance}");
                    return [
                        'success' => false,
                        'message' => "Insufficient balance in MT5 account {$specificAccount}. Required: {$amount}, Available: {$currentBalance}",
                        'accounts_updated' => 0,
                        'total_accounts' => 1
                    ];
                }

                // Deduct from the specific account
                $result = $this->deductBalanceFromAccount($account->Login, $amount, $comment);

                if ($result['success']) {
                    Log::info("Successfully deducted {$amount} from specific MT5 account: {$account->Login}");

                    // Update balance cache after successful operation
                    $this->updateBalanceCache($user->email, $account->Login, 'deduction', $amount);

                    return [
                        'success' => true,
                        'message' => "Successfully deducted {$amount} from MT5 account {$account->Login}",
                        'accounts_updated' => 1,
                        'total_accounts' => 1,
                        'amount_deducted' => $amount,
                        'remaining_amount' => 0,
                        'errors' => []
                    ];
                } else {
                    Log::error("Failed to deduct from specific MT5 account {$account->Login}: " . $result['message']);
                    return [
                        'success' => false,
                        'message' => "Failed to deduct from MT5 account {$account->Login}: " . $result['message'],
                        'accounts_updated' => 0,
                        'total_accounts' => 1,
                        'amount_deducted' => 0,
                        'remaining_amount' => $amount,
                        'errors' => ["Account {$account->Login}: " . $result['message']]
                    ];
                }
            }

            // Original logic for multiple accounts (when no specific account is provided)
            // Get user's MT5 accounts from the database (only real accounts for withdrawals)
            $mt5Accounts = Mt5Users::getAccounts($user->email)->filter(function ($account) {
                return stripos($account->Group, 'real') !== false;
            });

            if ($mt5Accounts->isEmpty()) {
                Log::warning("No real MT5 accounts found for user: " . $user->email);
                return [
                    'success' => false,
                    'message' => 'No real MT5 accounts found for user',
                    'accounts_updated' => 0,
                    'total_accounts' => 0
                ];
            }

            $successCount = 0;
            $totalAccounts = $mt5Accounts->count();
            $errors = [];
            $remainingAmount = $amount;

            // Deduct from accounts with available balance
            foreach ($mt5Accounts as $account) {
                if ($remainingAmount <= 0) break;

                // Use the account's current balance from the database (faster than API call)
                $currentBalance = $account->Balance ?? 0;
                if ($currentBalance <= 0) {
                    continue; // Skip accounts with no balance
                }

                // Calculate amount to deduct from this account
                $deductAmount = min($remainingAmount, $currentBalance);

                $result = $this->deductBalanceFromAccount($account->Login, $deductAmount, $comment);

                if ($result['success']) {
                    $successCount++;
                    $remainingAmount -= $deductAmount;
                    Log::info("Successfully deducted balance from MT5 account: " . $account->Login . " Amount: " . $deductAmount);
                } else {
                    $errors[] = "Account {$account->Login}: " . $result['message'];
                    Log::error("Failed to deduct balance from MT5 account: " . $account->Login . " Error: " . $result['message']);
                }
            }

            $success = $remainingAmount <= 0;
            $deductedAmount = $amount - $remainingAmount;
            $message = $success
                ? "Successfully deducted {$deductedAmount} from {$successCount}/{$totalAccounts} MT5 accounts"
                : "Partially deducted {$deductedAmount} from {$successCount}/{$totalAccounts} MT5 accounts. Remaining: {$remainingAmount}";

            Log::info("MT5 Balance Deduction Summary: {$deductedAmount} deducted from {$successCount}/{$totalAccounts} accounts for user: " . $user->email);

            return [
                'success' => $success,
                'message' => $message,
                'accounts_updated' => $successCount,
                'total_accounts' => $totalAccounts,
                'amount_deducted' => $deductedAmount,
                'remaining_amount' => $remainingAmount,
                'errors' => $errors
            ];

        } catch (\Exception $e) {
            Log::error("Error in deductBalanceFromUserAccounts: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Exception: ' . $e->getMessage(),
                'accounts_updated' => 0,
                'total_accounts' => 0
            ];
        }
    }

    /**
     * Deduct balance from a specific MT5 account
     */
    public function deductBalanceFromAccount($login, $amount, $comment = "Withdrawal")
    {
        try {
            // Build the command to deduct balance from MT5 account (negative amount)
            $command = sprintf(
                '%s %s add_balance --login %d --amount %.2f --comment %s',
                escapeshellarg($this->pythonExe),
                escapeshellarg($this->pythonScript),
                $login,
                -$amount, // Negative amount for deduction
                escapeshellarg($comment)
            );

            // Execute the command with timeout
            $output = $this->executeCommandWithTimeout($command, 10); // 10 second timeout
            $result = $this->extractJsonFromOutput($output);

            if ($result && isset($result['status']) && $result['status'] === 'success') {
                return [
                    'success' => true,
                    'message' => 'Balance deducted successfully',
                    'data' => $result
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $output ?: 'Unknown error occurred',
                    'data' => $result
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Exception: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Get balance of a specific MT5 account
     */
    public function getAccountBalance($login)
    {
        try {
            $command = sprintf(
                '%s %s get_balance --login %d',
                escapeshellarg($this->pythonExe),
                escapeshellarg($this->pythonScript),
                $login
            );

            // Add timeout to prevent hanging
            $output = $this->executeCommandWithTimeout($command, 10); // 10 second timeout
            $result = $this->extractJsonFromOutput($output);

            if ($result && isset($result['status']) && $result['status'] === 'success') {
                return [
                    'success' => true,
                    'message' => 'Balance retrieved successfully',
                    'data' => $result
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $output ?: 'Unknown error occurred',
                    'data' => $result
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Exception: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Change leverage for a specific MT5 account
     */
    public function changeLeverage($login, $leverage)
    {
        try {
            $command = sprintf(
                '%s %s change_leverage --login %d --leverage %d',
                escapeshellarg($this->pythonExe),
                escapeshellarg($this->pythonScript),
                $login,
                $leverage
            );

            $output = shell_exec($command . ' 2>&1');
            $result = json_decode($output, true);

            if ($result && isset($result['status']) && $result['status'] === 'success') {
                return [
                    'success' => true,
                    'message' => 'Leverage changed successfully',
                    'data' => $result
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $output ?: 'Unknown error occurred',
                    'data' => $result
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Exception: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Change password for a specific MT5 account
     */
    public function changePassword($login, $newPassword, $passwordType = 'main')
    {
        try {
            $command = sprintf(
                '%s %s change_password --login %d --new_password %s --password_type %s',
                escapeshellarg($this->pythonExe),
                escapeshellarg($this->pythonScript),
                $login,
                escapeshellarg($newPassword),
                $passwordType
            );

            $output = shell_exec($command . ' 2>&1');
            $result = json_decode($output, true);

            if ($result && isset($result['status']) && $result['status'] === 'success') {
                return [
                    'success' => true,
                    'message' => 'Password changed successfully',
                    'data' => $result
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $output ?: 'Unknown error occurred',
                    'data' => $result
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Exception: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Create a new MT5 account
     */
    public function createAccount($userData)
    {
        try {
            $command = sprintf(
                '%s %s create_account --first_name %s --last_name %s --password %s --group %s --leverage %d --email %s --country %s --city %s --state %s --address %s --zipcode %s --phone %s --initial_balance %.2f',
                escapeshellarg($this->pythonExe),
                escapeshellarg($this->pythonScript),
                escapeshellarg($userData['first_name']),
                escapeshellarg($userData['last_name']),
                escapeshellarg($userData['password']),
                escapeshellarg($userData['group']),
                $userData['leverage'],
                escapeshellarg($userData['email']),
                escapeshellarg($userData['country']),
                escapeshellarg($userData['city']),
                escapeshellarg($userData['state']),
                escapeshellarg($userData['address']),
                escapeshellarg($userData['zipcode']),
                escapeshellarg($userData['phone']),
                $userData['initial_balance'] ?? 0.0
            );

            $output = shell_exec($command . ' 2>&1');
            $result = json_decode($output, true);

            if ($result && isset($result['Login'])) {
                return [
                    'success' => true,
                    'message' => 'Account created successfully',
                    'data' => $result
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $output ?: 'Unknown error occurred',
                    'data' => $result
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Exception: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Execute command with timeout to prevent hanging
     */
    private function executeCommandWithTimeout($command, $timeout = 10)
    {
        try {
            // Use proc_open for better control and timeout handling
            $descriptorspec = [
                0 => ["pipe", "r"],  // stdin
                1 => ["pipe", "w"],  // stdout
                2 => ["pipe", "w"]   // stderr
            ];

            $process = proc_open($command, $descriptorspec, $pipes);

            if (is_resource($process)) {
                // Close stdin
                fclose($pipes[0]);

                // Set non-blocking mode for stdout and stderr
                stream_set_blocking($pipes[1], false);
                stream_set_blocking($pipes[2], false);

                $output = '';
                $error = '';
                $start_time = time();

                // Read output with timeout
                while (time() - $start_time < $timeout) {
                    $stdout = fread($pipes[1], 8192);
                    $stderr = fread($pipes[2], 8192);

                    if ($stdout !== false) {
                        $output .= $stdout;
                    }
                    if ($stderr !== false) {
                        $error .= $stderr;
                    }

                    // Check if process has finished
                    $status = proc_get_status($process);
                    if (!$status['running']) {
                        break;
                    }

                    // Small delay to prevent high CPU usage
                    usleep(100000); // 0.1 seconds
                }

                // Close pipes
                fclose($pipes[1]);
                fclose($pipes[2]);

                // Terminate process if still running
                $status = proc_get_status($process);
                if ($status['running']) {
                    proc_terminate($process);
                    proc_close($process);
                    throw new \Exception("Command timed out after {$timeout} seconds");
                }

                proc_close($process);

                // Return output or error
                return $output ?: $error;
            } else {
                throw new \Exception("Failed to start process");
            }

        } catch (\Exception $e) {
            \Log::error("Command execution failed: " . $e->getMessage() . " Command: " . $command);
            return "Error: " . $e->getMessage();
        }
    }

    /**
     * Extract JSON from Python script output
     * Python scripts output logging info and other text before JSON
     */
    private function extractJsonFromOutput($output)
    {
        if (empty($output)) {
            return null;
        }

        // Look for JSON in the output (starts with { and ends with })
        $lines = explode("\n", $output);
        foreach ($lines as $line) {
            $line = trim($line);
            if (strpos($line, '{') === 0 && strrpos($line, '}') === strlen($line) - 1) {
                $result = json_decode($line, true);
                if ($result !== null) {
                    return $result;
                }
            }
        }

        // If no valid JSON found, try to decode the entire output
        $result = json_decode($output, true);
        return $result;
    }

    /**
     * Update balance cache after MT5 operation
     */
    private function updateBalanceCache($userEmail, $accountLogin, $operation, $amount)
    {
        try {
            $balanceService = new \App\Services\BalanceUpdateService();
            $balanceService->updateBalanceAfterOperation($userEmail, $accountLogin, $operation, $amount);
        } catch (\Exception $e) {
            Log::error("Failed to update balance cache: " . $e->getMessage());
        }
    }

    /**
     * Change account password (optimized version)
     */
    public function changeAccountPassword($accountLogin, $newPassword, $passwordType = 'main')
    {
        try {
            Log::info("Changing password for account: {$accountLogin}, type: {$passwordType}");

            // Get current password from database for verification
            $userAccount = \App\Models\UserAccounts::where('Account', $accountLogin)->first();
            $currentPassword = $userAccount ? $userAccount->Master_Password : null;

            // Build command with current password for verification (if available)
            $commandParams = [
                '--login' => $accountLogin,
                '--new_password' => $newPassword,
                '--password_type' => $passwordType,
                '--timeout' => '30'  // 30 second timeout
            ];

            // Add current password if available for verification
            if ($currentPassword) {
                $commandParams['--current_password'] = $currentPassword;
                Log::info("Using stored current password for verification");
            } else {
                Log::warning("No current password found in database for account {$accountLogin}");
            }

            $command = $this->buildPythonCommand('change_password', $commandParams);

            // Execute with timeout and better error handling
            $result = $this->executePythonCommandWithTimeout($command, 30);

            if ($result['success']) {
                Log::info("Password changed successfully for account: {$accountLogin}");
                return [
                    'success' => true,
                    'message' => "Password updated successfully for account {$accountLogin}",
                    'account' => $accountLogin,
                    'password_type' => $passwordType
                ];
            } else {
                Log::error("Password change failed for account {$accountLogin}: " . $result['message']);
                return [
                    'success' => false,
                    'message' => $result['message'],
                    'account' => $accountLogin
                ];
            }

        } catch (\Exception $e) {
            Log::error("Exception during password change for account {$accountLogin}: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Password change failed: ' . $e->getMessage(),
                'account' => $accountLogin
            ];
        }
    }

    /**
     * Update account leverage (optimized version)
     */
    public function updateAccountLeverage($accountLogin, $newLeverage)
    {
        try {
            Log::info("Updating leverage for account: {$accountLogin} to {$newLeverage}");

            // Build optimized command with timeout (correct command name)
            $command = $this->buildPythonCommand('change_leverage', [
                '--login' => $accountLogin,
                '--leverage' => $newLeverage,
                '--timeout' => '30'  // 30 second timeout
            ]);

            // Execute with timeout and better error handling
            $result = $this->executePythonCommandWithTimeout($command, 30);

            if ($result['success']) {
                Log::info("Leverage updated successfully for account: {$accountLogin}");
                return [
                    'success' => true,
                    'message' => "Leverage updated successfully for account {$accountLogin}",
                    'account' => $accountLogin,
                    'new_leverage' => $newLeverage
                ];
            } else {
                Log::error("Leverage update failed for account {$accountLogin}: " . $result['message']);
                return [
                    'success' => false,
                    'message' => $result['message'],
                    'account' => $accountLogin
                ];
            }

        } catch (\Exception $e) {
            Log::error("Exception during leverage update for account {$accountLogin}: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Leverage update failed: ' . $e->getMessage(),
                'account' => $accountLogin
            ];
        }
    }

    /**
     * Build Python command with parameters
     */
    private function buildPythonCommand($action, $params = [])
    {
        $command = escapeshellarg($this->pythonExe) . ' ' . escapeshellarg($this->pythonScript) . ' ' . $action;

        foreach ($params as $key => $value) {
            $command .= ' ' . $key . ' ' . escapeshellarg($value);
        }

        return $command;
    }

    /**
     * Execute Python command with timeout and better error handling
     */
    private function executePythonCommandWithTimeout($command, $timeout = 30)
    {
        try {
            Log::info("Executing command: " . $command);

            // Set timeout for the command
            $descriptorspec = [
                0 => ["pipe", "r"],  // stdin
                1 => ["pipe", "w"],  // stdout
                2 => ["pipe", "w"]   // stderr
            ];

            $process = proc_open($command, $descriptorspec, $pipes);

            if (!is_resource($process)) {
                return [
                    'success' => false,
                    'message' => 'Failed to start Python process'
                ];
            }

            // Close stdin
            fclose($pipes[0]);

            // Set timeout for reading
            $start_time = time();
            $output = '';
            $error = '';

            while (time() - $start_time < $timeout) {
                $status = proc_get_status($process);
                if (!$status['running']) {
                    break;
                }
                usleep(100000); // 0.1 second
            }

            // Read output and error
            $output = stream_get_contents($pipes[1]);
            $error = stream_get_contents($pipes[2]);

            fclose($pipes[1]);
            fclose($pipes[2]);

            $return_code = proc_close($process);

            if ($return_code === 0 && !empty($output)) {
                // Try to extract JSON from output
                $lines = explode("\n", $output);
                foreach ($lines as $line) {
                    $line = trim($line);
                    if (strpos($line, '{') !== false && strpos($line, '}') !== false) {
                        $data = json_decode($line, true);
                        if ($data && isset($data['success'])) {
                            return $data;
                        }
                    }
                }

                return [
                    'success' => true,
                    'message' => 'Operation completed successfully',
                    'output' => $output
                ];
            } else {
                return [
                    'success' => false,
                    'message' => !empty($error) ? $error : 'Command execution failed',
                    'return_code' => $return_code,
                    'output' => $output
                ];
            }

        } catch (\Exception $e) {
            Log::error("Exception in executePythonCommandWithTimeout: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Execution failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get batch balances for multiple accounts (performance optimization)
     */
    public function getBatchBalances($accountLogins)
    {
        try {
            Log::info("Getting batch balances for accounts: " . implode(', ', $accountLogins));

            // Build optimized batch command
            $loginsString = implode(',', $accountLogins);
            $command = $this->buildPythonCommand('get_batch_balances', [
                '--logins' => $loginsString,
                '--timeout' => '30'
            ]);

            // Execute with timeout
            $result = $this->executePythonCommandWithTimeout($command, 30);

            if ($result['success']) {
                Log::info("Batch balances retrieved successfully for " . count($accountLogins) . " accounts");
                return [
                    'success' => true,
                    'balances' => $result['balances'] ?? [],
                    'message' => "Batch balances retrieved successfully"
                ];
            } else {
                Log::error("Batch balance retrieval failed: " . $result['message']);
                return [
                    'success' => false,
                    'balances' => [],
                    'message' => $result['message']
                ];
            }

        } catch (\Exception $e) {
            Log::error("Exception during batch balance retrieval: " . $e->getMessage());
            return [
                'success' => false,
                'balances' => [],
                'message' => 'Batch balance retrieval failed: ' . $e->getMessage()
            ];
        }
    }
}

/**
 * MT5 Web API Request Class
 * Based on the working implementation from mt5-test11.php
 */
class MT5WebAPIRequest
{
    private $m_curl = null;
    private $m_server = "";
    private $m_port = 443;

    public function __construct($server, $port = 443)
    {
        $this->m_server = $server;
        $this->m_port = $port;
        $this->init();
    }

    private function init()
    {
        $this->shutdown();
        if ($this->m_server == null) {
            return false;
        }

        $this->m_curl = curl_init();
        if ($this->m_curl == null) {
            return false;
        }

        // SSL Configuration - Disabled for IP address
        curl_setopt($this->m_curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($this->m_curl, CURLOPT_SSL_VERIFYHOST, 0);

        // Connection settings
        curl_setopt($this->m_curl, CURLOPT_MAXCONNECTS, 1);
        curl_setopt($this->m_curl, CURLOPT_HTTPHEADER, array('Connection: Keep-Alive'));

        return true;
    }

    public function shutdown()
    {
        if ($this->m_curl != null) {
            curl_close($this->m_curl);
        }
        $this->m_curl = null;
    }

    public function get($path)
    {
        if ($this->m_curl == null) {
            return false;
        }

        $url = "https://{$this->m_server}:{$this->m_port}{$path}";

        curl_setopt($this->m_curl, CURLOPT_POST, false);
        curl_setopt($this->m_curl, CURLOPT_URL, $url);
        curl_setopt($this->m_curl, CURLOPT_RETURNTRANSFER, true);

        // Set headers
        curl_setopt($this->m_curl, CURLOPT_HTTPHEADER, [
            'Accept: */*',
            'User-Agent: MetaTrader 5 Web API/5.2005 (Windows NT 6.2; x64)',
            'Connection: keep-alive'
        ]);

        $result = curl_exec($this->m_curl);

        if ($result === false) {
            Log::error('MT5 Web API Curl GET error: ' . curl_error($this->m_curl));
            return false;
        }

        $code = curl_getinfo($this->m_curl, CURLINFO_HTTP_CODE);
        if ($code != 200) {
            Log::error('MT5 Web API Curl GET code: ' . $code);
            return false;
        }

        return $result;
    }

    public function post($path, $body, $contentType = 'application/json')
    {
        if ($this->m_curl == null) {
            return false;
        }

        $url = "https://{$this->m_server}:{$this->m_port}{$path}";

        curl_setopt($this->m_curl, CURLOPT_POST, true);
        curl_setopt($this->m_curl, CURLOPT_URL, $url);
        curl_setopt($this->m_curl, CURLOPT_POSTFIELDS, $body);
        curl_setopt($this->m_curl, CURLOPT_RETURNTRANSFER, true);

        // Set headers based on content type (JSON for MT5 Web API)
        curl_setopt($this->m_curl, CURLOPT_HTTPHEADER, [
            'Accept: */*',
            'User-Agent: MetaTrader 5 Web API/5.2005 (Windows NT 6.2; x64)',
            'Connection: keep-alive',
            'Content-Type: ' . $contentType,
            'Content-Length: ' . strlen($body)
        ]);

        $result = curl_exec($this->m_curl);

        if ($result === false) {
            Log::error('MT5 Web API Curl POST error: ' . curl_error($this->m_curl));
            return false;
        }

        $code = curl_getinfo($this->m_curl, CURLINFO_HTTP_CODE);
        if ($code != 200) {
            Log::error('MT5 Web API Curl POST code: ' . $code);
            return false;
        }

        return $result;
    }

    public function auth($login, $password, $build, $agent)
    {
        if ($this->m_curl == null) {
            return false;
        }

        try {
            // Step 1: Auth Start
            $path = "/api/auth/start?version={$build}&agent={$agent}&login={$login}&type=manager";
            $result = $this->get($path);

            if ($result === false) {
                Log::error('MT5 Web API auth start failed');
                return false;
            }

            $auth_start_answer = json_decode($result);
            if ((int)$auth_start_answer->retcode != 0) {
                Log::error('MT5 Web API auth start error: ' . $auth_start_answer->retcode);
                return false;
            }

            // Step 2: Process server random
            $srv_rand = hex2bin($auth_start_answer->srv_rand);

            // Calculate password hash
            $password_utf16 = mb_convert_encoding($password, 'UTF-16LE', 'UTF-8');
            $password_hash = md5($password_utf16, true) . 'WebAPI';
            $srv_rand_answer = md5(md5($password_hash, true) . $srv_rand);

            // Generate client random
            $cli_rand_buf = random_bytes(16);
            $cli_rand = bin2hex($cli_rand_buf);

            // Step 3: Auth Answer
            $path = "/api/auth/answer?srv_rand_answer={$srv_rand_answer}&cli_rand={$cli_rand}";
            $result = $this->get($path);

            if ($result === false) {
                Log::error('MT5 Web API auth answer failed');
                return false;
            }

            $auth_answer = json_decode($result);
            if ((int)$auth_answer->retcode != 0) {
                Log::error('MT5 Web API auth answer error: ' . $auth_answer->retcode);
                return false;
            }

            // Verify server's answer
            $cli_rand_answer = md5(md5($password_hash, true) . $cli_rand_buf);
            if ($cli_rand_answer !== $auth_answer->cli_rand_answer) {
                Log::error('MT5 Web API auth answer error: invalid client answer');
                return false;
            }

            Log::info('MT5 Web API authentication successful');
            return true;

        } catch (\Exception $e) {
            Log::error('MT5 Web API authentication exception: ' . $e->getMessage());
            return false;
        }
    }
}
