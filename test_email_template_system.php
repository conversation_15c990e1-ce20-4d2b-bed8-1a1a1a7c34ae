<?php
/**
 * Test Email Template System
 * Verify all components are working correctly
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

echo "🧪 EMAIL TEMPLATE SYSTEM TEST\n";
echo "============================\n\n";

try {
    // Test 1: Check if NotificationTemplate model exists and has data
    echo "📋 Test 1: Database Connection & Templates\n";
    $templates = App\Models\NotificationTemplate::take(3)->get();
    
    if ($templates->count() > 0) {
        echo "✅ Found {$templates->count()} templates in database\n";
        foreach ($templates as $template) {
            echo "   - ID: {$template->id}, Name: {$template->name}, Action: {$template->act}\n";
        }
    } else {
        echo "❌ No templates found in database\n";
        exit(1);
    }
    
    // Test 2: Check route registration
    echo "\n🛣️ Test 2: Route Registration\n";
    $routes = collect(Route::getRoutes())->filter(function($route) {
        return str_contains($route->getName() ?? '', 'template.preview');
    });
    
    if ($routes->count() > 0) {
        echo "✅ Template preview route found\n";
        foreach ($routes as $route) {
            echo "   - Name: {$route->getName()}\n";
            echo "   - URI: {$route->uri()}\n";
            echo "   - Methods: " . implode(', ', $route->methods()) . "\n";
        }
    } else {
        echo "❌ Template preview route not found\n";
    }
    
    // Test 3: Check controller method exists
    echo "\n🎛️ Test 3: Controller Method\n";
    $controller = new App\Http\Controllers\Admin\NotificationController();
    
    if (method_exists($controller, 'templatePreview')) {
        echo "✅ templatePreview method exists in NotificationController\n";
    } else {
        echo "❌ templatePreview method not found in NotificationController\n";
    }
    
    if (method_exists($controller, 'sendTestEmail')) {
        echo "✅ sendTestEmail method exists in NotificationController\n";
    } else {
        echo "❌ sendTestEmail method not found in NotificationController\n";
    }
    
    // Test 4: Check JavaScript and CSS files
    echo "\n📁 Test 4: Asset Files\n";
    $jsFile = 'assets/admin/js/simple-email-editor.js';
    $cssFile = 'assets/admin/css/simple-email-editor.css';
    
    if (file_exists($jsFile)) {
        $jsSize = filesize($jsFile);
        echo "✅ JavaScript file exists: {$jsFile} ({$jsSize} bytes)\n";
    } else {
        echo "❌ JavaScript file missing: {$jsFile}\n";
    }
    
    if (file_exists($cssFile)) {
        $cssSize = filesize($cssFile);
        echo "✅ CSS file exists: {$cssFile} ({$cssSize} bytes)\n";
    } else {
        echo "❌ CSS file missing: {$cssFile}\n";
    }
    
    // Test 5: Check Blade template
    echo "\n📄 Test 5: Blade Templates\n";
    $editTemplate = 'resources/views/admin/notification/edit.blade.php';
    $globalTemplate = 'resources/views/admin/notification/global_template.blade.php';
    
    if (file_exists($editTemplate)) {
        echo "✅ Edit template exists: {$editTemplate}\n";
    } else {
        echo "❌ Edit template missing: {$editTemplate}\n";
    }
    
    if (file_exists($globalTemplate)) {
        echo "✅ Global template exists: {$globalTemplate}\n";
    } else {
        echo "❌ Global template missing: {$globalTemplate}\n";
    }
    
    // Test 6: Generate test URLs
    echo "\n🔗 Test 6: URL Generation\n";
    $firstTemplate = $templates->first();
    if ($firstTemplate) {
        try {
            $editUrl = route('admin.setting.notification.template.edit', $firstTemplate->id);
            echo "✅ Edit URL: {$editUrl}\n";
            
            $previewUrl = route('admin.setting.notification.template.preview', $firstTemplate->id);
            echo "✅ Preview URL: {$previewUrl}\n";
            
            $testUrl = route('admin.setting.notification.template.test');
            echo "✅ Test Email URL: {$testUrl}\n";
            
        } catch (Exception $e) {
            echo "❌ URL generation failed: {$e->getMessage()}\n";
        }
    }
    
    echo "\n✅ EMAIL TEMPLATE SYSTEM TEST COMPLETED\n";
    echo "All core components appear to be in place.\n";
    echo "\n🌐 Next Steps:\n";
    echo "1. Open browser and navigate to admin panel\n";
    echo "2. Go to Notification Templates\n";
    echo "3. Edit a template and test preview functionality\n";
    echo "4. Test shortcode insertion and editor toggle\n";
    echo "5. Send a test email to verify email functionality\n";
    
} catch (Exception $e) {
    echo "❌ Test failed with error: {$e->getMessage()}\n";
    echo "Stack trace:\n{$e->getTraceAsString()}\n";
    exit(1);
}
