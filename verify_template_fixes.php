<?php
/**
 * Email Template Fixes Verification Script
 * Run this script to verify that the template enhancements are working correctly
 * 
 * Usage: php verify_template_fixes.php
 */

// Include Laravel bootstrap
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🔍 EMAIL TEMPLATE FIXES VERIFICATION SCRIPT\n";
echo "==========================================\n\n";

try {
    // Check if NotificationTemplate model exists
    if (!class_exists('App\Models\NotificationTemplate')) {
        echo "❌ NotificationTemplate model not found\n";
        exit(1);
    }

    // Get all notification templates
    $templates = App\Models\NotificationTemplate::all();
    echo "📧 Found {$templates->count()} email templates in database\n\n";

    if ($templates->count() === 0) {
        echo "⚠️  No templates found in database\n";
        exit(0);
    }

    // Check each template for professional structure
    $enhancedCount = 0;
    $needsEnhancement = [];

    foreach ($templates as $template) {
        echo "📋 Checking Template: {$template->name} (ID: {$template->id})\n";
        echo "   Type: {$template->act}\n";
        echo "   Subject: {$template->subj}\n";
        
        $content = $template->email_body;
        $contentLength = strlen($content);
        echo "   Content Length: {$contentLength} characters\n";
        
        // Check for professional structure indicators
        $indicators = [
            'DOCTYPE html' => strpos($content, '<!DOCTYPE html>') !== false,
            'MBFX Logo' => strpos($content, 'mbf.mybrokerforex.com/assets/images/logoIcon/logo.png') !== false,
            'Professional Layout' => strpos($content, 'width="600"') !== false && strpos($content, 'cellpadding="0"') !== false,
            'Red Header' => strpos($content, '#dc3545') !== false || strpos($content, 'linear-gradient') !== false,
            'White Logo Background' => strpos($content, 'background-color: #ffffff') !== false,
            'Centered Logo' => strpos($content, 'text-align: center') !== false,
            'Professional Footer' => strpos($content, 'MBFX - Professional Trading Platform') !== false,
            'Responsive Design' => strpos($content, 'max-width') !== false
        ];
        
        $passedChecks = 0;
        $totalChecks = count($indicators);
        
        foreach ($indicators as $check => $passed) {
            $status = $passed ? '✅' : '❌';
            echo "   {$status} {$check}\n";
            if ($passed) $passedChecks++;
        }
        
        $percentage = round(($passedChecks / $totalChecks) * 100);
        echo "   📊 Professional Structure: {$passedChecks}/{$totalChecks} ({$percentage}%)\n";
        
        if ($percentage >= 75) {
            echo "   ✅ Template is professionally enhanced\n";
            $enhancedCount++;
        } else {
            echo "   ⚠️  Template needs enhancement\n";
            $needsEnhancement[] = $template->id;
        }
        
        // Show content preview
        $preview = substr(strip_tags($content), 0, 100);
        echo "   📄 Preview: {$preview}...\n";
        
        echo "\n";
    }

    // Summary
    echo "📊 ENHANCEMENT SUMMARY\n";
    echo "=====================\n";
    echo "✅ Enhanced Templates: {$enhancedCount}/{$templates->count()}\n";
    
    if (!empty($needsEnhancement)) {
        echo "⚠️  Templates Needing Enhancement: " . implode(', ', $needsEnhancement) . "\n";
        echo "\n🔧 RECOMMENDED ACTION:\n";
        echo "Run: php artisan email:enhance-templates --template=" . implode(',', $needsEnhancement) . "\n";
    } else {
        echo "🎉 All templates are professionally enhanced!\n";
    }

    echo "\n";

    // Check for specific design elements
    echo "🎨 DESIGN VERIFICATION\n";
    echo "=====================\n";
    
    $sampleTemplate = $templates->first();
    if ($sampleTemplate) {
        $content = $sampleTemplate->email_body;
        
        // Check for correct logo placement
        if (strpos($content, 'background-color: #ffffff') !== false && 
            strpos($content, 'text-align: center') !== false &&
            strpos($content, 'mbf.mybrokerforex.com/assets/images/logoIcon/logo.png') !== false) {
            echo "✅ Logo has white background and is centered\n";
        } else {
            echo "❌ Logo placement needs fixing\n";
        }
        
        // Check for notification title section
        if (strpos($content, 'background-color: #dc3545') !== false &&
            strpos($content, 'color: #ffffff') !== false) {
            echo "✅ Notification title section has red background\n";
        } else {
            echo "❌ Notification title section needs red background\n";
        }
        
        // Check for professional structure
        if (strpos($content, 'MBFX - Professional Trading Platform') !== false) {
            echo "✅ Professional footer is present\n";
        } else {
            echo "❌ Professional footer is missing\n";
        }
    }

    echo "\n";

    // Check Laravel notification system
    echo "🔔 NOTIFICATION SYSTEM CHECK\n";
    echo "============================\n";
    
    // Check if notify function exists in admin layout
    $adminLayoutPath = 'resources/views/admin/layouts/app.blade.php';
    if (file_exists($adminLayoutPath)) {
        $adminLayout = file_get_contents($adminLayoutPath);
        
        if (strpos($adminLayout, 'function notify(') !== false) {
            echo "✅ Laravel notify() function found in admin layout\n";
        } else {
            echo "❌ Laravel notify() function not found in admin layout\n";
        }
        
        if (strpos($adminLayout, 'toastr') !== false || strpos($adminLayout, 'iziToast') !== false) {
            echo "✅ Notification library detected\n";
        } else {
            echo "⚠️  Notification library not clearly detected\n";
        }
    } else {
        echo "⚠️  Admin layout file not found at expected location\n";
    }

    // Check template edit page for notification handling
    $editTemplatePath = 'resources/views/admin/notification/edit.blade.php';
    if (file_exists($editTemplatePath)) {
        $editTemplate = file_get_contents($editTemplatePath);
        
        if (strpos($editTemplate, "notify('success'") !== false) {
            echo "✅ Success notifications enabled in template editor\n";
        } else {
            echo "❌ Success notifications not enabled in template editor\n";
        }
        
        if (strpos($editTemplate, "notify('error'") !== false) {
            echo "✅ Error notifications enabled in template editor\n";
        } else {
            echo "❌ Error notifications not enabled in template editor\n";
        }
    }

    echo "\n";

    // Cache status check
    echo "🗄️  CACHE STATUS CHECK\n";
    echo "=====================\n";
    
    // Check if caches exist
    $cacheDirectories = [
        'bootstrap/cache/config.php' => 'Config Cache',
        'bootstrap/cache/routes-v7.php' => 'Route Cache',
        'bootstrap/cache/services.php' => 'Services Cache',
        'storage/framework/views' => 'View Cache Directory'
    ];
    
    foreach ($cacheDirectories as $path => $name) {
        if (file_exists($path)) {
            echo "✅ {$name}: EXISTS\n";
        } else {
            echo "❌ {$name}: NOT FOUND\n";
        }
    }
    
    echo "\n💡 TIP: If templates aren't updating, run:\n";
    echo "   php artisan optimize:clear\n";
    echo "   php artisan cache:clear\n";
    echo "   php artisan view:clear\n";

    echo "\n";

    // Final recommendations
    echo "🎯 FINAL RECOMMENDATIONS\n";
    echo "========================\n";
    
    if ($enhancedCount === $templates->count()) {
        echo "✅ All templates are enhanced - system is ready!\n";
        echo "✅ Test by editing a template and checking for notifications\n";
        echo "✅ Send test emails to verify the new design\n";
    } else {
        echo "⚠️  Some templates need enhancement\n";
        echo "🔧 Run: php artisan email:enhance-templates\n";
        echo "🔧 Clear caches: php artisan optimize:clear\n";
        echo "🔧 Test notifications in admin panel\n";
    }

} catch (Exception $e) {
    echo "❌ Error running verification: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
    exit(1);
}

echo "\n🎉 Verification completed!\n";
?>
