@extends('admin.layouts.app')

@section('panel')
<div class="row">
    <div class="col-lg-12">
        <div class="card b-radius--10">
            <div class="card-header">
                <h5 class="card-title">@lang('IB Commission Levels')</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.ib.levels.store') }}" method="POST">
                    @csrf
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Number of Levels')</label>
                                <div class="input-group">
                                    <input type="number" name="level_count" min="1" max="10" placeholder="Enter number of levels" class="form-control" value="{{ old('level_count', $ibLevels->count()) }}">
                                    <button type="button" class="btn btn--primary generate-levels">@lang('Generate')</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="levels-container mt-4" style="{{ $ibLevels->count() > 0 ? '' : 'display: none;' }}">
                        <h6 class="text--danger mb-3">@lang('Configure commission percentages for each level')</h6>
                        <div class="levels-list">
                            @foreach($ibLevels as $level)
                            <div class="row level-row mb-3">
                                <div class="col-md-2">
                                    <label>@lang('Level') {{ $level->level }}</label>
                                    <input type="hidden" name="levels[{{ $level->level }}][level]" value="{{ $level->level }}">
                                </div>
                                <div class="col-md-3">
                                    <input type="text" name="levels[{{ $level->level }}][name]" class="form-control" placeholder="Level Name" value="{{ $level->name }}" required>
                                </div>
                                <div class="col-md-2">
                                    <input type="number" name="levels[{{ $level->level }}][commission_percent]" class="form-control" placeholder="Commission %" step="0.01" min="0" max="100" value="{{ $level->commission_percent }}" required>
                                </div>
                                <div class="col-md-2">
                                    <input type="number" name="levels[{{ $level->level }}][max_commission_percent]" class="form-control" placeholder="Max %" step="0.01" min="0" max="100" value="{{ $level->max_commission_percent }}" required>
                                </div>
                                <div class="col-md-3">
                                    <input type="text" name="levels[{{ $level->level }}][description]" class="form-control" placeholder="Description" value="{{ $level->description }}">
                                </div>
                            </div>
                            @endforeach
                        </div>
                        <button type="submit" class="btn btn--primary">@lang('Save Levels')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@if($ibLevels->count() > 0)
<div class="row mt-4">
    <div class="col-lg-12">
        <div class="card b-radius--10">
            <div class="card-header">
                <h5 class="card-title">@lang('Current IB Levels')</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive--sm table-responsive">
                    <table class="table table--light style--two">
                        <thead>
                            <tr>
                                <th>@lang('Level')</th>
                                <th>@lang('Name')</th>
                                <th>@lang('Commission %')</th>
                                <th>@lang('Max Commission %')</th>
                                <th>@lang('Description')</th>
                                <th>@lang('Status')</th>
                                <th>@lang('Action')</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($ibLevels as $level)
                            <tr>
                                <td>{{ $level->level }}</td>
                                <td>{{ $level->name }}</td>
                                <td>{{ $level->commission_percent }}%</td>
                                <td>{{ $level->max_commission_percent }}%</td>
                                <td>{{ $level->description ?? 'N/A' }}</td>
                                <td>
                                    @if($level->status)
                                        <span class="badge badge--success">@lang('Active')</span>
                                    @else
                                        <span class="badge badge--danger">@lang('Inactive')</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="button--group">
                                        <form action="{{ route('admin.ib.levels.toggle_status', $level->id) }}" method="POST" style="display: inline;">
                                            @csrf
                                            <button type="submit" class="btn btn-sm btn-outline--{{ $level->status ? 'danger' : 'success' }}">
                                                @if($level->status)
                                                    <i class="las la-eye-slash"></i> @lang('Deactivate')
                                                @else
                                                    <i class="las la-eye"></i> @lang('Activate')
                                                @endif
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endif
@endsection

@push('breadcrumb-plugins')
<div class="d-inline">
    <a href="{{ route('admin.ib.levels.statistics') }}" class="btn btn--primary">
        <i class="las la-chart-bar"></i> @lang('View Statistics')
    </a>
</div>
@endpush

@push('script')
<script>
(function($) {
    "use strict";

    $('.generate-levels').on('click', function() {
        const levelCount = $('input[name="level_count"]').val();
        
        if (!levelCount || levelCount < 1) {
            alert('Please enter a valid number of levels');
            return;
        }

        let html = '';
        for (let i = 1; i <= levelCount; i++) {
            html += `
                <div class="row level-row mb-3">
                    <div class="col-md-2">
                        <label>Level ${i}</label>
                        <input type="hidden" name="levels[${i}][level]" value="${i}">
                    </div>
                    <div class="col-md-3">
                        <input type="text" name="levels[${i}][name]" class="form-control" placeholder="Level Name" value="Level ${i}" required>
                    </div>
                    <div class="col-md-2">
                        <input type="number" name="levels[${i}][commission_percent]" class="form-control" placeholder="Commission %" step="0.01" min="0" max="100" required>
                    </div>
                    <div class="col-md-2">
                        <input type="number" name="levels[${i}][max_commission_percent]" class="form-control" placeholder="Max %" step="0.01" min="0" max="100" required>
                    </div>
                    <div class="col-md-3">
                        <input type="text" name="levels[${i}][description]" class="form-control" placeholder="Description">
                    </div>
                </div>
            `;
        }

        $('.levels-list').html(html);
        $('.levels-container').show();
    });

})(jQuery);
</script>
@endpush
