<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\MT5CompleteMigrationService;
use App\Models\User;
use App\Models\UserAccounts;
use Illuminate\Support\Facades\DB;

class MT5FastBatchFix extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'mt5:fast-batch-fix {--batch=1000 : Batch size for processing}';

    /**
     * The console command description.
     */
    protected $description = 'Fast batch processing to fix MT5 migration issues';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $batchSize = (int) $this->option('batch');
        
        $this->info("🚀 Starting Fast MT5 Batch Fix (Batch Size: {$batchSize})");
        
        // Step 1: Create missing MT5 account records
        $this->info("📋 Step 1: Creating missing MT5 account records...");
        $this->createMT5AccountRecords($batchSize);
        
        // Step 2: Fix country data issues
        $this->info("📋 Step 2: Fixing country data issues...");
        $this->fixCountryData($batchSize);
        
        // Step 3: Fix IB system data
        $this->info("📋 Step 3: Fixing IB system data...");
        $this->fixIBSystemData($batchSize);
        
        // Step 4: Verify test user data
        $this->info("📋 Step 4: Verifying test user data...");
        $this->verifyTestUser();
        
        $this->info("✅ Fast batch fix completed!");
        
        // Show final statistics
        $this->showStatistics();
    }
    
    private function createMT5AccountRecords($batchSize)
    {
        $users = User::whereNotNull('mt5_login')
            ->whereDoesntHave('userAccounts')
            ->get();
        
        $totalUsers = $users->count();
        $this->info("Found {$totalUsers} users without MT5 account records");
        
        if ($totalUsers == 0) {
            $this->info("✅ All users already have MT5 account records");
            return;
        }
        
        $processed = 0;
        $created = 0;
        
        $bar = $this->output->createProgressBar($totalUsers);
        $bar->start();
        
        foreach ($users->chunk($batchSize) as $userChunk) {
            $accountsToInsert = [];
            
            foreach ($userChunk as $user) {
                $mt5User = DB::connection('mbf-dbmt5')
                    ->table('mt5_users')
                    ->where('Login', $user->mt5_login)
                    ->first();
                
                if ($mt5User) {
                    $accountsToInsert[] = [
                        'User_Id' => $user->id,
                        'Account' => $mt5User->Login,
                        'Master_Password' => 'migrated',
                        'created_at' => now(),
                        'updated_at' => now()
                    ];
                }
                $processed++;
                $bar->advance();
            }
            
            if (!empty($accountsToInsert)) {
                DB::table('user_accounts')->insert($accountsToInsert);
                $created += count($accountsToInsert);
            }
        }
        
        $bar->finish();
        $this->newLine();
        $this->info("✅ Created {$created} MT5 account records");
    }
    
    private function fixCountryData($batchSize)
    {
        // Fix users with empty or invalid country data
        $usersToFix = User::where(function($query) {
            $query->whereNull('country_code')
                  ->orWhere('country_code', '')
                  ->orWhere('country_code', 'Aland Islands');
        })->get();
        
        $totalUsers = $usersToFix->count();
        $this->info("Found {$totalUsers} users with country data issues");
        
        if ($totalUsers == 0) {
            $this->info("✅ All users have valid country data");
            return;
        }
        
        $fixed = 0;
        $bar = $this->output->createProgressBar($totalUsers);
        $bar->start();
        
        foreach ($usersToFix->chunk($batchSize) as $userChunk) {
            foreach ($userChunk as $user) {
                // Get better country data from MT5
                $mt5Users = DB::connection('mbf-dbmt5')
                    ->table('mt5_users')
                    ->where('Email', $user->email)
                    ->whereNotNull('Country')
                    ->where('Country', '!=', '')
                    ->where('Country', '!=', 'Aland Islands')
                    ->orderByRaw("CASE WHEN Country = 'Pakistan' THEN 1 ELSE 2 END")
                    ->first();
                
                if ($mt5Users && !empty($mt5Users->Country)) {
                    $user->update(['country_code' => $mt5Users->Country]);
                    $fixed++;
                }
                
                $bar->advance();
            }
        }
        
        $bar->finish();
        $this->newLine();
        $this->info("✅ Fixed country data for {$fixed} users");
    }
    
    private function fixIBSystemData($batchSize)
    {
        // Fix IB status for users with Affiliates groups
        $ibUsers = User::whereNull('ib_status')
            ->whereNotNull('mt5_group')
            ->where('mt5_group', 'like', '%Affiliates%')
            ->get();
        
        $totalUsers = $ibUsers->count();
        $this->info("Found {$totalUsers} users with IB group but no IB status");
        
        if ($totalUsers == 0) {
            $this->info("✅ All IB users have correct status");
            return;
        }
        
        $fixed = 0;
        $bar = $this->output->createProgressBar($totalUsers);
        $bar->start();
        
        foreach ($ibUsers->chunk($batchSize) as $userChunk) {
            foreach ($userChunk as $user) {
                $user->update([
                    'ib_status' => 1, // approved
                    'ib_type' => 'master',
                    'partner' => 1,
                    'ib_approved_at' => now()
                ]);
                $fixed++;
                $bar->advance();
            }
        }
        
        $bar->finish();
        $this->newLine();
        $this->info("✅ Fixed IB status for {$fixed} users");
    }
    
    private function verifyTestUser()
    {
        // Check our test user
        $testUser = User::where('email', '<EMAIL>')->first();
        
        if ($testUser) {
            $this->info("📊 Test User Verification (<EMAIL>):");
            $this->table(
                ['Field', 'Value'],
                [
                    ['ID', $testUser->id],
                    ['Name', $testUser->firstname . ' ' . $testUser->lastname],
                    ['Email', $testUser->email],
                    ['Mobile', $testUser->mobile ?: 'Not set'],
                    ['Country', $testUser->country_code ?: 'Not set'],
                    ['MT5 Login', $testUser->mt5_login ?: 'Not set'],
                    ['MT5 Group', $testUser->mt5_group ?: 'Not set'],
                    ['IB Status', $testUser->ib_status ?: 'Not set'],
                    ['Partner', $testUser->partner ?: 'Not set'],
                    ['MT5 Accounts', $testUser->userAccounts()->count()]
                ]
            );
        } else {
            $this->error("❌ Test user not found!");
        }
    }
    
    private function showStatistics()
    {
        $this->info("📊 Final Statistics:");
        $this->table(
            ['Metric', 'Count'],
            [
                ['Total Users', User::count()],
                ['Users with MT5 Login', User::whereNotNull('mt5_login')->count()],
                ['Total MT5 Accounts', UserAccounts::count()],
                ['IB Users (Approved)', User::where('ib_status', 1)->count()],
                ['Users with Country Data', User::whereNotNull('country_code')->where('country_code', '!=', '')->count()],
                ['Users with Mobile Data', User::whereNotNull('mobile')->where('mobile', '!=', '')->count()]
            ]
        );
    }
}
