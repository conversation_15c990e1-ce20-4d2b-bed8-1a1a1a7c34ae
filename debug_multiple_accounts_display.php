<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🔍 DEBUGGING MULTIPLE ACCOUNTS DISPLAY ISSUE\n";
echo "===========================================\n\n";

// Find a user with multiple accounts showing the issue
$testUser = \App\Models\User::select([
    'users.*',
    \DB::raw('(SELECT GROUP_CONCAT(DISTINCT CONCAT(ua.Account, ":", COALESCE(ua.Group_Name, "Unknown"), ":", COALESCE(ua.Account_Type, "real"), ":", COALESCE(ua.Balance, 0), ":", COALESCE(ua.Leverage, 100), ":", COALESCE(ua.Currency, "USD")) ORDER BY ua.Account SEPARATOR "|") FROM user_accounts ua WHERE ua.User_Id = users.id) as all_mt5_accounts_detailed')
])
->whereNotNull('mt5_login')
->whereHas('userAccounts')
->first();

if (!$testUser) {
    echo "❌ No users found with multiple accounts\n";
    exit;
}

echo "📧 Testing User: {$testUser->email}\n";
echo "🔑 Primary MT5: {$testUser->mt5_login}\n";
echo "📊 Raw Data: {$testUser->all_mt5_accounts_detailed}\n\n";

// Parse the data like the admin view does
$allMT5AccountsDetailed = [];
$primaryMT5 = $testUser->mt5_login;

if ($testUser->all_mt5_accounts_detailed) {
    // Parse enhanced format: login:group:type:balance:leverage:currency
    $accountsData = explode('|', $testUser->all_mt5_accounts_detailed);
    foreach ($accountsData as $accountData) {
        $parts = explode(':', $accountData);
        if (count($parts) >= 6) {
            $allMT5AccountsDetailed[] = [
                'login' => $parts[0],
                'group' => $parts[1],
                'type' => $parts[2],
                'balance' => floatval($parts[3]),
                'leverage' => intval($parts[4]),
                'currency' => $parts[5],
                'credit' => 0
            ];
        }
    }
    
    // Sort by account type hierarchy (IB first, then real, then demo)
    usort($allMT5AccountsDetailed, function($a, $b) {
        $hierarchy = ['ib' => 1, 'affiliate' => 2, 'multi-ib' => 3, 'real' => 4, 'demo' => 5];
        $priorityA = $hierarchy[$a['type']] ?? 999;
        $priorityB = $hierarchy[$b['type']] ?? 999;
        return $priorityA - $priorityB;
    });
}

echo "🔍 PARSED ACCOUNT DATA:\n";
echo "Total Accounts Found: " . count($allMT5AccountsDetailed) . "\n";
echo "Primary Account: {$primaryMT5}\n\n";

foreach ($allMT5AccountsDetailed as $index => $account) {
    $isPrimary = $account['login'] == $primaryMT5 ? " (PRIMARY)" : "";
    echo "Account {$index}: {$account['login']}{$isPrimary}\n";
    echo "  - Group: {$account['group']}\n";
    echo "  - Type: {$account['type']}\n";
    echo "  - Balance: {$account['balance']}\n";
    echo "  - Leverage: {$account['leverage']}\n";
    echo "  - Currency: {$account['currency']}\n\n";
}

// Check what the admin view logic would show
$additionalAccountsCount = count($allMT5AccountsDetailed) - 1;
echo "📊 ADMIN VIEW LOGIC:\n";
echo "Total Accounts: " . count($allMT5AccountsDetailed) . "\n";
echo "Additional Accounts Count: {$additionalAccountsCount}\n";
echo "Should Display: Additional Accounts ({$additionalAccountsCount})\n\n";

// Check which accounts would be displayed as "additional"
echo "🔍 ADDITIONAL ACCOUNTS THAT WOULD BE DISPLAYED:\n";
$displayedCount = 0;
foreach ($allMT5AccountsDetailed as $account) {
    if ($account['login'] != $primaryMT5) {
        $displayedCount++;
        echo "  {$displayedCount}. {$account['login']} ({$account['type']})\n";
    }
}

echo "\nActual Additional Accounts Displayed: {$displayedCount}\n";

if ($displayedCount != $additionalAccountsCount) {
    echo "❌ MISMATCH FOUND!\n";
    echo "Expected: {$additionalAccountsCount}\n";
    echo "Actual: {$displayedCount}\n\n";
    
    // Check if primary account is in the detailed list
    $primaryInList = false;
    foreach ($allMT5AccountsDetailed as $account) {
        if ($account['login'] == $primaryMT5) {
            $primaryInList = true;
            break;
        }
    }
    
    if (!$primaryInList) {
        echo "🔍 ROOT CAUSE: Primary account ({$primaryMT5}) not found in detailed accounts list\n";
        echo "This means the user_accounts table doesn't have the primary account\n";
        echo "Solution: Add primary account to user_accounts table\n";
    } else {
        echo "🔍 ROOT CAUSE: Logic error in counting or display\n";
    }
} else {
    echo "✅ COUNTING LOGIC IS CORRECT\n";
    echo "The display should work properly\n";
}

// Check user_accounts table directly
echo "\n🗄️ CHECKING USER_ACCOUNTS TABLE DIRECTLY:\n";
$userAccounts = \App\Models\UserAccounts::where('User_Id', $testUser->id)->get();
echo "Records in user_accounts: " . $userAccounts->count() . "\n";

foreach ($userAccounts as $account) {
    echo "  - Account: {$account->Account}\n";
    echo "    Group: {$account->Group_Name}\n";
    echo "    Type: {$account->Account_Type}\n";
    echo "    Balance: {$account->Balance}\n\n";
}

// Check if primary account is missing from user_accounts
$primaryInUserAccounts = $userAccounts->where('Account', $primaryMT5)->first();
if (!$primaryInUserAccounts) {
    echo "❌ PRIMARY ACCOUNT MISSING FROM user_accounts TABLE\n";
    echo "Primary account {$primaryMT5} should be added to user_accounts table\n";
    
    // Suggest fix
    echo "\n🔧 SUGGESTED FIX:\n";
    echo "INSERT INTO user_accounts (User_Id, Account, Account_Type, Group_Name, Balance, Currency, Leverage, created_at, updated_at) VALUES\n";
    echo "({$testUser->id}, '{$primaryMT5}', 'real', '{$testUser->mt5_group}', {$testUser->mt5_balance}, '{$testUser->mt5_currency}', {$testUser->mt5_leverage}, NOW(), NOW());\n";
} else {
    echo "✅ Primary account found in user_accounts table\n";
}

echo "\n🎯 CONCLUSION:\n";
if ($displayedCount != $additionalAccountsCount) {
    echo "The counting mismatch is likely due to the primary account not being in user_accounts table.\n";
    echo "Run the sync again to ensure all accounts are properly stored.\n";
} else {
    echo "The counting logic is working correctly.\n";
    echo "If you're still seeing issues, clear the view cache and check browser cache.\n";
}
