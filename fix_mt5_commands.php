<?php
/**
 * SCRIPT TO FIX ALL MT5 PYTHON COMMANDS FOR WINDOWS/PLESK
 * This script will fix all the incorrect Python command formats in MT5ServiceForLive.php
 */

echo "<h1>MT5 Command Fixer for Windows/Plesk</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";

$filePath = 'MT5ServiceForLive.php';

if (!file_exists($filePath)) {
    echo "<span class='error'>❌ File not found: {$filePath}</span><br>";
    exit;
}

echo "<span class='info'>📁 Reading file: {$filePath}</span><br>";

$content = file_get_contents($filePath);
$originalContent = $content;

// Define the fixes needed
$fixes = [
    // Fix 1: changeAccountLeverageWebAPI method
    [
        'search' => '$pythonScript = base_path(\'python/mt5manager.py\');
            $command = "python \"{$pythonScript}\" change_leverage --login {$accountLogin} --leverage {$newLeverage}";',
        'replace' => '$command = escapeshellarg($this->pythonExe) . " " . escapeshellarg($this->pythonScript) . " change_leverage --login {$accountLogin} --leverage {$newLeverage}";',
        'description' => 'changeAccountLeverageWebAPI method'
    ],
    
    // Fix 2: addBalanceToAccount method
    [
        'search' => 'sprintf(
                \'python "%s" add_balance --login %d --amount %.2f --comment "%s"\',
                $this->pythonScript,',
        'replace' => 'sprintf(
                \'%s %s add_balance --login %d --amount %.2f --comment %s\',
                escapeshellarg($this->pythonExe),
                escapeshellarg($this->pythonScript),',
        'description' => 'addBalanceToAccount method'
    ],
    
    // Fix 3: deductBalanceFromAccount method
    [
        'search' => 'sprintf(
                \'python "%s" add_balance --login %d --amount %.2f --comment "%s"\',
                $this->pythonScript,
                $login,
                -$amount, // Negative amount for deduction
                $comment',
        'replace' => 'sprintf(
                \'%s %s add_balance --login %d --amount %.2f --comment %s\',
                escapeshellarg($this->pythonExe),
                escapeshellarg($this->pythonScript),
                $login,
                -$amount, // Negative amount for deduction
                escapeshellarg($comment)',
        'description' => 'deductBalanceFromAccount method'
    ],
    
    // Fix 4: getAccountBalance method
    [
        'search' => 'sprintf(
                \'python "%s" get_balance --login %d\',
                $this->pythonScript,',
        'replace' => 'sprintf(
                \'%s %s get_balance --login %d\',
                escapeshellarg($this->pythonExe),
                escapeshellarg($this->pythonScript),',
        'description' => 'getAccountBalance method'
    ],
    
    // Fix 5: changeLeverage method
    [
        'search' => 'sprintf(
                \'python "%s" change_leverage --login %d --leverage %d\',
                $this->pythonScript,',
        'replace' => 'sprintf(
                \'%s %s change_leverage --login %d --leverage %d\',
                escapeshellarg($this->pythonExe),
                escapeshellarg($this->pythonScript),',
        'description' => 'changeLeverage method'
    ],
    
    // Fix 6: changePassword method
    [
        'search' => 'sprintf(
                \'python "%s" change_password --login %d --new_password "%s" --password_type %s --password "%s"\',
                $this->pythonScript,
                $login,
                $newPassword,
                $passwordType,
                $newPassword // Using same password for verification',
        'replace' => 'sprintf(
                \'%s %s change_password --login %d --new_password %s --password_type %s --password %s\',
                escapeshellarg($this->pythonExe),
                escapeshellarg($this->pythonScript),
                $login,
                escapeshellarg($newPassword),
                $passwordType,
                escapeshellarg($newPassword) // Using same password for verification',
        'description' => 'changePassword method'
    ],
    
    // Fix 7: createAccount method
    [
        'search' => 'sprintf(
                \'python "%s" create_account --first_name "%s" --last_name "%s" --password "%s" --group "%s" --leverage %d --email "%s" --country "%s" --city "%s" --state "%s" --address "%s" --zipcode "%s" --phone "%s" --initial_balance %.2f\',
                $this->pythonScript,
                $userData[\'first_name\'],
                $userData[\'last_name\'],
                $userData[\'password\'],
                $userData[\'group\'],
                $userData[\'leverage\'],
                $userData[\'email\'],
                $userData[\'country\'],
                $userData[\'city\'],
                $userData[\'state\'],
                $userData[\'address\'],
                $userData[\'zipcode\'],
                $userData[\'phone\'],',
        'replace' => 'sprintf(
                \'%s %s create_account --first_name %s --last_name %s --password %s --group %s --leverage %d --email %s --country %s --city %s --state %s --address %s --zipcode %s --phone %s --initial_balance %.2f\',
                escapeshellarg($this->pythonExe),
                escapeshellarg($this->pythonScript),
                escapeshellarg($userData[\'first_name\']),
                escapeshellarg($userData[\'last_name\']),
                escapeshellarg($userData[\'password\']),
                escapeshellarg($userData[\'group\']),
                $userData[\'leverage\'],
                escapeshellarg($userData[\'email\']),
                escapeshellarg($userData[\'country\']),
                escapeshellarg($userData[\'city\']),
                escapeshellarg($userData[\'state\']),
                escapeshellarg($userData[\'address\']),
                escapeshellarg($userData[\'zipcode\']),
                escapeshellarg($userData[\'phone\']),',
        'description' => 'createAccount method'
    ]
];

$fixedCount = 0;

foreach ($fixes as $fix) {
    echo "<br><span class='info'>🔧 Fixing: {$fix['description']}</span><br>";
    
    if (strpos($content, $fix['search']) !== false) {
        $content = str_replace($fix['search'], $fix['replace'], $content);
        $fixedCount++;
        echo "<span class='success'>✅ Fixed: {$fix['description']}</span><br>";
    } else {
        echo "<span class='error'>❌ Not found: {$fix['description']}</span><br>";
    }
}

// Write the fixed content back to the file
if ($fixedCount > 0) {
    if (file_put_contents($filePath, $content)) {
        echo "<br><span class='success'>🎉 Successfully applied {$fixedCount} fixes to {$filePath}</span><br>";
        echo "<span class='info'>📊 File size: " . number_format(strlen($content)) . " bytes</span><br>";
        echo "<span class='info'>📊 Total lines: " . substr_count($content, "\n") . "</span><br>";
    } else {
        echo "<br><span class='error'>❌ Failed to write to {$filePath}</span><br>";
    }
} else {
    echo "<br><span class='info'>ℹ️ No fixes were applied - file may already be correct</span><br>";
}

echo "<br><h3>Summary of Changes:</h3>";
echo "<ul>";
echo "<li>✅ Changed hardcoded 'python' to use \$this->pythonExe with escapeshellarg()</li>";
echo "<li>✅ Changed hardcoded script paths to use \$this->pythonScript with escapeshellarg()</li>";
echo "<li>✅ Added proper shell escaping for all parameters</li>";
echo "<li>✅ Removed unnecessary quotes around parameters (escapeshellarg handles this)</li>";
echo "</ul>";

echo "<p><strong>Next steps:</strong></p>";
echo "<ol>";
echo "<li>Upload the fixed MT5ServiceForLive.php to your live Windows/Plesk server</li>";
echo "<li>Rename it to MT5Service.php and replace the existing file</li>";
echo "<li>Ensure your .env has the correct paths with double backslashes</li>";
echo "<li>Clear Laravel caches</li>";
echo "<li>Test leverage and password changes</li>";
echo "</ol>";

echo "<p><em>Delete this fixer script after use.</em></p>";
?>
