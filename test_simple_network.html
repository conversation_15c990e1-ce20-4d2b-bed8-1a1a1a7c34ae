<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Network Test</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>Simple Network JavaScript Test</h1>
    
    <div class="test-section">
        <h2>Test 1: jQuery Loading</h2>
        <div id="jqueryTest">Testing...</div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Basic JavaScript Variables</h2>
        <div id="variableTest">Testing...</div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Console Commands</h2>
        <p>Open browser console (F12) and try these commands:</p>
        <div style="background: #f5f5f5; padding: 10px; border-radius: 3px; font-family: monospace;">
            typeof BACKEND_DATA<br>
            typeof ADMIN_BACKEND_DATA<br>
            console.log(BACKEND_DATA);<br>
            console.log(ADMIN_BACKEND_DATA);
        </div>
    </div>
    
    <div class="test-section">
        <h2>Test 4: Network Tree Simulation</h2>
        <div id="networkTest">
            <button onclick="testNetworkTree()">Test Network Tree</button>
            <div id="networkResult"></div>
        </div>
    </div>

    <script>
        // Simulate the data structure from Laravel
        window.networkDataJson = {
            direct_referrals: 5,
            total_referrals: 12,
            tree_data: {
                id: 6902,
                name: "Ahsan Farooq",
                title: "Master IB",
                mt5_login: "123456",
                mt5_balance: "1000.00",
                node_type: "master",
                children: [
                    {
                        id: 6903,
                        name: "Test User 1",
                        title: "Sub IB",
                        mt5_login: "123457",
                        mt5_balance: "500.00",
                        node_type: "ib"
                    }
                ]
            }
        };
        
        window.userDataJson = {
            id: 6902,
            firstname: "Ahsan",
            lastname: "Farooq",
            ib_type: "master",
            mt5_login: "123456",
            mt5_balance: 1000
        };
        
        window.routesJson = {
            getReferrals: "/user/partnership/get-referrals",
            network: "/user/partnership/network"
        };

        // Test 1: jQuery
        $(document).ready(function() {
            $('#jqueryTest').html('<span class="success">✓ jQuery loaded successfully</span>');
            
            // Test 2: Variables
            testVariables();
            
            // Create BACKEND_DATA like in the real application
            window.BACKEND_DATA = {
                networkData: window.networkDataJson || [],
                user: {
                    id: window.userDataJson.id || 0,
                    name: (window.userDataJson.firstname || '') + ' ' + (window.userDataJson.lastname || ''),
                    title: (window.userDataJson.ib_type || 'master') + ' IB',
                    mt5_login: window.userDataJson.mt5_login || 'N/A',
                    mt5_balance: (window.userDataJson.mt5_balance || 0).toFixed(2),
                    node_type: 'master'
                },
                routes: {
                    getReferrals: window.routesJson.getReferrals || '',
                    network: window.routesJson.network || ''
                }
            };
            
            window.ADMIN_BACKEND_DATA = window.BACKEND_DATA; // Same for testing
            
            console.log('Test BACKEND_DATA created:', window.BACKEND_DATA);
            console.log('Test ADMIN_BACKEND_DATA created:', window.ADMIN_BACKEND_DATA);
        });
        
        function testVariables() {
            let result = '';
            
            if (typeof window.networkDataJson !== 'undefined') {
                result += '<span class="success">✓ networkDataJson exists</span><br>';
            } else {
                result += '<span class="error">✗ networkDataJson missing</span><br>';
            }
            
            if (typeof window.userDataJson !== 'undefined') {
                result += '<span class="success">✓ userDataJson exists</span><br>';
            } else {
                result += '<span class="error">✗ userDataJson missing</span><br>';
            }
            
            if (typeof BACKEND_DATA !== 'undefined') {
                result += '<span class="success">✓ BACKEND_DATA created</span><br>';
            } else {
                result += '<span class="error">✗ BACKEND_DATA missing</span><br>';
            }
            
            $('#variableTest').html(result);
        }
        
        function testNetworkTree() {
            let result = '<h4>Network Tree Test Results:</h4>';
            
            if (typeof BACKEND_DATA !== 'undefined' && BACKEND_DATA.networkData) {
                result += '<span class="success">✓ Network data available</span><br>';
                result += '<span class="info">Direct Referrals: ' + BACKEND_DATA.networkData.direct_referrals + '</span><br>';
                result += '<span class="info">User: ' + BACKEND_DATA.user.name + '</span><br>';
                result += '<span class="info">MT5: ' + BACKEND_DATA.user.mt5_login + '</span><br>';
                
                // Simple tree display
                if (BACKEND_DATA.networkData.tree_data) {
                    result += '<div style="margin-top: 10px; padding: 10px; background: #f0f8ff; border-radius: 5px;">';
                    result += '<strong>Tree Structure:</strong><br>';
                    result += '📊 ' + BACKEND_DATA.networkData.tree_data.name + ' (' + BACKEND_DATA.networkData.tree_data.title + ')<br>';
                    
                    if (BACKEND_DATA.networkData.tree_data.children) {
                        BACKEND_DATA.networkData.tree_data.children.forEach(child => {
                            result += '&nbsp;&nbsp;└── ' + child.name + ' (' + child.title + ')<br>';
                        });
                    }
                    result += '</div>';
                }
            } else {
                result += '<span class="error">✗ Network data not available</span><br>';
            }
            
            $('#networkResult').html(result);
        }
    </script>
</body>
</html>
