# 🧪 Multi-Level IB System Testing Instructions

## 📋 **Pre-Testing Setup**

### **1. Database Migration & Seeding**
```bash
# Run the new migrations
php artisan migrate

# Seed the IB system with default data
php artisan db:seed --class=IbSystemSeeder

# Clear cache
php artisan cache:clear
php artisan config:clear
php artisan route:clear
```

### **2. Environment Configuration**
Add these to your `.env` file:
```env
# MT5 Webhook Configuration (optional)
MT5_WEBHOOK_SECRET=your_webhook_secret_here

# IB System Configuration
IB_SYSTEM_ENABLED=true
IB_DEFAULT_GROUP_ID=1
```

## 🔧 **Phase 1: Admin Panel Testing**

### **Test 1: IB Levels Management**
1. Login to admin panel
2. Navigate to **Multi-Level IB System > Commission Levels**
3. **Test Actions:**
   - Create 3 commission levels (50%, 30%, 20%)
   - Activate/deactivate levels
   - View level statistics
   - Verify validation works for invalid percentages

**Expected Results:**
- ✅ Levels are created successfully
- ✅ Status toggle works
- ✅ Statistics display correctly
- ✅ Validation prevents invalid data

### **Test 2: IB Groups Management**
1. Navigate to **Multi-Level IB System > IB Groups**
2. **Test Actions:**
   - Create new IB group with multiplier 1.25
   - Edit existing group
   - View group details and statistics
   - Deactivate/activate groups

**Expected Results:**
- ✅ Groups are created with correct multipliers
- ✅ Group statistics show member counts
- ✅ Edit functionality works properly

### **Test 3: Enhanced IB Application Approval**
1. Navigate to **Manage IB > IB Applications**
2. Find a pending application
3. **Test Actions:**
   - Use enhanced approval form
   - Select IB type (Master/Sub)
   - Assign to IB group
   - Set parent IB (for Sub-IBs)
   - Approve application

**Expected Results:**
- ✅ Enhanced form displays correctly
- ✅ Parent IB dropdown populates based on group
- ✅ Approval creates proper hierarchy
- ✅ User receives approval notification

## 👤 **Phase 2: User Interface Testing**

### **Test 4: IB Dashboard Access**
1. Login as approved IB user
2. **Test Navigation:**
   - Verify enhanced IB menu appears in sidebar
   - Access IB Dashboard
   - Check all dashboard widgets display data

**Expected Results:**
- ✅ Enhanced sidebar menu shows for approved IBs
- ✅ Dashboard displays commission statistics
- ✅ Referral information is correct
- ✅ Performance metrics are calculated

### **Test 5: IB Hierarchy Visualization**
1. As Master IB, navigate to **IB Partnership > IB Hierarchy**
2. **Test Features:**
   - View hierarchy tree
   - Check upline/downline relationships
   - Verify statistics are accurate

**Expected Results:**
- ✅ Hierarchy tree displays correctly
- ✅ Parent-child relationships are accurate
- ✅ Statistics match actual data

### **Test 6: Commission Reports**
1. Navigate to **IB Partnership > My Commissions**
2. **Test Filtering:**
   - Filter by date range
   - Filter by status (pending/paid)
   - Filter by symbol
   - Export functionality (if implemented)

**Expected Results:**
- ✅ Filters work correctly
- ✅ Commission data is accurate
- ✅ Pagination works properly

## 🔄 **Phase 3: Commission Calculation Testing**

### **Test 7: Manual Commission Calculation**
1. Create test trade data:
```php
// In tinker: php artisan tinker
$tradeData = [
    'email' => '<EMAIL>', // User with IB hierarchy
    'trade_id' => 'TEST_' . time(),
    'symbol' => 'EURUSD',
    'volume' => 1.0,
    'closed_at' => now()
];

$service = new App\Services\IbCommissionService();
$result = $service->calculateAndDistributeCommissions($tradeData);
```

**Expected Results:**
- ✅ Commissions calculated for each level
- ✅ Amounts distributed to IB wallets
- ✅ Transaction records created
- ✅ Notifications sent to IBs

### **Test 8: MT5 Webhook Testing**
1. Test webhook endpoint:
```bash
curl -X POST http://your-domain.com/api/mt5/webhook/test \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'
```

2. Test trade closure webhook:
```bash
curl -X POST http://your-domain.com/api/mt5/webhook/trade-close \
  -H "Content-Type: application/json" \
  -d '{
    "login": "12345",
    "ticket": "67890",
    "symbol": "EURUSD",
    "volume": 1.0,
    "profit": 10.50,
    "time_close": "2024-12-19 10:30:00"
  }'
```

**Expected Results:**
- ✅ Webhook responds with success
- ✅ Commission calculation triggered
- ✅ IB commissions created and paid

## 📊 **Phase 4: Data Integrity Testing**

### **Test 9: Hierarchy Validation**
1. **Test Scenarios:**
   - Try to create circular references
   - Exceed maximum hierarchy levels
   - Move IB to different parent
   - Delete IB with children

**Expected Results:**
- ✅ Circular references prevented
- ✅ Level limits enforced
- ✅ Move operations work correctly
- ✅ Deletion restrictions work

### **Test 10: Commission Accuracy**
1. **Verify Calculations:**
   - Check commission percentages match levels
   - Verify group multipliers applied
   - Confirm total doesn't exceed 100%
   - Test with different symbols and groups

**Expected Results:**
- ✅ All calculations are mathematically correct
- ✅ No commission leakage or over-payment
- ✅ Different symbols use correct rebate rules

## 🚀 **Phase 5: Performance Testing**

### **Test 11: Load Testing**
1. **Create Test Data:**
```php
// Create multiple IB levels and test users
php artisan tinker

// Create 100 test IBs with hierarchy
for ($i = 1; $i <= 100; $i++) {
    // Create test IB users and hierarchy
}

// Simulate 1000 trades
for ($i = 1; $i <= 1000; $i++) {
    // Process commission calculations
}
```

**Expected Results:**
- ✅ System handles multiple IBs efficiently
- ✅ Commission calculations complete in reasonable time
- ✅ Database queries are optimized (N+1 prevention)

### **Test 12: Console Commands**
1. **Test Commands:**
```bash
# Process pending commissions
php artisan ib:process-pending-commissions

# Monitor MT5 trades (run for a few minutes then stop)
php artisan mt5:monitor-trades --interval=30
```

**Expected Results:**
- ✅ Commands execute without errors
- ✅ Pending commissions are processed
- ✅ Monitoring runs continuously

## 🔍 **Phase 6: Edge Case Testing**

### **Test 13: Error Handling**
1. **Test Scenarios:**
   - Invalid trade data
   - Missing user accounts
   - Network failures
   - Database connection issues

**Expected Results:**
- ✅ Graceful error handling
- ✅ Proper logging of errors
- ✅ System continues operating
- ✅ No data corruption

### **Test 14: Security Testing**
1. **Test Security:**
   - Webhook signature validation
   - User permission checks
   - SQL injection prevention
   - XSS protection

**Expected Results:**
- ✅ Unauthorized access prevented
- ✅ Input validation works
- ✅ Security vulnerabilities addressed

## 📝 **Testing Checklist**

### **Admin Panel**
- [ ] IB levels management works
- [ ] IB groups management works
- [ ] Enhanced IB approval works
- [ ] Statistics display correctly
- [ ] All CRUD operations function

### **User Interface**
- [ ] Enhanced sidebar menu displays
- [ ] IB dashboard loads correctly
- [ ] Commission reports work
- [ ] Hierarchy visualization works
- [ ] All user actions function

### **Backend Logic**
- [ ] Commission calculations are accurate
- [ ] Hierarchy management works
- [ ] MT5 integration functions
- [ ] Webhook processing works
- [ ] Console commands execute

### **Data Integrity**
- [ ] No circular references possible
- [ ] Level limits enforced
- [ ] Commission totals are correct
- [ ] Database relationships intact

### **Performance**
- [ ] Page load times under 3 seconds
- [ ] Bulk operations complete efficiently
- [ ] Database queries optimized
- [ ] Memory usage reasonable

## 🎯 **Success Criteria**

The multi-level IB system is considered successfully implemented when:

1. ✅ All admin management interfaces work correctly
2. ✅ User interfaces display proper data and functionality
3. ✅ Commission calculations are mathematically accurate
4. ✅ MT5 integration processes trades correctly
5. ✅ System handles edge cases gracefully
6. ✅ Performance meets requirements
7. ✅ Security measures are in place
8. ✅ All tests pass without critical errors

## 🔧 **Troubleshooting Common Issues**

### **Issue: Commissions not calculating**
- Check if IB levels are configured
- Verify rebate rules exist for symbol groups
- Ensure user has proper IB hierarchy
- Check logs for errors

### **Issue: Hierarchy not displaying**
- Verify parent-child relationships in database
- Check if users have proper IB status
- Ensure IB groups are assigned

### **Issue: Webhook not working**
- Check webhook URL configuration
- Verify signature validation settings
- Check server logs for errors
- Test with curl commands

Remember to test thoroughly in a development environment before deploying to production!
