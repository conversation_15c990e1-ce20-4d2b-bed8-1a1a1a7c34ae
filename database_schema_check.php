<?php
/**
 * DATABASE SCHEMA INVESTIGATION
 * This script checks the actual database schema and constraints
 */

// Include Laravel bootstrap
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🔍 DATABASE SCHEMA INVESTIGATION\n";
echo "================================\n\n";

try {
    // 1. Check table structure
    echo "1️⃣ TABLE STRUCTURE\n";
    echo "==================\n";
    
    $columns = \DB::select("SHOW COLUMNS FROM notification_templates");
    
    foreach ($columns as $column) {
        echo "✅ Column: {$column->Field}\n";
        echo "   - Type: {$column->Type}\n";
        echo "   - Null: {$column->Null}\n";
        echo "   - Key: {$column->Key}\n";
        echo "   - Default: " . ($column->Default ?? 'NULL') . "\n";
        echo "   - Extra: {$column->Extra}\n\n";
    }
    
    // 2. Check indexes
    echo "2️⃣ INDEXES\n";
    echo "==========\n";
    
    $indexes = \DB::select("SHOW INDEX FROM notification_templates");
    
    foreach ($indexes as $index) {
        echo "✅ Index: {$index->Key_name}\n";
        echo "   - Column: {$index->Column_name}\n";
        echo "   - Unique: " . ($index->Non_unique == 0 ? 'YES' : 'NO') . "\n";
        echo "   - Type: {$index->Index_type}\n\n";
    }
    
    // 3. Check constraints
    echo "3️⃣ CONSTRAINTS\n";
    echo "==============\n";
    
    $constraints = \DB::select("
        SELECT 
            CONSTRAINT_NAME,
            CONSTRAINT_TYPE,
            COLUMN_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM information_schema.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'notification_templates'
    ", [\DB::connection()->getDatabaseName()]);
    
    if (empty($constraints)) {
        echo "✅ No foreign key constraints found\n";
    } else {
        foreach ($constraints as $constraint) {
            echo "✅ Constraint: {$constraint->CONSTRAINT_NAME}\n";
            echo "   - Type: {$constraint->CONSTRAINT_TYPE}\n";
            echo "   - Column: {$constraint->COLUMN_NAME}\n";
            if ($constraint->REFERENCED_TABLE_NAME) {
                echo "   - References: {$constraint->REFERENCED_TABLE_NAME}.{$constraint->REFERENCED_COLUMN_NAME}\n";
            }
            echo "\n";
        }
    }
    
    // 4. Check triggers
    echo "4️⃣ TRIGGERS\n";
    echo "===========\n";
    
    $triggers = \DB::select("
        SELECT TRIGGER_NAME, EVENT_MANIPULATION, ACTION_TIMING 
        FROM information_schema.TRIGGERS 
        WHERE EVENT_OBJECT_SCHEMA = ? AND EVENT_OBJECT_TABLE = 'notification_templates'
    ", [\DB::connection()->getDatabaseName()]);
    
    if (empty($triggers)) {
        echo "✅ No triggers found\n";
    } else {
        foreach ($triggers as $trigger) {
            echo "✅ Trigger: {$trigger->TRIGGER_NAME}\n";
            echo "   - Event: {$trigger->EVENT_MANIPULATION}\n";
            echo "   - Timing: {$trigger->ACTION_TIMING}\n\n";
        }
    }
    
    // 5. Test direct SQL operations
    echo "5️⃣ DIRECT SQL TEST\n";
    echo "==================\n";
    
    // Get a test template
    $template = \DB::select("SELECT id, subj, email_body, updated_at FROM notification_templates WHERE id = 1 LIMIT 1");
    
    if (!empty($template)) {
        $template = $template[0];
        echo "✅ Template found: ID {$template->id}\n";
        echo "   - Subject: {$template->subj}\n";
        echo "   - Email body length: " . strlen($template->email_body) . "\n";
        echo "   - Updated at: {$template->updated_at}\n";
        
        // Test direct update
        $testSubject = $template->subj . " [SQL TEST]";
        $testContent = $template->email_body . "\n<!-- SQL Test at " . date('Y-m-d H:i:s') . " -->";
        
        echo "\n📊 Testing direct SQL update...\n";
        
        $updateResult = \DB::update("
            UPDATE notification_templates 
            SET subj = ?, email_body = ?, updated_at = NOW() 
            WHERE id = ?
        ", [$testSubject, $testContent, $template->id]);
        
        echo "✅ Update result: {$updateResult} rows affected\n";
        
        // Verify the update
        $updatedTemplate = \DB::select("SELECT subj, email_body, updated_at FROM notification_templates WHERE id = ? LIMIT 1", [$template->id]);
        
        if (!empty($updatedTemplate)) {
            $updatedTemplate = $updatedTemplate[0];
            echo "✅ Verification:\n";
            echo "   - Subject changed: " . ($updatedTemplate->subj !== $template->subj ? 'YES' : 'NO') . "\n";
            echo "   - Content changed: " . ($updatedTemplate->email_body !== $template->email_body ? 'YES' : 'NO') . "\n";
            echo "   - Timestamp changed: " . ($updatedTemplate->updated_at !== $template->updated_at ? 'YES' : 'NO') . "\n";
            
            // Restore original
            \DB::update("
                UPDATE notification_templates 
                SET subj = ?, email_body = ? 
                WHERE id = ?
            ", [$template->subj, $template->email_body, $template->id]);
            
            echo "✅ Original content restored\n";
        }
    } else {
        echo "❌ No template found with ID 1\n";
    }
    
    // 6. Check database engine and settings
    echo "\n6️⃣ DATABASE ENGINE & SETTINGS\n";
    echo "==============================\n";
    
    $tableStatus = \DB::select("SHOW TABLE STATUS LIKE 'notification_templates'");
    
    if (!empty($tableStatus)) {
        $status = $tableStatus[0];
        echo "✅ Engine: {$status->Engine}\n";
        echo "✅ Collation: {$status->Collation}\n";
        echo "✅ Rows: {$status->Rows}\n";
        echo "✅ Data length: {$status->Data_length} bytes\n";
        echo "✅ Auto increment: {$status->Auto_increment}\n";
    }
    
    // Check MySQL settings that might affect updates
    $variables = \DB::select("SHOW VARIABLES WHERE Variable_name IN ('sql_mode', 'autocommit', 'transaction_isolation')");
    
    echo "\n📊 MySQL Settings:\n";
    foreach ($variables as $var) {
        echo "   - {$var->Variable_name}: {$var->Value}\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Database Schema Error: " . $e->getMessage() . "\n";
    echo "❌ File: " . $e->getFile() . "\n";
    echo "❌ Line: " . $e->getLine() . "\n";
}

echo "\n📋 SCHEMA INVESTIGATION COMPLETE\n";
echo "=================================\n";
echo "This reveals:\n";
echo "1. Actual database table structure and constraints\n";
echo "2. Index configuration that might affect performance\n";
echo "3. Foreign key constraints that might block updates\n";
echo "4. Triggers that might interfere with saves\n";
echo "5. Direct SQL operation capabilities\n";
echo "6. Database engine and configuration issues\n\n";

echo "🔍 Run this script to identify database-level issues.\n";

?>
