<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Duplicate resolution fields (only add missing columns)
            $table->boolean('is_primary_account')->default(true)->after('mt5_synced_at');
            $table->unsignedBigInteger('primary_account_id')->nullable()->after('is_primary_account');
            $table->enum('account_status', ['primary', 'secondary', 'merged', 'inactive'])->default('primary')->after('primary_account_id');
            $table->timestamp('duplicate_resolved_at')->nullable()->after('account_status');

            // IB identification fields (only add missing columns)
            $table->boolean('is_ib_account')->default(false)->after('duplicate_resolved_at');
            // Note: ib_type already exists, so we skip it
            $table->decimal('commission_earnings', 15, 2)->default(0)->after('ib_type');
            $table->timestamp('last_commission_sync')->nullable()->after('commission_earnings');

            // Add indexes for performance
            $table->index(['email', 'is_primary_account'], 'idx_email_primary');
            $table->index(['is_ib_account', 'ib_type'], 'idx_ib_status');
            $table->index(['account_status'], 'idx_account_status');
            $table->index(['primary_account_id'], 'idx_primary_account');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('idx_email_primary');
            $table->dropIndex('idx_ib_status');
            $table->dropIndex('idx_account_status');
            $table->dropIndex('idx_primary_account');
            
            $table->dropColumn([
                'is_primary_account',
                'primary_account_id',
                'account_status',
                'duplicate_resolved_at',
                'is_ib_account',
                'commission_earnings',
                'last_commission_sync'
                // Note: ib_type already existed, so we don't drop it
            ]);
        });
    }
};
