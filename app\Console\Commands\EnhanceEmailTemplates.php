<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\NotificationTemplate;

class EnhanceEmailTemplates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:enhance-templates {--template=* : Specific template IDs to enhance} {--preview : Show preview without applying changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'FIXED: Enhance all email templates with professional structure and design';

    /**
     * Create a new command instance.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🎨 FIXED: Professional Email Template Enhancement Tool');
        $this->info('====================================================');

        $specificTemplates = $this->option('template');
        $previewMode = $this->option('preview');

        if ($previewMode) {
            $this->info('📋 PREVIEW MODE - No changes will be applied');
            $this->newLine();
        }

        try {
            if (!empty($specificTemplates)) {
                $this->enhanceSpecificTemplates($specificTemplates, $previewMode);
            } else {
                $this->enhanceAllTemplates($previewMode);
            }
        } catch (\Exception $e) {
            $this->error('❌ Command failed: ' . $e->getMessage());
            $this->error('❌ Stack trace: ' . $e->getTraceAsString());
            return 1;
        }

        return 0;
    }

    /**
     * Enhance all email templates - FIXED VERSION
     */
    private function enhanceAllTemplates($previewMode = false)
    {
        $templates = NotificationTemplate::orderBy('name')->get();

        $this->info("📧 Found {$templates->count()} email templates to enhance");
        $this->newLine();

        if (!$previewMode && !$this->confirm('Do you want to enhance ALL email templates with professional structure?')) {
            $this->info('❌ Operation cancelled');
            return;
        }

        $enhanced = 0;
        $errors = 0;

        foreach ($templates as $template) {
            try {
                $this->info("Processing: {$template->name} (ID: {$template->id})");

                if ($previewMode) {
                    $this->showTemplatePreview($template);
                } else {
                    // CRITICAL SAFETY CHECKS to prevent data loss
                    $professionalContent = $this->getProfessionalTemplateStructure($template->act, $template->email_body);

                    // Validate generated content
                    if (empty($professionalContent) || strlen($professionalContent) < 500) {
                        $this->error("❌ SAFETY CHECK FAILED: Generated content too short for {$template->name}");
                        $this->error("   Content length: " . strlen($professionalContent) . " characters");
                        $errors++;
                        continue;
                    }

                    if (!str_contains($professionalContent, '<!DOCTYPE html>')) {
                        $this->error("❌ SAFETY CHECK FAILED: Invalid HTML structure for {$template->name}");
                        $errors++;
                        continue;
                    }

                    // Apply enhancement
                    $template->email_body = $professionalContent;

                    if ($template->save()) {
                        $enhanced++;
                        $this->line("✅ Enhanced: {$template->name} (" . strlen($professionalContent) . " chars)");
                    } else {
                        $this->error("❌ SAVE FAILED: Could not save {$template->name}");
                        $errors++;
                    }
                }
            } catch (\Exception $e) {
                $errors++;
                $this->error("❌ Error enhancing {$template->name}: " . $e->getMessage());
                $this->error("❌ Details: " . $e->getTraceAsString());
            }
        }

        $this->newLine();
        if ($previewMode) {
            $this->info("📋 Preview completed for {$templates->count()} templates");
        } else {
            $this->info("🎉 Enhancement completed!");
            $this->info("✅ Enhanced: {$enhanced} templates");
            if ($errors > 0) {
                $this->error("❌ Errors: {$errors} templates");
            }
        }
    }

    /**
     * Enhance specific templates - FIXED VERSION
     */
    private function enhanceSpecificTemplates($templateIds, $previewMode = false)
    {
        $this->info("📧 Enhancing specific templates: " . implode(', ', $templateIds));
        $this->newLine();

        $enhanced = 0;
        $errors = 0;

        foreach ($templateIds as $templateId) {
            try {
                $template = NotificationTemplate::findOrFail($templateId);
                $this->info("Processing: {$template->name} (ID: {$template->id})");

                if ($previewMode) {
                    $this->showTemplatePreview($template);
                } else {
                    // CRITICAL SAFETY CHECKS to prevent data loss
                    $professionalContent = $this->getProfessionalTemplateStructure($template->act, $template->email_body);

                    // Validate generated content
                    if (empty($professionalContent) || strlen($professionalContent) < 500) {
                        $this->error("❌ SAFETY CHECK FAILED: Generated content too short for {$template->name}");
                        $this->error("   Content length: " . strlen($professionalContent) . " characters");
                        $errors++;
                        continue;
                    }

                    if (!str_contains($professionalContent, '<!DOCTYPE html>')) {
                        $this->error("❌ SAFETY CHECK FAILED: Invalid HTML structure for {$template->name}");
                        $errors++;
                        continue;
                    }

                    // Apply enhancement
                    $template->email_body = $professionalContent;

                    if ($template->save()) {
                        $enhanced++;
                        $this->line("✅ Enhanced: {$template->name} (" . strlen($professionalContent) . " chars)");
                    } else {
                        $this->error("❌ SAVE FAILED: Could not save {$template->name}");
                        $errors++;
                    }
                }
            } catch (\Exception $e) {
                $errors++;
                $this->error("❌ Error enhancing template {$templateId}: " . $e->getMessage());
                $this->error("❌ Details: " . $e->getTraceAsString());
            }
        }

        $this->newLine();
        if ($previewMode) {
            $this->info("📋 Preview completed for " . count($templateIds) . " templates");
        } else {
            $this->info("🎉 Enhancement completed!");
            $this->info("✅ Enhanced: {$enhanced} templates");
            if ($errors > 0) {
                $this->error("❌ Errors: {$errors} templates");
            }
        }
    }

    /**
     * Show template preview
     */
    private function showTemplatePreview($template)
    {
        $this->line("📋 Template: {$template->name} ({$template->act})");
        $this->line("📧 Subject: {$template->subj}");

        $professionalContent = $this->getProfessionalTemplateStructure($template->act, $template->email_body);

        // Show first 200 characters of the enhanced content
        $preview = substr(strip_tags($professionalContent), 0, 200) . '...';
        $this->line("📄 Enhanced Preview: {$preview}");

        $this->newLine();
    }

    /**
     * FIXED: Professional email template structure generation
     */
    private function getProfessionalTemplateStructure($templateType, $existingContent = '')
    {
        // Check if the existing content is already professionally structured
        if ($this->isAlreadyProfessionallyStructured($existingContent)) {
            return $existingContent;
        }

        $titleText = $this->getTitleText($templateType);
        $descriptionText = $this->getDescriptionText($templateType);

        return '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . $titleText . '</title>
</head>
<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4; line-height: 1.6;">
    <!-- Full Width Container -->
    <table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color: #f4f4f4;">
        <tr>
            <td align="center">
                <!-- Email Container -->
                <table width="600" cellpadding="0" cellspacing="0" border="0" style="background-color: #ffffff; box-shadow: 0 0 10px rgba(0,0,0,0.1);">

                    

                    <!-- Logo Section with White Background -->
                    <tr>
                        <td style="background-color: #ffffff; padding: 40px 10px; text-align: center; border-bottom: 1px solid #e0e0e0;">
                            <img src="https://mbf.mybrokerforex.com/assets/images/logoIcon/logo.png" alt="MBFX Logo" style="max-width: 200px; height: auto; display: block; margin: 0 auto;">
                        </td>
                    </tr>

                    <!-- Notification Title Section -->
                    <tr>
                        <td style="text-align: center;">
                            <h2 style="margin: 0; padding-top:10px; color: #dc3545; font-size: 18px; font-weight: bold; text-transform: uppercase; letter-spacing: 0.5px;">' . $titleText . '</h2>
                        </td>
                    </tr>

                    <!-- Description Section -->
                    <tr>
                        <td style="background-color: #ffffff; padding: 0 40px 20px; text-align: center;">
                            <p style="margin: 0; color: #6c757d; font-size: 16px;">' . $descriptionText . '</p>
                        </td>
                    </tr>

                    <!-- Main Content Section -->
                    <tr>
                        <td style="background-color: #ffffff; padding: 20px 40px; color: #333333;">
                            ' . $this->getDefaultContent($templateType) . '
                        </td>
                    </tr>

                    <!-- Regards Section -->
                    <tr>
                        <td style="background-color: #ffffff; padding: 20px 40px 30px; color: #333333;">
                            <p style="margin: 15px 0 0 0; font-size: 16px;">Best regards,<br><strong>MBFX Team</strong></p>
                        </td>
                    </tr>

                    <!-- Footer Section -->
                    <tr>
                        <td style="background-color: #000000; padding: 30px 40px; text-align: center; color: #ffffff;">
                            <p style="margin: 0 0 10px 0; font-size: 18px; color: #dc3545; font-weight: bold;">MBFX - Professional Trading Platform</p>
                            <p style="margin: 0 0 15px 0; font-size: 14px; color: #cccccc;">
                                {{footer_login_account}} | {{footer_contact_support}} | {{footer_privacy_policy}}
                            </p>
                            <p style="margin: 0 0 10px 0; font-size: 14px; color: #cccccc;">
                                📧 Email: <EMAIL> | 🌐 Website: www.mybrokerforex.com
                            </p>
                            <p style="margin: 0; font-size: 12px; color: #999999;">© 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, contact support.</p>
                        </td>
                    </tr>

                </table>
            </td>
        </tr>
    </table>
</body>
</html>';
    }

    /**
     * Check if content is already professionally structured
     */
    private function isAlreadyProfessionallyStructured($content)
    {
        $indicators = [
            '<!DOCTYPE html>',
            '<table width="100%" cellpadding="0" cellspacing="0"',
            'https://mbf.mybrokerforex.com/assets/images/logoIcon/logo.png',
            'background: linear-gradient(135deg, #dc3545',
            'MBFX - Professional Trading Platform'
        ];

        foreach ($indicators as $indicator) {
            if (strpos($content, $indicator) === false) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get title text based on template type
     */
    private function getTitleText($templateType)
    {
        $titles = [
            'BAL_ADD' => 'Balance Added Successfully',
            'BAL_SUB' => 'Balance Deducted',
            'DEPOSIT_COMPLETE' => 'Deposit Completed',
            'DEPOSIT_APPROVE' => 'Deposit Approved',
            'DEPOSIT_REJECT' => 'Deposit Rejected',
            'DEPOSIT_REQUEST' => 'Deposit Request Received',
            'WITHDRAW_APPROVE' => 'Withdrawal Approved',
            'WITHDRAW_REJECT' => 'Withdrawal Rejected',
            'WITHDRAW_REQUEST' => 'Withdrawal Request Received',
            'MT5_ACCOUNT_CREATED' => 'MT5 Trading Account Created',
            'MT5_BALANCE_ADDED' => 'MT5 Balance Added',
            'MT5_BALANCE_DEDUCTED' => 'MT5 Balance Deducted',
            'MT5_PASSWORD_CHANGED' => 'MT5 Password Changed',
            'MT5_LEVERAGE_CHANGED' => 'MT5 Leverage Updated',
            'PASS_RESET_CODE' => 'Password Reset Code',
            'PASS_RESET_DONE' => 'Password Reset Successful',
            'EVER_CODE' => 'Email Verification Required',
            'SVER_CODE' => 'SMS Verification Code',
            'KYC_APPROVE' => 'KYC Documents Approved',
            'KYC_REJECT' => 'KYC Documents Rejected',
            'USER_REGISTRATION' => 'Welcome to {{site_name}}',
            'IB_APPLICATION_APPROVED' => 'IB Application Approved',
            'IB_APPLICATION_REJECTED' => 'IB Application Rejected',
            'SECURITY_ALERT' => 'Security Alert',
            'ACCOUNT_VERIFICATION_REQUIRED' => 'Account Verification Required'
        ];

        return $titles[$templateType] ?? 'Account Notification';
    }

    /**
     * Get description text based on template type
     */
    private function getDescriptionText($templateType)
    {
        $descriptions = [
            'BAL_ADD' => 'Your account balance has been successfully updated.',
            'BAL_SUB' => 'A deduction has been made from your account balance.',
            'DEPOSIT_COMPLETE' => 'Your deposit has been processed and added to your account.',
            'DEPOSIT_APPROVE' => 'Your deposit request has been approved and processed.',
            'DEPOSIT_REJECT' => 'We need to inform you about your recent deposit request.',
            'DEPOSIT_REQUEST' => 'We have received your deposit request and it is being processed.',
            'WITHDRAW_APPROVE' => 'Your withdrawal request has been approved and processed.',
            'WITHDRAW_REJECT' => 'We need to inform you about your recent withdrawal request.',
            'WITHDRAW_REQUEST' => 'We have received your withdrawal request and it is being processed.',
            'MT5_ACCOUNT_CREATED' => 'Your MT5 trading account is ready for use.',
            'MT5_BALANCE_ADDED' => 'Balance has been added to your MT5 trading account.',
            'MT5_BALANCE_DEDUCTED' => 'Balance has been deducted from your MT5 trading account.',
            'MT5_PASSWORD_CHANGED' => 'Your MT5 account password has been updated successfully.',
            'MT5_LEVERAGE_CHANGED' => 'Your MT5 account leverage has been updated.',
            'PASS_RESET_CODE' => 'Use the code below to reset your password.',
            'PASS_RESET_DONE' => 'Your password has been successfully updated.',
            'EVER_CODE' => 'Please verify your email address to continue.',
            'SVER_CODE' => 'Use the verification code below to complete your request.',
            'KYC_APPROVE' => 'Your identity verification documents have been approved.',
            'KYC_REJECT' => 'We need additional information for your identity verification.',
            'USER_REGISTRATION' => 'Thank you for joining our trading platform. Your account is ready.',
            'IB_APPLICATION_APPROVED' => 'Congratulations! Your IB application has been approved.',
            'IB_APPLICATION_REJECTED' => 'We have reviewed your IB application.',
            'SECURITY_ALERT' => 'We detected unusual activity on your account.',
            'ACCOUNT_VERIFICATION_REQUIRED' => 'Please verify your account to continue using our services.'
        ];

        return $descriptions[$templateType] ?? 'This is an important notification regarding your account.';
    }

    /**
     * FIXED: Complete content definitions for ALL template types
     */
    private function getDefaultContent($templateType)
    {
        $content = [
            // Balance Operations
            'BAL_ADD' => '<p>Dear {{fullname}},</p><p>We are pleased to inform you that your account balance has been successfully updated.</p><ul><li><strong>Amount Added:</strong> {{amount}} {{currency}}</li><li><strong>New Balance:</strong> {{new_balance}} {{currency}}</li><li><strong>Transaction ID:</strong> {{transaction_id}}</li><li><strong>Date:</strong> {{transaction_date}}</li></ul><p>You can now use this balance for trading and other platform services.</p>',

            'BAL_SUB' => '<p>Dear {{fullname}},</p><p>A deduction has been made from your account balance as requested.</p><ul><li><strong>Amount Deducted:</strong> {{amount}} {{currency}}</li><li><strong>Remaining Balance:</strong> {{new_balance}} {{currency}}</li><li><strong>Transaction ID:</strong> {{transaction_id}}</li><li><strong>Date:</strong> {{transaction_date}}</li></ul><p>If you have any questions about this transaction, please contact our support team.</p>',

            // Deposit Operations
            'DEPOSIT_COMPLETE' => '<p>Dear {{fullname}},</p><p>Your deposit has been successfully processed and added to your account balance.</p><ul><li><strong>Deposit Amount:</strong> {{amount}} {{currency}}</li><li><strong>Payment Method:</strong> {{method_name}}</li><li><strong>Transaction ID:</strong> {{trx}}</li><li><strong>Processing Date:</strong> {{transaction_date}}</li><li><strong>New Balance:</strong> {{post_balance}} {{currency}}</li></ul><p>You can now use these funds for trading and other platform services.</p>',

            'DEPOSIT_APPROVE' => '<p>Dear {{fullname}},</p><p>Great news! Your deposit request has been approved and processed successfully.</p><ul><li><strong>Approved Amount:</strong> {{amount}} {{currency}}</li><li><strong>Payment Method:</strong> {{method_name}}</li><li><strong>Transaction ID:</strong> {{trx}}</li><li><strong>Approval Date:</strong> {{approval_date}}</li><li><strong>Updated Balance:</strong> {{post_balance}} {{currency}}</li></ul><p>The funds are now available in your account for immediate use.</p>',

            'DEPOSIT_REJECT' => '<p>Dear {{fullname}},</p><p>We regret to inform you that your deposit request could not be processed at this time.</p><ul><li><strong>Requested Amount:</strong> {{amount}} {{currency}}</li><li><strong>Payment Method:</strong> {{method_name}}</li><li><strong>Transaction ID:</strong> {{trx}}</li><li><strong>Rejection Reason:</strong> {{rejection_reason}}</li><li><strong>Review Date:</strong> {{review_date}}</li></ul><p>Please contact our support team for assistance or try submitting a new deposit request.</p>',

            'DEPOSIT_REQUEST' => '<p>Dear {{fullname}},</p><p>We have received your deposit request and it is currently being processed by our team.</p><ul><li><strong>Requested Amount:</strong> {{amount}} {{currency}}</li><li><strong>Payment Method:</strong> {{method_name}}</li><li><strong>Transaction ID:</strong> {{trx}}</li><li><strong>Submission Date:</strong> {{submission_date}}</li><li><strong>Expected Processing:</strong> 1-3 business days</li></ul><p>You will receive a notification once your deposit has been processed.</p>',

            // Withdrawal Operations
            'WITHDRAW_APPROVE' => '<p>Dear {{fullname}},</p><p>Your withdrawal request has been approved and processed successfully.</p><ul><li><strong>Withdrawal Amount:</strong> {{amount}} {{currency}}</li><li><strong>Withdrawal Method:</strong> {{method_name}}</li><li><strong>Transaction ID:</strong> {{trx}}</li><li><strong>Processing Date:</strong> {{approval_date}}</li><li><strong>Remaining Balance:</strong> {{post_balance}} {{currency}}</li></ul><p>The funds will be transferred to your designated account within 1-5 business days.</p>',

            'WITHDRAW_REJECT' => '<p>Dear {{fullname}},</p><p>We regret to inform you that your withdrawal request could not be processed.</p><ul><li><strong>Requested Amount:</strong> {{amount}} {{currency}}</li><li><strong>Withdrawal Method:</strong> {{method_name}}</li><li><strong>Transaction ID:</strong> {{trx}}</li><li><strong>Rejection Reason:</strong> {{rejection_reason}}</li><li><strong>Review Date:</strong> {{review_date}}</li></ul><p>Please review the rejection reason and contact support if you need assistance.</p>',

            'WITHDRAW_REQUEST' => '<p>Dear {{fullname}},</p><p>We have received your withdrawal request and it is currently under review.</p><ul><li><strong>Requested Amount:</strong> {{amount}} {{currency}}</li><li><strong>Withdrawal Method:</strong> {{method_name}}</li><li><strong>Transaction ID:</strong> {{trx}}</li><li><strong>Submission Date:</strong> {{submission_date}}</li><li><strong>Review Period:</strong> 1-3 business days</li></ul><p>You will be notified once the review process is complete.</p>',

            // MT5 Account Operations
            'MT5_ACCOUNT_CREATED' => '<p>Dear {{fullname}},</p><p>Your MT5 trading account has been successfully created and is ready for trading.</p><ul><li><strong>MT5 Login:</strong> {{mt5_login}}</li><li><strong>MT5 Group:</strong> {{mt5_group}}</li><li><strong>Leverage:</strong> 1:{{leverage}}</li><li><strong>Server:</strong> {{server_name}}</li><li><strong>Account Type:</strong> {{account_type}}</li></ul><p>You can now download the MT5 platform and start trading with your new account.</p><div style="text-align: center; margin: 20px 0;"><a href="{{mt5_download_link}}" style="background: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px;">Download MT5 Platform</a></div>',

            'MT5_BALANCE_ADDED' => '<p>Dear {{fullname}},</p><p>Balance has been successfully added to your MT5 trading account.</p><ul><li><strong>MT5 Login:</strong> {{mt5_login}}</li><li><strong>Amount Added:</strong> {{amount}} {{currency}}</li><li><strong>Previous Balance:</strong> {{previous_balance}} {{currency}}</li><li><strong>New Balance:</strong> {{new_balance}} {{currency}}</li><li><strong>Transaction Date:</strong> {{transaction_date}}</li></ul><p>The funds are now available for trading on your MT5 platform.</p>',

            'MT5_BALANCE_DEDUCTED' => '<p>Dear {{fullname}},</p><p>A balance deduction has been processed on your MT5 trading account.</p><ul><li><strong>MT5 Login:</strong> {{mt5_login}}</li><li><strong>Amount Deducted:</strong> {{amount}} {{currency}}</li><li><strong>Previous Balance:</strong> {{previous_balance}} {{currency}}</li><li><strong>Remaining Balance:</strong> {{new_balance}} {{currency}}</li><li><strong>Transaction Date:</strong> {{transaction_date}}</li><li><strong>Reason:</strong> {{deduction_reason}}</li></ul><p>If you have questions about this deduction, please contact our support team.</p>',

            'MT5_PASSWORD_CHANGED' => '<p>Dear {{fullname}},</p><p>The password for your MT5 trading account has been successfully updated.</p><ul><li><strong>MT5 Login:</strong> {{mt5_login}}</li><li><strong>Change Date:</strong> {{change_date}}</li><li><strong>IP Address:</strong> {{ip_address}}</li><li><strong>Device:</strong> {{device_info}}</li></ul><p>If you did not make this change, please contact our support team immediately.</p><div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 15px 0;"><strong>Security Tip:</strong> Always use a strong, unique password for your trading account.</div>',

            'MT5_LEVERAGE_CHANGED' => '<p>Dear {{fullname}},</p><p>The leverage setting for your MT5 trading account has been updated.</p><ul><li><strong>MT5 Login:</strong> {{mt5_login}}</li><li><strong>Previous Leverage:</strong> 1:{{previous_leverage}}</li><li><strong>New Leverage:</strong> 1:{{new_leverage}}</li><li><strong>Change Date:</strong> {{change_date}}</li><li><strong>Requested By:</strong> {{requested_by}}</li></ul><p>The new leverage setting is now active on your trading account.</p><div style="background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 15px 0;"><strong>Risk Warning:</strong> Higher leverage increases both potential profits and losses.</div>',

            // Password & Security
            'PASS_RESET_CODE' => '<p>Dear {{fullname}},</p><p>You have requested to reset your password. Please use the verification code below to proceed.</p><div style="text-align: center; margin: 20px 0;"><span style="font-size: 24px; font-weight: bold; color: #dc3545; background: #f8f9fa; padding: 15px 25px; border-radius: 8px; border: 2px solid #dc3545;">{{code}}</span></div><ul><li><strong>Request Time:</strong> {{request_time}}</li><li><strong>IP Address:</strong> {{ip_address}}</li><li><strong>Valid For:</strong> 15 minutes</li></ul><p>If you did not request this password reset, please ignore this email and contact support.</p>',

            'PASS_RESET_DONE' => '<p>Dear {{fullname}},</p><p>Your password has been successfully updated and is now active on your account.</p><ul><li><strong>Change Date:</strong> {{change_date}}</li><li><strong>IP Address:</strong> {{ip_address}}</li><li><strong>Device:</strong> {{device_info}}</li></ul><p>If you did not make this change, please contact our support team immediately.</p><div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 15px 0;"><strong>Security Tip:</strong> Use a strong password with a combination of letters, numbers, and symbols.</div>',

            // Email & SMS Verification
            'EVER_CODE' => '<p>Dear {{fullname}},</p><p>Please verify your email address by using the verification code below:</p><div style="text-align: center; margin: 20px 0;"><span style="font-size: 24px; font-weight: bold; color: #dc3545; background: #f8f9fa; padding: 15px 25px; border-radius: 8px; border: 2px solid #dc3545;">{{code}}</span></div><ul><li><strong>Verification Purpose:</strong> {{verification_purpose}}</li><li><strong>Valid For:</strong> 10 minutes</li><li><strong>Request Time:</strong> {{request_time}}</li></ul><p>Enter this code on the verification page to complete your account setup.</p><div style="text-align: center; margin: 25px 0;">{{btn_verify_email}}</div>',

            'SVER_CODE' => '<p>Dear {{fullname}},</p><p>Your SMS verification code has been sent to your registered mobile number.</p><div style="text-align: center; margin: 20px 0;"><span style="font-size: 24px; font-weight: bold; color: #dc3545; background: #f8f9fa; padding: 15px 25px; border-radius: 8px; border: 2px solid #dc3545;">{{code}}</span></div><ul><li><strong>Mobile Number:</strong> {{mobile_number}}</li><li><strong>Verification Purpose:</strong> {{verification_purpose}}</li><li><strong>Valid For:</strong> 5 minutes</li></ul><p>Enter this code to complete the verification process.</p>',

            // KYC Operations
            'KYC_APPROVE' => '<p>Dear {{fullname}},</p><p>Congratulations! Your identity verification documents have been approved.</p><ul><li><strong>Verification Level:</strong> {{kyc_level}}</li><li><strong>Approval Date:</strong> {{approval_date}}</li><li><strong>Verified Documents:</strong> {{verified_documents}}</li><li><strong>Account Status:</strong> Fully Verified</li></ul><p>You now have access to all platform features including higher deposit/withdrawal limits.</p><div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 15px 0;"><strong>Benefits Unlocked:</strong> Higher transaction limits, priority support, and access to premium features.</div><div style="text-align: center; margin: 25px 0;">{{btn_view_dashboard}}</div>',

            'KYC_REJECT' => '<p>Dear {{fullname}},</p><p>We have reviewed your identity verification documents and require additional information.</p><ul><li><strong>Submission Date:</strong> {{submission_date}}</li><li><strong>Review Date:</strong> {{review_date}}</li><li><strong>Required Documents:</strong> {{required_documents}}</li><li><strong>Rejection Reason:</strong> {{rejection_reason}}</li></ul><p>Please resubmit the required documents through your account dashboard.</p><div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 15px 0;"><strong>Next Steps:</strong> Upload clear, high-quality images of the requested documents.</div><div style="text-align: center; margin: 25px 0;">{{btn_submit_kyc}}</div>',

            // User Registration & Account
            'USER_REGISTRATION' => '<p>Dear {{fullname}},</p><p>Welcome to {{site_name}}! Your account has been successfully created and is ready to use.</p><ul><li><strong>Username:</strong> {{username}}</li><li><strong>Email:</strong> {{email}}</li><li><strong>Registration Date:</strong> {{registration_date}}</li><li><strong>Account ID:</strong> {{account_id}}</li></ul><p>You can now access all our trading features and services.</p><div style="text-align: center; margin: 25px 0;">{{btn_view_dashboard}}</div>',

            // IB/Partnership System
            'IB_APPLICATION_APPROVED' => '<p>Dear {{fullname}},</p><p>Congratulations! Your Introducing Broker (IB) application has been approved.</p><ul><li><strong>IB Type:</strong> {{ib_type}}</li><li><strong>Referral Code:</strong> {{referral_code}}</li><li><strong>Commission Rate:</strong> {{commission_rate}}%</li><li><strong>Approval Date:</strong> {{approval_date}}</li><li><strong>IB Level:</strong> {{ib_level}}</li></ul><p>You can now start earning commissions by referring new clients to our platform.</p><div style="text-align: center; margin: 20px 0;"><a href="{{site_url}}/user/ib-dashboard" style="background: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px;">Access IB Dashboard</a></div>',

            'IB_APPLICATION_REJECTED' => '<p>Dear {{fullname}},</p><p>We have reviewed your IB application and unfortunately cannot approve it at this time.</p><ul><li><strong>Application Date:</strong> {{application_date}}</li><li><strong>IB Type:</strong> {{requested_ib_type}}</li><li><strong>Review Date:</strong> {{review_date}}</li><li><strong>Rejection Reason:</strong> {{rejection_reason}}</li></ul><p>You may reapply after addressing the mentioned requirements.</p><p>Thank you for your interest in our IB program.</p>',

            'IB_APPLICATION_USER' => '<p>Dear {{fullname}},</p><p>Thank you for applying to become an Introducing Broker (IB) with {{site_name}}.</p><ul><li><strong>Application Date:</strong> {{application_date}}</li><li><strong>IB Type:</strong> {{requested_ib_type}}</li><li><strong>Review Time:</strong> 3-5 business days</li><li><strong>Application ID:</strong> {{application_id}}</li></ul><p>Your application is now under review and you will be notified of the outcome.</p>',

            'IB_APPLICATION_ADMIN' => '<p>Dear Admin,</p><p>A new IB (Introducing Broker) application has been submitted and requires review.</p><ul><li><strong>Applicant:</strong> {{fullname}} ({{username}})</li><li><strong>Email:</strong> {{email}}</li><li><strong>Application Date:</strong> {{application_date}}</li><li><strong>Requested IB Type:</strong> {{requested_ib_type}}</li></ul><p>Please review the application in the admin panel.</p><div style="text-align: center; margin: 20px 0;"><a href="{{admin_url}}/ib-applications" style="background: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px;">Review IB Application</a></div>',

            // Security & Alerts
            'SECURITY_ALERT' => '<p>Dear {{fullname}},</p><p>We detected unusual activity on your account and want to ensure your security.</p><ul><li><strong>Activity Type:</strong> {{activity_type}}</li><li><strong>Date & Time:</strong> {{activity_date}}</li><li><strong>IP Address:</strong> {{ip_address}}</li><li><strong>Location:</strong> {{location}}</li><li><strong>Device:</strong> {{device_info}}</li></ul><p>If this was you, no action is needed. If not, please secure your account immediately.</p><div style="background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 15px 0;"><strong>Immediate Actions:</strong> Change your password, enable 2FA, and contact support.</div>',

            'ACCOUNT_VERIFICATION_REQUIRED' => '<p>Dear {{fullname}},</p><p>To continue using our services, please verify your account by completing the required steps.</p><ul><li><strong>Verification Level:</strong> {{required_level}}</li><li><strong>Required Documents:</strong> {{required_documents}}</li><li><strong>Deadline:</strong> {{deadline}}</li><li><strong>Current Status:</strong> {{current_status}}</li></ul><p>Complete verification to maintain full access to your account features.</p><div style="text-align: center; margin: 20px 0;"><a href="{{site_url}}/user/kyc" style="background: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px;">Complete Verification</a></div>',

            // Profile & Permissions
            'PROFILE_PERMISSION_REJECTED' => '<p>Dear {{fullname}},</p><p>Your request for profile permission changes has been reviewed.</p><ul><li><strong>Request Type:</strong> {{permission_type}}</li><li><strong>Status:</strong> Rejected</li><li><strong>Reason:</strong> {{rejection_reason}}</li><li><strong>Review Date:</strong> {{review_date}}</li></ul><p>Please contact support if you need clarification on this decision.</p>',
        ];

        return $content[$templateType] ?? '<p>Dear {{fullname}},</p><p>This is an important notification regarding your account.</p><p>{{message}}</p><p>If you have any questions, please contact our support team.</p>';
    }
}
