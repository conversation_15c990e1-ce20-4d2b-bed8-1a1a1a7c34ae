<?php
/**
 * VISUAL BUILDER FIX VERIFICATION SCRIPT
 * This script verifies that the duplicate file conflicts have been resolved
 */

echo "🔍 VISUAL BUILDER FIX VERIFICATION\n";
echo "==================================\n\n";

// 1. CHECK FILE EXISTENCE
echo "1️⃣ FILE EXISTENCE CHECK\n";
echo "========================\n";

$files = [
    'assets/admin/css/visual-builder-email-editor.css' => 'ACTIVE CSS',
    'assets/admin/css/visual-email-editor.css' => 'DUPLICATE CSS (should be deleted)',
    'assets/admin/js/visual-builder-email-editor.js' => 'ACTIVE JS',
    'assets/admin/js/visual-email-editor.js' => 'DUPLICATE JS (should be deleted)'
];

foreach ($files as $file => $description) {
    if (file_exists($file)) {
        $size = filesize($file);
        $modified = date('Y-m-d H:i:s', filemtime($file));
        echo "✅ {$description}: EXISTS ({$size} bytes, modified: {$modified})\n";
    } else {
        echo "❌ {$description}: NOT FOUND\n";
    }
}

echo "\n";

// 2. CHECK TEMPLATE LOADING
echo "2️⃣ TEMPLATE LOADING CHECK\n";
echo "==========================\n";

$templateFile = 'resources/views/admin/notification/edit.blade.php';
if (file_exists($templateFile)) {
    $content = file_get_contents($templateFile);
    
    // Check which CSS file is loaded
    if (str_contains($content, 'visual-builder-email-editor.css')) {
        echo "✅ Template loads CORRECT CSS: visual-builder-email-editor.css\n";
    } else {
        echo "❌ Template CSS loading issue\n";
    }
    
    // Check which JS file is loaded
    if (str_contains($content, 'visual-builder-email-editor.js')) {
        echo "✅ Template loads CORRECT JS: visual-builder-email-editor.js\n";
    } else {
        echo "❌ Template JS loading issue\n";
    }
    
    // Check for duplicate loading
    $cssCount = substr_count($content, 'visual-') + substr_count($content, 'email-editor.css');
    $jsCount = substr_count($content, 'visual-') + substr_count($content, 'email-editor.js');
    
    echo "✅ CSS file references: {$cssCount}\n";
    echo "✅ JS file references: {$jsCount}\n";
    
} else {
    echo "❌ Template file not found\n";
}

echo "\n";

// 3. CHECK JAVASCRIPT STRUCTURE
echo "3️⃣ JAVASCRIPT STRUCTURE CHECK\n";
echo "==============================\n";

$jsFile = 'assets/admin/js/visual-builder-email-editor.js';
if (file_exists($jsFile)) {
    $jsContent = file_get_contents($jsFile);
    
    // Check for conflicting form handlers
    $formHandlerCount = substr_count($jsContent, 'form.addEventListener(\'submit\'');
    echo "✅ Form submission handlers: {$formHandlerCount}\n";
    
    if ($formHandlerCount > 1) {
        echo "⚠️  WARNING: Multiple form handlers detected - may cause conflicts\n";
    }
    
    // Check for prepareFormSubmission function
    $hasPrepareFunction = str_contains($jsContent, 'function prepareFormSubmission');
    echo "✅ prepareFormSubmission function: " . ($hasPrepareFunction ? 'EXISTS' : 'MISSING') . "\n";
    
    // Check for DOM manipulation
    $domManipulation = substr_count($jsContent, 'innerHTML') + substr_count($jsContent, 'appendChild');
    echo "✅ DOM manipulation operations: {$domManipulation}\n";
    
    if ($domManipulation > 10) {
        echo "⚠️  WARNING: High DOM manipulation count - may cause layout issues\n";
    }
    
    // Check for error handling
    $errorHandling = substr_count($jsContent, 'try {') + substr_count($jsContent, 'catch');
    echo "✅ Error handling blocks: {$errorHandling}\n";
    
} else {
    echo "❌ JavaScript file not found\n";
}

echo "\n";

// 4. RECOMMENDATIONS
echo "4️⃣ RECOMMENDATIONS\n";
echo "==================\n";

$duplicateCssExists = file_exists('assets/admin/css/visual-email-editor.css');
$duplicateJsExists = file_exists('assets/admin/js/visual-email-editor.js');

if ($duplicateCssExists || $duplicateJsExists) {
    echo "🚨 CRITICAL: Duplicate files still exist!\n";
    if ($duplicateCssExists) {
        echo "   - DELETE: assets/admin/css/visual-email-editor.css\n";
    }
    if ($duplicateJsExists) {
        echo "   - DELETE: assets/admin/js/visual-email-editor.js\n";
    }
} else {
    echo "✅ GOOD: Duplicate files have been removed\n";
}

$activeFilesExist = file_exists('assets/admin/css/visual-builder-email-editor.css') && 
                   file_exists('assets/admin/js/visual-builder-email-editor.js');

if ($activeFilesExist) {
    echo "✅ GOOD: Active Visual Builder files exist\n";
} else {
    echo "❌ CRITICAL: Active Visual Builder files missing\n";
}

echo "\n📋 NEXT STEPS:\n";
echo "==============\n";
echo "1. Upload the fixed visual-builder-email-editor.js file\n";
echo "2. Clear Laravel caches (php artisan cache:clear)\n";
echo "3. Clear browser cache (Ctrl+F5)\n";
echo "4. Test email template editing\n";
echo "5. Verify no page layout destruction occurs\n";
echo "6. Confirm template changes are saved\n\n";

echo "🎯 EXPECTED OUTCOME:\n";
echo "====================\n";
echo "✅ No page layout destruction during form submission\n";
echo "✅ No JavaScript errors in browser console\n";
echo "✅ Visual Builder content syncs to form fields\n";
echo "✅ Template changes persist after saving\n";
echo "✅ Clean, professional user experience\n\n";

echo "🔧 FIX VERIFICATION COMPLETE\n";

?>
