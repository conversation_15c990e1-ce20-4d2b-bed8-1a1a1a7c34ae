{{-- Network Tree Node Partial --}}
@foreach($nodes as $node)
<div class="network-node level-{{ $node['level'] }}">
    <div class="user-card {{ $node['is_ib'] ? 'ib-user' : 'regular-user' }}">
        <div class="user-avatar">
            @if($node['is_ib'])
                @if($node['ib_type'] == 'master')
                    <i class="las la-crown text-primary" title="Master IB"></i>
                @else
                    <i class="las la-user-tie text-success" title="Sub IB"></i>
                @endif
            @else
                <i class="las la-user text-muted" title="Client"></i>
            @endif
        </div>
        <div class="user-info">
            @php
                $ibIndicator = '';
                if ($node['is_ib']) {
                    if ($node['ib_type'] === 'master') {
                        $ibIndicator = ' (M)';
                    } elseif ($node['ib_type'] === 'sub') {
                        $ibIndicator = ' (S)';
                    }
                } else {
                    $ibIndicator = ' (C)';
                }
            @endphp
            <h6 class="mb-1">{{ $node['user']->fullname }}{{ $ibIndicator }}</h6>
            <small class="text-muted">{{ $node['user']->username }}</small>
            @if($node['mt5_login'])
                <br><small class="text-info">MT5: {{ $node['mt5_login'] }}</small>
            @endif
        </div>
        <div class="user-stats">
            @if($node['is_ib'])
                <span class="badge badge--{{ $node['ib_type'] == 'master' ? 'primary' : 'success' }}">
                    {{ ucfirst($node['ib_type']) }} IB
                </span>
                <br>
                <small class="text-success">${{ number_format($node['total_commission'], 2) }}</small>
            @else
                <span class="badge badge--secondary">Client</span>
                @if($node['mt5_balance'] > 0)
                    <br><small class="text-info">${{ number_format($node['mt5_balance'], 2) }}</small>
                @endif
            @endif
        </div>
    </div>

    {{-- Recursively render children --}}
    @if(count($node['children']) > 0)
        <div class="network-children">
            @include('templates.basic.user.ib.partials.network-tree-node', ['nodes' => $node['children'], 'level' => $node['level'] + 1])
        </div>
    @endif
</div>
@endforeach

@push('style-lib')
<style>
.ib-user .user-card {
    border-left: 4px solid #28a745;
}

.regular-user .user-card {
    border-left: 4px solid #6c757d;
}

.level-1 {
    margin-left: 0;
}

.level-2 {
    margin-left: 20px;
}

.level-3 {
    margin-left: 40px;
}

.level-4 {
    margin-left: 60px;
}

.level-5 {
    margin-left: 80px;
}

.network-children {
    position: relative;
}

.network-children::before {
    content: '';
    position: absolute;
    left: -20px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.network-node:last-child .network-children::before {
    height: 50%;
}
</style>
@endpush
