<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use Carbon\Carbon;

class IdentifyMT5IBUsers extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'mt5:identify-ib-users 
                            {--days=365 : Number of days to analyze commission data}
                            {--min-commission=100 : Minimum commission to qualify as IB}
                            {--min-referrals=1 : Minimum referrals to qualify as IB}
                            {--dry-run : Show results without making changes}
                            {--sync : Actually update the database}';

    /**
     * The console command description.
     */
    protected $description = 'Identify real IB users from MT5 commission data and synchronize with local database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 IDENTIFYING REAL MT5 IB USERS FROM COMMISSION DATA');
        $this->info('=====================================================');

        $days = $this->option('days');
        $minCommission = $this->option('min-commission');
        $minReferrals = $this->option('min-referrals');
        $dryRun = $this->option('dry-run');
        $sync = $this->option('sync');

        if (!$dryRun && !$sync) {
            $this->warn('⚠️  Use --dry-run to preview or --sync to actually update the database');
            return;
        }

        $startDate = Carbon::now()->subDays($days);
        $this->info("📅 Analyzing commission data from: {$startDate->format('Y-m-d')} to now");
        $this->info("💰 Minimum commission threshold: \${$minCommission}");
        $this->info("👥 Minimum referrals threshold: {$minReferrals}");

        try {
            // Step 1: Identify IB users from commission data
            $this->info("\n📊 STEP 1: Analyzing MT5 Commission Data");
            $ibCandidates = $this->identifyIBCandidates($startDate, $minCommission);
            
            // Step 2: Analyze referral hierarchies
            $this->info("\n🌳 STEP 2: Analyzing Referral Hierarchies");
            $hierarchyData = $this->analyzeReferralHierarchies($ibCandidates, $minReferrals);
            
            // Step 3: Cross-reference with local database
            $this->info("\n🔗 STEP 3: Cross-referencing with Local Database");
            $syncData = $this->crossReferenceLocalUsers($hierarchyData);
            
            // Step 4: Display results
            $this->info("\n📋 STEP 4: Analysis Results");
            $this->displayResults($syncData);
            
            // Step 5: Sync if requested
            if ($sync && !$dryRun) {
                $this->info("\n💾 STEP 5: Synchronizing Database");
                $this->syncDatabase($syncData);
            } elseif ($dryRun) {
                $this->warn("\n🧪 DRY RUN MODE - No changes made to database");
            }

        } catch (\Exception $e) {
            $this->error("❌ Error: " . $e->getMessage());
            $this->error("Stack trace: " . $e->getTraceAsString());
        }
    }

    /**
     * Identify IB candidates from MT5 commission data
     */
    private function identifyIBCandidates($startDate, $minCommission)
    {
        $this->info("🔍 Querying MT5 deals table for commission data...");

        $commissionData = DB::connection('mbf-dbmt5')
            ->table('mt5_deals_2025')
            ->select([
                'Login',
                DB::raw('COUNT(*) as commission_count'),
                DB::raw('SUM(Profit) as total_commission'),
                DB::raw('MIN(Time) as first_commission'),
                DB::raw('MAX(Time) as last_commission'),
                DB::raw('COUNT(DISTINCT Symbol) as symbols_traded')
            ])
            ->where('Action', 18) // Commission action
            ->where('Time', '>=', $startDate)
            ->where('Profit', '>', 0) // Only positive commissions
            ->groupBy('Login')
            ->having('total_commission', '>=', $minCommission)
            ->orderBy('total_commission', 'desc')
            ->get();

        $this->info("✅ Found " . $commissionData->count() . " MT5 accounts with commission earnings >= \${$minCommission}");

        return $commissionData;
    }

    /**
     * Analyze referral hierarchies from MT5 groups
     */
    private function analyzeReferralHierarchies($ibCandidates, $minReferrals)
    {
        $this->info("🌳 Analyzing MT5 group structures for IB hierarchies...");

        $hierarchyData = [];

        foreach ($ibCandidates as $candidate) {
            // Get user details from MT5 users table
            $mt5User = DB::connection('mbf-dbmt5')
                ->table('mt5_users')
                ->where('Login', $candidate->Login)
                ->first();

            if (!$mt5User) {
                continue;
            }

            // Determine IB type based on group
            $ibType = $this->determineIBType($mt5User->Group);
            
            // Count potential referrals (users with similar group patterns)
            $referralCount = $this->countPotentialReferrals($candidate->Login, $mt5User->Group);

            $hierarchyData[] = [
                'mt5_login' => $candidate->Login,
                'name' => trim($mt5User->FirstName . ' ' . $mt5User->LastName),
                'email' => $mt5User->Email,
                'group' => $mt5User->Group,
                'ib_type' => $ibType,
                'total_commission' => $candidate->total_commission,
                'commission_count' => $candidate->commission_count,
                'first_commission' => $candidate->first_commission,
                'last_commission' => $candidate->last_commission,
                'symbols_traded' => $candidate->symbols_traded,
                'potential_referrals' => $referralCount,
                'qualifies_as_ib' => $referralCount >= $minReferrals && $candidate->total_commission >= 100
            ];
        }

        // Filter only qualified IBs
        $qualifiedIBs = array_filter($hierarchyData, function($ib) {
            return $ib['qualifies_as_ib'];
        });

        $this->info("✅ Found " . count($qualifiedIBs) . " qualified IB candidates");

        return $qualifiedIBs;
    }

    /**
     * Determine IB type based on MT5 group
     */
    private function determineIBType($group)
    {
        $group = strtolower($group);
        
        if (strpos($group, 'affiliates') !== false) {
            return 'master';
        } elseif (strpos($group, 'ib') !== false) {
            return 'sub';
        } elseif (strpos($group, 'partner') !== false) {
            return 'master';
        }
        
        return 'master'; // Default to master for commission earners
    }

    /**
     * Count potential referrals for an IB
     */
    private function countPotentialReferrals($ibLogin, $ibGroup)
    {
        // This is a simplified approach - in reality, you'd need more complex logic
        // to determine actual referral relationships from MT5 data
        
        // For now, count users in similar groups or with related patterns
        $referralCount = DB::connection('mbf-dbmt5')
            ->table('mt5_users')
            ->where('Group', 'like', '%' . substr($ibGroup, 0, 10) . '%')
            ->where('Login', '!=', $ibLogin)
            ->count();

        return min($referralCount, 50); // Cap at 50 for performance
    }

    /**
     * Cross-reference with local database
     */
    private function crossReferenceLocalUsers($hierarchyData)
    {
        $this->info("🔗 Cross-referencing " . count($hierarchyData) . " IB candidates with local database...");

        $syncData = [
            'existing_users' => [],
            'missing_users' => [],
            'update_needed' => [],
            'already_ib' => []
        ];

        foreach ($hierarchyData as $ibData) {
            $localUser = User::where('mt5_login', $ibData['mt5_login'])
                ->orWhere('email', $ibData['email'])
                ->first();

            if ($localUser) {
                if ($localUser->ib_status == 1) { // FIXED: Check for 1 instead of 'approved'
                    $syncData['already_ib'][] = array_merge($ibData, ['local_user_id' => $localUser->id]);
                } else {
                    $syncData['update_needed'][] = array_merge($ibData, ['local_user_id' => $localUser->id]);
                }
                $syncData['existing_users'][] = array_merge($ibData, ['local_user_id' => $localUser->id]);
            } else {
                $syncData['missing_users'][] = $ibData;
            }
        }

        return $syncData;
    }

    /**
     * Display analysis results
     */
    private function displayResults($syncData)
    {
        $this->info("📊 ANALYSIS RESULTS SUMMARY");
        $this->info("==========================");
        
        $this->table([
            'Category',
            'Count',
            'Description'
        ], [
            ['Total IB Candidates', count($syncData['existing_users']) + count($syncData['missing_users']), 'Users earning commissions in MT5'],
            ['Existing Local Users', count($syncData['existing_users']), 'Found in local database'],
            ['Missing Local Users', count($syncData['missing_users']), 'Need to be imported'],
            ['Already Approved IBs', count($syncData['already_ib']), 'Already have IB status'],
            ['Need IB Status Update', count($syncData['update_needed']), 'Exist locally but not IB approved'],
        ]);

        if (count($syncData['update_needed']) > 0) {
            $this->info("\n👥 USERS NEEDING IB STATUS UPDATE:");
            $this->table([
                'Local ID', 'Name', 'Email', 'MT5 Login', 'Commission', 'Referrals', 'IB Type'
            ], array_map(function($user) {
                return [
                    $user['local_user_id'],
                    $user['name'],
                    $user['email'],
                    $user['mt5_login'],
                    '$' . number_format($user['total_commission'], 2),
                    $user['potential_referrals'],
                    ucfirst($user['ib_type']) . ' IB'
                ];
            }, array_slice($syncData['update_needed'], 0, 10)));
        }

        if (count($syncData['missing_users']) > 0) {
            $this->info("\n🆕 MISSING USERS TO IMPORT:");
            $this->table([
                'Name', 'Email', 'MT5 Login', 'Commission', 'Referrals', 'IB Type'
            ], array_map(function($user) {
                return [
                    $user['name'],
                    $user['email'],
                    $user['mt5_login'],
                    '$' . number_format($user['total_commission'], 2),
                    $user['potential_referrals'],
                    ucfirst($user['ib_type']) . ' IB'
                ];
            }, array_slice($syncData['missing_users'], 0, 10)));
        }
    }

    /**
     * Sync database with identified IB users
     */
    private function syncDatabase($syncData)
    {
        $this->info("💾 Starting database synchronization...");

        $updatedCount = 0;
        $importedCount = 0;

        // Update existing users to IB status
        foreach ($syncData['update_needed'] as $userData) {
            $user = User::find($userData['local_user_id']);
            if ($user) {
                $user->ib_status = 1; // FIXED: Use 1 instead of 'approved'
                $user->ib_type = $userData['ib_type'];
                $user->partner = 1;
                $user->save();
                $updatedCount++;

                $this->line("✅ Updated user {$user->id}: {$user->firstname} {$user->lastname} to {$userData['ib_type']} IB");
            }
        }

        // Import missing users (simplified - you may want more detailed import logic)
        foreach (array_slice($syncData['missing_users'], 0, 10) as $userData) {
            // This would require more complex logic to create complete user records
            $this->line("ℹ️  Would import: {$userData['name']} ({$userData['email']}) - {$userData['ib_type']} IB");
            // Actual import logic would go here
        }

        $this->info("✅ Database sync completed:");
        $this->info("   - Updated {$updatedCount} existing users to IB status");
        $this->info("   - {$importedCount} new users imported");
    }
}
