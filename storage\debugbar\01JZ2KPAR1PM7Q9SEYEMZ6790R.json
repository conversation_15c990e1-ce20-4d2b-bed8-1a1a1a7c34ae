{"__meta": {"id": "01JZ2KPAR1PM7Q9SEYEMZ6790R", "datetime": "2025-07-01 09:04:38", "utime": **********.659835, "method": "POST", "uri": "/mbf.mybrokerforex.com-********/admin", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751360676.640131, "end": **********.659859, "duration": 2.019727945327759, "duration_str": "2.02s", "measures": [{"label": "Booting", "start": 1751360676.640131, "relative_start": 0, "end": **********.124375, "relative_end": **********.124375, "duration": 0.*****************, "duration_str": "484ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.124396, "relative_start": 0.****************, "end": **********.659862, "relative_end": 3.0994415283203125e-06, "duration": 1.***************, "duration_str": "1.54s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.178399, "relative_start": 0.****************, "end": **********.185796, "relative_end": **********.185796, "duration": 0.0073969364166259766, "duration_str": "7.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.649766, "relative_start": 2.****************, "end": **********.650925, "relative_end": **********.650925, "duration": 0.0011589527130126953, "duration_str": "1.16ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.12", "Environment": "production", "Debug Mode": "Enabled", "URL": "localhost/mbf.mybrokerforex.com-********", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 4, "nb_statements": 3, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01776, "accumulated_duration_str": "17.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "vendor/beyondcode/laravel-query-detector/src/QueryDetector.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetector.php", "line": 25}, {"index": 10, "namespace": null, "name": "vendor/beyondcode/laravel-query-detector/src/QueryDetectorMiddleware.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetectorMiddleware.php", "line": 30}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21}], "start": **********.176878, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "QueryDetector.php:25", "source": {"index": 9, "namespace": null, "name": "vendor/beyondcode/laravel-query-detector/src/QueryDetector.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetector.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fvendor%2Fbeyondcode%2Flaravel-query-detector%2Fsrc%2FQueryDetector.php&line=25", "ajax": false, "filename": "QueryDetector.php", "line": "25"}, "connection": "mbf-db", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `extensions` where `act` = 'google-recaptcha2' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["google-recaptcha2", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Lib\\Captcha.php", "line": 78}, {"index": 17, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Lib\\Captcha.php", "line": 63}, {"index": 18, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Helpers\\helpers.php", "line": 100}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.734251, "duration": 0.01509, "duration_str": "15.09ms", "memory": 0, "memory_str": null, "filename": "Captcha.php:78", "source": {"index": 16, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Lib\\Captcha.php", "line": 78}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FLib%2FCaptcha.php&line=78", "ajax": false, "filename": "Captcha.php", "line": "78"}, "connection": "mbf-db", "explain": null, "start_percent": 0, "width_percent": 84.966}, {"sql": "select * from `extensions` where `act` = 'custom-captcha' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["custom-captcha", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Lib\\Captcha.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Lib\\Captcha.php", "line": 64}, {"index": 18, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Helpers\\helpers.php", "line": 100}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7636101, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "Captcha.php:95", "source": {"index": 16, "namespace": null, "name": "app/Lib/Captcha.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Lib\\Captcha.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FLib%2FCaptcha.php&line=95", "ajax": false, "filename": "Captcha.php", "line": "95"}, "connection": "mbf-db", "explain": null, "start_percent": 84.966, "width_percent": 7.545}, {"sql": "select * from `admins` where `username` = 'admin' limit 1", "type": "query", "params": [], "bindings": ["admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 381}, {"index": 18, "namespace": null, "name": "vendor/laravel/ui/auth-backend/AuthenticatesUsers.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php", "line": 86}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/Auth/LoginController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\Auth\\LoginController.php", "line": 88}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.245124, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:139", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=139", "ajax": false, "filename": "EloquentUserProvider.php", "line": "139"}, "connection": "mbf-db", "explain": null, "start_percent": 92.511, "width_percent": 7.489}]}, "models": {"data": {"App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "https://localhost/mbf.mybrokerforex.com-********/admin", "action_name": "admin.login.submit", "controller_action": "App\\Http\\Controllers\\Admin\\Auth\\LoginController@login", "uri": "POST admin", "controller": "App\\Http\\Controllers\\Admin\\Auth\\LoginController@login<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FAuth%2FLoginController.php&line=67\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin\\Auth", "prefix": "/admin", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FAuth%2FLoginController.php&line=67\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/Auth/LoginController.php:67-98</a>", "middleware": "web", "duration": "2.01s", "peak_memory": "28MB", "response": "Redirect to https://localhost/mbf.mybrokerforex.com-********/admin", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1439448450 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1439448450\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1703041395 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cWf2pkyBC6iCHshDbAf4CxRGZHOxST1Jm91sQjLd</span>\"\n  \"<span class=sf-dump-key>username</span>\" => \"<span class=sf-dump-str title=\"5 characters\">admin</span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1703041395\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1090152148 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">83</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">https://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">https://localhost/mbf.mybrokerforex.com-********/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">en-US,en;q=0.9,ms-MY;q=0.8,ms;q=0.7,zh-CN;q=0.6,zh;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InRMWUFsZGhLd0gxRTQ5Z1I0dGlOY0E9PSIsInZhbHVlIjoiaWtaN2ZuMnJPNEpDWFVSUS9BQysxOE1OMjZoaU1vZDlvWHp2Q3BvMFBGbXJxcUZjNTR3eE1mS3JyOGdaNUlxU014SXJpWWVFRGljU0NWSWEzaFhQdnB0N05jOGE0YnRvTmY5bG9ld2ZGMGppa0gvQWF2cHNweXRnRnF6ejBZVysiLCJtYWMiOiJhMjA1YjJkZGRlYjVmZWYzZjU3OTFhZjg0ZDU3OWQ5ZThkNTA1NDEzMWYwODYxYjlmMWZkNjUwZDk0YmE4MWY2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImczN3lqZXUrZVBGSG1SVDhCV0xIT3c9PSIsInZhbHVlIjoiMUJVZG5hclBYckFObDB4VityNmxHa2wxU1F6d2NyMG1rNUVCMFd0bWkreWgyNzZDcEY1Zk1MSUgvd2RaTXhrYmlmOHhPMHArTXJuZFJtVStOckNkNDlYSEtqZjJ2Yk1hSEhXSExyWWhHd3dhbldYRXh5Vy9UTitvbi9sMkJMRXYiLCJtYWMiOiJmZTRjMTZjNjM5NmQ4ODdkNmM5MjA2YzQ4OTg1ZmYxYTg3MWJiODg5Y2QxMjJhMjUwZWFmODIzOWRlNTVmNDdlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1090152148\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-24565473 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cWf2pkyBC6iCHshDbAf4CxRGZHOxST1Jm91sQjLd</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dboxNSFfC48cNdXTKxoPSmN07T4vIBxj3NpxgLBd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-24565473\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1577987758 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 09:04:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">https://localhost/mbf.mybrokerforex.com-********/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1577987758\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-97888910 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6wydTXTQETk9uM050OalbDmHEZPTqQskjidNxWTA</span>\"\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"54 characters\">https://localhost/mbf.mybrokerforex.com-********/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>flasher::envelopes</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-97888910\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://localhost/mbf.mybrokerforex.com-********/admin", "action_name": "admin.login.submit", "controller_action": "App\\Http\\Controllers\\Admin\\Auth\\LoginController@login"}, "badge": "302 Found"}}