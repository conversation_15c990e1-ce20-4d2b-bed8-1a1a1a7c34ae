<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🔧 FIXING IB ACCOUNT PRIORITY ISSUES\n";
echo "===================================\n\n";

$dryRun = true; // Set to false to actually apply fixes

if ($dryRun) {
    echo "🧪 DRY RUN MODE - No changes will be made\n";
    echo "Set \$dryRun = false to apply fixes\n\n";
}

// Step 1: Identify users with IB account priority issues
echo "1. IDENTIFYING IB ACCOUNT PRIORITY ISSUES\n";
echo "----------------------------------------\n";

$ibPriorityIssues = \DB::select("
    SELECT
        u.id,
        u.email,
        u.firstname,
        u.lastname,
        u.mt5_login as current_primary,
        u.mt5_group as current_primary_group,
        u.partner as ib_status,
        u.ib_type,
        ua.Account as ib_account,
        ua.Group_Name as ib_group
    FROM users u
    JOIN user_accounts ua ON u.id = ua.User_Id
    WHERE u.partner = 1
    AND ua.Account_Type = 'ib'
    AND u.mt5_login COLLATE utf8mb4_unicode_ci != ua.Account COLLATE utf8mb4_unicode_ci
    ORDER BY u.id
");

echo "Found " . count($ibPriorityIssues) . " users with IB account priority issues:\n";
foreach ($ibPriorityIssues as $issue) {
    echo "  - {$issue->email}: Primary={$issue->current_primary}, IB Account={$issue->ib_account}\n";
}
echo "\n";

// Step 2: Find users who lost IB status
echo "2. IDENTIFYING USERS WHO LOST IB STATUS\n";
echo "--------------------------------------\n";

$lostIbStatus = \DB::select("
    SELECT 
        u.id,
        u.email,
        u.firstname,
        u.lastname,
        u.partner,
        u.ib_type,
        ua.Account as ib_account,
        ua.Group_Name as ib_group,
        ua.Account_Type
    FROM users u
    JOIN user_accounts ua ON u.id = ua.User_Id
    WHERE u.partner = 0
    AND ua.Account_Type = 'ib'
    ORDER BY u.id
");

echo "Found " . count($lostIbStatus) . " users who lost IB status:\n";
foreach ($lostIbStatus as $lost) {
    echo "  - {$lost->email}: Has IB account {$lost->ib_account} but partner=0\n";
}
echo "\n";

// Step 3: Find missing primary accounts in user_accounts table
echo "3. IDENTIFYING MISSING PRIMARY ACCOUNTS\n";
echo "--------------------------------------\n";

$missingPrimary = \DB::select("
    SELECT
        u.id,
        u.email,
        u.mt5_login as primary_account,
        u.mt5_group,
        u.mt5_balance
    FROM users u
    LEFT JOIN user_accounts ua ON u.id = ua.User_Id AND u.mt5_login COLLATE utf8mb4_unicode_ci = ua.Account COLLATE utf8mb4_unicode_ci
    WHERE u.mt5_login IS NOT NULL
    AND u.mt5_login != ''
    AND ua.Account IS NULL
    ORDER BY u.id
    LIMIT 20
");

echo "Found " . count($missingPrimary) . " users with missing primary accounts in user_accounts:\n";
foreach ($missingPrimary as $missing) {
    echo "  - {$missing->email}: Primary {$missing->primary_account} not in user_accounts\n";
}
echo "\n";

// Step 4: Apply fixes
if (!$dryRun) {
    echo "4. APPLYING FIXES\n";
    echo "----------------\n";
    
    // Fix 1: Restore IB accounts as primary
    if (count($ibPriorityIssues) > 0) {
        echo "Fixing IB account priorities...\n";
        $updated = \DB::update("
            UPDATE users u
            JOIN user_accounts ua ON u.id = ua.User_Id
            SET 
                u.mt5_login = ua.Account,
                u.mt5_group = ua.Group_Name,
                u.partner = 1,
                u.ib_status = 1
            WHERE ua.Account_Type = 'ib'
            AND u.mt5_login != ua.Account
        ");
        echo "✅ Updated {$updated} users with IB account priority issues\n";
    }
    
    // Fix 2: Restore lost IB status
    if (count($lostIbStatus) > 0) {
        echo "Restoring lost IB status...\n";
        $restored = \DB::update("
            UPDATE users u
            JOIN user_accounts ua ON u.id = ua.User_Id
            SET 
                u.partner = 1,
                u.ib_status = 1,
                u.ib_type = CASE 
                    WHEN ua.Group_Name LIKE '%affiliate%' THEN 'affiliate'
                    WHEN ua.Group_Name LIKE '%ib%' THEN 'ib'
                    ELSE 'ib'
                END
            WHERE ua.Account_Type = 'ib'
            AND u.partner = 0
        ");
        echo "✅ Restored IB status for {$restored} users\n";
    }
    
    // Fix 3: Add missing primary accounts to user_accounts table
    if (count($missingPrimary) > 0) {
        echo "Adding missing primary accounts to user_accounts table...\n";
        $added = 0;
        foreach ($missingPrimary as $missing) {
            try {
                \App\Models\UserAccounts::create([
                    'User_Id' => $missing->id,
                    'Account' => $missing->primary_account,
                    'Master_Password' => 'synced_from_mt5',
                    'Investor_Password' => 'synced_from_mt5',
                    'Phone_Password' => '',
                    'Group_Name' => $missing->mt5_group,
                    'Account_Type' => 'real', // Will be updated by next sync
                    'Leverage' => 100,
                    'Balance' => $missing->mt5_balance ?? 0,
                    'Currency' => 'USD',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                $added++;
            } catch (\Exception $e) {
                echo "❌ Failed to add account {$missing->primary_account}: " . $e->getMessage() . "\n";
            }
        }
        echo "✅ Added {$added} missing primary accounts to user_accounts table\n";
    }
    
} else {
    echo "4. FIXES THAT WOULD BE APPLIED (DRY RUN)\n";
    echo "---------------------------------------\n";
    
    if (count($ibPriorityIssues) > 0) {
        echo "Would fix IB account priorities for " . count($ibPriorityIssues) . " users\n";
    }
    
    if (count($lostIbStatus) > 0) {
        echo "Would restore IB status for " . count($lostIbStatus) . " users\n";
    }
    
    if (count($missingPrimary) > 0) {
        echo "Would add " . count($missingPrimary) . " missing primary accounts to user_accounts table\n";
    }
}

echo "\n";

// Step 5: Verification queries
echo "5. VERIFICATION QUERIES\n";
echo "----------------------\n";

echo "After fixes, run these queries to verify:\n\n";

echo "-- Check IB users have correct primary accounts:\n";
echo "SELECT u.email, u.mt5_login, u.partner, ua.Account_Type \n";
echo "FROM users u \n";
echo "JOIN user_accounts ua ON u.id = ua.User_Id AND u.mt5_login = ua.Account \n";
echo "WHERE u.partner = 1;\n\n";

echo "-- Check all users have primary accounts in user_accounts:\n";
echo "SELECT COUNT(*) as missing_primary_accounts \n";
echo "FROM users u \n";
echo "LEFT JOIN user_accounts ua ON u.id = ua.User_Id AND u.mt5_login = ua.Account \n";
echo "WHERE u.mt5_login IS NOT NULL AND ua.Account IS NULL;\n\n";

echo "-- Check multiple account display data:\n";
echo "SELECT u.email, u.mt5_login, \n";
echo "       GROUP_CONCAT(ua.Account ORDER BY ua.Account) as all_accounts \n";
echo "FROM users u \n";
echo "JOIN user_accounts ua ON u.id = ua.User_Id \n";
echo "GROUP BY u.id \n";
echo "HAVING COUNT(ua.Account) > 1 \n";
echo "LIMIT 5;\n\n";

echo "🎯 SUMMARY:\n";
echo "- IB Priority Issues: " . count($ibPriorityIssues) . "\n";
echo "- Lost IB Status: " . count($lostIbStatus) . "\n";
echo "- Missing Primary Accounts: " . count($missingPrimary) . "\n";

if ($dryRun) {
    echo "\n⚠️  Set \$dryRun = false and run again to apply fixes\n";
} else {
    echo "\n✅ All fixes have been applied!\n";
    echo "Run the enhanced sync command to ensure data consistency:\n";
    echo "php artisan mt5:sync-users --limit=100 --force\n";
}

echo "\n🔧 NEXT STEPS:\n";
echo "1. Run this script with \$dryRun = false to apply fixes\n";
echo "2. Run enhanced sync: php artisan mt5:sync-users --limit=100 --force\n";
echo "3. Clear view cache: php artisan view:clear\n";
echo "4. Check admin interface for correct multiple account display\n";
echo "5. Verify IB users maintain their status and primary accounts\n";
