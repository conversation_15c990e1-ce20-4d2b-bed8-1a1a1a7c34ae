<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Creation Form - Functionality Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://maxst.icons8.com/vue-static/landings/line-awesome/line-awesome/1.3.0/css/line-awesome.min.css">
    <style>
        /* Clean Minimalist Account Creation Styles */
        .account-creation-container {
            background: white;
            min-height: 100vh;
            padding: 40px 0;
        }

        .main-card {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: white;
            box-shadow: none;
            overflow: visible;
        }

        .card-header-custom {
            background: white;
            padding: 32px 32px 0 32px;
            border: none;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 32px;
        }

        .card-header-custom h5 {
            color: #111827;
            font-size: 24px;
            font-weight: 700;
            margin: 0 0 16px 0;
            letter-spacing: -0.025em;
        }

        .card-body-custom {
            padding: 0 32px 32px 32px;
        }

        /* Account Type Cards */
        .account-type-container {
            margin-bottom: 32px;
        }

        .account-type-label {
            color: #111827;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            display: block;
        }

        .account-cards-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 24px;
        }

        .account-card {
            position: relative;
            cursor: pointer;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 24px 20px;
            text-align: center;
            transition: all 0.2s ease;
            background: white;
            min-height: 140px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .account-card:hover {
            border-color: #d1d5db;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        .account-card.active {
            border-color: #111827;
            background: #111827;
            color: white;
        }

        .account-card .card-icon {
            font-size: 32px;
            margin-bottom: 12px;
            color: #6b7280;
            transition: all 0.2s ease;
            display: block;
        }

        .account-card.active .card-icon {
            color: white;
        }

        .account-card .card-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
            color: #111827;
            transition: color 0.2s ease;
        }

        .account-card.active .card-title {
            color: white;
        }

        .account-card .card-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin: 0;
            line-height: 1.4;
            transition: color 0.2s ease;
        }

        .account-card.active .card-subtitle {
            color: rgba(255,255,255,0.8);
        }

        .check-icon {
            position: absolute;
            top: 12px;
            right: 12px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #111827;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            opacity: 0;
            transition: all 0.2s ease;
        }

        .account-card.active .check-icon {
            opacity: 1;
        }

        /* Form Styling */
        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            color: #111827;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
        }

        .form-control, .form-select {
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 12px 16px;
            font-size: 14px;
            transition: all 0.2s ease;
            background: white;
            color: #111827;
        }

        .form-control:focus, .form-select:focus {
            border-color: #111827;
            box-shadow: 0 0 0 1px #111827;
            background: white;
            outline: none;
        }

        /* Password Input Group */
        .password-input-group {
            position: relative;
        }

        .password-toggle-btn {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6b7280;
            font-size: 16px;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
            z-index: 10;
        }

        .password-toggle-btn:hover {
            color: #111827;
            background: #f9fafb;
        }

        /* Password Requirements */
        .password-requirements {
            margin-top: 12px;
            padding: 16px;
            background: #f9fafb;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
            transition: all 0.2s ease;
        }

        .password-requirements.valid {
            border-color: #10b981;
            background: #f0fdf4;
        }

        .requirement {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 13px;
            color: #6b7280;
            transition: all 0.2s ease;
        }

        .requirement:last-child {
            margin-bottom: 0;
        }

        .requirement i {
            margin-right: 8px;
            font-size: 14px;
            width: 14px;
            text-align: center;
        }

        .requirement.valid {
            color: #10b981;
        }

        .requirement.valid i {
            color: #10b981;
        }

        /* Buttons */
        .btn-create {
            background: #111827;
            border: 1px solid #111827;
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .btn-create:hover {
            background: #1f2937;
            border-color: #1f2937;
        }

        .btn-cancel {
            background: white;
            border: 1px solid #e5e7eb;
            color: #6b7280;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .btn-cancel:hover {
            border-color: #d1d5db;
            color: #111827;
            background: #f9fafb;
            text-decoration: none;
        }

        .account-type-radio {
            position: absolute;
            opacity: 0;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="account-creation-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10 col-xl-8">
                    <div class="main-card">
                        <div class="card-header-custom">
                            <h5>Create New Trading Account</h5>
                        </div>
                        <div class="card-body-custom">
                            <form id="accountCreationForm" action="#" method="POST">
                                <!-- Account Type Selection -->
                                <div class="account-type-container">
                                    <label class="account-type-label">Account Type</label>
                                    <div class="account-cards-row">
                                        <div class="account-card active" data-type="real" onclick="selectAccountType('real')">
                                            <input type="radio" class="account-type-radio" name="groups" id="real_account" value="real" checked>
                                            <div class="check-icon">
                                                <i class="las la-check-circle"></i>
                                            </div>
                                            <div class="card-icon">
                                                <i class="las la-chart-line"></i>
                                            </div>
                                            <h6 class="card-title">Real Account</h6>
                                            <p class="card-subtitle">Live trading with real money</p>
                                        </div>
                                        
                                        <div class="account-card" data-type="demo" onclick="selectAccountType('demo')">
                                            <input type="radio" class="account-type-radio" name="groups" id="demo_account" value="demo\MBFX\PREMIUM_200_USD_B">
                                            <div class="check-icon">
                                                <i class="las la-check-circle"></i>
                                            </div>
                                            <div class="card-icon">
                                                <i class="las la-play-circle"></i>
                                            </div>
                                            <h6 class="card-title">Demo Account</h6>
                                            <p class="card-subtitle">Practice with virtual money</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Form Fields -->
                                <div class="form-group">
                                    <label class="form-label">Account Title</label>
                                    <input type="text" class="form-control" name="title" value="Standard Account" readonly>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">Leverage</label>
                                    <select class="form-select" id="leverage" name="leverage" required>
                                        <option value="100">1:100</option>
                                        <option value="200">1:200</option>
                                        <option value="500">1:500</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">Account Nickname</label>
                                    <input type="text" class="form-control" name="name" id="account_name" placeholder="Enter a memorable name for your account" required>
                                </div>

                                <div class="form-group" id="initial_balance_field" style="display: none;">
                                    <label class="form-label">Initial Balance</label>
                                    <select class="form-select" id="initial_balance">
                                        <option value="1000">$1,000</option>
                                        <option value="5000">$5,000</option>
                                        <option value="10000" selected>$10,000</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">Main Password</label>
                                    <div class="password-input-group">
                                        <input type="password" class="form-control" name="password" id="password" placeholder="Enter a secure password" style="padding-right: 55px;" required>
                                        <button class="password-toggle-btn" type="button" id="togglePassword">
                                            <i class="las la-eye" id="passwordIcon"></i>
                                        </button>
                                    </div>

                                    <div class="password-requirements" id="passwordRequirements">
                                        <div class="requirement" id="length-req">
                                            <i class="las la-times me-2"></i> 8-15 characters
                                        </div>
                                        <div class="requirement" id="case-req">
                                            <i class="las la-times me-2"></i> Uppercase and lowercase letters
                                        </div>
                                        <div class="requirement" id="number-req">
                                            <i class="las la-times me-2"></i> At least one number
                                        </div>
                                    </div>
                                </div>

                                <!-- Submit Buttons -->
                                <div class="form-group mb-0">
                                    <div class="d-flex gap-4">
                                        <button type="submit" class="btn-create flex-fill" id="createAccountBtn">
                                            <span class="btn-text">
                                                <i class="las la-plus-circle me-2"></i>Create Account
                                            </span>
                                            <span class="btn-loading d-none">
                                                <i class="las la-spinner la-spin me-2"></i>Creating...
                                            </span>
                                        </button>
                                        <a href="#" class="btn-cancel">
                                            <i class="las la-times me-2"></i>Cancel
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // Global function for account type selection
        function selectAccountType(type) {
            $('.account-card').removeClass('active');
            $('.account-card[data-type="' + type + '"]').addClass('active');
            
            if (type === 'real') {
                $('#real_account').prop('checked', true);
                $('#demo_account').prop('checked', false);
                $('#initial_balance_field').slideUp();
                $('#initial_balance').removeAttr('name');
            } else {
                $('#demo_account').prop('checked', true);
                $('#real_account').prop('checked', false);
                $('#initial_balance_field').slideDown();
                $('#initial_balance').attr('name', 'initial_balance');
            }
        }

        $(document).ready(function() {
            // Password toggle
            $('#togglePassword').on('click', function() {
                const passwordField = $('#password');
                const passwordIcon = $('#passwordIcon');
                const isPassword = passwordField.attr('type') === 'password';
                
                passwordField.attr('type', isPassword ? 'text' : 'password');
                passwordIcon.removeClass().addClass(isPassword ? 'las la-eye-slash' : 'las la-eye');
            });

            // Password validation
            $('#password').on('input', function() {
                const password = $(this).val();
                
                updateRequirement('#length-req', password.length >= 8 && password.length <= 15);
                
                const hasUpper = /[A-Z]/.test(password);
                const hasLower = /[a-z]/.test(password);
                updateRequirement('#case-req', hasUpper && hasLower);
                
                const hasNumber = /\d/.test(password);
                updateRequirement('#number-req', hasNumber);
                
                const allValid = password.length >= 8 && password.length <= 15 && hasUpper && hasLower && hasNumber;
                $('#passwordRequirements').toggleClass('valid', allValid);
            });

            function updateRequirement(selector, isValid) {
                const $element = $(selector);
                const $icon = $element.find('i');
                
                if (isValid) {
                    $icon.removeClass('las la-times').addClass('las la-check');
                    $element.addClass('valid');
                } else {
                    $icon.removeClass('las la-check').addClass('las la-times');
                    $element.removeClass('valid');
                }
            }

            // Form submission test
            $('#accountCreationForm').on('submit', function(e) {
                e.preventDefault();
                
                const formData = new FormData(this);
                console.log('Form Data:');
                for (let [key, value] of formData.entries()) {
                    console.log(key + ': ' + value);
                }
                
                alert('Form submission test successful! Check console for form data.');
            });
        });
    </script>
</body>
</html>
