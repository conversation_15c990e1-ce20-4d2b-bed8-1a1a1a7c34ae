# 🚀 QUICK MT5 REAL-TIME SYNC DEPLOYMENT

## **📁 FILES YOU NEED**

I have created all the necessary files for you:

### **✅ Core Files Created:**
1. **`MT5_RealTime_Sync_Task.xml`** - Windows Task Scheduler configuration
2. **`mt5_sync_runner.bat`** - Sync execution script  
3. **`setup_mt5_realtime_sync.bat`** - Automated setup script
4. **Enhanced sync command** - Already updated in your Laravel app

---

## **🖥️ DEPLOYMENT ON YOUR IRELAND SERVER**

### **Step 1: Upload Files to Server**
Upload these files to your project root directory:
```
C:\Inetpub\vhosts\mybrokerforex.com\mbf.mybrokerforex.com\
├── MT5_RealTime_Sync_Task.xml
├── mt5_sync_runner.bat
├── setup_mt5_realtime_sync.bat
└── (your existing Laravel files)
```

### **Step 2: Run Setup Script**
```batch
# Open Command Prompt as Administrator
cd "C:\Inetpub\vhosts\mybrokerforex.com\mbf.mybrokerforex.com"
setup_mt5_realtime_sync.bat
```

### **Step 3: Import Task in Task Scheduler**
1. **Open Task Scheduler**: Press `Win + R`, type `taskschd.msc`, press Enter
2. **Import Task**: Right-click "Task Scheduler Library" → "Import Task..."
3. **Browse to XML**: Select `MT5_RealTime_Sync_Task.xml` from your project directory
4. **Click OK** to import the task

### **Step 4: Verify Task Settings**
Check that the imported task has these settings:
- **Name**: MT5_RealTime_Sync_Task
- **Trigger**: Every 1 minute
- **Action**: Run `mt5_sync_runner.bat`
- **User**: SYSTEM (highest privileges)
- **Status**: Enabled

### **Step 5: Test the Task**
1. **Manual Test**: Right-click the task → "Run"
2. **Check Logs**: Look at `storage\logs\mt5_sync.log`
3. **Verify Success**: Last Run Result should be "0x0"

---

## **🔧 ALTERNATIVE COMMAND LINE SETUP**

If you prefer command line setup:

```batch
# Create task directly (run as Administrator)
schtasks /create /tn "MT5_RealTime_Sync" /tr "C:\Inetpub\vhosts\mybrokerforex.com\mbf.mybrokerforex.com\mt5_sync_runner.bat" /sc minute /mo 1 /ru SYSTEM /rl HIGHEST /f
```

---

## **🧪 TESTING COMMANDS**

### **Manual Sync Test:**
```batch
cd "C:\Inetpub\vhosts\mybrokerforex.com\mbf.mybrokerforex.com"
"C:\Program Files (x86)\Plesk\Additional\PleskPHP84\php.exe" artisan mt5:sync-users --fast --force --limit=1000
```

### **Dry Run Test:**
```batch
"C:\Program Files (x86)\Plesk\Additional\PleskPHP84\php.exe" artisan mt5:sync-users --dry-run --limit=100
```

### **Check Sync Status:**
```batch
"C:\Program Files (x86)\Plesk\Additional\PleskPHP84\php.exe" artisan tinker --execute="echo 'Last sync: ' . \Cache::get('mt5_sync_stats.last_sync', 'Never');"
```

---

## **📊 MONITORING**

### **Log Files to Monitor:**
- **Sync Log**: `storage\logs\mt5_sync.log`
- **Laravel Log**: `storage\logs\laravel.log`
- **Windows Event Log**: Task Scheduler events

### **Expected Results:**
- **Frequency**: Sync runs every 1 minute
- **Performance**: 1000+ records processed per minute
- **Duplicate Consolidation**: Multiple MT5 accounts per email consolidated
- **Admin Dashboard**: Real-time user updates within 2 minutes

---

## **🔍 TROUBLESHOOTING**

### **Common Issues:**

#### **Task Not Running:**
- Check Task Scheduler service: `net start "Task Scheduler"`
- Verify task is enabled and not expired
- Check user permissions (should be SYSTEM)

#### **Permission Errors:**
- Run Command Prompt as Administrator
- Ensure SYSTEM user has access to project directory
- Check PHP path is correct for Plesk

#### **Sync Errors:**
- Check database connections in `.env`
- Verify MT5 database credentials
- Review Laravel logs for detailed errors

---

## **✅ SUCCESS CHECKLIST**

- [ ] **Files uploaded to server**
- [ ] **Setup script executed successfully**
- [ ] **Task imported in Task Scheduler**
- [ ] **Task running every minute**
- [ ] **Manual sync test completed**
- [ ] **Logs showing successful sync**
- [ ] **Admin dashboard showing consolidated users**
- [ ] **Real-time updates working**

---

## **🎉 COMPLETION**

Once deployed, your MT5 real-time sync will:

✅ **Automatically sync every minute**  
✅ **Consolidate duplicate emails**  
✅ **Update admin dashboard in real-time**  
✅ **Handle 1000+ records per minute**  
✅ **Maintain data integrity**  
✅ **Provide comprehensive logging**  

**Your Ireland server will now have fully automated MT5 database synchronization!** 🚀
