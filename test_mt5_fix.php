<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🧪 TESTING MT5 CURRENCY COLUMN FIX\n";
echo "==================================\n";

try {
    // Test the exact query that was failing
    $users = \App\Models\User::with([
        'mt5Accounts.mt5Data:Login,Group,Balance,Credit'
    ])->take(1)->get();
    
    echo "✅ SUCCESS: No Currency column error!\n";
    echo "✅ Query executed successfully with " . $users->count() . " users\n";
    
    if ($users->count() > 0) {
        $user = $users->first();
        $mt5Count = $user->mt5Accounts->count();
        echo "✅ User has {$mt5Count} MT5 accounts\n";
        
        if ($mt5Count > 0) {
            $account = $user->mt5Accounts->first();
            if ($account->mt5Data) {
                echo "✅ MT5 Data loaded successfully:\n";
                echo "   - Login: {$account->mt5Data->Login}\n";
                echo "   - Group: {$account->mt5Data->Group}\n";
                echo "   - Balance: {$account->mt5Data->Balance}\n";
                echo "   - Credit: {$account->mt5Data->Credit}\n";
            } else {
                echo "⚠️ No MT5 data found for account\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "❌ File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n✅ MT5 CURRENCY COLUMN FIX TEST COMPLETED!\n";
