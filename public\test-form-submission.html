<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Form Submission</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        textarea { height: 200px; }
        .btn { background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #c82333; }
        .result { margin: 20px 0; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .debug { background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test Form Submission</h1>
        <p>This page tests the template update form submission directly</p>
        
        <form id="testForm" method="POST" action="../admin/notification/template/update/1">
            <input type="hidden" name="_token" value="test-token">
            <input type="hidden" name="_method" value="POST">
            
            <div class="form-group">
                <label for="subject">Subject:</label>
                <input type="text" id="subject" name="subject" value="TEST FORM SUBMISSION" required>
            </div>
            
            <div class="form-group">
                <label for="email_body">Email Body:</label>
                <textarea id="email_body" name="email_body" required><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Email</title>
</head>
<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4;">
    <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
        <div style="padding: 20px;">
            <h1 style="color: #dc3545;">Form Submission Test</h1>
            <p>Dear {{fullname}},</p>
            <p>This is a test email template submitted via form at: <span id="timestamp"></span></p>
            <p>Best regards,<br>MBFX Team</p>
        </div>
    </div>
</body>
</html></textarea>
            </div>
            
            <div class="form-group">
                <label for="email_body_final">Email Body Final (Hidden Field):</label>
                <textarea id="email_body_final" name="email_body_final"></textarea>
            </div>
            
            <div class="form-group">
                <label for="original_email_body">Original Email Body (Hidden Field):</label>
                <input type="text" id="original_email_body" name="original_email_body" value="original-content">
            </div>
            
            <div class="form-group">
                <label for="sms_body">SMS Body:</label>
                <textarea id="sms_body" name="sms_body" required>Test SMS: {{fullname}}, this is a test SMS notification.</textarea>
            </div>
            
            <div class="form-group">
                <label>
                    <input type="checkbox" name="email_status" value="1" checked> Email Status Enabled
                </label>
            </div>
            
            <div class="form-group">
                <label>
                    <input type="checkbox" name="sms_status" value="1" checked> SMS Status Enabled
                </label>
            </div>
            
            <button type="button" class="btn" onclick="prepareAndSubmit()">Prepare & Submit Form</button>
            <button type="button" class="btn" onclick="testFormData()">Test Form Data</button>
        </form>
        
        <div id="results" style="display: none;"></div>
        
        <div class="info">
            <h3>How to Use This Test:</h3>
            <ol>
                <li><strong>Update Timestamp:</strong> Click "Test Form Data" to add current timestamp</li>
                <li><strong>Check Form Fields:</strong> Verify all fields are populated correctly</li>
                <li><strong>Submit Form:</strong> Click "Prepare & Submit Form" to test submission</li>
                <li><strong>Check Network Tab:</strong> Monitor the POST request in browser DevTools</li>
                <li><strong>Check Laravel Logs:</strong> Monitor storage/logs/laravel.log for debug output</li>
            </ol>
        </div>
        
        <div class="debug" id="debugOutput"></div>
    </div>

    <script>
        function updateTimestamp() {
            const timestamp = new Date().toISOString();
            const emailBody = document.getElementById('email_body');
            emailBody.value = emailBody.value.replace(
                '<span id="timestamp"></span>', 
                timestamp
            );
            return timestamp;
        }
        
        function prepareAndSubmit() {
            const results = document.getElementById('results');
            const debugOutput = document.getElementById('debugOutput');
            
            results.style.display = 'block';
            results.className = 'result info';
            results.innerHTML = '🔄 Preparing form submission...';
            
            // Update timestamp
            const timestamp = updateTimestamp();
            
            // Simulate Visual Builder content sync
            const emailBody = document.getElementById('email_body').value;
            const emailBodyFinal = document.getElementById('email_body_final');
            
            // Copy content to final field (simulating Visual Builder sync)
            emailBodyFinal.value = emailBody;
            
            // Debug output
            let debug = 'FORM SUBMISSION DEBUG:\n';
            debug += '=====================\n';
            debug += 'Timestamp: ' + timestamp + '\n';
            debug += 'Subject: ' + document.getElementById('subject').value + '\n';
            debug += 'Email Body Length: ' + emailBody.length + '\n';
            debug += 'Email Body Final Length: ' + emailBodyFinal.value.length + '\n';
            debug += 'Original Email Body: ' + document.getElementById('original_email_body').value + '\n';
            debug += 'SMS Body Length: ' + document.getElementById('sms_body').value.length + '\n';
            debug += 'Email Status: ' + (document.querySelector('input[name="email_status"]').checked ? 'Enabled' : 'Disabled') + '\n';
            debug += 'SMS Status: ' + (document.querySelector('input[name="sms_status"]').checked ? 'Enabled' : 'Disabled') + '\n';
            debug += '\nEmail Body Preview:\n' + emailBody.substring(0, 200) + '...\n';
            
            debugOutput.textContent = debug;
            
            results.className = 'result success';
            results.innerHTML = '✅ Form prepared successfully! Check debug output below, then submit manually or check network tab.';
            
            // Note: We don't actually submit because we need proper CSRF token
            console.log('Form prepared for submission. Check debug output.');
        }
        
        function testFormData() {
            const results = document.getElementById('results');
            const debugOutput = document.getElementById('debugOutput');
            
            results.style.display = 'block';
            results.className = 'result info';
            results.innerHTML = '🔄 Testing form data...';
            
            // Update timestamp
            const timestamp = updateTimestamp();
            
            // Test all form fields
            const formData = new FormData(document.getElementById('testForm'));
            
            let debug = 'FORM DATA TEST:\n';
            debug += '===============\n';
            debug += 'Timestamp: ' + timestamp + '\n';
            
            for (let [key, value] of formData.entries()) {
                if (typeof value === 'string' && value.length > 100) {
                    debug += key + ': ' + value.length + ' characters (preview: ' + value.substring(0, 50) + '...)\n';
                } else {
                    debug += key + ': ' + value + '\n';
                }
            }
            
            debugOutput.textContent = debug;
            
            results.className = 'result success';
            results.innerHTML = '✅ Form data test completed! Check debug output below.';
        }
        
        // Auto-update timestamp on page load
        window.onload = function() {
            updateTimestamp();
            console.log('🔧 Test Form Submission page loaded');
        };
    </script>
</body>
</html>
