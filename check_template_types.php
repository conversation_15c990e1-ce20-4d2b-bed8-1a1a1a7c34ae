<?php
/**
 * CHECK ALL TEMPLATE TYPES IN DATABASE
 * This script identifies all template types that need content definitions
 */

// Include Laravel bootstrap
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🔍 CHECKING ALL TEMPLATE TYPES IN DATABASE\n";
echo "==========================================\n\n";

try {
    $templates = \App\Models\NotificationTemplate::orderBy('act')->get();
    
    echo "📊 Found {$templates->count()} templates\n\n";
    
    $templateTypes = [];
    
    foreach ($templates as $template) {
        if (!in_array($template->act, $templateTypes)) {
            $templateTypes[] = $template->act;
        }
        
        $contentLength = strlen($template->email_body);
        $isEmpty = $contentLength === 0;
        
        echo sprintf(
            "%-3d | %-30s | %-40s | %6d chars | %s\n",
            $template->id,
            $template->act,
            substr($template->name, 0, 40),
            $contentLength,
            $isEmpty ? '❌ EMPTY' : '✅ HAS CONTENT'
        );
    }
    
    echo "\n📋 UNIQUE TEMPLATE TYPES FOUND:\n";
    echo "===============================\n";
    
    foreach ($templateTypes as $type) {
        echo "- {$type}\n";
    }
    
    echo "\n📊 SUMMARY:\n";
    echo "===========\n";
    echo "Total templates: {$templates->count()}\n";
    echo "Unique types: " . count($templateTypes) . "\n";
    
    $emptyTemplates = $templates->where('email_body', '')->count();
    echo "Empty templates: {$emptyTemplates}\n";
    
    if ($emptyTemplates > 0) {
        echo "\n🚨 CRITICAL: {$emptyTemplates} templates have empty content!\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

?>
