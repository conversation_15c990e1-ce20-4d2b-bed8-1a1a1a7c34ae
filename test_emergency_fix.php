<?php
/**
 * EMERGENCY FIX TEST
 * This script tests the emergency fix for template corruption
 */

// Include Laravel bootstrap
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🚨 EMERGENCY FIX TEST\n";
echo "=====================\n\n";

try {
    // Test the emergency fix logic
    echo "🔧 Testing emergency fix logic...\n\n";
    
    // Simulate corrupted Visual Builder content (like what's in your logs)
    $corruptedContent = "Balance Added Successfully\r\n\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            Balance Notification\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Balance Added Successfully\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Your account balance has been updated with a new deposit.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},We are pleased to inform you that {{amount}} {{currency}} has been added to your account.Amount: {{amount}} {{currency}}New Balance: {{new_balance}} {{currency}}Transaction ID: {{transaction_id}}Date: {{transaction_date}}The funds are now available in your account.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,\r\n                            MBFX Team\r\n                            \r\n                                If you have any questions, please contact our support team.\r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            \r\n                                Account Settings |\r\n                                Contact Support |\r\n                                Privacy Policy\r\n                            \r\n                            \r\n                                &copy; 2025 MBFX. All rights reserved.\r\n                            \r\n                            \r\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\r\n                                update your preferences.";
    
    // Simulate original professional content (HTML encoded like in form)
    $originalContent = "&lt;!DOCTYPE html&gt;\r\n&lt;html lang=&quot;en&quot;&gt;\r\n&lt;head&gt;\r\n    &lt;meta charset=&quot;UTF-8&quot;&gt;\r\n    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;&gt;\r\n    &lt;title&gt;Balance Added Successfully&lt;/title&gt;\r\n&lt;/head&gt;\r\n&lt;body style=&quot;margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4; line-height: 1.6;&quot;&gt;\r\n    &lt;!-- Full Width Container --&gt;\r\n    &lt;table width=&quot;100%&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; style=&quot;background-color: #f4f4f4;&quot;&gt;\r\n        &lt;tr&gt;\r\n            &lt;td align=&quot;center&quot;&gt;\r\n                &lt;!-- Email Container --&gt;\r\n                &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; style=&quot;background-color: #ffffff; box-shadow: 0 0 10px rgba(0,0,0,0.1);&quot;&gt;\r\n\r\n                    &lt;!-- Header Banner - Full Width --&gt;\r\n                    &lt;tr&gt;\r\n                        &lt;td width=&quot;100%&quot; style=&quot;background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); padding: 20px 0; text-align: center;&quot;&gt;\r\n                            &lt;h1 style=&quot;margin: 0; font-size: 24px; font-weight: bold; color: #ffffff;&quot;&gt;Balance Notification&lt;/h1&gt;\r\n                        &lt;/td&gt;\r\n                    &lt;/tr&gt;\r\n\r\n                    &lt;!-- Logo Section --&gt;\r\n                    &lt;tr&gt;\r\n                        &lt;td style=&quot;background-color: #ffffff; text-align: center; padding: 20px; border-bottom: 2px solid #e9ecef;&quot;&gt;\r\n                            &lt;img src=&quot;https://mbf.mybrokerforex.com/assets/images/logoIcon/logo.png&quot; alt=&quot;MBFX&quot; style=&quot;height: 60px; width: auto; display: block; margin: 0 auto;&quot; onerror=&quot;this.style.display='none'&quot;&gt;\r\n                        &lt;/td&gt;\r\n                    &lt;/tr&gt;\r\n\r\n                    &lt;!-- Title Section --&gt;\r\n                    &lt;tr&gt;\r\n                        &lt;td style=&quot;background-color: #ffffff; padding: 30px 40px 20px; text-align: center;&quot;&gt;\r\n                            &lt;h2 style=&quot;margin: 0; color: #dc3545; font-size: 28px; font-weight: bold;&quot;&gt;Balance Added Successfully&lt;/h2&gt;\r\n                        &lt;/td&gt;\r\n                    &lt;/tr&gt;\r\n\r\n                    &lt;!-- Description Section --&gt;\r\n                    &lt;tr&gt;\r\n                        &lt;td style=&quot;background-color: #ffffff; padding: 0 40px 20px; text-align: center;&quot;&gt;\r\n                            &lt;p style=&quot;margin: 0; color: #6c757d; font-size: 16px;&quot;&gt;Your account balance has been updated with a new deposit.&lt;/p&gt;\r\n                        &lt;/td&gt;\r\n                    &lt;/tr&gt;\r\n\r\n                    &lt;!-- Main Content Section --&gt;\r\n                    &lt;tr&gt;\r\n                        &lt;td style=&quot;background-color: #ffffff; padding: 20px 40px; color: #333333;&quot;&gt;\r\n                            &lt;p&gt;Dear {{fullname}},&lt;/p&gt;&lt;p&gt;We are pleased to inform you that {{amount}} {{currency}} has been added to your account.&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Amount:&lt;/strong&gt; {{amount}} {{currency}}&lt;/li&gt;&lt;li&gt;&lt;strong&gt;New Balance:&lt;/strong&gt; {{new_balance}} {{currency}}&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Transaction ID:&lt;/strong&gt; {{transaction_id}}&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Date:&lt;/strong&gt; {{transaction_date}}&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;The funds are now available in your account.&lt;/p&gt;\r\n                        &lt;/td&gt;\r\n                    &lt;/tr&gt;\r\n\r\n                    &lt;!-- Regards Section --&gt;\r\n                    &lt;tr&gt;\r\n                        &lt;td style=&quot;background-color: #ffffff; padding: 30px 40px; border-top: 1px solid #e9ecef;&quot;&gt;\r\n                            &lt;p style=&quot;margin: 0 0 10px 0; font-size: 16px;&quot;&gt;Best regards,&lt;br&gt;\r\n                            &lt;strong&gt;MBFX Team&lt;/strong&gt;&lt;/p&gt;\r\n                            &lt;p style=&quot;font-size: 12px; color: #6c757d; margin: 15px 0 0 0;&quot;&gt;\r\n                                If you have any questions, please contact our support team.\r\n                            &lt;/p&gt;\r\n                        &lt;/td&gt;\r\n                    &lt;/tr&gt;\r\n\r\n                    &lt;!-- Footer Section - Full Width --&gt;\r\n                    &lt;tr&gt;\r\n                        &lt;td width=&quot;100%&quot; style=&quot;background: linear-gradient(135deg, #343a40 0%, #23272b 100%); color: #ffffff; padding: 30px 40px; text-align: center;&quot;&gt;\r\n                            &lt;p style=&quot;margin: 0 0 10px 0; font-size: 14px;&quot;&gt;&lt;strong&gt;MBFX&lt;/strong&gt; - Professional Trading Platform&lt;/p&gt;\r\n                            &lt;p style=&quot;margin: 0 0 10px 0; font-size: 14px;&quot;&gt;\r\n                                &lt;a href=&quot;{{site_url}}/user/profile/setting&quot; style=&quot;color: #ffffff; text-decoration: none;&quot;&gt;Account Settings&lt;/a&gt; |\r\n                                &lt;a href=&quot;{{site_url}}/contact&quot; style=&quot;color: #ffffff; text-decoration: none;&quot;&gt;Contact Support&lt;/a&gt; |\r\n                                &lt;a href=&quot;{{site_url}}/policy/privacy-policy/99&quot; style=&quot;color: #ffffff; text-decoration: none;&quot;&gt;Privacy Policy&lt;/a&gt;\r\n                            &lt;/p&gt;\r\n                            &lt;p style=&quot;margin: 15px 0 0 0; font-size: 14px;&quot;&gt;\r\n                                &copy; 2025 MBFX. All rights reserved.\r\n                            &lt;/p&gt;\r\n                            &lt;p style=&quot;font-size: 10px; color: #999999; margin: 10px 0 0 0;&quot;&gt;\r\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\r\n                                &lt;a href=&quot;{{site_url}}/user/profile/setting&quot; style=&quot;color: #999999; text-decoration: none;&quot;&gt;update your preferences&lt;/a&gt;.\r\n                            &lt;/p&gt;\r\n                        &lt;/td&gt;\r\n                    &lt;/tr&gt;\r\n\r\n                &lt;/table&gt;\r\n            &lt;/td&gt;\r\n        &lt;/tr&gt;\r\n    &lt;/table&gt;\r\n&lt;/body&gt;\r\n&lt;/html&gt;";
    
    echo "📊 Test Data:\n";
    echo "- Corrupted content length: " . strlen($corruptedContent) . "\n";
    echo "- Original content length: " . strlen($originalContent) . "\n";
    echo "- Corrupted has DOCTYPE: " . (str_contains($corruptedContent, '<!DOCTYPE') ? 'YES' : 'NO') . "\n";
    echo "- Original has DOCTYPE: " . (str_contains($originalContent, '&lt;!DOCTYPE') ? 'YES' : 'NO') . "\n\n";
    
    // Test the emergency fix logic
    echo "🔧 Testing emergency fix logic...\n";
    
    $isVisualBuilderCorrupted = false;
    if (!empty($corruptedContent)) {
        $cleanContent = strip_tags($corruptedContent);
        if (strlen($cleanContent) < 500 || !str_contains($corruptedContent, '<!DOCTYPE')) {
            $isVisualBuilderCorrupted = true;
            echo "✅ DETECTED: Visual Builder content is corrupted\n";
        }
    }
    
    $finalContent = '';
    if ($isVisualBuilderCorrupted && !empty($originalContent)) {
        // Use original content instead of corrupted Visual Builder content
        $finalContent = html_entity_decode($originalContent);
        echo "✅ EMERGENCY FIX: Using original content\n";
        echo "✅ Final content length: " . strlen($finalContent) . "\n";
        echo "✅ Final has DOCTYPE: " . (str_contains($finalContent, '<!DOCTYPE') ? 'YES' : 'NO') . "\n";
        echo "✅ Final has table structure: " . (str_contains($finalContent, '<table') ? 'YES' : 'NO') . "\n";
    } else {
        echo "❌ Emergency fix not triggered\n";
    }
    
    echo "\n📋 EXPECTED RESULTS:\n";
    echo "====================\n";
    echo "When you upload the updated NotificationController.php:\n";
    echo "1. Visual Builder corruption will be detected automatically\n";
    echo "2. Original professional content will be used instead\n";
    echo "3. Template will maintain professional table-based structure\n";
    echo "4. No more basic wrapper corruption\n";
    echo "5. Changes will be preserved correctly\n\n";
    
    echo "📤 UPLOAD INSTRUCTIONS:\n";
    echo "========================\n";
    echo "1. Upload the updated app/Http/Controllers/Admin/NotificationController.php\n";
    echo "2. Clear Laravel caches: php artisan cache:clear\n";
    echo "3. Test template editing immediately\n";
    echo "4. Check logs for 'EMERGENCY' messages\n\n";
    
    echo "✅ EMERGENCY FIX TEST COMPLETED\n";
    
} catch (\Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
}
?>
