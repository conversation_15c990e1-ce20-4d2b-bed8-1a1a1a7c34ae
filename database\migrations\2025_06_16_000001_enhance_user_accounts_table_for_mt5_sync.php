<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Enhance user_accounts table for MT5 sync with account type tracking
     */
    public function up()
    {
        Schema::table('user_accounts', function (Blueprint $table) {
            // Add missing fields for enhanced MT5 sync
            if (!Schema::hasColumn('user_accounts', 'Investor_Password')) {
                $table->string('Investor_Password')->nullable()->after('Master_Password');
            }

            if (!Schema::hasColumn('user_accounts', 'Phone_Password')) {
                $table->string('Phone_Password')->nullable()->after('Investor_Password');
            }

            if (!Schema::hasColumn('user_accounts', 'Group_Name')) {
                $table->string('Group_Name')->nullable()->after('Phone_Password');
            }

            if (!Schema::hasColumn('user_accounts', 'Account_Type')) {
                $table->string('Account_Type')->default('real')->after('Group_Name');
            }

            if (!Schema::hasColumn('user_accounts', 'Leverage')) {
                $table->integer('Leverage')->nullable()->after('Account_Type');
            }

            if (!Schema::hasColumn('user_accounts', 'Balance')) {
                $table->decimal('Balance', 15, 2)->default(0)->after('Leverage');
            }

            if (!Schema::hasColumn('user_accounts', 'Currency')) {
                $table->string('Currency', 3)->default('USD')->after('Balance');
            }
            
            // Add indexes for faster queries (simplified to avoid key length issues)
            if (!Schema::hasIndex('user_accounts', 'User_Id')) {
                $table->index('User_Id', 'idx_user_id');
            }

            if (!Schema::hasIndex('user_accounts', 'Account_Type')) {
                $table->index('Account_Type', 'idx_account_type');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::table('user_accounts', function (Blueprint $table) {
            // Remove added columns
            $table->dropColumn([
                'Investor_Password',
                'Phone_Password',
                'Group_Name',
                'Account_Type',
                'Leverage',
                'Balance',
                'Currency'
            ]);
            
            // Remove indexes
            $table->dropIndex('idx_user_id');
            $table->dropIndex('idx_account_type');
        });
    }
};
