<?php

/**
 * Check MT5 group names to understand filtering logic
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== MT5 Group Analysis ===\n\n";

// Get unique group names from MT5 database
$groups = DB::connection('mbf-dbmt5')->table('mt5_users')
    ->select('Group')
    ->distinct()
    ->orderBy('Group')
    ->get();

echo "All MT5 Groups in database:\n";
echo "==========================\n";

foreach ($groups as $group) {
    $groupName = $group->Group;
    
    // Test current filtering logic
    $isRealCurrent = stripos($groupName, 'real') !== false;
    $isDemoCurrent = stripos($groupName, 'demo') !== false;
    
    // Test improved filtering logic
    $isRealImproved = stripos($groupName, 'real') !== false || 
                     stripos($groupName, 'Real') !== false ||
                     (stripos($groupName, 'demo') === false && stripos($groupName, 'Demo') === false);
    
    $accountCount = DB::connection('mbf-dbmt5')->table('mt5_users')
        ->where('Group', $groupName)
        ->count();
    
    $totalBalance = DB::connection('mbf-dbmt5')->table('mt5_users')
        ->where('Group', $groupName)
        ->sum('Balance');
    
    echo "Group: {$groupName}\n";
    echo "  Accounts: {$accountCount} | Total Balance: \${$totalBalance}\n";
    echo "  Current Logic - Real: " . ($isRealCurrent ? 'YES' : 'NO') . " | Demo: " . ($isDemoCurrent ? 'YES' : 'NO') . "\n";
    echo "  Improved Logic - Real: " . ($isRealImproved ? 'YES' : 'NO') . "\n";
    echo "\n";
}

// Find users with real accounts (using improved logic)
echo "Users with Real Accounts (using improved logic):\n";
echo "===============================================\n";

$realGroups = $groups->filter(function($group) {
    $groupName = $group->Group;
    return stripos($groupName, 'real') !== false || 
           stripos($groupName, 'Real') !== false ||
           (stripos($groupName, 'demo') === false && stripos($groupName, 'Demo') === false);
})->pluck('Group');

if ($realGroups->count() > 0) {
    $usersWithRealAccounts = DB::connection('mbf-dbmt5')->table('mt5_users')
        ->whereIn('Group', $realGroups->toArray())
        ->where('Balance', '>', 0)
        ->take(10)
        ->get();
    
    foreach ($usersWithRealAccounts as $mt5User) {
        echo "Email: {$mt5User->Email} | Login: {$mt5User->Login} | Group: {$mt5User->Group} | Balance: \${$mt5User->Balance}\n";
        
        // Check if user exists in Laravel
        $user = \App\Models\User::where('email', $mt5User->Email)->first();
        if ($user) {
            $walletCount = $user->wallets()->count();
            echo "  ✅ Laravel User ID: {$user->id} | Wallets: {$walletCount}\n";
            
            if ($walletCount > 0) {
                echo "  🎯 Perfect for testing!\n";
            }
        } else {
            echo "  ❌ No Laravel user found\n";
        }
        echo "\n";
    }
} else {
    echo "No real account groups found with improved logic.\n";
}

echo "=== Analysis Complete ===\n";
