<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Http\Controllers\Admin\IbDataController;
use App\Http\Controllers\Admin\CommissionController;
use App\Http\Controllers\User\PartnershipController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

echo "🔍 COMPREHENSIVE IB SYSTEM TESTING\n";
echo "=====================================\n";

$startTime = microtime(true);
$totalTests = 0;
$passedTests = 0;
$failedTests = 0;

// Test 1: IB Status Standardization Verification
echo "\n📊 Test 1: IB Status Standardization\n";
$totalTests++;
try {
    $conflictingUsers = \App\Models\User::whereNotNull('ib_status')
        ->where(function($query) {
            $query->where(function($q) {
                $q->where('partner', 1)->where('ib_status', '!=', 'approved');
            })->orWhere(function($q) {
                $q->where('partner', 2)->where('ib_status', '!=', 'pending');
            })->orWhere(function($q) {
                $q->where('partner', 3)->where('ib_status', '!=', 'rejected');
            });
        })
        ->count();

    if ($conflictingUsers === 0) {
        echo "✅ IB Status Standardization: PASSED (No conflicts found)\n";
        $passedTests++;
    } else {
        echo "❌ IB Status Standardization: FAILED ({$conflictingUsers} conflicts found)\n";
        $failedTests++;
    }
} catch (Exception $e) {
    echo "❌ IB Status Standardization: ERROR - " . $e->getMessage() . "\n";
    $failedTests++;
}

// Test 2: Admin IB Pages Performance
echo "\n📊 Test 2: Admin IB Pages Performance\n";
$totalTests++;
try {
    $controller = new IbDataController();
    $request = new Request();
    
    $start = microtime(true);
    $response = $controller->activeAccountsByIbStatus($request);
    $end = microtime(true);
    $time = round(($end - $start) * 1000, 2);
    
    if ($time < 3000) { // Less than 3 seconds
        echo "✅ Admin IB Pages Performance: PASSED ({$time}ms)\n";
        $passedTests++;
    } else {
        echo "❌ Admin IB Pages Performance: FAILED ({$time}ms - too slow)\n";
        $failedTests++;
    }
} catch (Exception $e) {
    echo "❌ Admin IB Pages Performance: ERROR - " . $e->getMessage() . "\n";
    $failedTests++;
}

// Test 3: Commission System
echo "\n📊 Test 3: Commission System\n";
$totalTests++;
try {
    // Check if commission table exists
    $tableExists = \Schema::hasTable('ib_commissions');
    
    if ($tableExists) {
        $commissionCount = \DB::table('ib_commissions')->count();
        echo "✅ Commission System: PASSED (Table exists, {$commissionCount} records)\n";
        $passedTests++;
    } else {
        echo "❌ Commission System: FAILED (Table does not exist)\n";
        $failedTests++;
    }
} catch (Exception $e) {
    echo "❌ Commission System: ERROR - " . $e->getMessage() . "\n";
    $failedTests++;
}

// Test 4: Network Visualization Performance
echo "\n📊 Test 4: Network Visualization Performance\n";
$totalTests++;
try {
    // Test with a real IB user
    $testUser = \App\Models\User::where('partner', 1)->first();
    
    if ($testUser) {
        Auth::login($testUser);
        
        $controller = new PartnershipController();
        $request = new Request();
        
        $start = microtime(true);
        $response = $controller->network($request);
        $end = microtime(true);
        $time = round(($end - $start) * 1000, 2);
        
        if ($time < 3000) { // Less than 3 seconds
            echo "✅ Network Visualization: PASSED ({$time}ms)\n";
            $passedTests++;
        } else {
            echo "❌ Network Visualization: FAILED ({$time}ms - too slow)\n";
            $failedTests++;
        }
    } else {
        echo "⚠️ Network Visualization: SKIPPED (No IB users found)\n";
    }
} catch (Exception $e) {
    echo "❌ Network Visualization: ERROR - " . $e->getMessage() . "\n";
    $failedTests++;
}

// Test 5: MT5 Integration
echo "\n📊 Test 5: MT5 Integration\n";
$totalTests++;
try {
    // Test MT5 database connection
    $mt5Users = \DB::connection('mbf-dbmt5')
        ->table('mt5_users')
        ->limit(1)
        ->get();
    
    if ($mt5Users->count() > 0) {
        echo "✅ MT5 Integration: PASSED (Connection successful)\n";
        $passedTests++;
    } else {
        echo "❌ MT5 Integration: FAILED (No data found)\n";
        $failedTests++;
    }
} catch (Exception $e) {
    echo "❌ MT5 Integration: ERROR - " . $e->getMessage() . "\n";
    $failedTests++;
}

// Test 6: IB Hierarchy System
echo "\n📊 Test 6: IB Hierarchy System\n";
$totalTests++;
try {
    // Test referral relationships
    $usersWithReferrals = \App\Models\User::whereNotNull('ref_by')->count();
    $ibsWithReferrals = \App\Models\User::where('partner', 1)
        ->whereHas('referrals')
        ->count();
    
    echo "✅ IB Hierarchy System: PASSED ({$usersWithReferrals} users with referrals, {$ibsWithReferrals} IBs with referrals)\n";
    $passedTests++;
} catch (Exception $e) {
    echo "❌ IB Hierarchy System: ERROR - " . $e->getMessage() . "\n";
    $failedTests++;
}

// Test 7: Database Performance
echo "\n📊 Test 7: Database Performance\n";
$totalTests++;
try {
    // Test complex query performance
    $start = microtime(true);
    $result = \App\Models\User::with(['referrals', 'referrer'])
        ->where('partner', 1)
        ->limit(10)
        ->get();
    $end = microtime(true);
    $time = round(($end - $start) * 1000, 2);
    
    if ($time < 1000) { // Less than 1 second
        echo "✅ Database Performance: PASSED ({$time}ms)\n";
        $passedTests++;
    } else {
        echo "❌ Database Performance: FAILED ({$time}ms - too slow)\n";
        $failedTests++;
    }
} catch (Exception $e) {
    echo "❌ Database Performance: ERROR - " . $e->getMessage() . "\n";
    $failedTests++;
}

// Test 8: IB Statistics
echo "\n📊 Test 8: IB Statistics\n";
$totalTests++;
try {
    $stats = [
        'total_users' => \App\Models\User::count(),
        'approved_ibs' => \App\Models\User::where('partner', 1)->count(),
        'pending_ibs' => \App\Models\User::where('partner', 2)->count(),
        'rejected_ibs' => \App\Models\User::where('partner', 3)->count(),
        'users_with_mt5' => \App\Models\User::whereNotNull('mt5_login')->count()
    ];
    
    echo "✅ IB Statistics: PASSED\n";
    echo "   - Total Users: {$stats['total_users']}\n";
    echo "   - Approved IBs: {$stats['approved_ibs']}\n";
    echo "   - Pending IBs: {$stats['pending_ibs']}\n";
    echo "   - Rejected IBs: {$stats['rejected_ibs']}\n";
    echo "   - Users with MT5: {$stats['users_with_mt5']}\n";
    $passedTests++;
} catch (Exception $e) {
    echo "❌ IB Statistics: ERROR - " . $e->getMessage() . "\n";
    $failedTests++;
}

// Final Results
$endTime = microtime(true);
$totalTime = round(($endTime - $startTime) * 1000, 2);

echo "\n🎉 COMPREHENSIVE IB SYSTEM TEST RESULTS\n";
echo "=====================================\n";
echo "📊 Total Tests: {$totalTests}\n";
echo "✅ Passed: {$passedTests}\n";
echo "❌ Failed: {$failedTests}\n";
echo "⏱️ Total Time: {$totalTime}ms\n";

$successRate = round(($passedTests / $totalTests) * 100, 1);
echo "📈 Success Rate: {$successRate}%\n";

if ($successRate >= 90) {
    echo "\n🎉 EXCELLENT! IB System is performing well!\n";
} elseif ($successRate >= 75) {
    echo "\n✅ GOOD! IB System is mostly functional with minor issues.\n";
} elseif ($successRate >= 50) {
    echo "\n⚠️ WARNING! IB System has significant issues that need attention.\n";
} else {
    echo "\n❌ CRITICAL! IB System has major problems that require immediate fixing.\n";
}

echo "\n📋 NEXT STEPS:\n";
echo "1. Review any failed tests above\n";
echo "2. Test admin IB pages in browser\n";
echo "3. Test user network pages in browser\n";
echo "4. Verify commission sync functionality\n";
echo "5. Test with real MT5 data\n";
echo "\n✅ IB System comprehensive testing completed!\n";
