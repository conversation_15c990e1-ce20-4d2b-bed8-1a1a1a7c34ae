<?php

require 'vendor/autoload.php';
$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 TESTING ADMIN USER LIST CONTROLLER\n";
echo "=====================================\n\n";

// Simulate the admin controller query
$users = \App\Models\User::select([
    'users.*',
    \DB::raw('CASE WHEN mt5_login IS NOT NULL THEN 1 ELSE 0 END as has_mt5'),
    \DB::raw('CASE WHEN mt5_synced_at IS NOT NULL THEN 1 ELSE 0 END as is_synced'),
    \DB::raw('CASE WHEN is_ib_account = 1 THEN 1 ELSE 0 END as is_ib'),
    \DB::raw('(SELECT GROUP_CONCAT(DISTINCT CONCAT(u2.mt5_login, ":", COALESCE(u2.mt5_group, "Unknown"), ":", COALESCE(u2.mt5_balance, 0), ":", COALESCE(u2.mt5_credit, 0)) ORDER BY u2.mt5_login SEPARATOR "|") FROM users u2 WHERE u2.email = users.email AND u2.mt5_login IS NOT NULL AND u2.mt5_login != "") as all_mt5_accounts_detailed'),
    \DB::raw('(SELECT COUNT(*) FROM users u3 WHERE u3.email = users.email) as total_accounts_for_email')
])
// PERMANENT DUPLICATE CONSOLIDATION: Only show the most recent account per email
->whereIn('users.id', function($query) {
    $query->select(\DB::raw('MAX(id)'))
          ->from('users')
          ->whereNotNull('email')
          ->where('email', '!=', '')
          ->groupBy('email');
})
// CRITICAL FIX: Exclude test/dummy users from main list (show real users first)
->where('users.email', 'not like', '%@exness.%')
->where('users.email', 'not like', '%test%')
->where('users.email', 'not like', '%dummy%')
->orderBy('users.created_at', 'desc')
->orderBy('users.id', 'desc')
->limit(10)
->get();

echo "Admin User List (after fixes):\n";
foreach($users as $user) {
    echo $user->id . ' - ' . $user->email . ' - ' . $user->created_at . ' - MT5: ' . $user->mt5_login . "\n";
}

echo "\nTotal users in filtered list: " . $users->count() . "\n";

// Test search functionality
echo "\n🔍 TESTING SEARCH FUNCTIONALITY\n";
echo "-------------------------------\n";

$searchTerms = ['878045', 'hammedali', 'mbfx'];

foreach($searchTerms as $search) {
    $searchResults = \App\Models\User::where('users.email', 'not like', '%@exness.%')
        ->where('users.email', 'not like', '%test%')
        ->where('users.email', 'not like', '%dummy%')
        ->where(function($q) use ($search) {
            $q->where('users.username', 'like', "%{$search}%")
              ->orWhere('users.email', 'like', "%{$search}%")
              ->orWhere('users.firstname', 'like', "%{$search}%")
              ->orWhere('users.lastname', 'like', "%{$search}%")
              ->orWhere('users.mt5_login', 'like', "%{$search}%")
              ->orWhere('users.mobile', 'like', "%{$search}%")
              ->orWhere('users.country_code', 'like', "%{$search}%")
              ->orWhere('users.all_mt5_accounts', 'like', "%{$search}%");
        })
        ->limit(5)
        ->get();
    
    echo "Search for '$search': " . $searchResults->count() . " results\n";
    foreach($searchResults as $result) {
        echo "   - {$result->email} (MT5: {$result->mt5_login})\n";
    }
}

echo "\n✅ ADMIN USER LIST TESTING COMPLETED!\n";
