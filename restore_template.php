<?php
/**
 * EMERGENCY TEMPLATE RESTORATION SCRIPT
 * This script restores the broken email template to its original professional structure
 */

// Include Laravel bootstrap
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🚨 EMERGENCY TEMPLATE RESTORATION\n";
echo "==================================\n\n";

try {
    // Get the broken template (ID 31 from logs)
    $template = \App\Models\NotificationTemplate::find(31);
    if (!$template) {
        echo "❌ Template ID 31 not found\n";
        exit;
    }
    
    echo "✅ Found template ID: {$template->id}\n";
    echo "✅ Current subject: {$template->subj}\n";
    echo "✅ Current email body length: " . strlen($template->email_body) . "\n";
    echo "✅ Current content preview: " . substr(strip_tags($template->email_body), 0, 100) . "...\n\n";
    
    // Check if template is corrupted (basic wrapper instead of professional structure)
    $isCorrupted = str_contains($template->email_body, '<div class="email-container">') && 
                   !str_contains($template->email_body, '<table');
    
    if ($isCorrupted) {
        echo "🚨 CORRUPTION DETECTED: Template has basic wrapper instead of professional structure\n";
        echo "🔄 Restoring professional email template...\n\n";
        
        // Restore the professional email template structure
        $professionalTemplate = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Notification</title>
</head>
<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4; line-height: 1.6;">
    <!-- Full Width Container -->
    <table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color: #f4f4f4;">
        <tr>
            <td align="center">
                <!-- Email Container -->
                <table width="600" cellpadding="0" cellspacing="0" border="0" style="background-color: #ffffff; box-shadow: 0 0 10px rgba(0,0,0,0.1);">

                    <!-- Header Banner - Full Width -->
                    <tr>
                        <td width="100%" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); padding: 20px 0; text-align: center;">
                            <h1 style="margin: 0; font-size: 24px; font-weight: bold; color: #ffffff;">Account Notification</h1>
                        </td>
                    </tr>

                    <!-- Logo Section -->
                    <tr>
                        <td style="background-color: #ffffff; text-align: center; padding: 20px; border-bottom: 2px solid #e9ecef;">
                            <img src="https://mbf.mybrokerforex.com/assets/images/logoIcon/logo.png" alt="MBFX" style="height: 60px; width: auto; display: block; margin: 0 auto;" onerror="this.style.display=\'none\'">
                        </td>
                    </tr>

                    <!-- Title Section -->
                    <tr>
                        <td style="background-color: #ffffff; padding: 30px 40px 20px; text-align: center;">
                            <h2 style="margin: 0; color: #dc3545; font-size: 28px; font-weight: bold;">Account Notification</h2>
                        </td>
                    </tr>

                    <!-- Description Section -->
                    <tr>
                        <td style="background-color: #ffffff; padding: 0 40px 20px; text-align: center;">
                            <p style="margin: 0; color: #6c757d; font-size: 16px;">This is an important notification regarding your account.</p>
                        </td>
                    </tr>

                    <!-- Main Content Section -->
                    <tr>
                        <td style="background-color: #ffffff; padding: 20px 40px; color: #333333;">
                            <p>Dear Admin,</p><p>A new IB (Introducing Broker) application has been submitted and requires review.</p><ul><li><strong>Applicant:</strong> {{fullname}} ({{username}})</li><li><strong>Email:</strong> {{email}}</li><li><strong>Application Date:</strong> {{application_date}}</li><li><strong>Requested IB Type:</strong> {{requested_ib_type}}</li></ul><p>Please review the application in the admin panel.</p><a href="{{admin_url}}/admin/users/ib/pending" class="btn">Review IB Application</a>
                        </td>
                    </tr>

                    <!-- Regards Section -->
                    <tr>
                        <td style="background-color: #ffffff; padding: 30px 40px; border-top: 1px solid #e9ecef;">
                            <p style="margin: 0 0 10px 0; font-size: 16px;">Best regards,<br>
                            <strong>MBFX Team</strong></p>
                            <p style="font-size: 12px; color: #6c757d; margin: 15px 0 0 0;">
                                If you have any questions, please contact our support team.
                            </p>
                        </td>
                    </tr>

                    <!-- Footer Section - Full Width -->
                    <tr>
                        <td width="100%" style="background: linear-gradient(135deg, #343a40 0%, #23272b 100%); color: #ffffff; padding: 30px 40px; text-align: center;">
                            <p style="margin: 0 0 10px 0; font-size: 14px;"><strong>MBFX</strong> - Professional Trading Platform</p>
                            <p style="margin: 0 0 10px 0; font-size: 14px;">
                                <a href="{{site_url}}/user/profile/setting" style="color: #ffffff; text-decoration: none;">Account Settings</a> |
                                <a href="{{site_url}}/contact" style="color: #ffffff; text-decoration: none;">Contact Support</a> |
                                <a href="{{site_url}}/policy/privacy-policy/99" style="color: #ffffff; text-decoration: none;">Privacy Policy</a>
                            </p>
                            <p style="margin: 15px 0 0 0; font-size: 14px;">
                                &copy; 2025 MBFX. All rights reserved.
                            </p>
                            <p style="font-size: 10px; color: #999999; margin: 10px 0 0 0;">
                                This email was sent to {{email}}. If you no longer wish to receive these emails,
                                <a href="{{site_url}}/user/profile/setting" style="color: #999999; text-decoration: none;">update your preferences</a>.
                            </p>
                        </td>
                    </tr>

                </table>
            </td>
        </tr>
    </table>
</body>
</html>';

        // Update the template
        $template->email_body = $professionalTemplate;
        $saveResult = $template->save();
        
        if ($saveResult) {
            echo "✅ RESTORATION SUCCESSFUL!\n";
            echo "✅ Template restored to professional structure\n";
            echo "✅ New content length: " . strlen($template->email_body) . " characters\n";
            echo "✅ Professional structure confirmed: " . (str_contains($template->email_body, '<table') ? 'YES' : 'NO') . "\n";
            echo "✅ DOCTYPE present: " . (str_contains($template->email_body, '<!DOCTYPE') ? 'YES' : 'NO') . "\n\n";
            
            // Verify by reloading
            $template->refresh();
            echo "✅ VERIFICATION: Template reloaded from database\n";
            echo "✅ Verified content length: " . strlen($template->email_body) . " characters\n";
            echo "✅ Verified structure: " . (str_contains($template->email_body, '<table') ? 'PROFESSIONAL' : 'BASIC') . "\n";
        } else {
            echo "❌ RESTORATION FAILED: Could not save template\n";
        }
    } else {
        echo "✅ Template appears to be in good condition\n";
        echo "✅ Professional structure: " . (str_contains($template->email_body, '<table') ? 'YES' : 'NO') . "\n";
        echo "✅ DOCTYPE present: " . (str_contains($template->email_body, '<!DOCTYPE') ? 'YES' : 'NO') . "\n";
    }
    
    echo "\n📋 NEXT STEPS:\n";
    echo "==============\n";
    echo "1. Upload the fixed JavaScript files to live server\n";
    echo "2. Clear all Laravel caches\n";
    echo "3. Test template editing with the restored template\n";
    echo "4. The Visual Builder should now preserve the professional structure\n";
    
} catch (\Exception $e) {
    echo "❌ CRITICAL ERROR: " . $e->getMessage() . "\n";
    echo "❌ File: " . $e->getFile() . "\n";
    echo "❌ Line: " . $e->getLine() . "\n";
}

echo "\n✅ RESTORATION SCRIPT COMPLETED\n";
?>
