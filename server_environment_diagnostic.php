<?php
/**
 * Server Environment Diagnostic Tool
 * Compare XAMPP vs Windows Server 2022/Plesk Environment
 */

echo "🔍 SERVER ENVIRONMENT DIAGNOSTIC REPORT\n";
echo "=====================================\n\n";

// 1. PHP Version and Configuration
echo "📋 PHP ENVIRONMENT:\n";
echo "- PHP Version: " . PHP_VERSION . "\n";
echo "- PHP SAPI: " . php_sapi_name() . "\n";
echo "- Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "\n";
echo "- Operating System: " . PHP_OS . "\n";
echo "- Architecture: " . (PHP_INT_SIZE * 8) . "-bit\n\n";

// 2. Critical PHP Settings
echo "⚙️ CRITICAL PHP SETTINGS:\n";
$criticalSettings = [
    'memory_limit',
    'max_execution_time',
    'max_input_time',
    'post_max_size',
    'upload_max_filesize',
    'max_input_vars',
    'max_input_nesting_level',
    'default_charset',
    'mbstring.internal_encoding',
    'mbstring.http_output',
    'output_buffering',
    'zlib.output_compression'
];

foreach ($criticalSettings as $setting) {
    $value = ini_get($setting);
    echo "- {$setting}: " . ($value === false ? 'Not Set' : $value) . "\n";
}

// 3. Request Processing Settings
echo "\n📡 REQUEST PROCESSING:\n";
echo "- Request Method: " . ($_SERVER['REQUEST_METHOD'] ?? 'CLI') . "\n";
echo "- Content Type: " . ($_SERVER['CONTENT_TYPE'] ?? 'Not Set') . "\n";
echo "- Content Length: " . ($_SERVER['CONTENT_LENGTH'] ?? 'Not Set') . "\n";
echo "- HTTP Accept Encoding: " . ($_SERVER['HTTP_ACCEPT_ENCODING'] ?? 'Not Set') . "\n";

// 4. Character Encoding
echo "\n🔤 CHARACTER ENCODING:\n";
echo "- Default Charset: " . ini_get('default_charset') . "\n";
echo "- Internal Encoding: " . (function_exists('mb_internal_encoding') ? mb_internal_encoding() : 'mbstring not available') . "\n";
echo "- Locale: " . setlocale(LC_ALL, 0) . "\n";

// 5. Database Connection (if available)
if (file_exists('.env')) {
    echo "\n💾 DATABASE ENVIRONMENT:\n";
    $envContent = file_get_contents('.env');
    if (preg_match('/DB_CHARSET=(.+)/', $envContent, $matches)) {
        echo "- DB Charset: " . trim($matches[1]) . "\n";
    }
    if (preg_match('/DB_COLLATION=(.+)/', $envContent, $matches)) {
        echo "- DB Collation: " . trim($matches[1]) . "\n";
    }
}

// 6. File System
echo "\n📁 FILE SYSTEM:\n";
echo "- Current Directory: " . getcwd() . "\n";
echo "- Document Root: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Not Set') . "\n";
echo "- Script Filename: " . ($_SERVER['SCRIPT_FILENAME'] ?? 'Not Set') . "\n";

// 7. Test Form Data Processing
echo "\n🧪 FORM DATA PROCESSING TEST:\n";

// Simulate the problematic form data
$testData = [
    'email_body' => '<p>Test content for email body</p>',
    'email_body_final' => '<p>Test content for email body final</p>',
    'subject' => 'Test Subject'
];

echo "- Test Data Created: " . json_encode($testData) . "\n";

// Test different input methods
$_POST = $testData;
$_REQUEST = $testData;

echo "- POST Data: " . json_encode($_POST) . "\n";
echo "- REQUEST Data: " . json_encode($_REQUEST) . "\n";

// Test input filtering
if (function_exists('filter_input')) {
    $filteredBody = filter_input(INPUT_POST, 'email_body', FILTER_UNSAFE_RAW);
    echo "- Filtered Input: " . ($filteredBody ? 'Working' : 'Not Working') . "\n";
}

// 8. JavaScript/Asset Path Testing
echo "\n🌐 ASSET PATH TESTING:\n";
$jsPath = 'assets/admin/js/simple-email-editor.js';
$cssPath = 'assets/admin/css/simple-email-editor.css';

echo "- JS File Exists: " . (file_exists($jsPath) ? 'YES' : 'NO') . "\n";
echo "- CSS File Exists: " . (file_exists($cssPath) ? 'YES' : 'NO') . "\n";

if (file_exists($jsPath)) {
    echo "- JS File Size: " . filesize($jsPath) . " bytes\n";
    echo "- JS File Modified: " . date('Y-m-d H:i:s', filemtime($jsPath)) . "\n";
}

// 9. Laravel Environment
echo "\n🚀 LARAVEL ENVIRONMENT:\n";
if (file_exists('artisan')) {
    echo "- Laravel Detected: YES\n";
    if (file_exists('bootstrap/app.php')) {
        echo "- Bootstrap File: EXISTS\n";
    }
    if (file_exists('config/app.php')) {
        echo "- Config File: EXISTS\n";
    }
} else {
    echo "- Laravel Detected: NO\n";
}

// 10. Recommendations
echo "\n💡 RECOMMENDATIONS FOR WINDOWS SERVER 2022/PLESK:\n";
echo "1. Check PHP version compatibility (should be 8.1+ for Laravel 10)\n";
echo "2. Verify max_input_vars is sufficient (recommended: 3000+)\n";
echo "3. Ensure proper character encoding (UTF-8)\n";
echo "4. Check if output buffering is causing issues\n";
echo "5. Verify JavaScript files are loading correctly\n";
echo "6. Test form data processing with different content types\n";

echo "\n✅ DIAGNOSTIC COMPLETE\n";
echo "Save this output and compare between XAMPP and live server.\n";
?>
