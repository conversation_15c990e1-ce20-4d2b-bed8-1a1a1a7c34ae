@extends($activeTemplate . 'layouts.master')
@section('content')
    <div class="row justify-content-center gy-4">
        <div class="col-lg-8">
            <div class="card custom--card">
                <div class="card-header">
                    <h5 class="card-title">@lang('Internal Transfer')</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('user.transfer.submit') }}" method="POST" id="transferForm">
                        @csrf
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">@lang('From Account') <span class="text--danger">*</span></label>
                                    <select name="from_account" class="form-control form--control" id="fromAccount" required>
                                        <option value="">@lang('Select Source Account')</option>
                                        @foreach($mt5Accounts as $account)
                                            <option value="{{ $account->Login }}"
                                                    data-balance="{{ $account->Balance }}"
                                                    data-group="{{ $account->Group }}"
                                                    data-type="{{ $account->AccountType }}">
                                                {{ $account->DisplayFormat }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">@lang('To Account') <span class="text--danger">*</span></label>
                                    <select name="to_account" class="form-control form--control" id="toAccount" required>
                                        <option value="">@lang('Select Destination Account')</option>
                                        @foreach($mt5Accounts as $account)
                                            <option value="{{ $account->Login }}"
                                                    data-balance="{{ $account->Balance }}"
                                                    data-group="{{ $account->Group }}"
                                                    data-type="{{ $account->AccountType }}">
                                                {{ $account->DisplayFormat }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">@lang('Transfer Amount') <span class="text--danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number" step="0.01" min="1" name="amount" class="form-control form--control" 
                                               id="transferAmount" placeholder="0.00" required>
                                        <span class="input-group-text">USD</span>
                                    </div>
                                    <small class="text-muted">@lang('Available Balance'): <span id="availableBalance">$0.00</span></small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">@lang('Transfer Summary')</label>
                                    <div class="transfer-summary p-3 bg-light rounded">
                                        <div class="d-flex justify-content-between">
                                            <span>@lang('Transfer Amount'):</span>
                                            <span id="summaryAmount">$0.00</span>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <span>@lang('Transfer Fee'):</span>
                                            <span class="text-success">@lang('FREE')</span>
                                        </div>
                                        <hr>
                                        <div class="d-flex justify-content-between fw-bold">
                                            <span>@lang('Total Amount'):</span>
                                            <span id="totalAmount">$0.00</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn--primary btn--lg w-100" id="transferBtn" disabled>
                                <i class="las la-exchange-alt"></i> @lang('Transfer Now')
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Transfer Information Section -->
            <div class="card custom--card">
                <div class="card-header">
                    <h5 class="card-title">@lang('Transfer Information')</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="las la-check-circle text-success"></i>
                            @lang('Instant transfers between your accounts')
                        </li>
                        <li class="mb-2">
                            <i class="las la-check-circle text-success"></i>
                            @lang('No transfer fees')
                        </li>
                        <li class="mb-2">
                            <i class="las la-check-circle text-success"></i>
                            @lang('Real-time balance updates')
                        </li>
                        <li class="mb-2">
                            <i class="las la-info-circle text-info"></i>
                            @lang('Minimum transfer amount: $1.00')
                        </li>
                        <li class="mb-2">
                            <i class="las la-info-circle text-info"></i>
                            @lang('Account selection shows: Login (Type) - Balance')
                        </li>
                        <li class="mb-0">
                            <i class="las la-shield-alt text-warning"></i>
                            @lang('All transfers are secured and logged')
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Recent Transfers Section -->
            <div class="card custom--card mt-4">
                <div class="card-header">
                    <h5 class="card-title">@lang('Recent Transfers')</h5>
                </div>
                <div class="card-body">
                    @forelse($recentTransfers as $transfer)
                        <div class="transfer-item mb-3 p-3 border rounded">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center mb-1">
                                        <i class="las la-exchange-alt text-primary me-2"></i>
                                        <h6 class="mb-0 fw-bold">${{ number_format($transfer['amount'], 2) }}</h6>
                                    </div>
                                    <div class="transfer-details">
                                        @if($transfer['from_account'] && $transfer['to_account'])
                                            <small class="text-muted d-block">
                                                <i class="las la-arrow-right me-1"></i>
                                                From: <span class="fw-semibold">{{ $transfer['from_account'] }}</span>
                                                To: <span class="fw-semibold">{{ $transfer['to_account'] }}</span>
                                            </small>
                                        @endif
                                        <small class="text-muted d-block">
                                            <i class="las la-clock me-1"></i>
                                            {{ $transfer['created_at']->format('M d, Y H:i') }}
                                        </small>
                                        <small class="text-muted d-block">
                                            <i class="las la-hashtag me-1"></i>
                                            TRX: {{ $transfer['trx'] }}
                                        </small>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-success">
                                        <i class="las la-check-circle me-1"></i>
                                        Completed
                                    </span>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-4">
                            <i class="las la-exchange-alt text-muted" style="font-size: 3rem;"></i>
                            <p class="text-muted mb-0">@lang('No recent transfers found')</p>
                            <small class="text-muted">@lang('Your transfer history will appear here')</small>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>

    <!-- Transfer Confirmation Modal -->
    <div class="modal fade" id="transferConfirmationModal" tabindex="-1" role="dialog" aria-labelledby="transferConfirmationModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="transferConfirmationModalLabel">@lang('Confirm Transfer')</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="transfer-confirmation-details">
                        <h6 class="mb-3">@lang('Please confirm your transfer details:')</h6>
                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="card border">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-6">
                                                <strong>@lang('From Account:')</strong>
                                                <p class="mb-0" id="confirmFromAccount">-</p>
                                            </div>
                                            <div class="col-6">
                                                <strong>@lang('To Account:')</strong>
                                                <p class="mb-0" id="confirmToAccount">-</p>
                                            </div>
                                        </div>
                                        <hr>
                                        <div class="row">
                                            <div class="col-6">
                                                <strong>@lang('Transfer Amount:')</strong>
                                                <p class="mb-0 text--primary" id="confirmAmount">$0.00</p>
                                            </div>
                                            <div class="col-6">
                                                <strong>@lang('Available Balance:')</strong>
                                                <p class="mb-0" id="confirmAvailableBalance">$0.00</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="alert alert-info">
                            <i class="las la-info-circle"></i>
                            @lang('This transfer will be processed immediately and cannot be undone.')
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn--secondary" data-bs-dismiss="modal">@lang('Cancel')</button>
                    <button type="button" class="btn btn--primary" id="confirmTransferBtn">
                        <i class="las la-exchange-alt me-2"></i>@lang('Confirm Transfer')
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Transfer Result Modal -->
    <div class="modal fade" id="transferResultModal" tabindex="-1" role="dialog" aria-labelledby="transferResultModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header" id="resultModalHeader">
                    <h5 class="modal-title" id="transferResultModalLabel">@lang('Transfer Result')</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center" id="transferResultContent">
                        <!-- Content will be populated by JavaScript -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn--primary" data-bs-dismiss="modal" id="resultModalCloseBtn">@lang('Close')</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('topContent')
    <h4 class="mb-4">{{ __($pageTitle) }}</h4>
@endpush

@push('style')
<style>
    .transfer-summary {
        border: 1px solid #e9ecef;
    }

    /* Enhanced dropdown styling for MT5 accounts */
    select[name="from_account"], select[name="to_account"] {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-size: 14px;
    }

    select[name="from_account"] option, select[name="to_account"] option {
        padding: 8px 12px;
        font-size: 13px;
    }

    /* Recent Transfers Styling */
    .transfer-item {
        transition: all 0.3s ease;
        border: 1px solid #e9ecef !important;
        background-color: #f8f9fa;
    }

    .transfer-item:hover {
        background-color: #ffffff;
        border-color: rgb(220, 53, 69) !important;
        box-shadow: 0 2px 8px rgba(220, 53, 69, 0.1);
        transform: translateY(-1px);
    }

    .transfer-details small {
        font-size: 11px;
        line-height: 1.4;
    }

    .transfer-item .badge {
        font-size: 10px;
        padding: 4px 8px;
    }

    .transfer-item h6 {
        color: rgb(220, 53, 69);
        font-size: 14px;
    }

    .transfer-item i.las {
        color: rgb(220, 53, 69);
    }

    /* Animation for new transfer items */
    @keyframes slideInDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Highlight new transfer briefly */
    .transfer-item[style*="animation"] {
        border-color: rgb(220, 53, 69) !important;
        background-color: rgba(220, 53, 69, 0.05);
    }
    .btn--primary {
        background-color: rgb(220, 53, 69) !important;
        border-color: rgb(220, 53, 69) !important;
        color: white !important;
    }
    .btn--primary:hover {
        background-color: rgb(200, 35, 51) !important;
        border-color: rgb(200, 35, 51) !important;
        color: white !important;
    }
    .btn--primary:focus, .btn--primary:active, .btn--primary.active {
        background-color: rgb(200, 35, 51) !important;
        border-color: rgb(200, 35, 51) !important;
        color: white !important;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    }
    .text--primary {
        color: rgb(220, 53, 69) !important;
    }

    /* Override any blue button styles - COMPREHENSIVE */
    .btn-primary, .btn.btn-primary, button.btn-primary, input[type="submit"].btn-primary {
        background-color: rgb(220, 53, 69) !important;
        border-color: rgb(220, 53, 69) !important;
        color: white !important;
    }

    .btn-primary:hover, .btn.btn-primary:hover, button.btn-primary:hover, input[type="submit"].btn-primary:hover {
        background-color: rgb(200, 35, 51) !important;
        border-color: rgb(200, 35, 51) !important;
        color: white !important;
    }

    .btn-primary:focus, .btn.btn-primary:focus, button.btn-primary:focus, input[type="submit"].btn-primary:focus {
        background-color: rgb(200, 35, 51) !important;
        border-color: rgb(200, 35, 51) !important;
        color: white !important;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    }

    .btn-primary:active, .btn.btn-primary:active, button.btn-primary:active, input[type="submit"].btn-primary:active,
    .btn-primary.active, .btn.btn-primary.active, button.btn-primary.active, input[type="submit"].btn-primary.active {
        background-color: rgb(180, 25, 41) !important;
        border-color: rgb(180, 25, 41) !important;
        color: white !important;
    }

    .btn-primary:disabled, .btn.btn-primary:disabled, button.btn-primary:disabled, input[type="submit"].btn-primary:disabled {
        background-color: rgba(220, 53, 69, 0.6) !important;
        border-color: rgba(220, 53, 69, 0.6) !important;
        color: rgba(255, 255, 255, 0.8) !important;
    }

    /* Force override Bootstrap defaults */
    .btn-primary:not(:disabled):not(.disabled):active,
    .btn-primary:not(:disabled):not(.disabled).active {
        background-color: rgb(180, 25, 41) !important;
        border-color: rgb(180, 25, 41) !important;
    }

    /* Transfer form specific overrides */
    #transferForm .btn, #transferForm button, #transferForm input[type="submit"] {
        background-color: rgb(220, 53, 69) !important;
        border-color: rgb(220, 53, 69) !important;
        color: white !important;
    }

    #transferForm .btn:hover, #transferForm button:hover, #transferForm input[type="submit"]:hover {
        background-color: rgb(200, 35, 51) !important;
        border-color: rgb(200, 35, 51) !important;
        color: white !important;
    }
</style>
@endpush

@push('script')
<script>
$(document).ready(function() {
    console.log('🔄 Transfer page JavaScript loaded');

    // Get form elements with proper selectors - FIXED
    const fromAccount = $('#fromAccount, select[name="from_account"]');
    const toAccount = $('#toAccount, select[name="to_account"]');
    const transferAmount = $('#transferAmount, input[name="amount"]');
    const availableBalance = $('#availableBalance');
    const summaryAmount = $('#summaryAmount');
    const totalAmount = $('#totalAmount');
    const transferBtn = $('#transferBtn');
    const transferForm = $('#transferForm');

    console.log('🔍 Form elements found:', {
        fromAccount: fromAccount.length,
        toAccount: toAccount.length,
        transferAmount: transferAmount.length,
        transferBtn: transferBtn.length,
        transferForm: transferForm.length
    });

    // Update available balance when source account changes
    fromAccount.on('change', function() {
        console.log('🔄 Source account changed:', $(this).val());
        const selectedOption = $(this).find('option:selected');
        const balance = selectedOption.data('balance') || 0;

        if (availableBalance.length) {
            availableBalance.text('$' + parseFloat(balance).toFixed(2));
        }

        validateForm();
        updateToAccountOptions();
    });

    // Update destination account options (exclude selected source)
    function updateToAccountOptions() {
        const selectedFrom = fromAccount.val();
        console.log('🔄 Updating destination options, excluding:', selectedFrom);

        toAccount.find('option').each(function() {
            if ($(this).val() === selectedFrom) {
                $(this).hide();
            } else {
                $(this).show();
            }
        });
        toAccount.val(''); // Reset destination selection
        validateForm();
    }

    // Update summary when amount changes
    transferAmount.on('input', function() {
        const amount = parseFloat($(this).val()) || 0;
        console.log('🔄 Amount changed:', amount);

        if (summaryAmount.length) {
            summaryAmount.text('$' + amount.toFixed(2));
        }
        if (totalAmount.length) {
            totalAmount.text('$' + amount.toFixed(2));
        }

        validateForm();
    });

    // Validate form and enable/disable submit button
    function validateForm() {
        const fromVal = fromAccount.val();
        const toVal = toAccount.val();
        const amount = parseFloat(transferAmount.val()) || 0;
        const balance = parseFloat(fromAccount.find('option:selected').data('balance')) || 0;

        const isValid = fromVal && toVal && fromVal !== toVal && amount > 0 && amount <= balance && amount >= 1;

        console.log('🔍 Form validation:', {
            fromVal: fromVal,
            toVal: toVal,
            amount: amount,
            balance: balance,
            isValid: isValid
        });

        transferBtn.prop('disabled', !isValid);

        // Add visual feedback
        if (isValid) {
            transferBtn.removeClass('btn-secondary').addClass('btn--primary');
        } else {
            transferBtn.removeClass('btn--primary').addClass('btn-secondary');
        }
    }

    // Validate on all changes
    toAccount.on('change', validateForm);

    // Enhanced form submission with Laravel modal confirmation
    transferForm.on('submit', function(e) {
        e.preventDefault();

        const fromText = fromAccount.find('option:selected').text();
        const toText = toAccount.find('option:selected').text();
        const amount = transferAmount.val();
        const availableBalance = fromAccount.find('option:selected').data('balance') || 0;

        console.log('🚀 Transfer form submitted:', {
            from: fromText,
            to: toText,
            amount: amount
        });

        // Show Laravel confirmation modal instead of native confirm
        showTransferConfirmationModal(fromText, toText, amount, availableBalance);
    });

    // Function to show transfer confirmation modal with enhanced account display
    function showTransferConfirmationModal(fromText, toText, amount, availableBalance) {
        // Extract just the account info for cleaner display in modal
        const fromAccountInfo = fromText.trim();
        const toAccountInfo = toText.trim();

        $('#confirmFromAccount').text(fromAccountInfo);
        $('#confirmToAccount').text(toAccountInfo);
        $('#confirmAmount').text('$' + parseFloat(amount).toFixed(2));
        $('#confirmAvailableBalance').text('$' + parseFloat(availableBalance).toFixed(2));

        $('#transferConfirmationModal').modal('show');
    }

    // Handle confirmation modal submit
    $('#confirmTransferBtn').on('click', function() {
        $('#transferConfirmationModal').modal('hide');
        processTransfer();
    });

    // Function to process the actual transfer
    function processTransfer() {
        // Show loading state
        const originalText = transferBtn.html();
        transferBtn.prop('disabled', true);
        transferBtn.html('<i class="fas fa-bolt me-2" style="color: #ffc107;"></i>Processing Instantly...');

        // Get form data
        const formData = new FormData(transferForm[0]);

        // Submit via AJAX
        fetch(transferForm.attr('action'), {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'Accept': 'application/json'
            }
        })
        .then(response => {
            console.log('📥 Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('📥 Response data:', data);

            if (data.success) {
                // ✅ INSTANT SUCCESS FEEDBACK - No page reload needed!
                console.log('✅ Transfer completed instantly!');

                // Update dropdown balances instantly with new data
                if (data.data && data.data.updated_balances) {
                    updateDropdownBalances(data.data.updated_balances);
                }

                // Show success modal
                showTransferResultModal(true, data.message, data.data);

                // Reset form instantly
                transferForm[0].reset();
                if (availableBalance.length) availableBalance.text('$0.00');
                if (summaryAmount.length) summaryAmount.text('$0.00');
                if (totalAmount.length) totalAmount.text('$0.00');

                // Reset button instantly
                transferBtn.prop('disabled', false);
                transferBtn.html(originalText);

                // ✅ REMOVED PAGE RELOAD - Instant UI updates instead!
                // Add new transfer to recent transfers list
                if (data.data) {
                    addToRecentTransfers(data.data);
                }

                // Optional: Refresh balances in background for accuracy
                setTimeout(() => {
                    refreshAllBalances();
                }, 2000); // Refresh after 2 seconds for accuracy

            } else {
                showTransferResultModal(false, data.message || 'Transfer failed. Please try again.');

                // Reset button
                transferBtn.prop('disabled', false);
                transferBtn.html(originalText);
            }
        })
        .catch(error => {
            console.error('❌ Transfer error:', error);
            showTransferResultModal(false, 'Network error. Please check your connection and try again.');

            // Reset button
            transferBtn.prop('disabled', false);
            transferBtn.html(originalText);
        });
    }

    // Function to show transfer result modal
    function showTransferResultModal(success, message, data = null) {
        const modal = $('#transferResultModal');
        const header = $('#resultModalHeader');
        const content = $('#transferResultContent');
        const closeBtn = $('#resultModalCloseBtn');

        let html = ''; // ✅ FIXED: Declare html variable at function scope

        if (success) {
            header.removeClass('bg-danger').addClass('bg-success');
            modal.find('.modal-title').text('Transfer Successful');

            html = `
                <div class="text-success mb-3">
                    <i class="las la-check-circle" style="font-size: 4rem; color: rgb(220, 53, 69);"></i>
                </div>
                <h5 class="text-success mb-3" style="color: rgb(220, 53, 69);">✅ Transfer Completed Instantly!</h5>
                <p class="mb-3">${message}</p>
                <div class="alert alert-success border-0" style="background-color: rgba(220, 53, 69, 0.1); color: rgb(220, 53, 69);">
                    <i class="las la-bolt me-2"></i>
                    <strong>Instant Transfer:</strong> Your MT5 accounts have been updated immediately!
                </div>
            `;

            if (data) {
                html += `
                    <div class="card border-success">
                        <div class="card-body">
                            <h6 class="card-title">Transfer Details:</h6>
                            <p class="mb-1"><strong>Transaction ID:</strong> ${data.transaction_id || 'N/A'}</p>
                            <p class="mb-1"><strong>Amount:</strong> $${parseFloat(data.amount || 0).toFixed(2)}</p>
                            <p class="mb-1"><strong>From Account:</strong> ${data.from_account || 'N/A'}</p>
                            <p class="mb-0"><strong>To Account:</strong> ${data.to_account || 'N/A'}</p>
                        </div>
                    </div>
                `;
            }

            closeBtn.removeClass('btn--secondary').addClass('btn--primary');
        } else {
            header.removeClass('bg-success').addClass('bg-danger');
            modal.find('.modal-title').text('Transfer Failed');

            html = `
                <div class="text-danger mb-3">
                    <i class="las la-times-circle" style="font-size: 4rem;"></i>
                </div>
                <h5 class="text-danger mb-3">Transfer Failed</h5>
                <p class="mb-3">${message}</p>
                <div class="alert alert-danger">
                    <i class="las la-exclamation-triangle me-2"></i>
                    Please check your account details and try again.
                </div>
            `;

            closeBtn.removeClass('btn--primary').addClass('btn--secondary');
        }

        content.html(html);
        modal.modal('show');
    }

    // ✅ INSTANT BALANCE UPDATE FUNCTIONS

    // Update dropdown balances instantly after transfer
    function updateDropdownBalances(updatedBalances) {
        console.log('🔄 Updating dropdown balances instantly...', updatedBalances);

        if (updatedBalances.from_account) {
            const fromLogin = updatedBalances.from_account.login;
            const fromBalance = updatedBalances.from_account.balance;

            // Update from account dropdown options
            $('select[name="from_account"] option[value="' + fromLogin + '"]').each(function() {
                const currentText = $(this).text();
                const newText = currentText.replace(/Balance: \$[\d,]+\.?\d*/, 'Balance: $' + fromBalance);
                $(this).text(newText);
                $(this).attr('data-balance', fromBalance.replace(/,/g, ''));
            });

            // Update to account dropdown options
            $('select[name="to_account"] option[value="' + fromLogin + '"]').each(function() {
                const currentText = $(this).text();
                const newText = currentText.replace(/Balance: \$[\d,]+\.?\d*/, 'Balance: $' + fromBalance);
                $(this).text(newText);
                $(this).attr('data-balance', fromBalance.replace(/,/g, ''));
            });
        }

        if (updatedBalances.to_account) {
            const toLogin = updatedBalances.to_account.login;
            const toBalance = updatedBalances.to_account.balance;

            // Update from account dropdown options
            $('select[name="from_account"] option[value="' + toLogin + '"]').each(function() {
                const currentText = $(this).text();
                const newText = currentText.replace(/Balance: \$[\d,]+\.?\d*/, 'Balance: $' + toBalance);
                $(this).text(newText);
                $(this).attr('data-balance', toBalance.replace(/,/g, ''));
            });

            // Update to account dropdown options
            $('select[name="to_account"] option[value="' + toLogin + '"]').each(function() {
                const currentText = $(this).text();
                const newText = currentText.replace(/Balance: \$[\d,]+\.?\d*/, 'Balance: $' + toBalance);
                $(this).text(newText);
                $(this).attr('data-balance', toBalance.replace(/,/g, ''));
            });
        }

        console.log('✅ Dropdown balances updated instantly!');
    }

    // Refresh all balances from server (background update for accuracy)
    function refreshAllBalances() {
        console.log('🔄 Refreshing all balances in background...');

        fetch('{{ route("user.transfer.balances") }}', {
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'Accept': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.accounts) {
                console.log('📥 Updated balances received:', data.accounts);

                // Update all dropdown options with fresh data
                data.accounts.forEach(account => {
                    const login = account.login;
                    const displayFormat = account.display_format;
                    const balance = account.balance;

                    // Update from account dropdown
                    $('select[name="from_account"] option[value="' + login + '"]').each(function() {
                        $(this).text(displayFormat);
                        $(this).attr('data-balance', balance);
                    });

                    // Update to account dropdown
                    $('select[name="to_account"] option[value="' + login + '"]').each(function() {
                        $(this).text(displayFormat);
                        $(this).attr('data-balance', balance);
                    });
                });

                console.log('✅ All balances refreshed successfully!');
            }
        })
        .catch(error => {
            console.warn('⚠️ Background balance refresh failed:', error);
            // Don't show error to user - this is just a background update
        });
    }

    // Add new transfer to recent transfers list
    function addToRecentTransfers(transferData) {
        console.log('📝 Adding new transfer to recent list:', transferData);

        const recentTransfersContainer = $('.card-body').has('.transfer-item').first();

        // Check if there's an empty state message
        const emptyState = recentTransfersContainer.find('.text-center.py-4');
        if (emptyState.length) {
            emptyState.remove();
        }

        // Create new transfer item HTML
        const now = new Date();
        const formattedDate = now.toLocaleDateString('en-US', {
            month: 'short',
            day: '2-digit',
            year: 'numeric'
        }) + ' ' + now.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
        });

        const newTransferHtml = `
            <div class="transfer-item mb-3 p-3 border rounded" style="animation: slideInDown 0.5s ease-out;">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-1">
                            <i class="las la-exchange-alt text-primary me-2"></i>
                            <h6 class="mb-0 fw-bold">$${parseFloat(transferData.amount || 0).toFixed(2)}</h6>
                        </div>
                        <div class="transfer-details">
                            <small class="text-muted d-block">
                                <i class="las la-arrow-right me-1"></i>
                                From: <span class="fw-semibold">${transferData.from_account || 'N/A'}</span>
                                To: <span class="fw-semibold">${transferData.to_account || 'N/A'}</span>
                            </small>
                            <small class="text-muted d-block">
                                <i class="las la-clock me-1"></i>
                                ${formattedDate}
                            </small>
                            <small class="text-muted d-block">
                                <i class="las la-hashtag me-1"></i>
                                TRX: ${transferData.transaction_id || 'N/A'}
                            </small>
                        </div>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-success">
                            <i class="las la-check-circle me-1"></i>
                            Completed
                        </span>
                    </div>
                </div>
            </div>
        `;

        // Add to the top of the recent transfers list
        recentTransfersContainer.prepend(newTransferHtml);

        // Remove excess items (keep only 10)
        const transferItems = recentTransfersContainer.find('.transfer-item');
        if (transferItems.length > 10) {
            transferItems.slice(10).remove();
        }

        console.log('✅ New transfer added to recent list!');
    }

    // Initial validation
    validateForm();
});
</script>
@endpush
