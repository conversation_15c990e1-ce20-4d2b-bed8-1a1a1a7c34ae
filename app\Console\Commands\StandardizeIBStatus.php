<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StandardizeIBStatus extends Command
{
    protected $signature = 'ib:standardize-status 
                            {--dry-run : Show what would be done without making changes}
                            {--force : Force update even if conflicts exist}';

    protected $description = 'Standardize IB status fields across partner and ib_status columns';

    public function handle()
    {
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');

        $this->info('🔄 Starting IB Status Standardization...');
        $this->info('=====================================');

        if ($dryRun) {
            $this->warn('🧪 DRY RUN MODE - No changes will be made');
        }

        try {
            // Analyze current status distribution
            $this->analyzeCurrentStatus();

            // Standardize status values
            $updated = $this->standardizeStatus($dryRun, $force);

            // Verify results
            $this->verifyResults();

            $this->info('');
            $this->info('🎉 IB Status Standardization Completed!');
            $this->info("✅ Updated {$updated} user records");

            if (!$dryRun) {
                $this->info('✅ All changes have been saved to the database');
            } else {
                $this->warn('🧪 DRY RUN - No actual changes were made');
            }

        } catch (\Exception $e) {
            $this->error('💥 Status standardization failed: ' . $e->getMessage());
            Log::error('IB Status Standardization Failed', ['error' => $e->getMessage()]);
            return 1;
        }

        return 0;
    }

    /**
     * Analyze current status distribution
     */
    private function analyzeCurrentStatus()
    {
        $this->info('📊 Current IB Status Distribution:');

        // Partner field distribution
        $partnerStats = User::selectRaw('partner, COUNT(*) as count')
            ->groupBy('partner')
            ->orderBy('partner')
            ->get();

        $this->table(['Partner Value', 'Count', 'Meaning'], 
            $partnerStats->map(function($stat) {
                $meaning = match($stat->partner) {
                    0 => 'Normal User',
                    1 => 'Approved IB',
                    2 => 'Pending IB',
                    3 => 'Rejected IB',
                    default => 'Unknown'
                };
                return [$stat->partner, $stat->count, $meaning];
            })->toArray()
        );

        // IB Status field distribution
        $ibStatusStats = User::selectRaw('ib_status, COUNT(*) as count')
            ->whereNotNull('ib_status')
            ->groupBy('ib_status')
            ->orderBy('ib_status')
            ->get();

        if ($ibStatusStats->count() > 0) {
            $this->info('');
            $this->table(['IB Status', 'Count'], 
                $ibStatusStats->map(function($stat) {
                    return [$stat->ib_status ?? 'NULL', $stat->count];
                })->toArray()
            );
        }

        // Identify conflicts
        $conflicts = User::whereNotNull('ib_status')
            ->where(function($query) {
                $query->where(function($q) {
                    // partner=1 (approved) but ib_status != 'approved'
                    $q->where('partner', 1)->where('ib_status', '!=', 'approved');
                })->orWhere(function($q) {
                    // partner=2 (pending) but ib_status != 'pending'
                    $q->where('partner', 2)->where('ib_status', '!=', 'pending');
                })->orWhere(function($q) {
                    // partner=3 (rejected) but ib_status != 'rejected'
                    $q->where('partner', 3)->where('ib_status', '!=', 'rejected');
                });
            })
            ->count();

        if ($conflicts > 0) {
            $this->warn("⚠️ Found {$conflicts} users with conflicting status values");
        } else {
            $this->info("✅ No status conflicts found");
        }
    }

    /**
     * Standardize status values
     */
    private function standardizeStatus($dryRun = false, $force = false)
    {
        $updated = 0;

        $this->info('');
        $this->info('🔧 Standardizing IB Status Values...');

        // Strategy: Use partner field as source of truth, update ib_status to match
        $statusMappings = [
            1 => 'approved',  // partner=1 -> ib_status='approved'
            2 => 'pending',   // partner=2 -> ib_status='pending'
            3 => 'rejected'   // partner=3 -> ib_status='rejected'
        ];

        foreach ($statusMappings as $partnerValue => $ibStatusValue) {
            $query = User::where('partner', $partnerValue);
            
            if (!$force) {
                // Only update if ib_status is null or different
                $query->where(function($q) use ($ibStatusValue) {
                    $q->whereNull('ib_status')->orWhere('ib_status', '!=', $ibStatusValue);
                });
            }

            $usersToUpdate = $query->get();

            foreach ($usersToUpdate as $user) {
                if (!$dryRun) {
                    $user->update([
                        'ib_status' => $ibStatusValue,
                        'is_ib_account' => $partnerValue === 1, // Mark as IB if approved
                        'ib_approved_at' => $partnerValue === 1 ? ($user->ib_approved_at ?? now()) : null
                    ]);
                }

                $updated++;

                if ($updated <= 10) { // Show first 10 updates
                    $this->line("  ✅ User {$user->id} ({$user->email}): partner={$partnerValue} -> ib_status='{$ibStatusValue}'");
                }
            }

            if ($usersToUpdate->count() > 10) {
                $this->line("  ... and " . ($usersToUpdate->count() - 10) . " more users");
            }
        }

        // Handle users with partner=0 but have ib_status set
        $normalUsersWithIbStatus = User::where('partner', 0)
            ->whereNotNull('ib_status')
            ->get();

        foreach ($normalUsersWithIbStatus as $user) {
            if (!$dryRun) {
                $user->update([
                    'ib_status' => null,
                    'is_ib_account' => false
                ]);
            }
            $updated++;
            $this->line("  🔄 User {$user->id}: Cleared IB status (partner=0)");
        }

        return $updated;
    }

    /**
     * Verify results after standardization
     */
    private function verifyResults()
    {
        $this->info('');
        $this->info('🔍 Verification Results:');

        // Check for remaining conflicts
        $conflicts = User::whereNotNull('ib_status')
            ->where(function($query) {
                $query->where(function($q) {
                    $q->where('partner', 1)->where('ib_status', '!=', 'approved');
                })->orWhere(function($q) {
                    $q->where('partner', 2)->where('ib_status', '!=', 'pending');
                })->orWhere(function($q) {
                    $q->where('partner', 3)->where('ib_status', '!=', 'rejected');
                });
            })
            ->count();

        if ($conflicts === 0) {
            $this->info('✅ All status conflicts resolved');
        } else {
            $this->warn("⚠️ {$conflicts} conflicts still remain");
        }

        // Show final distribution
        $finalStats = User::selectRaw('
                partner,
                ib_status,
                is_ib_account,
                COUNT(*) as count
            ')
            ->where('partner', '>', 0)
            ->groupBy('partner', 'ib_status', 'is_ib_account')
            ->orderBy('partner')
            ->get();

        $this->table(['Partner', 'IB Status', 'Is IB Account', 'Count'], 
            $finalStats->map(function($stat) {
                return [
                    $stat->partner,
                    $stat->ib_status ?? 'NULL',
                    $stat->is_ib_account ? 'Yes' : 'No',
                    $stat->count
                ];
            })->toArray()
        );
    }
}
