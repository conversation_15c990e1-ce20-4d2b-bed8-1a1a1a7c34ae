<?php
// Test script to check for template rendering errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "Testing Email Template System Errors\n";
echo "=====================================\n\n";

// Test 1: Check if <PERSON><PERSON> is accessible
echo "1. Testing Laravel Application Access...\n";
try {
    $output = file_get_contents('https://localhost/mbf.mybrokerforex.com-31052025/admin/notification/template/edit/1');
    if ($output === false) {
        echo "❌ FAIL: Cannot access Laravel application\n";
    } else {
        echo "✅ PASS: Laravel application accessible\n";
        
        // Check for PHP errors in output
        if (strpos($output, 'Parse error') !== false || strpos($output, 'Fatal error') !== false) {
            echo "❌ FAIL: PHP errors detected in template\n";
            
            // Extract error details
            preg_match('/Parse error:.*?line (\d+)/', $output, $matches);
            if ($matches) {
                echo "   Error at line: " . $matches[1] . "\n";
            }
            
            preg_match('/Fatal error:.*?line (\d+)/', $output, $matches);
            if ($matches) {
                echo "   Fatal error at line: " . $matches[1] . "\n";
            }
        } else {
            echo "✅ PASS: No PHP errors detected\n";
        }
        
        // Check for JavaScript syntax errors
        if (strpos($output, 'SyntaxError') !== false || strpos($output, 'unexpected token') !== false) {
            echo "❌ FAIL: JavaScript syntax errors detected\n";
        } else {
            echo "✅ PASS: No obvious JavaScript syntax errors\n";
        }
    }
} catch (Exception $e) {
    echo "❌ FAIL: Exception occurred - " . $e->getMessage() . "\n";
}

echo "\n";

// Test 2: Check multiple template pages
echo "2. Testing Multiple Template Pages...\n";
$templateIds = [1, 2, 3, 4, 5, 10, 15, 20];
$successCount = 0;
$totalCount = count($templateIds);

foreach ($templateIds as $id) {
    echo "   Testing template ID $id... ";
    try {
        $url = "https://localhost/mbf.mybrokerforex.com-31052025/admin/notification/template/edit/$id";
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'ignore_errors' => true
            ],
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false
            ]
        ]);
        
        $output = file_get_contents($url, false, $context);
        
        if ($output === false) {
            echo "❌ FAIL (Cannot access)\n";
        } elseif (strpos($output, 'Parse error') !== false || strpos($output, 'Fatal error') !== false) {
            echo "❌ FAIL (PHP Error)\n";
        } elseif (strpos($output, '500 Internal Server Error') !== false) {
            echo "❌ FAIL (500 Error)\n";
        } else {
            echo "✅ PASS\n";
            $successCount++;
        }
    } catch (Exception $e) {
        echo "❌ FAIL (Exception: " . $e->getMessage() . ")\n";
    }
}

echo "\n   Summary: $successCount/$totalCount templates accessible\n\n";

// Test 3: Check global template
echo "3. Testing Global Template...\n";
try {
    $url = "https://localhost/mbf.mybrokerforex.com-31052025/admin/notification/global";
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'ignore_errors' => true
        ],
        'ssl' => [
            'verify_peer' => false,
            'verify_peer_name' => false
        ]
    ]);
    
    $output = file_get_contents($url, false, $context);
    
    if ($output === false) {
        echo "❌ FAIL: Cannot access global template\n";
    } elseif (strpos($output, 'Parse error') !== false || strpos($output, 'Fatal error') !== false) {
        echo "❌ FAIL: PHP errors in global template\n";
    } else {
        echo "✅ PASS: Global template accessible\n";
    }
} catch (Exception $e) {
    echo "❌ FAIL: Exception - " . $e->getMessage() . "\n";
}

echo "\n";

// Test 4: Check templates list
echo "4. Testing Templates List...\n";
try {
    $url = "https://localhost/mbf.mybrokerforex.com-31052025/admin/notification/templates";
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'ignore_errors' => true
        ],
        'ssl' => [
            'verify_peer' => false,
            'verify_peer_name' => false
        ]
    ]);
    
    $output = file_get_contents($url, false, $context);
    
    if ($output === false) {
        echo "❌ FAIL: Cannot access templates list\n";
    } elseif (strpos($output, 'Parse error') !== false || strpos($output, 'Fatal error') !== false) {
        echo "❌ FAIL: PHP errors in templates list\n";
    } else {
        echo "✅ PASS: Templates list accessible\n";
    }
} catch (Exception $e) {
    echo "❌ FAIL: Exception - " . $e->getMessage() . "\n";
}

echo "\n=====================================\n";
echo "Test completed. Check results above.\n";
?>
