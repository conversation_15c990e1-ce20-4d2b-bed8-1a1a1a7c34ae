@extends($activeTemplate . 'layouts.master')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title">
                <h4>@lang('My Sub IBs')</h4>
                <p class="text-muted">@lang('Manage and monitor your Sub-IB network')</p>
            </div>
        </div>
    </div>

    <!-- Sub IB Statistics -->
    <div class="row gy-4">
        <div class="col-xl-3 col-lg-4 col-sm-6">
            <div class="dashboard-widget">
                <div class="dashboard-widget__icon">
                    <i class="las la-users"></i>
                </div>
                <div class="dashboard-widget__content">
                    <h4 class="dashboard-widget__number">{{ $subIbs->total() }}</h4>
                    <span class="dashboard-widget__caption">@lang('Total Sub IBs')</span>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-4 col-sm-6">
            <div class="dashboard-widget">
                <div class="dashboard-widget__icon">
                    <i class="las la-check-circle"></i>
                </div>
                <div class="dashboard-widget__content">
                    <h4 class="dashboard-widget__number">{{ $subIbs->where('ib_status', 'approved')->count() }}</h4>
                    <span class="dashboard-widget__caption">@lang('Active Sub IBs')</span>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-4 col-sm-6">
            <div class="dashboard-widget">
                <div class="dashboard-widget__icon">
                    <i class="las la-clock"></i>
                </div>
                <div class="dashboard-widget__content">
                    <h4 class="dashboard-widget__number">{{ $subIbs->where('ib_status', 'pending')->count() }}</h4>
                    <span class="dashboard-widget__caption">@lang('Pending Sub IBs')</span>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-4 col-sm-6">
            <div class="dashboard-widget">
                <div class="dashboard-widget__icon">
                    <i class="las la-dollar-sign"></i>
                </div>
                <div class="dashboard-widget__content">
                    <h4 class="dashboard-widget__number">
                        {{ showAmount(collect($subIbStats)->sum('total_commissions')) }}
                    </h4>
                    <span class="dashboard-widget__caption">@lang('Total Sub IB Commissions')</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Sub IBs Table -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">@lang('Sub IB List')</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table--light">
                            <thead>
                                <tr>
                                    <th>@lang('Sub IB')</th>
                                    <th>@lang('Group')</th>
                                    <th>@lang('Status')</th>
                                    <th>@lang('Joined Date')</th>
                                    <th>@lang('Total Commissions')</th>
                                    <th>@lang('Paid Commissions')</th>
                                    <th>@lang('Total Trades')</th>
                                    <th>@lang('Action')</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($subIbs as $subIb)
                                @php
                                    $stats = $subIbStats[$subIb->id] ?? [
                                        'total_commissions' => 0,
                                        'paid_commissions' => 0,
                                        'total_trades' => 0
                                    ];
                                @endphp
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ $subIb->fullname }}</strong>
                                            <br>
                                            <small class="text-muted">{{ $subIb->username }}</small>
                                            <br>
                                            <small class="text-info">{{ $subIb->email }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        @if($subIb->ibGroup)
                                            <span class="badge badge--primary">{{ $subIb->ibGroup->name }}</span>
                                        @else
                                            <span class="text-muted">@lang('No Group')</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($subIb->ib_status == 'approved')
                                            <span class="badge badge--success">@lang('Active')</span>
                                        @elseif($subIb->ib_status == 'pending')
                                            <span class="badge badge--warning">@lang('Pending')</span>
                                        @else
                                            <span class="badge badge--danger">@lang('Rejected')</span>
                                        @endif
                                    </td>
                                    <td>{{ showDateTime($subIb->ib_approved_at ?? $subIb->created_at, 'd M Y') }}</td>
                                    <td>
                                        <span class="fw-bold text--success">
                                            {{ showAmount($stats['total_commissions']) }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-bold text--primary">
                                            {{ showAmount($stats['paid_commissions']) }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge--info">{{ $stats['total_trades'] }}</span>
                                    </td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline--primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                @lang('Actions')
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="viewSubIbDetails({{ $subIb->id }})">
                                                        <i class="las la-eye"></i> @lang('View Details')
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="viewSubIbCommissions({{ $subIb->id }})">
                                                        <i class="las la-dollar-sign"></i> @lang('View Commissions')
                                                    </a>
                                                </li>
                                                @if($subIb->ib_status == 'approved')
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="contactSubIb('{{ $subIb->email }}')">
                                                        <i class="las la-envelope"></i> @lang('Contact')
                                                    </a>
                                                </li>
                                                @endif
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="8" class="text-center">@lang('No Sub IBs found')</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
                @if($subIbs->hasPages())
                <div class="card-footer">
                    {{ $subIbs->links() }}
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Performance Overview -->
    @if($subIbs->count() > 0)
    <div class="row mt-4">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">@lang('Top Performing Sub IBs')</h5>
                </div>
                <div class="card-body">
                    @php
                        $topPerformers = $subIbs->sortByDesc(function($subIb) use ($subIbStats) {
                            return $subIbStats[$subIb->id]['total_commissions'] ?? 0;
                        })->take(5);
                    @endphp
                    
                    @foreach($topPerformers as $performer)
                    @php
                        $stats = $subIbStats[$performer->id] ?? ['total_commissions' => 0, 'total_trades' => 0];
                    @endphp
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <h6 class="mb-0">{{ $performer->fullname }}</h6>
                            <small class="text-muted">{{ $stats['total_trades'] }} trades</small>
                        </div>
                        <div class="text-end">
                            <span class="fw-bold">{{ showAmount($stats['total_commissions']) }}</span>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">@lang('Sub IB Status Distribution')</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <h4 class="text--success">{{ $subIbs->where('ib_status', 'approved')->count() }}</h4>
                            <span class="caption">@lang('Active')</span>
                        </div>
                        <div class="col-4">
                            <h4 class="text--warning">{{ $subIbs->where('ib_status', 'pending')->count() }}</h4>
                            <span class="caption">@lang('Pending')</span>
                        </div>
                        <div class="col-4">
                            <h4 class="text--danger">{{ $subIbs->where('ib_status', 'rejected')->count() }}</h4>
                            <span class="caption">@lang('Rejected')</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

<!-- Sub IB Details Modal -->
<div class="modal fade" id="subIbDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@lang('Sub IB Details')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="subIbDetailsContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>
@endsection

@push('script')
<script>
function viewSubIbDetails(subIbId) {
    // Implementation for viewing Sub IB details
    $('#subIbDetailsModal').modal('show');
    $('#subIbDetailsContent').html('<div class="text-center"><i class="las la-spinner la-spin"></i> Loading...</div>');
    
    // You can implement AJAX call here to load Sub IB details
    setTimeout(function() {
        $('#subIbDetailsContent').html('<p>Sub IB details for ID: ' + subIbId + '</p>');
    }, 1000);
}

function viewSubIbCommissions(subIbId) {
    // Implementation for viewing Sub IB commissions
    alert('View commissions for Sub IB ID: ' + subIbId);
}

function contactSubIb(email) {
    // Implementation for contacting Sub IB
    window.location.href = 'mailto:' + email;
}
</script>
@endpush
