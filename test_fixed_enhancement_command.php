<?php
/**
 * COMPREHENSIVE TEST FOR FIXED ENHANCEMENT COMMAND
 * This script tests the fixed enhancement command with safety checks
 */

// Include Laravel bootstrap
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🔧 TESTING FIXED ENHANCEMENT COMMAND\n";
echo "====================================\n\n";

// 1. CHECK CURRENT TEMPLATE STATE
echo "1️⃣ CURRENT TEMPLATE STATE\n";
echo "=========================\n";

$templates = \App\Models\NotificationTemplate::all();
$emptyTemplates = $templates->where('email_body', '')->count();
$shortTemplates = $templates->filter(function($t) { return strlen($t->email_body) < 500; })->count();

echo "📊 Total templates: {$templates->count()}\n";
echo "📊 Empty templates: {$emptyTemplates}\n";
echo "📊 Short templates (<500 chars): {$shortTemplates}\n";

if ($emptyTemplates > 0) {
    echo "🚨 WARNING: {$emptyTemplates} templates are empty!\n";
    echo "💡 Run emergency_template_recovery.php first if needed.\n\n";
}

// 2. TEST CONTENT GENERATION FOR EACH TEMPLATE TYPE
echo "2️⃣ CONTENT GENERATION TEST\n";
echo "===========================\n";

$command = new \App\Console\Commands\EnhanceEmailTemplates();
$reflection = new ReflectionClass($command);
$method = $reflection->getMethod('getDefaultContent');
$method->setAccessible(true);

$uniqueTypes = $templates->pluck('act')->unique()->sort();
$contentResults = [];

foreach ($uniqueTypes as $type) {
    try {
        $content = $method->invoke($command, $type);
        $length = strlen($content);
        $hasShortcodes = preg_match('/\{\{[^}]+\}\}/', $content);
        
        $contentResults[$type] = [
            'length' => $length,
            'has_shortcodes' => $hasShortcodes,
            'content' => substr($content, 0, 100) . '...'
        ];
        
        $status = $length > 100 ? '✅' : '❌';
        $shortcodeStatus = $hasShortcodes ? '✅' : '⚠️';
        
        echo sprintf("%-30s | %4d chars | %s Content | %s Shortcodes\n", 
            $type, $length, $status, $shortcodeStatus);
            
    } catch (\Exception $e) {
        echo sprintf("%-30s | ERROR: %s\n", $type, $e->getMessage());
    }
}

echo "\n";

// 3. TEST PROFESSIONAL STRUCTURE GENERATION
echo "3️⃣ PROFESSIONAL STRUCTURE TEST\n";
echo "===============================\n";

$structureMethod = $reflection->getMethod('getProfessionalTemplateStructure');
$structureMethod->setAccessible(true);

$testTemplate = $templates->first();
if ($testTemplate) {
    try {
        $professionalContent = $structureMethod->invoke($command, $testTemplate->act, $testTemplate->email_body);
        
        $hasDoctype = str_contains($professionalContent, '<!DOCTYPE html>');
        $hasLogo = str_contains($professionalContent, 'mbf.mybrokerforex.com/assets/images/logoIcon/logo.png');
        $hasGradient = str_contains($professionalContent, 'linear-gradient(135deg, #dc3545');
        $hasFooter = str_contains($professionalContent, 'MBFX - Professional Trading Platform');
        
        echo "✅ Test template: {$testTemplate->name} ({$testTemplate->act})\n";
        echo "📄 Generated content length: " . strlen($professionalContent) . " characters\n";
        echo "🏗️ Has DOCTYPE: " . ($hasDoctype ? '✅ YES' : '❌ NO') . "\n";
        echo "🖼️ Has Logo: " . ($hasLogo ? '✅ YES' : '❌ NO') . "\n";
        echo "🎨 Has Gradient: " . ($hasGradient ? '✅ YES' : '❌ NO') . "\n";
        echo "📋 Has Footer: " . ($hasFooter ? '✅ YES' : '❌ NO') . "\n";
        
        if (strlen($professionalContent) > 500 && $hasDoctype && $hasLogo && $hasFooter) {
            echo "🎉 STRUCTURE TEST: PASSED\n";
        } else {
            echo "❌ STRUCTURE TEST: FAILED\n";
        }
        
    } catch (\Exception $e) {
        echo "❌ Structure generation error: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ No templates found for testing\n";
}

echo "\n";

// 4. TEST COMMAND EXECUTION (PREVIEW MODE)
echo "4️⃣ COMMAND EXECUTION TEST\n";
echo "=========================\n";

try {
    echo "📋 Testing command in preview mode with first template...\n";
    
    ob_start();
    $exitCode = \Artisan::call('email:enhance-templates', [
        '--preview' => true,
        '--template' => [$testTemplate->id ?? 1]
    ]);
    $output = ob_get_clean();
    
    echo "✅ Command exit code: {$exitCode}\n";
    echo "📄 Command output length: " . strlen($output) . " characters\n";
    
    if (str_contains($output, 'Enhanced Preview:')) {
        echo "✅ Preview generation: WORKING\n";
    } else {
        echo "❌ Preview generation: FAILED\n";
    }
    
} catch (\Exception $e) {
    ob_end_clean();
    echo "❌ Command execution error: " . $e->getMessage() . "\n";
}

echo "\n";

// 5. SAFETY RECOMMENDATIONS
echo "5️⃣ SAFETY RECOMMENDATIONS\n";
echo "==========================\n";

if ($emptyTemplates > 0) {
    echo "🚨 CRITICAL: Run recovery script first:\n";
    echo "   php emergency_template_recovery.php\n\n";
}

echo "📋 Safe testing steps:\n";
echo "1. Test with single template first:\n";
echo "   php artisan email:enhance-templates --preview --template=1\n\n";
echo "2. Test with single template (actual):\n";
echo "   php artisan email:enhance-templates --template=1\n\n";
echo "3. If successful, enhance all:\n";
echo "   php artisan email:enhance-templates\n\n";

echo "🔍 Verification steps:\n";
echo "1. Check Visual Builder shows content\n";
echo "2. Verify HTML structure is complete\n";
echo "3. Test email preview functionality\n";
echo "4. Confirm shortcodes are present\n\n";

echo "🔧 TESTING COMPLETED\n";

?>
