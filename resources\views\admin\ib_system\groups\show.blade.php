@extends('admin.layouts.app')

@section('panel')
<div class="row">
    <div class="col-lg-8">
        <div class="card b-radius--10">
            <div class="card-header">
                <h5 class="card-title">@lang('IB Group Details - ') {{ $ibGroup->name }}</h5>
                <div class="card-header-right">
                    @if($ibGroup->status)
                        <span class="badge badge--success">@lang('Active')</span>
                    @else
                        <span class="badge badge--danger">@lang('Inactive')</span>
                    @endif
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>@lang('Group Name')</label>
                            <input type="text" class="form-control" value="{{ $ibGroup->name }}" readonly>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>@lang('Commission Multiplier')</label>
                            <input type="text" class="form-control" value="{{ $ibGroup->commission_multiplier }}x" readonly>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>@lang('Maximum Levels')</label>
                            <input type="text" class="form-control" value="{{ $ibGroup->max_levels }}" readonly>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>@lang('Created Date')</label>
                            <input type="text" class="form-control" value="{{ showDateTime($ibGroup->created_at) }}" readonly>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>@lang('Description')</label>
                    <textarea class="form-control" rows="3" readonly>{{ $ibGroup->description ?? 'No description provided' }}</textarea>
                </div>

                @if($ibGroup->rules_json)
                <div class="form-group">
                    <label>@lang('Additional Rules')</label>
                    <textarea class="form-control" rows="4" readonly>{{ json_encode($ibGroup->rules_json, JSON_PRETTY_PRINT) }}</textarea>
                </div>
                @endif
            </div>
        </div>

        <!-- Group Members -->
        <div class="card b-radius--10 mt-4">
            <div class="card-header">
                <h5 class="card-title">@lang('Group Members')</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive--sm table-responsive">
                    <table class="table table--light style--two">
                        <thead>
                            <tr>
                                <th>@lang('User')</th>
                                <th>@lang('IB Type')</th>
                                <th>@lang('IB Status')</th>
                                <th>@lang('Parent IB')</th>
                                <th>@lang('Joined Date')</th>
                                <th>@lang('Action')</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($ibGroup->users as $user)
                            <tr>
                                <td>
                                    <div>
                                        <strong>{{ $user->fullname }}</strong>
                                        <br>
                                        <small class="text-muted">{{ $user->username }}</small>
                                    </div>
                                </td>
                                <td>
                                    @if($user->ib_type)
                                        <span class="badge badge--{{ $user->ib_type == 'master' ? 'primary' : 'info' }}">
                                            {{ ucfirst($user->ib_type) }} IB
                                        </span>
                                    @else
                                        <span class="text-muted">N/A</span>
                                    @endif
                                </td>
                                <td>
                                    @if($user->ib_status == 'approved')
                                        <span class="badge badge--success">@lang('Approved')</span>
                                    @elseif($user->ib_status == 'pending')
                                        <span class="badge badge--warning">@lang('Pending')</span>
                                    @elseif($user->ib_status == 'rejected')
                                        <span class="badge badge--danger">@lang('Rejected')</span>
                                    @else
                                        <span class="text-muted">N/A</span>
                                    @endif
                                </td>
                                <td>
                                    @if($user->ibParent)
                                        {{ $user->ibParent->fullname }}
                                        <br>
                                        <small class="text-muted">{{ $user->ibParent->username }}</small>
                                    @else
                                        <span class="text-muted">@lang('No Parent')</span>
                                    @endif
                                </td>
                                <td>{{ showDateTime($user->ib_approved_at ?? $user->created_at) }}</td>
                                <td>
                                    <a href="{{ route('admin.users.detail', $user->id) }}" class="btn btn-sm btn-outline--primary">
                                        <i class="las la-eye"></i> @lang('View')
                                    </a>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="6" class="text-center">@lang('No members in this group yet')</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Statistics Card -->
        <div class="card b-radius--10">
            <div class="card-header">
                <h5 class="card-title">@lang('Group Statistics')</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text--primary">{{ $stats['total_ibs'] }}</h4>
                        <span class="caption">@lang('Total IBs')</span>
                    </div>
                    <div class="col-6">
                        <h4 class="text--success">{{ $stats['active_ibs'] }}</h4>
                        <span class="caption">@lang('Active IBs')</span>
                    </div>
                </div>
                <div class="row text-center mt-3">
                    <div class="col-6">
                        <h4 class="text--warning">{{ $stats['pending_ibs'] }}</h4>
                        <span class="caption">@lang('Pending IBs')</span>
                    </div>
                    <div class="col-6">
                        <h4 class="text--info">{{ $stats['active_rules'] }}</h4>
                        <span class="caption">@lang('Active Rules')</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rebate Rules -->
        <div class="card b-radius--10 mt-4">
            <div class="card-header">
                <h5 class="card-title">@lang('Rebate Rules')</h5>
            </div>
            <div class="card-body">
                @forelse($ibGroup->rebateRules->take(5) as $rule)
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <h6 class="mb-0">{{ $rule->display_name }}</h6>
                        <small class="text-muted">
                            Min: {{ $rule->min_volume }} lots
                            @if($rule->max_volume)
                                | Max: {{ $rule->max_volume }} lots
                            @endif
                        </small>
                    </div>
                    <div class="text-end">
                        <span class="badge badge--primary">${{ $rule->rebate_per_lot }}</span>
                        <br>
                        <small class="text-muted">per lot</small>
                    </div>
                </div>
                @empty
                <div class="text-center py-3">
                    <i class="las la-rules text-muted" style="font-size: 3rem;"></i>
                    <p class="text-muted mt-2">@lang('No rebate rules configured')</p>
                </div>
                @endforelse

                @if($ibGroup->rebateRules->count() > 5)
                <div class="text-center mt-3">
                    <small class="text-muted">
                        @lang('And') {{ $ibGroup->rebateRules->count() - 5 }} @lang('more rules...')
                    </small>
                </div>
                @endif
            </div>
        </div>

        <!-- Actions -->
        <div class="card b-radius--10 mt-4">
            <div class="card-header">
                <h5 class="card-title">@lang('Actions')</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.ib.groups.edit', $ibGroup->id) }}" class="btn btn--primary">
                        <i class="las la-edit"></i> @lang('Edit Group')
                    </a>
                    
                    <form action="{{ route('admin.ib.groups.toggle_status', $ibGroup->id) }}" method="POST">
                        @csrf
                        <button type="submit" class="btn btn--{{ $ibGroup->status ? 'warning' : 'success' }} w-100">
                            @if($ibGroup->status)
                                <i class="las la-eye-slash"></i> @lang('Deactivate Group')
                            @else
                                <i class="las la-eye"></i> @lang('Activate Group')
                            @endif
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('breadcrumb-plugins')
<div class="d-inline">
    <a href="{{ route('admin.ib.groups.index') }}" class="btn btn--dark">
        <i class="las la-arrow-left"></i> @lang('Back to Groups')
    </a>
</div>
@endpush
