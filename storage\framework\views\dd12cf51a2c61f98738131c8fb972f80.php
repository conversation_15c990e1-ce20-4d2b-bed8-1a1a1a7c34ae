<div class="row mb--20">
  <div class="col-lg-12">
    <div class="card mt-30">
      <div class="card-header d-flex justify-content-between align-items-center">
        <div>
          <h5 class="card-title mb-0">
            <?php echo app('translator')->get('Direct Referrals of'); ?> <?php echo e($user->fullname); ?>

            <span class="badge badge--primary ms-2"><?php echo e($directReferralsPaginated->total()); ?></span>
          </h5>
          <small class="text-muted">
            <?php if($user->ib_status == 1): ?>
              <?php echo e(ucfirst($user->ib_type ?? 'master')); ?> IB - MT5: <?php echo e($user->mt5_login ?? 'N/A'); ?>

            <?php else: ?>
              Client - MT5: <?php echo e($user->mt5_login ?? 'N/A'); ?>

            <?php endif; ?>
          </small>
        </div>
        <div class="d-flex gap-2">
          <button class="btn btn--info btn-sm" onclick="refreshReferralsList()" title="Refresh List">
            <i class="fa fa-sync"></i>
          </button>
          <button class="btn btn--primary" data-bs-toggle="modal" data-bs-target="#referralModal">
            <i class="fa fa-plus"></i> <?php echo app('translator')->get('Add Referral'); ?>
          </button>
        </div>
      </div>

      <div class="card-body p-0">
        <div class="table-responsive--sm table-responsive">
          <table class="table table--light style--two custom-data-table" id="referrals-table">
            <thead>
              <tr>
                <th><?php echo app('translator')->get('User'); ?></th>
                <th><?php echo app('translator')->get('Username'); ?></th>
                <th><?php echo app('translator')->get('IB Type'); ?></th>
                <th><?php echo app('translator')->get('MT5 Account'); ?></th>
                <th><?php echo app('translator')->get('Total Deposits'); ?></th>
                <th><?php echo app('translator')->get('Joined At'); ?></th>
                <th><?php echo app('translator')->get('Status'); ?></th>
                <th><?php echo app('translator')->get('Action'); ?></th>
              </tr>
            </thead>
            <tbody>
              <?php $__empty_1 = true; $__currentLoopData = $directReferralsPaginated; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $referral): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr>
                  <td>
                    <span class="fw-bold"><?php echo e($referral->fullname); ?></span>
                    <br><span class="small text-muted"><?php echo e($referral->email); ?></span>
                  </td>
                  <td><?php echo e($referral->username); ?></td>
                  <td>
                    <?php if($referral->ib_status == 1): ?>
                      <span class="badge badge--success">
                        <i class="fas fa-crown"></i> <?php echo e(ucfirst($referral->ib_type ?? 'master')); ?> IB
                      </span>
                      <?php if($referral->ib_parent_id): ?>
                        <br><small class="text-muted">Under: <?php echo e($referral->ib_parent_id == $user->id ? 'This User' : 'Other IB'); ?></small>
                      <?php endif; ?>
                    <?php else: ?>
                      <span class="badge badge--info">
                        <i class="fas fa-user"></i> Client
                      </span>
                    <?php endif; ?>
                  </td>
                  <td>
                    <?php if($referral->mt5_login): ?>
                      <strong class="text-primary"><?php echo e($referral->mt5_login); ?></strong>
                      <br><small class="text-muted"><?php echo e($referral->mt5_group ?? 'N/A'); ?></small>
                      <?php if($referral->mt5_balance): ?>
                        <br><small class="text-success">$<?php echo e(number_format($referral->mt5_balance, 2)); ?></small>
                      <?php endif; ?>
                    <?php else: ?>
                      <span class="text-muted">No MT5 Account</span>
                    <?php endif; ?>
                  </td>
                  <td>
                    <?php
                      // FIXED: Use pre-loaded deposits to prevent N+1 queries
                      $totalDeposits = $referral->deposits ? $referral->deposits->sum('amount') : 0;
                      $depositCount = $referral->deposits ? $referral->deposits->count() : 0;
                    ?>
                    <strong class="text-info">$<?php echo e(number_format($totalDeposits, 2)); ?></strong>
                    <br><small class="text-muted"><?php echo e($depositCount); ?> deposits</small>
                  </td>
                  <td><?php echo e(showDateTime($referral->created_at)); ?></td>
                  <td>
                    <?php if($referral->status == 1): ?>
                      <span class="badge badge--success">Active</span>
                    <?php else: ?>
                      <span class="badge badge--warning">Inactive</span>
                    <?php endif; ?>
                  </td>
                  <td>
                    <div class="dropdown">
                      <button class="btn btn--primary btn--shadow btn--sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v"></i>
                      </button>
                      <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="<?php echo e(route('admin.users.detail', $referral->id)); ?>">
                          <i class="las la-desktop"></i> View Details
                        </a></li>
                        <?php if($referral->ib_status == 1): ?>
                          <li><a class="dropdown-item" href="<?php echo e(route('admin.users.detail', $referral->id)); ?>#network-tab-pane">
                            <i class="fas fa-sitemap"></i> View Network
                          </a></li>
                        <?php endif; ?>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="#" onclick="removeReferral(<?php echo e($referral->id); ?>)">
                          <i class="fas fa-unlink"></i> Remove Referral
                        </a></li>
                      </ul>
                    </div>
                  </td>
                </tr>
              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                  <td colspan="8" class="text-center text-muted py-4">
                    <i class="las la-users" style="font-size: 2rem;"></i>
                    <br><?php echo app('translator')->get('No direct referrals found'); ?>
                    <br><small>Use the "Add Referral" button to start building your network</small>
                  </td>
                </tr>
              <?php endif; ?>
            </tbody>
          </table>
        </div>
      </div>
      <div class="card-footer py-4" id="referrals-pagination">
        <!-- AJAX Pagination will be loaded here -->
        <div class="d-flex justify-content-between align-items-center">
          <div class="pagination-info">
            <span class="text-muted">
              Showing <?php echo e($directReferralsPaginated->firstItem() ?? 0); ?> to <?php echo e($directReferralsPaginated->lastItem() ?? 0); ?>

              of <?php echo e($directReferralsPaginated->total()); ?> direct referrals
            </span>
          </div>
          <div class="pagination-controls">
            <?php if($directReferralsPaginated->hasPages()): ?>
              <nav aria-label="Referrals pagination">
                <ul class="pagination pagination-sm mb-0">
                  
                  <?php if($directReferralsPaginated->onFirstPage()): ?>
                    <li class="page-item disabled"><span class="page-link"><i class="las la-angle-left"></i></span></li>
                  <?php else: ?>
                    <li class="page-item">
                      <a class="page-link" href="#" onclick="loadReferralsPage(<?php echo e($directReferralsPaginated->currentPage() - 1); ?>)">
                        <i class="las la-angle-left"></i>
                      </a>
                    </li>
                  <?php endif; ?>

                  
                  <?php $__currentLoopData = $directReferralsPaginated->getUrlRange(1, $directReferralsPaginated->lastPage()); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $page => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($page == $directReferralsPaginated->currentPage()): ?>
                      <li class="page-item active"><span class="page-link"><?php echo e($page); ?></span></li>
                    <?php else: ?>
                      <li class="page-item">
                        <a class="page-link" href="#" onclick="loadReferralsPage(<?php echo e($page); ?>)"><?php echo e($page); ?></a>
                      </li>
                    <?php endif; ?>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                  
                  <?php if($directReferralsPaginated->hasMorePages()): ?>
                    <li class="page-item">
                      <a class="page-link" href="#" onclick="loadReferralsPage(<?php echo e($directReferralsPaginated->currentPage() + 1); ?>)">
                        <i class="las la-angle-right"></i>
                      </a>
                    </li>
                  <?php else: ?>
                    <li class="page-item disabled"><span class="page-link"><i class="las la-angle-right"></i></span></li>
                  <?php endif; ?>
                </ul>
              </nav>
            <?php endif; ?>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="modal fade" id="referralModal" tabindex="-1" aria-labelledby="addReferralModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content add-referral-modal">
      <!-- Header -->
      <div class="modal-header add-referral-header">
        <h5 class="modal-title add-referral-title" id="addReferralModalLabel">Add Direct Referral</h5>
        <button type="button" class="btn-close add-referral-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>

      <!-- Body -->
      <div class="modal-body add-referral-body">
        <div class="add-referral-user-details">
          <p class="add-referral-user-name"><?php echo e($user->fullname); ?></p>
          <p class="add-referral-question">Add a direct referral for this user</p>
        </div>

        <form action="<?php echo e(route('admin.users.referral.add', $user->id)); ?>" method="post">
          <?php echo csrf_field(); ?>

          <!-- ISSUE 3 FIX: Only Select Users and MT5 Account search -->
          <div class="mb-3">
            <label class="form-label">Search Method:</label>
            <div class="btn-group w-100" role="group">
              <input type="radio" class="btn-check" name="search_method" id="search_by_user" value="user" checked>
              <label class="btn btn-outline-primary" for="search_by_user">Select Users</label>

              <input type="radio" class="btn-check" name="search_method" id="search_by_mt5" value="mt5">
              <label class="btn btn-outline-primary" for="search_by_mt5">MT5 Account</label>
            </div>
          </div>

          <!-- Hidden input for selected user ID -->
          <input type="hidden" name="referral_user" id="selected_user_id" value="">

          <!-- ISSUE 2 FIX: Enhanced User Selection with Real-time Search -->
          <div class="mb-3 search-section" id="user-search-section">
            <label for="user_search_input" class="form-label">🔍 Search Users:</label>
            <input type="text" id="user_search_input" class="form-control" placeholder="Type name, email, or MT5 ID to search users..." style="font-size: 12px;">

            <!-- Real-time user search results -->
            <div id="user-search-results" class="mt-2" style="display: none;">
              <div class="alert alert-info" style="font-size: 12px;">
                <strong>Search Results:</strong>
                <div id="user-search-results-content"></div>
              </div>
            </div>

            <!-- Fallback dropdown for manual selection -->
            <div class="mt-2">
              <label for="addReferralUser" class="form-label">Or select from dropdown:</label>
              <select id="addReferralUser" class="form-control" style="font-size: 12px;">
                <option value="" disabled selected>Choose a user</option>
                <?php $__currentLoopData = $allUsers->take(100); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $u): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <option value="<?php echo e($u->id); ?>"
                          data-name="<?php echo e($u->fullname); ?>"
                          data-email="<?php echo e($u->email); ?>"
                          data-mt5="<?php echo e($u->mt5_login); ?>"
                          data-group="<?php echo e($u->mt5_group); ?>">
                    <?php echo e($u->fullname); ?> (<?php echo e($u->email); ?>)
                    <?php if($u->mt5_login): ?> - MT5: <?php echo e($u->mt5_login); ?> <?php endif; ?>
                  </option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </select>
              <small class="text-muted">Showing first 100 users. Use search above for better results.</small>
            </div>
          </div>

          <!-- ISSUE 2 FIX: Enhanced MT5 Account Search with AJAX -->
          <div class="mb-3 search-section" id="mt5-search-section" style="display: none;">
            <div class="alert alert-info">
              <strong>MT5 Account Search</strong><br>
              <small>Search and select MT5 accounts by account number. Only MT5 login IDs will be displayed.</small>
            </div>

            <label for="mt5_search" class="form-label">🔍 Search MT5 Account Numbers:</label>
            <input type="text" id="mt5_search" class="form-control" placeholder="Type MT5 account number (e.g., 878046, 311112)" style="font-size: 16px; padding: 10px;">

            <!-- Real-time search results -->
            <div id="mt5-search-results" class="mt-2" style="display: none;">
              <div class="alert alert-success">
                <strong>Search Results:</strong>
                <div id="mt5-search-results-content"></div>
              </div>
            </div>

            <!-- All MT5 Accounts List -->
            <div class="mt-3">
              <label class="form-label">📋 All Available MT5 Accounts:</label>
              <div class="mt5-accounts-list" style="max-height: 300px; overflow-y: auto; border: 2px solid #007bff; padding: 15px; border-radius: 8px; background-color: #f8f9fa;">
                <div class="text-center text-primary">
                  <i class="fas fa-spinner fa-spin fa-2x"></i><br>
                  <strong>Loading MT5 accounts...</strong>
                </div>
              </div>
            </div>
          </div>



          <!-- Selected User Display -->
          <div id="selected-user-display" class="mb-3" style="display: none;">
            <div class="alert alert-info">
              <strong>Selected User:</strong>
              <div id="selected-user-info"></div>
            </div>
          </div>

          <!-- Sub-IB Assignment Option - Always visible -->
          <div class="mb-3">
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="assign_as_sub_ib" name="assign_as_sub_ib" value="1">
              <label class="form-check-label" for="assign_as_sub_ib">
                <strong>Assign as Sub-IB</strong>
                <small class="text-muted d-block">Check this to assign the selected user as a Sub-IB under this Master IB</small>
              </label>
            </div>
          </div>

          <!-- ISSUE 3 FIX: Footer Buttons with proper functionality -->
          <div class="modal-footer add-referral-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            <button type="submit" class="btn btn--primary" id="add-referral-submit">Add Referral</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>



<?php if (isset($component)) { $__componentOriginalbd5922df145d522b37bf664b524be380 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbd5922df145d522b37bf664b524be380 = $attributes; } ?>
<?php $component = App\View\Components\ConfirmationModal::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('confirmation-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\ConfirmationModal::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbd5922df145d522b37bf664b524be380)): ?>
<?php $attributes = $__attributesOriginalbd5922df145d522b37bf664b524be380; ?>
<?php unset($__attributesOriginalbd5922df145d522b37bf664b524be380); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbd5922df145d522b37bf664b524be380)): ?>
<?php $component = $__componentOriginalbd5922df145d522b37bf664b524be380; ?>
<?php unset($__componentOriginalbd5922df145d522b37bf664b524be380); ?>
<?php endif; ?>


<?php $__env->startPush('style'); ?>
<style>
/* ISSUE 1 FIX: Font Size Standardization - 12px maximum for entire modal */
#referralModal * {
    font-size: 12px !important;
}

#referralModal .modal-title {
    font-size: 12px !important;
    font-weight: bold;
}

#referralModal .form-label {
    font-size: 12px !important;
    font-weight: 600;
}

#referralModal .form-control {
    font-size: 12px !important;
    padding: 6px 8px !important;
}

#referralModal .btn {
    font-size: 12px !important;
    padding: 6px 12px !important;
}

#referralModal .alert {
    font-size: 12px !important;
    padding: 8px 12px !important;
}

#referralModal .mt5-account-item {
    font-size: 12px !important;
}

#referralModal .mt5-search-result-item {
    font-size: 12px !important;
}

#referralModal small {
    font-size: 10px !important;
}

#referralModal .select2-container .select2-selection {
    font-size: 12px !important;
    min-height: 32px !important;
}

#referralModal .select2-container .select2-results__option {
    font-size: 12px !important;
    padding: 4px 8px !important;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script'); ?>
  <script>
    console.log('🔧 Direct Referral Script Loading...');

    $(document).ready(function () {
      console.log('🔧 Direct Referral Script Ready!');

      // PHASE 4 ENHANCEMENT: Enhanced search functionality

      // Initialize Select2 for user selection
      function initializeUserSelect() {
        $('#addReferralUser').select2({
          width: '100%',
          placeholder: 'Search by name, email, or MT5 ID...',
          allowClear: true,
          matcher: function(params, data) {
            if ($.trim(params.term) === '') return data;

            if (data.element) {
              let name = $(data.element).data('name') || '';
              let email = $(data.element).data('email') || '';
              let mt5 = $(data.element).data('mt5') || '';
              let searchTerm = params.term.toLowerCase();

              if (name.toLowerCase().includes(searchTerm) ||
                  email.toLowerCase().includes(searchTerm) ||
                  mt5.toString().includes(searchTerm)) {
                return data;
              }
            }
            return null;
          }
        });
      }

      initializeUserSelect();

      // ISSUE 2 FIX: Enhanced real-time user search functionality
      $('#user_search_input').on('input', function() {
        const searchTerm = $(this).val().trim();

        if (searchTerm.length >= 2) {
          // Show search results section
          $('#user-search-results').show();
          $('#user-search-results-content').html('<i class="fas fa-spinner fa-spin"></i> Searching users...');

          // Make AJAX call for real-time user search
          $.ajax({
            url: '<?php echo e(route("admin.users.search")); ?>',
            method: 'GET',
            data: {
              search: searchTerm,
              limit: 20
            },
            headers: {
              'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
              console.log('🔧 User search response:', response);

              if (response.success && response.users && response.users.length > 0) {
                let html = '';
                response.users.forEach(function(user) {
                  html += `
                    <div class="user-search-result-item" style="padding: 6px; border: 1px solid #007bff; border-radius: 4px; margin-bottom: 3px; cursor: pointer; background-color: #f8f9fa; font-size: 12px;"
                         data-user-id="${user.id}" data-user-name="${user.fullname}"
                         data-user-email="${user.email}" data-mt5-login="${user.mt5_login || ''}" data-mt5-group="${user.mt5_group || ''}">
                      <strong>👤 ${user.fullname}</strong>
                      <small class="text-muted d-block">📧 ${user.email}</small>
                      ${user.mt5_login ? `<small class="text-info d-block">🏦 MT5: ${user.mt5_login}</small>` : ''}
                    </div>
                  `;
                });
                $('#user-search-results-content').html(html);

                // Add click handlers for user search results
                $('.user-search-result-item').click(function() {
                  $('.user-search-result-item, .mt5-search-result-item, .mt5-account-item').removeClass('bg-primary text-white');
                  $(this).addClass('bg-primary text-white');

                  const userData = {
                    id: $(this).data('user-id'),
                    name: $(this).data('user-name'),
                    email: $(this).data('user-email'),
                    mt5_login: $(this).data('mt5-login'),
                    mt5_group: $(this).data('mt5-group')
                  };

                  // Set both form fields
                  $('#selected_user_id').val(userData.id);
                  $('input[name="referral_user"]').val(userData.id);
                  displaySelectedUser(userData);
                  $('#sub-ib-assignment').show();

                  console.log('🔧 User selected from search:', userData);
                });
              } else {
                $('#user-search-results-content').html('<div class="text-warning" style="font-size: 12px;">No users found for: ' + searchTerm + '</div>');
              }
            },
            error: function(xhr, status, error) {
              console.error('❌ User search error:', error);
              $('#user-search-results-content').html('<div class="text-danger" style="font-size: 12px;">Error searching users. Please try again.</div>');
            }
          });
        } else if (searchTerm.length === 0) {
          $('#user-search-results').hide();
        }
      });

      // ISSUE 2 & 3 FIX: Handle search method switching with proper loading
      $('input[name="search_method"]').change(function() {
        const method = $(this).val();
        console.log('🔧 Search method changed to:', method);

        $('.search-section').hide();
        $('#selected-user-display').hide();
        $('#sub-ib-assignment').hide();

        // Clear previous selections
        $('#addReferralUser').val('').trigger('change');
        $('#mt5_search').val('');
        $('#selected_user_id').val('');

        // Show appropriate section
        if (method === 'user') {
          console.log('🔧 Showing user search section');
          $('#user-search-section').show();
        } else if (method === 'mt5') {
          console.log('🔧 Showing MT5 search section and loading accounts');
          $('#mt5-search-section').show();
          loadAllMT5Accounts(); // Load MT5 accounts when tab is selected
        }
      });

      // ISSUE 2 FIX: Enhanced MT5 accounts loading with better error handling
      function loadAllMT5Accounts() {
        console.log('🔧 Loading MT5 accounts...');

        $('.mt5-accounts-list').html(`
          <div class="text-center text-primary">
            <i class="fas fa-spinner fa-spin fa-2x"></i><br>
            <strong>Loading MT5 accounts...</strong>
          </div>
        `);

        const ajaxUrl = '<?php echo e(route("admin.users.mt5.accounts.all")); ?>';
        console.log('🔧 AJAX URL:', ajaxUrl);

        $.ajax({
          url: ajaxUrl,
          type: 'GET',
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
          },
          success: function(response) {
            console.log('✅ MT5 Accounts Response:', response);

            if (response.success && response.accounts && response.accounts.length > 0) {
              let html = `<div class="alert alert-success mb-2"><strong>${response.accounts.length} MT5 Accounts Available</strong></div>`;

              response.accounts.forEach(function(account) {
                html += `
                  <div class="mt5-account-item" style="padding: 10px; border: 1px solid #007bff; border-radius: 6px; margin-bottom: 5px; cursor: pointer; background-color: #f8f9fa; transition: all 0.3s;"
                       data-user-id="${account.user_id}" data-mt5-login="${account.mt5_login}"
                       data-mt5-group="${account.mt5_group}" data-user-name="${account.user_name}"
                       onmouseover="this.style.backgroundColor='#e3f2fd'" onmouseout="this.style.backgroundColor='#f8f9fa'">
                    <strong>🏦 MT5: ${account.mt5_login}</strong>
                    <small class="text-muted d-block">📊 Group: ${account.mt5_group || 'N/A'}</small>
                    <small class="text-info d-block">👤 User: ${account.user_name}</small>
                  </div>
                `;
              });
              $('.mt5-accounts-list').html(html);

              // Add click handlers for MT5 account selection
              $('.mt5-account-item').click(function() {
                $('.mt5-account-item, .mt5-search-result-item').removeClass('bg-primary text-white');
                $(this).addClass('bg-primary text-white');

                const userData = {
                  id: $(this).data('user-id'),
                  name: $(this).data('user-name'),
                  mt5_login: $(this).data('mt5-login'),
                  mt5_group: $(this).data('mt5-group'),
                  email: 'MT5 Account: ' + $(this).data('mt5-login')
                };

                // ISSUE 3 FIX: Set both form fields for MT5 account selection
                $('#selected_user_id').val(userData.id);
                $('input[name="referral_user"]').val(userData.id);
                displaySelectedUser(userData);
                $('#sub-ib-assignment').show();

                console.log('MT5 Account Selected from list:', userData);

                console.log('MT5 Account Selected:', userData);
              });
            } else {
              $('.mt5-accounts-list').html(`
                <div class="alert alert-warning text-center">
                  <i class="fas fa-exclamation-triangle"></i><br>
                  <strong>No MT5 accounts found</strong><br>
                  <small>Please check if users have MT5 accounts configured.</small>
                </div>
              `);
            }
          },
          error: function(xhr, status, error) {
            console.error('MT5 Accounts Loading Error:', error);
            $('.mt5-accounts-list').html(`
              <div class="alert alert-danger text-center">
                <i class="fas fa-exclamation-circle"></i><br>
                <strong>Error loading MT5 accounts</strong><br>
                <small>Please try again or contact support.</small><br>
                <button class="btn btn-sm btn-outline-danger mt-2" onclick="loadAllMT5Accounts()">Retry</button>
              </div>
            `);
          }
        });
      }

      // ISSUE 2 FIX: Real-time AJAX MT5 search functionality
      $('#mt5_search').on('input', function() {
        const searchTerm = $(this).val().trim();

        if (searchTerm.length >= 3) {
          // Show search results section
          $('#mt5-search-results').show();
          $('#mt5-search-results-content').html('<i class="fas fa-spinner fa-spin"></i> Searching...');

          // Make AJAX call for real-time search
          $.ajax({
            url: '<?php echo e(route("admin.users.search.mt5")); ?>',
            method: 'POST',
            data: {
              mt5_id: searchTerm,
              _token: '<?php echo e(csrf_token()); ?>'
            },
            success: function(response) {
              if (response.success && response.users.length > 0) {
                let html = '';
                response.users.forEach(function(user) {
                  html += `
                    <div class="mt5-search-result-item" style="padding: 8px; border: 1px solid #28a745; border-radius: 4px; margin-bottom: 5px; cursor: pointer; background-color: #d4edda;"
                         data-user-id="${user.id}" data-mt5-login="${user.mt5_login}"
                         data-mt5-group="${user.mt5_group}" data-user-name="${user.user_name}">
                      <strong>🎯 MT5: ${user.mt5_login}</strong>
                      <small class="text-muted d-block">Group: ${user.mt5_group || 'N/A'}</small>
                    </div>
                  `;
                });
                $('#mt5-search-results-content').html(html);

                // Add click handlers for search results
                $('.mt5-search-result-item').click(function() {
                  $('.mt5-search-result-item, .mt5-account-item').removeClass('bg-primary text-white');
                  $(this).addClass('bg-primary text-white');

                  const userData = {
                    id: $(this).data('user-id'),
                    name: $(this).data('user-name'),
                    mt5_login: $(this).data('mt5-login'),
                    mt5_group: $(this).data('mt5-group'),
                    email: 'MT5 Search Result: ' + $(this).data('mt5-login')
                  };

                  // ISSUE 3 FIX: Set both form fields for MT5 search result selection
                  $('#selected_user_id').val(userData.id);
                  $('input[name="referral_user"]').val(userData.id);
                  displaySelectedUser(userData);
                  $('#sub-ib-assignment').show();

                  console.log('MT5 Search Result Selected:', userData);
                });
              } else {
                $('#mt5-search-results-content').html('<div class="text-warning">No MT5 accounts found for: ' + searchTerm + '</div>');
              }
            },
            error: function() {
              $('#mt5-search-results-content').html('<div class="text-danger">Error searching MT5 accounts. Please try again.</div>');
            }
          });
        } else if (searchTerm.length >= 1) {
          // Filter visible MT5 accounts in the main list
          $('.mt5-account-item').each(function() {
            const mt5Login = $(this).data('mt5-login').toString().toLowerCase();
            if (mt5Login.includes(searchTerm.toLowerCase())) {
              $(this).show();
            } else {
              $(this).hide();
            }
          });
          $('#mt5-search-results').hide();
        } else {
          // Show all accounts if search is empty
          $('.mt5-account-item').show();
          $('#mt5-search-results').hide();
        }
      });



      // ISSUE 3 FIX: Enhanced user selection handler with validation
      $('#addReferralUser').change(function() {
        const selectedOption = $(this).find('option:selected');
        if (selectedOption.val()) {
          const userId = selectedOption.val();

          // Set BOTH hidden input fields with validation
          $('#selected_user_id').val(userId);
          $('input[name="referral_user"]').val(userId);

          console.log('🔧 User selected from dropdown:', userId);
          console.log('🔧 Both fields set - selected_user_id:', $('#selected_user_id').val());
          console.log('🔧 Both fields set - referral_user:', $('input[name="referral_user"]').val());

          displaySelectedUser({
            id: userId,
            name: selectedOption.data('name'),
            email: selectedOption.data('email'),
            mt5_login: selectedOption.data('mt5'),
            mt5_group: selectedOption.data('group')
          });

          // Show Sub-IB assignment option
          $('#sub-ib-assignment').show();
        } else {
          $('#selected_user_id').val('');
          $('input[name="referral_user"]').val('');
          $('#sub-ib-assignment').hide();
          console.log('🔧 User deselected - fields cleared');
        }
      });

      // Search functions
      function searchByMT5(mt5Id) {
        $('#mt5-search-results').html('<div class="text-muted"><i class="fas fa-spinner fa-spin"></i> Searching...</div>');

        // PHASE 4 FIX: Make actual AJAX call to search MT5 users
        $.ajax({
          url: '<?php echo e(route("admin.users.search.mt5")); ?>',
          method: 'POST',
          data: {
            mt5_id: mt5Id,
            _token: '<?php echo e(csrf_token()); ?>'
          },
          success: function(response) {
            if (response.success && response.users.length > 0) {
              let html = '<div class="alert alert-success"><strong>Users Found:</strong><br>';
              response.users.forEach(function(user) {
                html += `
                  <div class="border-bottom pb-2 mb-2">
                    <strong>${user.name}</strong> (${user.email})<br>
                    <small>MT5: ${user.mt5_login} | Group: ${user.mt5_group || 'N/A'}</small><br>
                    <button type="button" class="btn btn-sm btn--primary mt-1" onclick="selectFoundUser(${user.id}, '${user.name}', '${user.email}', '${user.mt5_login}', '${user.mt5_group || ''}')">
                      Select This User
                    </button>
                  </div>
                `;
              });
              html += '</div>';
              $('#mt5-search-results').html(html);
            } else {
              $('#mt5-search-results').html('<div class="alert alert-warning">No users found with MT5 ID: ' + mt5Id + '</div>');
            }
          },
          error: function() {
            $('#mt5-search-results').html('<div class="alert alert-danger">Error searching for users. Please try again.</div>');
          }
        });
      }



      // ISSUE 3 FIX: Enhanced user display function
      function displaySelectedUser(userData) {
        $('#selected-user-info').html(`
          <strong>${userData.name}</strong><br>
          Email: ${userData.email}<br>
          ${userData.mt5_login ? `MT5 Login: ${userData.mt5_login}<br>` : ''}
          ${userData.mt5_group ? `MT5 Group: ${userData.mt5_group}` : ''}
        `);
        $('#selected-user-display').show();

        // Button is always enabled - no disabled state needed
        $('#add-referral-submit').prop('disabled', false);
      }

      // ISSUE 3 FIX: Enhanced global function for selecting found users
      window.selectFoundUser = function(userId, userName, userEmail, mt5Login, mt5Group) {
        // Set both hidden inputs for form submission
        $('#selected_user_id').val(userId);
        $('input[name="referral_user"]').val(userId);

        console.log('User selected via search:', userId);
        console.log('Form field values set:', {
          selected_user_id: $('#selected_user_id').val(),
          referral_user: $('input[name="referral_user"]').val()
        });

        // Display selected user info
        displaySelectedUser({
          id: userId,
          name: userName,
          email: userEmail,
          mt5_login: mt5Login,
          mt5_group: mt5Group
        });

        // Show Sub-IB assignment option
        $('#sub-ib-assignment').show();

        // Clear search results
        $('#mt5-search-results').empty();
      };

      // ISSUE 1 FIX: Enhanced form validation and debugging - ONLY for referral forms
      $('form[action*="referral.add"]').on('submit', function(e) {
        const selectedUserId = $('#selected_user_id').val();
        const referralUserField = $('input[name="referral_user"]').val();

        // Debug logging
        console.log('🔧 Form submission debug:');
        console.log('  - Selected User ID:', selectedUserId);
        console.log('  - Referral User Field:', referralUserField);
        console.log('  - Form data:', $(this).serialize());

        // Force set the field value if it's empty but selectedUserId has value
        if (selectedUserId && (!referralUserField || referralUserField === '')) {
          console.log('🔧 Force setting referral_user field to:', selectedUserId);
          $('input[name="referral_user"]').val(selectedUserId);
        }

        // Final validation
        const finalReferralUser = $('input[name="referral_user"]').val();
        if (!finalReferralUser || finalReferralUser === '') {
          e.preventDefault();
          alert('❌ Please select a user before adding referral.\n\nDebug info:\n- Selected User ID: ' + selectedUserId + '\n- Referral User Field: ' + finalReferralUser);
          return false;
        }

        // Show loading state on submit button
        $('#add-referral-submit').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Adding...');

        // Final debug
        console.log('✅ Form validation passed. Final form data:', $(this).serialize());
      });

      // ISSUE 3 FIX: Complete form reset when modal is closed
      $('#referralModal').on('hidden.bs.modal', function() {
        // Clear all form fields
        $('#selected_user_id').val('');
        $('input[name="referral_user"]').val('');
        $('#addReferralUser').val('').trigger('change');
        $('#mt5_search').val('');
        $('#mt5-search-results').hide().empty();
        $('.mt5-accounts-list').empty();
        $('#selected-user-display').hide();
        $('#sub-ib-assignment').hide();
        $('#assign_as_sub_ib').prop('checked', false);
        $('#add-referral-submit').prop('disabled', false).html('Add Referral');
        $('.search-section').hide();
        $('#user-search-section').show();
        $('input[name="search_method"][value="user"]').prop('checked', true);
        $('.mt5-account-item, .mt5-search-result-item').removeClass('bg-primary text-white');

        console.log('Modal reset completed');
      });

    }); // Close $(document).ready()

    // PART 1: Enhanced Direct Referral Management Functions

    // Refresh referrals list
    function refreshReferralsList() {
      console.log('🔄 Refreshing referrals list...');
      location.reload();
    }

    // Remove referral functionality
    function removeReferral(referralId) {
      if (confirm('Are you sure you want to remove this referral relationship?')) {
        console.log('🗑️ Removing referral:', referralId);

        $.ajax({
          url: '<?php echo e(route("admin.users.referral.remove", $user->id)); ?>',
          method: 'POST',
          data: {
            referral_id: referralId,
            _token: '<?php echo e(csrf_token()); ?>'
          },
          success: function(response) {
            if (response.success) {
              alert('Referral removed successfully');
              refreshReferralsList();
            } else {
              alert('Error: ' + response.message);
            }
          },
          error: function() {
            alert('Error removing referral. Please try again.');
          }
        });
      }
    }

    // AJAX Pagination for Direct Referrals - FIXED
    function loadReferralsPage(page) {
      const userId = <?php echo e($user->id); ?>;
      const loadingHtml = `
        <tr>
          <td colspan="7" class="text-center py-4">
            <i class="fas fa-spinner fa-spin"></i> Loading referrals...
          </td>
        </tr>
      `;

      // Show loading state
      $('#referrals-table tbody').html(loadingHtml);

      // Make AJAX request to the correct route
      fetch(`<?php echo e(route('admin.users.referrals.paginated', $user->id)); ?>?page=${page}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
          'X-Requested-With': 'XMLHttpRequest'
        }
      })
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        if (data.success) {
          // Update table body
          $('#referrals-table tbody').html(data.html);

          // Update pagination
          $('#referrals-pagination').html(data.pagination);
        } else {
          showError(data.message || 'Failed to load referrals');
        }
      })
      .catch(error => {
        console.error('Referrals pagination error:', error);
        showError('Network error loading referrals');
      });
    }

    function showError(message) {
      $('tbody').html(`
        <tr>
          <td colspan="9" class="text-center text-danger py-4">
            <i class="fas fa-exclamation-triangle"></i> ${message}
          </td>
        </tr>
      `);
    }
  </script>
<?php $__env->stopPush(); ?><?php /**PATH C:\xampp\htdocs\mbf.mybrokerforex.com-31052025\resources\views/components/user-detail/referral.blade.php ENDPATH**/ ?>