<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Visual Builder Content Loading Test</title>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-container { max-width: 1200px; margin: 0 auto; }
        .debug-output { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto; }
        .visual-builder-container { border: 2px solid #dc3545; min-height: 400px; margin: 20px 0; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .status.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .status.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .status.warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Visual Builder Content Loading Test</h1>
        
        <div id="status-container">
            <div class="status warning">
                <strong>🔄 Initializing test...</strong>
            </div>
        </div>
        
        <h2>📊 Debug Output</h2>
        <div id="debug-output" class="debug-output">
            <div>🚀 Starting Visual Builder content loading test...</div>
        </div>
        
        <h2>🎨 Visual Builder Interface</h2>
        <div class="visual-builder-container">
            <!-- Visual Builder will be initialized here -->
        </div>
        
        <h2>📝 Test Controls</h2>
        <button onclick="testContentLoading()" style="background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">Test Content Loading</button>
        <button onclick="showDebugInfo()" style="background: #6c757d; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">Show Debug Info</button>
    </div>

    <script>
    // Simulate server data (like what comes from Laravel)
    window.serverTemplateData = {
        content: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to MBFX</title>
</head>
<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4; line-height: 1.6;">
    <table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color: #f4f4f4;">
        <tr>
            <td align="center">
                <table width="600" cellpadding="0" cellspacing="0" border="0" style="background-color: #ffffff; box-shadow: 0 0 10px rgba(0,0,0,0.1);">
                    
                    <!-- Header Section -->
                    <tr>
                        <td style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); padding: 30px 40px; text-align: center;">
                            <img src="https://mbf.mybrokerforex.com/assets/images/logoIcon/logo.png" alt="MBFX Logo" style="max-width: 150px; height: auto;">
                        </td>
                    </tr>

                    <!-- Title Section -->
                    <tr>
                        <td style="background-color: #ffffff; padding: 30px 40px 20px; text-align: center;">
                            <h2 style="margin: 0; color: #dc3545; font-size: 28px; font-weight: bold;">Welcome to MBFX</h2>
                        </td>
                    </tr>

                    <!-- Main Content Section -->
                    <tr>
                        <td style="background-color: #ffffff; padding: 20px 40px; color: #333333;">
                            <p>Dear {{fullname}},</p>
                            <p>Welcome to {{site_name}}! Your account has been successfully created and is ready to use.</p>
                            <ul>
                                <li><strong>Username:</strong> {{username}}</li>
                                <li><strong>Email:</strong> {{email}}</li>
                                <li><strong>Registration Date:</strong> {{registration_date}}</li>
                            </ul>
                            <p>You can now access all our trading features and services.</p>
                        </td>
                    </tr>

                    <!-- Footer Section -->
                    <tr>
                        <td style="background-color: #000000; padding: 30px 40px; text-align: center; color: #ffffff;">
                            <p style="margin: 0 0 10px 0; font-size: 18px; font-weight: bold;">MBFX - Professional Trading Platform</p>
                            <p style="margin: 0; font-size: 12px; color: #999999;">© 2025 MBFX. All rights reserved.</p>
                        </td>
                    </tr>

                </table>
            </td>
        </tr>
    </table>
</body>
</html>`,
        tokens: [
            { token: '{{fullname}}', description: 'Full name of the user', category: 'User' },
            { token: '{{username}}', description: 'Username', category: 'User' },
            { token: '{{email}}', description: 'Email address', category: 'User' },
            { token: '{{site_name}}', description: 'Site name', category: 'Site' },
            { token: '{{registration_date}}', description: 'Registration date', category: 'Date' }
        ]
    };
    
    // Capture console output
    const originalLog = console.log;
    const originalError = console.error;
    const originalWarn = console.warn;
    const debugOutput = document.getElementById('debug-output');
    
    function addToDebug(type, message) {
        const div = document.createElement('div');
        div.style.color = type === 'error' ? 'red' : type === 'warn' ? 'orange' : 'black';
        div.style.marginBottom = '2px';
        div.textContent = `[${type.toUpperCase()}] ${message}`;
        debugOutput.appendChild(div);
        debugOutput.scrollTop = debugOutput.scrollHeight;
    }
    
    console.log = function(...args) {
        originalLog.apply(console, args);
        addToDebug('log', args.join(' '));
    };
    
    console.error = function(...args) {
        originalError.apply(console, args);
        addToDebug('error', args.join(' '));
    };
    
    console.warn = function(...args) {
        originalWarn.apply(console, args);
        addToDebug('warn', args.join(' '));
    };
    
    function updateStatus(type, message) {
        const statusContainer = document.getElementById('status-container');
        statusContainer.innerHTML = `<div class="status ${type}"><strong>${message}</strong></div>`;
    }
    
    function testContentLoading() {
        console.log('🔧 Manual content loading test triggered...');
        
        if (typeof window.initializeEnhancedTemplateEditor === 'function') {
            console.log('✅ Enhanced template editor function found');
            window.initializeEnhancedTemplateEditor();
            updateStatus('success', '✅ Enhanced template editor initialized');
        } else {
            console.error('❌ Enhanced template editor function not found');
            updateStatus('error', '❌ Enhanced template editor function not found');
        }
    }
    
    function showDebugInfo() {
        console.log('📊 DEBUG INFO:');
        console.log('- Server template data available:', typeof window.serverTemplateData !== 'undefined');
        console.log('- Template data available:', typeof window.templateData !== 'undefined');
        console.log('- VisualBuilderEmailEditor class:', typeof VisualBuilderEmailEditor !== 'undefined');
        console.log('- Enhanced init function:', typeof window.initializeEnhancedTemplateEditor !== 'undefined');
        
        if (window.templateData) {
            console.log('- Template content length:', window.templateData.content ? window.templateData.content.length : 0);
            console.log('- Template tokens count:', window.templateData.tokens ? window.templateData.tokens.length : 0);
        }
        
        if (window.visualBuilderInstance) {
            console.log('- Visual Builder instance exists:', !!window.visualBuilderInstance);
            console.log('- Visual Builder content:', window.visualBuilderInstance.content ? window.visualBuilderInstance.content.length + ' chars' : 'no content');
        }
    }
    
    // Test initialization
    console.log('🔧 Starting Visual Builder content loading test...');
    console.log('📊 Server data available:', typeof window.serverTemplateData !== 'undefined');
    console.log('📊 Template content length:', window.serverTemplateData.content.length);
    console.log('📊 Tokens count:', window.serverTemplateData.tokens.length);
    
    updateStatus('warning', '🔄 Ready for testing - Click "Test Content Loading" button');
    </script>
    
    <!-- Load the Visual Builder JavaScript -->
    <script src="assets/admin/js/visual-builder-email-editor.js"></script>
</body>
</html>
