<?php
/**
 * VISUAL BUILDER INTERFACE DIAGNOSTIC
 * This script analyzes the web interface layer specifically
 */

// Include Laravel bootstrap
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🔍 VISUAL BUILDER INTERFACE DIAGNOSTIC\n";
echo "======================================\n\n";

// 1. FORM STRUCTURE ANALYSIS
echo "1️⃣ FORM STRUCTURE ANALYSIS\n";
echo "===========================\n";

try {
    // Read the edit template file
    $editTemplate = file_get_contents('resources/views/admin/notification/edit.blade.php');
    
    // Check form configuration
    $formAction = 'route(\'admin.setting.notification.template.update\',$template->id)';
    $hasFormAction = str_contains($editTemplate, $formAction);
    echo "✅ Form action configured: " . ($hasFormAction ? 'YES' : 'NO') . "\n";
    
    // Check CSRF token
    $hasCsrf = str_contains($editTemplate, '@csrf');
    echo "✅ CSRF token present: " . ($hasCsrf ? 'YES' : 'NO') . "\n";
    
    // Check form method
    $hasPostMethod = str_contains($editTemplate, 'method="post"');
    echo "✅ POST method configured: " . ($hasPostMethod ? 'YES' : 'NO') . "\n";
    
    // Check critical form fields
    $criticalFields = [
        'name="subject"' => 'Subject field',
        'name="email_body"' => 'Email body field',
        'name="email_body_final"' => 'Email body final field',
        'name="original_email_body"' => 'Original email body field',
        'name="sms_body"' => 'SMS body field',
        'name="email_status"' => 'Email status field'
    ];
    
    echo "\n📊 Form Fields Analysis:\n";
    foreach ($criticalFields as $field => $description) {
        $present = str_contains($editTemplate, $field);
        echo "   - {$description}: " . ($present ? '✅ PRESENT' : '❌ MISSING') . "\n";
    }
    
    // Check submit button
    $hasSubmitButton = str_contains($editTemplate, 'type="submit"');
    echo "\n✅ Submit button present: " . ($hasSubmitButton ? 'YES' : 'NO') . "\n";
    
    // Check button ID for JavaScript binding
    $hasButtonId = str_contains($editTemplate, 'id="update-template-btn"');
    echo "✅ Button ID for JS binding: " . ($hasButtonId ? 'YES' : 'NO') . "\n";
    
} catch (\Exception $e) {
    echo "❌ Form structure analysis error: " . $e->getMessage() . "\n";
}

echo "\n";

// 2. JAVASCRIPT INTEGRATION ANALYSIS
echo "2️⃣ JAVASCRIPT INTEGRATION ANALYSIS\n";
echo "===================================\n";

try {
    // Check if Visual Builder JS file exists
    $jsFile = 'assets/admin/js/visual-builder-email-editor.js';
    if (file_exists($jsFile)) {
        $jsContent = file_get_contents($jsFile);
        $jsSize = filesize($jsFile);
        echo "✅ Visual Builder JS file: EXISTS ({$jsSize} bytes)\n";
        
        // Check for critical JavaScript functions
        $jsFunctions = [
            'visualBuilderInstance' => 'Visual Builder instance',
            'getContent()' => 'Get content function',
            'setContent(' => 'Set content function',
            'prepareFormSubmission' => 'Form submission preparation',
            'update-template-btn' => 'Button event binding',
            'email_body_final' => 'Final content field handling'
        ];
        
        echo "\n📊 JavaScript Functions Analysis:\n";
        foreach ($jsFunctions as $function => $description) {
            $present = str_contains($jsContent, $function);
            echo "   - {$description}: " . ($present ? '✅ PRESENT' : '❌ MISSING') . "\n";
        }
        
        // Check for error handling
        $hasErrorHandling = str_contains($jsContent, 'catch') || str_contains($jsContent, 'error');
        echo "\n✅ Error handling present: " . ($hasErrorHandling ? 'YES' : 'NO') . "\n";
        
        // Check for console logging
        $hasLogging = str_contains($jsContent, 'console.log');
        echo "✅ Console logging present: " . ($hasLogging ? 'YES' : 'NO') . "\n";
        
    } else {
        echo "❌ Visual Builder JS file: NOT FOUND\n";
    }
    
    // Check if JS file is included in the template
    $jsIncluded = str_contains($editTemplate, 'visual-builder-email-editor.js');
    echo "✅ JS file included in template: " . ($jsIncluded ? 'YES' : 'NO') . "\n";
    
} catch (\Exception $e) {
    echo "❌ JavaScript analysis error: " . $e->getMessage() . "\n";
}

echo "\n";

// 3. ROUTE & CONTROLLER ANALYSIS
echo "3️⃣ ROUTE & CONTROLLER ANALYSIS\n";
echo "===============================\n";

try {
    // Check if route exists
    $routeExists = \Route::has('admin.setting.notification.template.update');
    echo "✅ Update route exists: " . ($routeExists ? 'YES' : 'NO') . "\n";
    
    if ($routeExists) {
        $route = \Route::getRoutes()->getByName('admin.setting.notification.template.update');
        echo "✅ Route URI: " . $route->uri() . "\n";
        echo "✅ Route methods: " . implode(', ', $route->methods()) . "\n";
        
        // Check route parameters
        $parameterNames = $route->parameterNames();
        echo "✅ Route parameters: " . (empty($parameterNames) ? 'NONE' : implode(', ', $parameterNames)) . "\n";
    }
    
    // Check controller method exists
    $controllerClass = 'App\Http\Controllers\Admin\NotificationController';
    $methodExists = method_exists($controllerClass, 'templateUpdate');
    echo "✅ Controller method exists: " . ($methodExists ? 'YES' : 'NO') . "\n";
    
    // Check controller file
    $controllerFile = 'app/Http/Controllers/Admin/NotificationController.php';
    if (file_exists($controllerFile)) {
        $controllerSize = filesize($controllerFile);
        echo "✅ Controller file: EXISTS ({$controllerSize} bytes)\n";
        
        // Check for validation rules
        $controllerContent = file_get_contents($controllerFile);
        $hasValidation = str_contains($controllerContent, 'validate(') || str_contains($controllerContent, 'Validator::');
        echo "✅ Validation present: " . ($hasValidation ? 'YES' : 'NO') . "\n";
        
        // Check for logging
        $hasLogging = str_contains($controllerContent, 'Log::') || str_contains($controllerContent, '\Log::');
        echo "✅ Logging present: " . ($hasLogging ? 'YES' : 'NO') . "\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Route/Controller analysis error: " . $e->getMessage() . "\n";
}

echo "\n";

// 4. SESSION & CSRF ANALYSIS
echo "4️⃣ SESSION & CSRF ANALYSIS\n";
echo "===========================\n";

try {
    // Check session configuration
    $sessionDriver = config('session.driver');
    $sessionLifetime = config('session.lifetime');
    echo "✅ Session driver: {$sessionDriver}\n";
    echo "✅ Session lifetime: {$sessionLifetime} minutes\n";
    
    // Check if sessions are working
    $sessionId = session()->getId();
    echo "✅ Session ID: " . (strlen($sessionId) > 0 ? 'PRESENT' : 'MISSING') . "\n";
    
    // Test CSRF token generation
    $csrfToken = csrf_token();
    echo "✅ CSRF token: " . (strlen($csrfToken) > 0 ? 'GENERATED' : 'FAILED') . "\n";
    
    // Check session storage
    $sessionPath = config('session.files');
    if ($sessionPath && is_dir($sessionPath)) {
        $writable = is_writable($sessionPath);
        echo "✅ Session storage writable: " . ($writable ? 'YES' : 'NO') . "\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Session/CSRF analysis error: " . $e->getMessage() . "\n";
}

echo "\n";

// 5. TEMPLATE LOADING ANALYSIS
echo "5️⃣ TEMPLATE LOADING ANALYSIS\n";
echo "=============================\n";

try {
    // Test template loading
    $template = \App\Models\NotificationTemplate::find(1);
    if ($template) {
        echo "✅ Template loaded: ID {$template->id}\n";
        echo "✅ Template subject: {$template->subj}\n";
        echo "✅ Template email_body length: " . strlen($template->email_body) . "\n";
        
        // Check if template has required fields
        $requiredFields = ['subj', 'email_body', 'sms_body'];
        foreach ($requiredFields as $field) {
            $hasValue = !empty($template->$field);
            echo "✅ Field '{$field}' has value: " . ($hasValue ? 'YES' : 'NO') . "\n";
        }
        
        // Check template timestamps
        echo "✅ Created at: {$template->created_at}\n";
        echo "✅ Updated at: {$template->updated_at}\n";
        
        // Check if template is recently modified
        $lastModified = $template->updated_at;
        $minutesAgo = now()->diffInMinutes($lastModified);
        echo "✅ Last modified: {$minutesAgo} minutes ago\n";
        
        if ($minutesAgo > 60) {
            echo "⚠️  POTENTIAL ISSUE: Template hasn't been updated recently\n";
        }
        
    } else {
        echo "❌ Template ID 1 not found\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Template loading analysis error: " . $e->getMessage() . "\n";
}

echo "\n📋 INTERFACE DIAGNOSTIC COMPLETE\n";
echo "=================================\n";
echo "This diagnostic focuses on the web interface layer:\n";
echo "1. Form structure and field configuration\n";
echo "2. JavaScript integration and event handling\n";
echo "3. Route and controller connectivity\n";
echo "4. Session and CSRF token functionality\n";
echo "5. Template loading and data integrity\n\n";

echo "🔍 Run this to identify interface-specific issues.\n";

?>
