<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SymbolGroup;
use App\Models\Symbol;

class SymbolGroupSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $symbolGroups = [
            [
                'name' => 'Crypto Majors',
                'description' => 'Major cryptocurrency pairs',
                'symbols' => ['BTCUSD', 'ETHUSD', 'LTCUSD']
            ],
            [
                'name' => 'Indices.C',
                'description' => 'Cash indices',
                'symbols' => ['AUS200', 'DJ30', 'EUSTX50', 'FRA40', 'FTSE100', 'GER40', 'NAS100', 'RUSS2000', 'SP500']
            ],
            [
                'name' => 'Indices.V',
                'description' => 'Variable spread indices',
                'symbols' => ['AUS200', 'DJ30', 'EUSTX50', 'FRA40', 'FTSE100', 'NAS100', 'RUSS2000', 'SP500', 'GER40']
            ],
            [
                'name' => 'Indices.P',
                'description' => 'Premium indices',
                'symbols' => ['AUS200', 'DJ30', 'EUSTX50', 'FRA40', 'FTSE100', 'GER40', 'NAS100', 'RUSS2000', 'SP500']
            ],
            [
                'name' => 'Commodities.C',
                'description' => 'Cash commodities',
                'symbols' => ['XPDUSD', 'XPTUSD', 'USOIL', 'XNGUSD', 'GOLDUSD', 'SILVERUSD']
            ],
            [
                'name' => 'Commodities.V',
                'description' => 'Variable spread commodities',
                'symbols' => ['XPDUSD', 'XPTUSD', 'USOIL', 'XNGUSD', 'GOLDUSD', 'SILVERUSD']
            ],
            [
                'name' => 'Commodities.P',
                'description' => 'Premium commodities',
                'symbols' => ['XPDUSD', 'XPTUSD', 'USOIL', 'XNGUSD', 'GOLDUSD', 'SILVERUSD']
            ],
            [
                'name' => 'Metals CFD',
                'description' => 'Metal CFDs',
                'symbols' => ['XPDUSD', 'XPTUSD']
            ],
            [
                'name' => 'Energies.C',
                'description' => 'Cash energy commodities',
                'symbols' => ['USOIL', 'XNGUSD']
            ],
            [
                'name' => 'Energies.V',
                'description' => 'Variable spread energy commodities',
                'symbols' => ['USOIL', 'XNGUSD']
            ],
            [
                'name' => 'Forex.P',
                'description' => 'Premium forex pairs',
                'symbols' => ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'USDCAD', 'AUDUSD', 'NZDUSD']
            ],
            [
                'name' => 'Forex.V',
                'description' => 'Variable spread forex pairs',
                'symbols' => ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'USDCAD', 'AUDUSD', 'NZDUSD']
            ],
            [
                'name' => 'Forex.C',
                'description' => 'Cash forex pairs',
                'symbols' => ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'USDCAD', 'AUDUSD', 'NZDUSD']
            ]
        ];

        foreach ($symbolGroups as $groupData) {
            // Get symbol IDs
            $symbolIds = Symbol::whereIn('symbol', $groupData['symbols'])->pluck('id')->toArray();
            
            $symbolGroup = SymbolGroup::updateOrCreate(
                ['name' => $groupData['name']],
                [
                    'description' => $groupData['description'],
                    'symbols_json' => $symbolIds,
                    'status' => true
                ]
            );
            
            $this->command->info("Created symbol group: {$groupData['name']} with " . count($symbolIds) . " symbols");
        }

        $this->command->info('Symbol groups seeded successfully!');
    }
}
