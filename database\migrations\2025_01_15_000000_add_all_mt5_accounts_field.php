<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add field to store all MT5 accounts for this email (for comprehensive search)
            $table->text('all_mt5_accounts')->nullable()->after('mt5_synced_at')
                  ->comment('Comma-separated list of all MT5 accounts for this email address');
            
            // Add index for better search performance
            $table->index(['email', 'mt5_login'], 'idx_email_mt5_search');
            $table->index('all_mt5_accounts', 'idx_all_mt5_accounts');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('idx_email_mt5_search');
            $table->dropIndex('idx_all_mt5_accounts');
            $table->dropColumn('all_mt5_accounts');
        });
    }
};
