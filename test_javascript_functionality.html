<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript Functionality Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .test-container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-section { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn-outline--primary { background: white; color: #007bff; border: 1px solid #007bff; }
        .btn-outline--secondary { background: white; color: #6c757d; border: 1px solid #6c757d; }
        .btn--primary { background: #dc3545; color: white; border: 1px solid #dc3545; }
        .btn.active { background: #dc3545 !important; color: white !important; border-color: #dc3545 !important; }
        .visual-editor-panel, .html-editor-panel { padding: 20px; border: 1px solid #ddd; margin: 10px 0; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .hidden { display: none; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 JavaScript Functionality Test</h1>
        <p>This page tests the JavaScript functionality of the email template editor.</p>

        <!-- Test 1: Button Toggle Functionality -->
        <div class="test-section">
            <h3>Test 1: Editor Toggle Buttons</h3>
            <div class="editor-toggle-buttons">
                <button type="button" class="btn btn-outline--primary" id="visual-editor-btn">
                    Visual Editor
                </button>
                <button type="button" class="btn btn-outline--secondary" id="html-editor-btn">
                    HTML Editor
                </button>
            </div>
            
            <div id="visual-email-builder" class="visual-editor-panel">
                <h4>Visual Editor Panel</h4>
                <p>This is the visual editor interface.</p>
            </div>
            
            <div id="html-editor-panel" class="html-editor-panel hidden">
                <h4>HTML Editor Panel</h4>
                <textarea rows="5" style="width: 100%;">This is the HTML editor interface.</textarea>
            </div>
            
            <div id="toggle-test-result" class="test-result"></div>
        </div>

        <!-- Test 2: Component Addition -->
        <div class="test-section">
            <h3>Test 2: Component Addition</h3>
            <button class="btn btn-outline--primary" onclick="testAddComponent('text')">Add Text Component</button>
            <button class="btn btn-outline--primary" onclick="testAddComponent('button')">Add Button Component</button>
            <div id="component-test-result" class="test-result"></div>
            <div id="component-output" style="border: 1px solid #ddd; padding: 10px; margin: 10px 0; min-height: 50px;"></div>
        </div>

        <!-- Test 3: Preview Functionality -->
        <div class="test-section">
            <h3>Test 3: Preview Functionality</h3>
            <button class="btn btn-outline--primary" onclick="testPreview()">Test Preview</button>
            <div id="preview-test-result" class="test-result"></div>
        </div>

        <!-- Test 4: Shortcode Functionality -->
        <div class="test-section">
            <h3>Test 4: Shortcode Functionality</h3>
            <div class="shortcode-tags">
                <span class="shortcode-tag" data-shortcode="{{fullname}}" onclick="testShortcode(this)">{{fullname}}</span>
                <span class="shortcode-tag" data-shortcode="{{username}}" onclick="testShortcode(this)">{{username}}</span>
                <span class="shortcode-tag" data-shortcode="{{message}}" onclick="testShortcode(this)">{{message}}</span>
            </div>
            <div id="shortcode-test-result" class="test-result"></div>
        </div>

        <!-- Overall Test Results -->
        <div class="test-section">
            <h3>📊 Overall Test Results</h3>
            <button class="btn btn--primary" onclick="runAllJSTests()">Run All JavaScript Tests</button>
            <div id="overall-results" class="test-result"></div>
        </div>
    </div>

    <script>
        let testResults = [];
        let currentMode = 'visual';

        // Test 1: Toggle Functionality
        function switchToVisualEditor() {
            currentMode = 'visual';
            $('#visual-editor-btn').addClass('active').removeClass('btn-outline--primary').addClass('btn--primary');
            $('#html-editor-btn').removeClass('active').removeClass('btn--primary').addClass('btn-outline--secondary');
            
            $('#visual-email-builder').show().removeClass('hidden');
            $('#html-editor-panel').hide().addClass('hidden');
            
            logTest('Toggle to Visual Editor', true, 'Successfully switched to visual editor');
        }

        function switchToHtmlEditor() {
            currentMode = 'html';
            $('#html-editor-btn').addClass('active').removeClass('btn-outline--secondary').addClass('btn--primary');
            $('#visual-editor-btn').removeClass('active').removeClass('btn--primary').addClass('btn-outline--primary');
            
            $('#html-editor-panel').show().removeClass('hidden');
            $('#visual-email-builder').hide().addClass('hidden');
            
            logTest('Toggle to HTML Editor', true, 'Successfully switched to HTML editor');
        }

        // Event handlers for toggle buttons
        $('#visual-editor-btn').on('click', function(e) {
            e.preventDefault();
            switchToVisualEditor();
            updateToggleTestResult();
        });

        $('#html-editor-btn').on('click', function(e) {
            e.preventDefault();
            switchToHtmlEditor();
            updateToggleTestResult();
        });

        function updateToggleTestResult() {
            const resultDiv = $('#toggle-test-result');
            resultDiv.removeClass('pass fail').addClass('pass');
            resultDiv.html(`<strong>PASS:</strong> Toggle functionality working. Current mode: ${currentMode}`);
        }

        // Test 2: Component Addition
        function testAddComponent(componentType) {
            let componentHTML = '';
            
            switch(componentType) {
                case 'text':
                    componentHTML = `
                        <div style="padding: 20px; font-family: Arial, sans-serif;">
                            <p style="margin: 0; color: #333;">Dear {{fullname}},</p>
                            <div style="margin: 20px 0; color: #333;">{{message}}</div>
                        </div>
                    `;
                    break;
                case 'button':
                    componentHTML = `
                        <div style="padding: 20px; text-align: center;">
                            <a href="#" style="background: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px;">
                                Click Here
                            </a>
                        </div>
                    `;
                    break;
            }
            
            $('#component-output').append(componentHTML);
            
            const resultDiv = $('#component-test-result');
            resultDiv.removeClass('pass fail').addClass('pass');
            resultDiv.html(`<strong>PASS:</strong> ${componentType} component added successfully`);
            
            logTest(`Add ${componentType} Component`, true, `${componentType} component added to output`);
        }

        // Test 3: Preview Functionality
        function testPreview() {
            try {
                const testHTML = '<div style="padding: 20px;"><h3>Test Email</h3><p>This is a test email preview.</p></div>';
                const previewWindow = window.open('', '_blank', 'width=600,height=400');
                
                const fullHTML = `
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>Email Preview Test</title>
                        <style>body { font-family: Arial, sans-serif; margin: 20px; }</style>
                    </head>
                    <body>${testHTML}</body>
                    </html>
                `;
                
                previewWindow.document.write(fullHTML);
                previewWindow.document.close();
                
                const resultDiv = $('#preview-test-result');
                resultDiv.removeClass('pass fail').addClass('pass');
                resultDiv.html('<strong>PASS:</strong> Preview window opened successfully');
                
                logTest('Preview Functionality', true, 'Preview window opened and content loaded');
                
                // Close preview window after 3 seconds
                setTimeout(() => {
                    if (previewWindow && !previewWindow.closed) {
                        previewWindow.close();
                    }
                }, 3000);
                
            } catch (error) {
                const resultDiv = $('#preview-test-result');
                resultDiv.removeClass('pass fail').addClass('fail');
                resultDiv.html(`<strong>FAIL:</strong> Preview failed - ${error.message}`);
                
                logTest('Preview Functionality', false, `Preview failed: ${error.message}`);
            }
        }

        // Test 4: Shortcode Functionality
        function testShortcode(element) {
            const shortcode = $(element).data('shortcode');
            
            try {
                // Test clipboard functionality
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(shortcode).then(() => {
                        showShortcodeSuccess(shortcode);
                    });
                } else {
                    // Fallback method
                    const textArea = document.createElement('textarea');
                    textArea.value = shortcode;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    showShortcodeSuccess(shortcode);
                }
            } catch (error) {
                const resultDiv = $('#shortcode-test-result');
                resultDiv.removeClass('pass fail').addClass('fail');
                resultDiv.html(`<strong>FAIL:</strong> Shortcode copy failed - ${error.message}`);
                
                logTest('Shortcode Copy', false, `Failed to copy ${shortcode}: ${error.message}`);
            }
        }

        function showShortcodeSuccess(shortcode) {
            const resultDiv = $('#shortcode-test-result');
            resultDiv.removeClass('pass fail').addClass('pass');
            resultDiv.html(`<strong>PASS:</strong> Shortcode "${shortcode}" copied to clipboard`);
            
            logTest('Shortcode Copy', true, `Successfully copied ${shortcode} to clipboard`);
        }

        // Utility function to log test results
        function logTest(testName, passed, message) {
            testResults.push({
                test: testName,
                passed: passed,
                message: message,
                timestamp: new Date().toLocaleString()
            });
        }

        // Run all tests
        function runAllJSTests() {
            testResults = []; // Clear previous results
            
            // Test 1: Toggle functionality
            switchToVisualEditor();
            setTimeout(() => switchToHtmlEditor(), 500);
            setTimeout(() => switchToVisualEditor(), 1000);
            
            // Test 2: Component addition
            setTimeout(() => testAddComponent('text'), 1500);
            setTimeout(() => testAddComponent('button'), 2000);
            
            // Test 3: Preview (user will see popup)
            setTimeout(() => testPreview(), 2500);
            
            // Generate final results
            setTimeout(() => {
                const passed = testResults.filter(r => r.passed).length;
                const total = testResults.length;
                const passRate = total > 0 ? ((passed / total) * 100).toFixed(1) : 0;
                
                const resultDiv = $('#overall-results');
                resultDiv.removeClass('pass fail').addClass(passRate >= 80 ? 'pass' : 'fail');
                resultDiv.html(`
                    <h4>JavaScript Test Summary</h4>
                    <p><strong>Tests Run:</strong> ${total}</p>
                    <p><strong>Passed:</strong> ${passed}</p>
                    <p><strong>Pass Rate:</strong> ${passRate}%</p>
                    <p><strong>Status:</strong> ${passRate >= 80 ? '✅ EXCELLENT' : '⚠️ NEEDS IMPROVEMENT'}</p>
                `);
            }, 3500);
        }

        // Initialize with visual editor
        $(document).ready(function() {
            switchToVisualEditor();
            
            // Add some styling for shortcode tags
            $('.shortcode-tag').css({
                'display': 'inline-block',
                'background': '#dc3545',
                'color': 'white',
                'padding': '4px 8px',
                'margin': '2px',
                'border-radius': '3px',
                'cursor': 'pointer',
                'font-size': '12px'
            });
        });
    </script>
</body>
</html>
