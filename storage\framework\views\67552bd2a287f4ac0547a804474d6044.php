<div class="sidebar-menu">
    <div class="sidebar-menu__inner">
        <span class="sidebar-menu__close d-xl-none d-block"><i class="fas fa-times"></i></span>
        <div class="sidebar-logo">
            <a href="<?php echo e(route('user.home')); ?>" class="sidebar-logo__link">
                <img src="<?php echo e(siteLogo()); ?>">
            </a>
        </div>
        <ul class="sidebar-menu-list">
            <li class="sidebar-menu-list__item ">
                <a href="<?php echo e(route('user.home')); ?>" class="sidebar-menu-list__link <?php echo e(menuActive('user.home')); ?>">
                    <span class="icon"><span class="icon-dashboard"></span></span>
                    <span class="text"><?php echo app('translator')->get('Dashboard'); ?></span>
                </a>
            </li>
            <!--<li class="sidebar-menu-list__item ">-->
            <!--    <a href="<?php echo e(url('/ibform/')); ?>" class="sidebar-menu-list__link <?php echo e(url('/ibform/')); ?>">-->
            <!--        <span class="icon"><span class="icon-affiliation"></span></span>-->
            <!--        <span class="text"><?php echo app('translator')->get('Become an IB'); ?></span>-->
            <!--    </a>-->
            <!--</li>-->
            <li class="sidebar-menu-list__item ">
                <a href="<?php echo e(route('user.order.open')); ?>"
                    class="sidebar-menu-list__link <?php echo e(menuActive('user.order.*')); ?> ">
                    <span class="icon"><span class="icon-order"></span></span>
                    <span class="text"><?php echo app('translator')->get('Manage Trades'); ?></span>
                </a>
            </li>

            <?php
                $user = auth()->user();
            ?>


            <?php if($user->kv == 1): ?>
                
                <li class="sidebar-menu-list__item ">
                    <a href="<?php echo e(route('user.account-type-index')); ?>"
                        class="sidebar-menu-list__link <?php echo e(menuActive('user.account-type-index*')); ?> ">
                        <span class="icon"><span class="las la-user-plus"></span></span>
                        <span class="text"><?php echo app('translator')->get('New Account'); ?></span>
                    </a>
                </li>
            <?php else: ?>
                <li class="sidebar-menu-list__item ">
                    <a href="<?php echo e(route('admin.kycVerification')); ?>"
                        class="sidebar-menu-list__link <?php echo e(menuActive('user.account-type-index*')); ?> ">
                        <span class="icon"><span class="las la-user-plus"></span></span>
                        <span class="text"><?php echo app('translator')->get('New Account'); ?></span>
                    </a>
                </li>
            <?php endif; ?>

            <li class="sidebar-menu-list__item ">
                <a href="<?php echo e(route('user.user-accounts')); ?>"
                    class="sidebar-menu-list__link <?php echo e(menuActive('user.user-accounts*')); ?> ">
                    <span class="icon"><span class="las la-users"></span></span>
                    <span class="text"><?php echo app('translator')->get('My Accounts'); ?></span>
                </a>
            </li>

            <li class="sidebar-menu-list__item ">
                <a href="<?php echo e(route('user.trade.history')); ?>"
                    class="sidebar-menu-list__link <?php echo e(menuActive('user.trade.history')); ?> ">
                    <span class="icon"><span class="icon-trade"></span></span>
                    <span class="text"><?php echo app('translator')->get('Trade History'); ?></span>
                </a>
            </li>

            <!-- Copy Trading Nav -->

            <li class="sidebar-menu-list__item">
                <a href="javascript:void(0)" class="sidebar-menu-list__link <?php echo e(menuActive('user.copy-trading')); ?>">
                    <span class="icon"><span class="las la-clone"></span></span>
                    <span class="text"><?php echo app('translator')->get('Copy Trading'); ?></span>
                    <span class="arrow"><i class="fas fa-angle-down"></i></span> <!-- Dropdown arrow -->
                </a>
                <ul class="submenu">
                    <li class="submenu-item">
                        <a href="<?php echo e(route('user.copy-trading')); ?>"
                            class="sidebar-menu-list__link <?php echo e(menuActive('user.copy-trading')); ?>">
                            <span class="icon"><span class="las la-dot-circle"></span></span>
                            <span class="text"><?php echo app('translator')->get('Follower Access'); ?></span>
                        </a>
                    </li>
                    <li class="submenu-item">
                        <a href="<?php echo e(route('user.follower-access')); ?>"
                            class="sidebar-menu-list__link <?php echo e(menuActive('user.follower-access.follower-access')); ?>">
                            <span class="icon"><span class="las la-dot-circle"></span></span>
                            <span class="text"><?php echo app('translator')->get('Provider Access'); ?></span>
                        </a>
                    </li>
                    <li class="submenu-item">
                        <a href="<?php echo e(route('user.ratings')); ?>"
                            class="sidebar-menu-list__link <?php echo e(menuActive('user.ratings.ratings')); ?>">
                            <span class="icon"><span class="las la-dot-circle"></span></span>
                            <span class="text"><?php echo app('translator')->get('Ratings'); ?></span>
                        </a>
                    </li>
                </ul>
            </li>
            <!-- copy trading nav ends -->

            

            <?php if($user->kv == 1): ?>
                
                <?php if($user->isIb()): ?>
                    
                    <li class="sidebar-menu-list__item">
                        <a href="<?php echo e(route('user.partnership.network')); ?>"
                            class="sidebar-menu-list__link <?php echo e(menuActive('user.partnership.*')); ?>">
                            <span class="icon"><span class="lar la-handshake"></span></span>
                            <span class="text"><?php echo app('translator')->get('Partnership'); ?></span>
                        </a>
                    </li>
                <?php elseif($user->ib_status == 'rejected' || $user->partner == 3): ?>
                    
                    <li class="sidebar-menu-list__item">
                        <a href="<?php echo e(route('user.rejected_user_ib')); ?>"
                            class="sidebar-menu-list__link <?php echo e(menuActive('user.user_become_ib')); ?>">
                            <span class="icon"><span class="lar la-handshake"></span></span>
                            <span class="text"><?php echo app('translator')->get('Partnership'); ?></span>
                        </a>
                    </li>
                <?php elseif($user->ib_status == 'pending' || $user->partner == 2): ?>
                    
                    <li class="sidebar-menu-list__item">
                        <a href="<?php echo e(route('user.pending_user_ib')); ?>"
                            class="sidebar-menu-list__link <?php echo e(menuActive('user.user_become_ib')); ?>">
                            <span class="icon"><span class="lar la-handshake"></span></span>
                            <span class="text"><?php echo app('translator')->get('Partnership'); ?></span>
                        </a>
                    </li>
                <?php else: ?>
                    
                    <li class="sidebar-menu-list__item">
                        <a href="<?php echo e(route('user.user_become_ib')); ?>"
                            class="sidebar-menu-list__link <?php echo e(menuActive('user.user_become_ib')); ?>">
                            <span class="icon"><span class="lar la-handshake"></span></span>
                            <span class="text"><?php echo app('translator')->get('Become an IB'); ?></span>
                        </a>
                    </li>
                <?php endif; ?>
            <?php else: ?>
                
                <li class="sidebar-menu-list__item">
                    <a href="<?php echo e(route('admin.kycVerification')); ?>"
                        class="sidebar-menu-list__link <?php echo e(menuActive('user.user_become_ib')); ?>">
                        <span class="icon"><span class="lar la-handshake"></span></span>
                        <span class="text"><?php echo app('translator')->get('Partnership'); ?></span>
                    </a>
                </li>
            <?php endif; ?>

            

            <li class="sidebar-menu-list__item ">
                <a href="<?php echo e(route('user.walletOverview')); ?>"
                    class="sidebar-menu-list__link <?php echo e(menuActive('user.wallet.*')); ?>">
                    <span class="icon"><span class="icon-wallet"></span></span>
                    <span class="text"><?php echo app('translator')->get('Wallet'); ?></span>
                </a>
            </li>

            <li class="sidebar-menu-list__item ">
                <a href="<?php echo e(route('user.deposit')); ?>"
                    class="sidebar-menu-list__link <?php echo e(menuActive('user.deposit.*')); ?>">
                    <span class="icon"><span class="icon-deposit"></span></span>
                    <span class="text"><?php echo app('translator')->get('Deposit'); ?></span>
                </a>
            </li>

            <li class="sidebar-menu-list__item ">
                <a href="<?php echo e(route('user.withdraw')); ?>"
                    class="sidebar-menu-list__link <?php echo e(menuActive('user.withdraw')); ?>">
                    <span class="icon"><span class="icon-withdraw"></span></span>
                    <span class="text"><?php echo app('translator')->get('Withdraw '); ?></span>
                </a>
            </li>

            <li class="sidebar-menu-list__item ">
                <a href="<?php echo e(route('user.transfer')); ?>"
                    class="sidebar-menu-list__link <?php echo e(menuActive('user.transfer')); ?>">
                    <span class="icon"><span class="las la-exchange-alt"></span></span>
                    <span class="text"><?php echo app('translator')->get('Transfer'); ?></span>
                </a>
            </li>


            <!-- <li class="sidebar-menu-list__item ">
                <a href="<?php echo e(route('user.referrals')); ?>"
                    class="sidebar-menu-list__link <?php echo e(menuActive('user.referrals')); ?>">
                    <span class="icon"><span class="icon-affiliation"></span></span>
                    <span class="text"><?php echo app('translator')->get('My Affiliation'); ?></span>
                </a>
            </li> -->
            <li class="sidebar-menu-list__item ">
                <a href="<?php echo e(route('user.transactions')); ?>"
                    class="sidebar-menu-list__link <?php echo e(menuActive('user.transactions')); ?>">
                    <span class="icon"><span class="icon-transaction"></span></span>
                    <span class="text"><?php echo app('translator')->get('Transaction Histoy'); ?></span>
                </a>
            </li>
            <li class="sidebar-menu-list__item ">
                <a href="<?php echo e(route('ticket.index')); ?>" class="sidebar-menu-list__link <?php echo e(menuActive('ticket.*')); ?>">
                    <span class="icon"><span class="icon-support"></span></span>
                    <span class="text"><?php echo app('translator')->get('Get Support'); ?></span>
                </a>
            </li>
            <li class="sidebar-menu-list__item ">
                <a href="<?php echo e(route('user.twofactor')); ?>"
                    class="sidebar-menu-list__link <?php echo e(menuActive('user.twofactor')); ?>">
                    <span class="icon"><span class="icon-security"></span></span>
                    <span class="text"><?php echo app('translator')->get('Security'); ?></span>
                </a>
            </li>
            <li class="sidebar-menu-list__item ">
                <a href="<?php echo e(route('user.logout')); ?>" class="sidebar-menu-list__link">
                    <span class="icon"><span class="icon-logout"></span></span>
                    <span class="text"><?php echo app('translator')->get('Logout'); ?></span>
                </a>
            </li>
        </ul>
    </div>
</div>
<?php $__env->startPush('style'); ?>
<style>
/* Enhanced Sidebar Active State Styling */
.sidebar-menu-list__item .sidebar-menu-list__link.active,
.sidebar-menu-list__item .sidebar-menu-list__link:hover {
    background-color: rgb(220, 53, 69) !important;
    color: white !important;
    border-radius: 5px;
}

.sidebar-menu-list__item .sidebar-menu-list__link.active .icon,
.sidebar-menu-list__item .sidebar-menu-list__link:hover .icon {
    color: white !important;
}

.sidebar-menu-list__item .sidebar-menu-list__link.active .text,
.sidebar-menu-list__item .sidebar-menu-list__link:hover .text {
    color: white !important;
}

/* Submenu styling */
.submenu .sidebar-menu-list__link.active {
    background-color: rgba(220, 53, 69, 0.8) !important;
    margin-left: 10px;
}

/* Transfer menu specific styling */
.sidebar-menu-list__item a[href*="transfer"].active {
    background-color: rgb(220, 53, 69) !important;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script'); ?>
    <script>
        if ($('li').hasClass('active')) {
            $('#sidebar__menuWrapper').animate({
                scrollTop: eval($(".active").offset().top - 320)
            }, 500);
        }
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const dropdownItems = document.querySelectorAll('.sidebar-menu-list__item');

            dropdownItems.forEach(item => {
                item.addEventListener('click', function (e) {
                    // Prevent toggling if a submenu link is clicked
                    if (e.target.closest('.submenu-link')) return;

                    // Toggle active class
                    dropdownItems.forEach(otherItem => {
                        if (otherItem !== item) {
                            otherItem.classList.remove('active');
                        }
                    });
                    item.classList.toggle('active');
                });
            });

            // Ensure active menu items are properly highlighted
            const currentUrl = window.location.href;
            const menuLinks = document.querySelectorAll('.sidebar-menu-list__link');

            menuLinks.forEach(link => {
                if (currentUrl.includes(link.getAttribute('href'))) {
                    link.classList.add('active');
                    // Also add active class to parent li if it exists
                    const parentLi = link.closest('.sidebar-menu-list__item');
                    if (parentLi) {
                        parentLi.classList.add('active');
                    }
                }
            });
        });
    </script>
<?php $__env->stopPush(); ?><?php /**PATH C:\xampp\htdocs\mbf.mybrokerforex.com-31052025\resources\views/templates/basic/partials/user_sidebar.blade.php ENDPATH**/ ?>