@extends('admin.layouts.app')

@section('panel')
<div class="row">
    <div class="col-lg-12">
        <div class="card b-radius--10">
            <div class="card-header">
                <h5 class="card-title">@lang('IB Groups Management')</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive--sm table-responsive">
                    <table class="table table--light style--two">
                        <thead>
                            <tr>
                                <th>@lang('Name')</th>
                                <th>@lang('Description')</th>
                                <th>@lang('Commission Multiplier')</th>
                                <th>@lang('Max Levels')</th>
                                <th>@lang('Total IBs')</th>
                                <th>@lang('Status')</th>
                                <th>@lang('Action')</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($ibGroups as $group)
                            <tr>
                                <td>
                                    <strong>{{ $group->name }}</strong>
                                </td>
                                <td>{{ Str::limit($group->description, 50) ?? 'N/A' }}</td>
                                <td>
                                    <span class="badge badge--primary">{{ $group->commission_multiplier }}x</span>
                                </td>
                                <td>
                                    <span class="badge badge--info">{{ $group->max_levels }}</span>
                                </td>
                                <td>
                                    <span class="badge badge--success">{{ $group->users->count() }}</span>
                                </td>
                                <td>
                                    @if($group->status)
                                        <span class="badge badge--success">@lang('Active')</span>
                                    @else
                                        <span class="badge badge--danger">@lang('Inactive')</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="button--group">
                                        <a href="{{ route('admin.ib.groups.show', $group->id) }}" class="btn btn-sm btn-outline--primary">
                                            <i class="las la-eye"></i> @lang('View')
                                        </a>
                                        <a href="{{ route('admin.ib.groups.edit', $group->id) }}" class="btn btn-sm btn-outline--info">
                                            <i class="las la-edit"></i> @lang('Edit')
                                        </a>
                                        <form action="{{ route('admin.ib.groups.toggle_status', $group->id) }}" method="POST" style="display: inline;">
                                            @csrf
                                            <button type="submit" class="btn btn-sm btn-outline--{{ $group->status ? 'danger' : 'success' }}">
                                                @if($group->status)
                                                    <i class="las la-eye-slash"></i> @lang('Deactivate')
                                                @else
                                                    <i class="las la-eye"></i> @lang('Activate')
                                                @endif
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="7" class="text-center">@lang('No IB groups found')</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if($ibGroups->hasPages())
            <div class="card-footer">
                {{ $ibGroups->links() }}
            </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('breadcrumb-plugins')
<div class="d-inline">
    <a href="{{ route('admin.ib.groups.create') }}" class="btn btn--primary">
        <i class="las la-plus"></i> @lang('Create New Group')
    </a>
    <a href="{{ route('admin.ib.groups.statistics') }}" class="btn btn--info">
        <i class="las la-chart-bar"></i> @lang('View Statistics')
    </a>
</div>
@endpush
