{"__meta": {"id": "01JZ2KY9RS934EV2EKKQA5JT5E", "datetime": "2025-07-01 09:08:59", "utime": **********.802725, "method": "GET", "uri": "/mbf.mybrokerforex.com-********/admin/users/detail/53630", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.282396, "end": **********.802763, "duration": 5.520366907119751, "duration_str": "5.52s", "measures": [{"label": "Booting", "start": **********.282396, "relative_start": 0, "end": **********.774202, "relative_end": **********.774202, "duration": 0.****************, "duration_str": "492ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.774224, "relative_start": 0.*****************, "end": **********.802766, "relative_end": 3.0994415283203125e-06, "duration": 5.****************, "duration_str": "5.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.825871, "relative_start": 0.****************, "end": **********.833808, "relative_end": **********.833808, "duration": 0.007936954498291016, "duration_str": "7.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.462193, "relative_start": 5.***************, "end": **********.788996, "relative_end": **********.788996, "duration": 0.*****************, "duration_str": "327ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: admin.users.detail", "start": **********.467848, "relative_start": 5.***************, "end": **********.467848, "relative_end": **********.467848, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.user-detail.detail", "start": **********.485927, "relative_start": 5.**************, "end": **********.485927, "relative_end": **********.485927, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: pagination::bootstrap-4", "start": **********.57402, "relative_start": 5.***************, "end": **********.57402, "relative_end": **********.57402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.user-detail.account", "start": **********.575395, "relative_start": 5.***************, "end": **********.575395, "relative_end": **********.575395, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.confirmation-modal", "start": **********.579432, "relative_start": 5.****************, "end": **********.579432, "relative_end": **********.579432, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: pagination::bootstrap-4", "start": **********.581077, "relative_start": 5.***************, "end": **********.581077, "relative_end": **********.581077, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.user-detail.transaction", "start": **********.582239, "relative_start": 5.***************, "end": **********.582239, "relative_end": **********.582239, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.confirmation-modal", "start": **********.584176, "relative_start": 5.3017799854278564, "end": **********.584176, "relative_end": **********.584176, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: pagination::bootstrap-4", "start": **********.586413, "relative_start": 5.304016828536987, "end": **********.586413, "relative_end": **********.586413, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.user-detail.referral", "start": **********.587476, "relative_start": 5.305079936981201, "end": **********.587476, "relative_end": **********.587476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.confirmation-modal", "start": **********.603259, "relative_start": 5.3208630084991455, "end": **********.603259, "relative_end": **********.603259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.user-detail.network", "start": **********.605413, "relative_start": 5.323016881942749, "end": **********.605413, "relative_end": **********.605413, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.user-detail.tickets", "start": **********.609791, "relative_start": 5.327394962310791, "end": **********.609791, "relative_end": **********.609791, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.confirmation-modal", "start": **********.620062, "relative_start": 5.337666034698486, "end": **********.620062, "relative_end": **********.620062, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.user-detail.note", "start": **********.621549, "relative_start": 5.339152812957764, "end": **********.621549, "relative_end": **********.621549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.confirmation-modal", "start": **********.623417, "relative_start": 5.341020822525024, "end": **********.623417, "relative_end": **********.623417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.user-detail.security", "start": **********.624861, "relative_start": 5.342464923858643, "end": **********.624861, "relative_end": **********.624861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.confirmation-modal", "start": **********.626995, "relative_start": 5.344599008560181, "end": **********.626995, "relative_end": **********.626995, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin.layouts.app", "start": **********.642077, "relative_start": 5.359680891036987, "end": **********.642077, "relative_end": **********.642077, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin.partials.sidenav", "start": **********.731125, "relative_start": 5.448729038238525, "end": **********.731125, "relative_end": **********.731125, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin.partials.topnav", "start": **********.770379, "relative_start": 5.487982988357544, "end": **********.770379, "relative_end": **********.770379, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin.partials.breadcrumb", "start": **********.780126, "relative_start": 5.497730016708374, "end": **********.780126, "relative_end": **********.780126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin.layouts.master", "start": **********.782434, "relative_start": 5.500037908554077, "end": **********.782434, "relative_end": **********.782434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.notify", "start": **********.785327, "relative_start": 5.5029308795928955, "end": **********.785327, "relative_end": **********.785327, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 41507432, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.12", "Environment": "production", "Debug Mode": "Enabled", "URL": "localhost/mbf.mybrokerforex.com-********", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 24, "nb_templates": 24, "templates": [{"name": "admin.users.detail", "param_count": null, "params": [], "start": **********.467693, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/users/detail.blade.phpadmin.users.detail", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fadmin%2Fusers%2Fdetail.blade.php&line=1", "ajax": false, "filename": "detail.blade.php", "line": "?"}}, {"name": "components.user-detail.detail", "param_count": null, "params": [], "start": **********.485768, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/components/user-detail/detail.blade.phpcomponents.user-detail.detail", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fcomponents%2Fuser-detail%2Fdetail.blade.php&line=1", "ajax": false, "filename": "detail.blade.php", "line": "?"}}, {"name": "pagination::bootstrap-4", "param_count": null, "params": [], "start": **********.57386, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination/resources/views/bootstrap-4.blade.phppagination::bootstrap-4", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2Fresources%2Fviews%2Fbootstrap-4.blade.php&line=1", "ajax": false, "filename": "bootstrap-4.blade.php", "line": "?"}}, {"name": "components.user-detail.account", "param_count": null, "params": [], "start": **********.575249, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/components/user-detail/account.blade.phpcomponents.user-detail.account", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fcomponents%2Fuser-detail%2Faccount.blade.php&line=1", "ajax": false, "filename": "account.blade.php", "line": "?"}}, {"name": "components.confirmation-modal", "param_count": null, "params": [], "start": **********.579282, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/components/confirmation-modal.blade.phpcomponents.confirmation-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fcomponents%2Fconfirmation-modal.blade.php&line=1", "ajax": false, "filename": "confirmation-modal.blade.php", "line": "?"}}, {"name": "pagination::bootstrap-4", "param_count": null, "params": [], "start": **********.580924, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination/resources/views/bootstrap-4.blade.phppagination::bootstrap-4", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2Fresources%2Fviews%2Fbootstrap-4.blade.php&line=1", "ajax": false, "filename": "bootstrap-4.blade.php", "line": "?"}}, {"name": "components.user-detail.transaction", "param_count": null, "params": [], "start": **********.582087, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/components/user-detail/transaction.blade.phpcomponents.user-detail.transaction", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fcomponents%2Fuser-detail%2Ftransaction.blade.php&line=1", "ajax": false, "filename": "transaction.blade.php", "line": "?"}}, {"name": "components.confirmation-modal", "param_count": null, "params": [], "start": **********.584046, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/components/confirmation-modal.blade.phpcomponents.confirmation-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fcomponents%2Fconfirmation-modal.blade.php&line=1", "ajax": false, "filename": "confirmation-modal.blade.php", "line": "?"}}, {"name": "pagination::bootstrap-4", "param_count": null, "params": [], "start": **********.586262, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination/resources/views/bootstrap-4.blade.phppagination::bootstrap-4", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2Fresources%2Fviews%2Fbootstrap-4.blade.php&line=1", "ajax": false, "filename": "bootstrap-4.blade.php", "line": "?"}}, {"name": "components.user-detail.referral", "param_count": null, "params": [], "start": **********.587324, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/components/user-detail/referral.blade.phpcomponents.user-detail.referral", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fcomponents%2Fuser-detail%2Freferral.blade.php&line=1", "ajax": false, "filename": "referral.blade.php", "line": "?"}}, {"name": "components.confirmation-modal", "param_count": null, "params": [], "start": **********.603065, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/components/confirmation-modal.blade.phpcomponents.confirmation-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fcomponents%2Fconfirmation-modal.blade.php&line=1", "ajax": false, "filename": "confirmation-modal.blade.php", "line": "?"}}, {"name": "components.user-detail.network", "param_count": null, "params": [], "start": **********.605263, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/components/user-detail/network.blade.phpcomponents.user-detail.network", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fcomponents%2Fuser-detail%2Fnetwork.blade.php&line=1", "ajax": false, "filename": "network.blade.php", "line": "?"}}, {"name": "components.user-detail.tickets", "param_count": null, "params": [], "start": **********.609627, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/components/user-detail/tickets.blade.phpcomponents.user-detail.tickets", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fcomponents%2Fuser-detail%2Ftickets.blade.php&line=1", "ajax": false, "filename": "tickets.blade.php", "line": "?"}}, {"name": "components.confirmation-modal", "param_count": null, "params": [], "start": **********.619908, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/components/confirmation-modal.blade.phpcomponents.confirmation-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fcomponents%2Fconfirmation-modal.blade.php&line=1", "ajax": false, "filename": "confirmation-modal.blade.php", "line": "?"}}, {"name": "components.user-detail.note", "param_count": null, "params": [], "start": **********.621392, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/components/user-detail/note.blade.phpcomponents.user-detail.note", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fcomponents%2Fuser-detail%2Fnote.blade.php&line=1", "ajax": false, "filename": "note.blade.php", "line": "?"}}, {"name": "components.confirmation-modal", "param_count": null, "params": [], "start": **********.62326, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/components/confirmation-modal.blade.phpcomponents.confirmation-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fcomponents%2Fconfirmation-modal.blade.php&line=1", "ajax": false, "filename": "confirmation-modal.blade.php", "line": "?"}}, {"name": "components.user-detail.security", "param_count": null, "params": [], "start": **********.624702, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/components/user-detail/security.blade.phpcomponents.user-detail.security", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fcomponents%2Fuser-detail%2Fsecurity.blade.php&line=1", "ajax": false, "filename": "security.blade.php", "line": "?"}}, {"name": "components.confirmation-modal", "param_count": null, "params": [], "start": **********.626834, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/components/confirmation-modal.blade.phpcomponents.confirmation-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fcomponents%2Fconfirmation-modal.blade.php&line=1", "ajax": false, "filename": "confirmation-modal.blade.php", "line": "?"}}, {"name": "admin.layouts.app", "param_count": null, "params": [], "start": **********.64191, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/app.blade.phpadmin.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fadmin%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "admin.partials.sidenav", "param_count": null, "params": [], "start": **********.73096, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/partials/sidenav.blade.phpadmin.partials.sidenav", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fadmin%2Fpartials%2Fsidenav.blade.php&line=1", "ajax": false, "filename": "sidenav.blade.php", "line": "?"}}, {"name": "admin.partials.topnav", "param_count": null, "params": [], "start": **********.770219, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/partials/topnav.blade.phpadmin.partials.topnav", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fadmin%2Fpartials%2Ftopnav.blade.php&line=1", "ajax": false, "filename": "topnav.blade.php", "line": "?"}}, {"name": "admin.partials.breadcrumb", "param_count": null, "params": [], "start": **********.779971, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/partials/breadcrumb.blade.phpadmin.partials.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fadmin%2Fpartials%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}}, {"name": "admin.layouts.master", "param_count": null, "params": [], "start": **********.782277, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/master.blade.phpadmin.layouts.master", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fadmin%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}}, {"name": "partials.notify", "param_count": null, "params": [], "start": **********.78517, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/partials/notify.blade.phppartials.notify", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fpartials%2Fnotify.blade.php&line=1", "ajax": false, "filename": "notify.blade.php", "line": "?"}}]}, "queries": {"count": 41, "nb_statements": 39, "nb_visible_statements": 41, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 4.20708, "accumulated_duration_str": "4.21s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "vendor/beyondcode/laravel-query-detector/src/QueryDetector.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetector.php", "line": 25}, {"index": 10, "namespace": null, "name": "vendor/beyondcode/laravel-query-detector/src/QueryDetectorMiddleware.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetectorMiddleware.php", "line": 30}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21}], "start": **********.82442, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "QueryDetector.php:25", "source": {"index": 9, "namespace": null, "name": "vendor/beyondcode/laravel-query-detector/src/QueryDetector.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetector.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fvendor%2Fbeyondcode%2Flaravel-query-detector%2Fsrc%2FQueryDetector.php&line=25", "ajax": false, "filename": "QueryDetector.php", "line": "25"}, "connection": "mbf-db", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Middleware\\RedirectIfNotAdmin.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8916178, "duration": 0.027120000000000002, "duration_str": "27.12ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mbf-db", "explain": null, "start_percent": 0, "width_percent": 0.645}, {"sql": "select * from `users` where `users`.`id` = '53630' limit 1", "type": "query", "params": [], "bindings": ["53630"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 233}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9338498, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "ManageUsersController.php:233", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 233}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FManageUsersController.php&line=233", "ajax": false, "filename": "ManageUsersController.php", "line": "233"}, "connection": "mbf-db", "explain": null, "start_percent": 0.645, "width_percent": 0.035}, {"sql": "select * from `wallets` where `wallets`.`user_id` in (53630)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 233}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9458, "duration": 0.005730000000000001, "duration_str": "5.73ms", "memory": 0, "memory_str": null, "filename": "ManageUsersController.php:233", "source": {"index": 23, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 233}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FManageUsersController.php&line=233", "ajax": false, "filename": "ManageUsersController.php", "line": "233"}, "connection": "mbf-db", "explain": null, "start_percent": 0.68, "width_percent": 0.136}, {"sql": "select * from `currencies` where `currencies`.`id` in (1, 3, 4, 5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 233}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.969835, "duration": 0.00787, "duration_str": "7.87ms", "memory": 0, "memory_str": null, "filename": "ManageUsersController.php:233", "source": {"index": 28, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 233}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FManageUsersController.php&line=233", "ajax": false, "filename": "ManageUsersController.php", "line": "233"}, "connection": "mbf-db", "explain": null, "start_percent": 0.816, "width_percent": 0.187}, {"sql": "select `id`, `user_id`, `amount`, `status`, `created_at`, `method_code`, `method_currency`, `charge`, `rate`, `final_amount` from `deposits` where `status` != 0 and `deposits`.`user_id` in (53630) limit 100", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 233}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.995214, "duration": 0.00746, "duration_str": "7.46ms", "memory": 0, "memory_str": null, "filename": "ManageUsersController.php:233", "source": {"index": 23, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 233}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FManageUsersController.php&line=233", "ajax": false, "filename": "ManageUsersController.php", "line": "233"}, "connection": "mbf-db", "explain": null, "start_percent": 1.003, "width_percent": 0.177}, {"sql": "select `id`, `user_id`, `amount`, `status`, `created_at`, `currency`, `charge`, `rate`, `final_amount` from `withdrawals` where `status` != 0 and `withdrawals`.`user_id` in (53630) limit 100", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 233}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.0189471, "duration": 0.0077, "duration_str": "7.7ms", "memory": 0, "memory_str": null, "filename": "ManageUsersController.php:233", "source": {"index": 23, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 233}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FManageUsersController.php&line=233", "ajax": false, "filename": "ManageUsersController.php", "line": "233"}, "connection": "mbf-db", "explain": null, "start_percent": 1.18, "width_percent": 0.183}, {"sql": "select `id`, `user_id`, `amount`, `post_balance`, `trx_type`, `details`, `created_at` from `transactions` where `transactions`.`user_id` in (53630) order by `id` desc limit 100", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 233}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.0432959, "duration": 0.0069299999999999995, "duration_str": "6.93ms", "memory": 0, "memory_str": null, "filename": "ManageUsersController.php:233", "source": {"index": 23, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 233}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FManageUsersController.php&line=233", "ajax": false, "filename": "ManageUsersController.php", "line": "233"}, "connection": "mbf-db", "explain": null, "start_percent": 1.363, "width_percent": 0.165}, {"sql": "select `id`, `user_id`, `subject`, `status`, `priority`, `created_at` from `support_tickets` where `support_tickets`.`user_id` in (53630) limit 50", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 233}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.06512, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "ManageUsersController.php:233", "source": {"index": 23, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 233}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FManageUsersController.php&line=233", "ajax": false, "filename": "ManageUsersController.php", "line": "233"}, "connection": "mbf-db", "explain": null, "start_percent": 1.528, "width_percent": 0.028}, {"sql": "select * from `user_accounts` where `user_accounts`.`User_Id` in (53630)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 233}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.080795, "duration": 0.01559, "duration_str": "15.59ms", "memory": 0, "memory_str": null, "filename": "ManageUsersController.php:233", "source": {"index": 23, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 233}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FManageUsersController.php&line=233", "ajax": false, "filename": "ManageUsersController.php", "line": "233"}, "connection": "mbf-db", "explain": null, "start_percent": 1.556, "width_percent": 0.371}, {"sql": "select `id`, `to_ib_user_id`, `commission_amount`, `status`, `created_at` from `ib_commissions` where `ib_commissions`.`to_ib_user_id` in (53630) limit 100", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 233}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1125429, "duration": 0.01623, "duration_str": "16.23ms", "memory": 0, "memory_str": null, "filename": "ManageUsersController.php:233", "source": {"index": 23, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 233}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FManageUsersController.php&line=233", "ajax": false, "filename": "ManageUsersController.php", "line": "233"}, "connection": "mbf-db", "explain": null, "start_percent": 1.926, "width_percent": 0.386}, {"sql": "select `id`, `firstname`, `lastname`, `email`, `ref_by`, `created_at` from `users` where `users`.`ref_by` in (53630) limit 100", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 233}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.142126, "duration": 0.34178, "duration_str": "342ms", "memory": 0, "memory_str": null, "filename": "ManageUsersController.php:233", "source": {"index": 23, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 233}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FManageUsersController.php&line=233", "ajax": false, "filename": "ManageUsersController.php", "line": "233"}, "connection": "mbf-db", "explain": null, "start_percent": 2.312, "width_percent": 8.124}, {"sql": "select count(*) as aggregate from `trades` where `trader_id` = 53630", "type": "query", "params": [], "bindings": [53630], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Models\\User.php", "line": 748}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 242}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.499159, "duration": 0.01071, "duration_str": "10.71ms", "memory": 0, "memory_str": null, "filename": "User.php:748", "source": {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Models\\User.php", "line": 748}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FModels%2FUser.php&line=748", "ajax": false, "filename": "User.php", "line": "748"}, "connection": "mbf-db", "explain": null, "start_percent": 10.436, "width_percent": 0.255}, {"sql": "select count(*) as aggregate from `trades` where `order_id` = 53630", "type": "query", "params": [], "bindings": [53630], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Models\\User.php", "line": 749}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 242}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.518214, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "User.php:749", "source": {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Models\\User.php", "line": 749}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FModels%2FUser.php&line=749", "ajax": false, "filename": "User.php", "line": "749"}, "connection": "mbf-db", "explain": null, "start_percent": 10.691, "width_percent": 0.025}, {"sql": "select count(*) as aggregate from `transactions` where `user_id` = 53630", "type": "query", "params": [], "bindings": [53630], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Models\\User.php", "line": 751}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 242}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.527614, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "User.php:751", "source": {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Models\\User.php", "line": 751}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FModels%2FUser.php&line=751", "ajax": false, "filename": "User.php", "line": "751"}, "connection": "mbf-db", "explain": null, "start_percent": 10.716, "width_percent": 0.025}, {"sql": "select * from `currencies` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 245}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.5379539, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "ManageUsersController.php:245", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FManageUsersController.php&line=245", "ajax": false, "filename": "ManageUsersController.php", "line": "245"}, "connection": "mbf-db", "explain": null, "start_percent": 10.74, "width_percent": 0.026}, {"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 8, "namespace": null, "name": "app/Services/AccountService.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Services\\AccountService.php", "line": 17}, {"index": 9, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 248}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.555976, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "AccountService.php:17", "source": {"index": 8, "namespace": null, "name": "app/Services/AccountService.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Services\\AccountService.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FServices%2FAccountService.php&line=17", "ajax": false, "filename": "AccountService.php", "line": "17"}, "connection": "mbf-dbmt5", "explain": null, "start_percent": 10.767, "width_percent": 0}, {"sql": "select * from `mt5_users` where `Email` = '<EMAIL>'", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Services/AccountService.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Services\\AccountService.php", "line": 29}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 249}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.556381, "duration": 3.03356, "duration_str": "3.03s", "memory": 0, "memory_str": null, "filename": "AccountService.php:29", "source": {"index": 13, "namespace": null, "name": "app/Services/AccountService.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Services\\AccountService.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FServices%2FAccountService.php&line=29", "ajax": false, "filename": "AccountService.php", "line": "29"}, "connection": "mbf-dbmt5", "explain": null, "start_percent": 10.767, "width_percent": 72.106}, {"sql": "select * from `mt5_users` where `Email` = '<EMAIL>' and `Group` Like '%Multi-IB%'", "type": "query", "params": [], "bindings": ["<EMAIL>", "%Multi-IB%"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Services/AccountService.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Services\\AccountService.php", "line": 42}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 290}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.604268, "duration": 0.4018, "duration_str": "402ms", "memory": 0, "memory_str": null, "filename": "AccountService.php:42", "source": {"index": 13, "namespace": null, "name": "app/Services/AccountService.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Services\\AccountService.php", "line": 42}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FServices%2FAccountService.php&line=42", "ajax": false, "filename": "AccountService.php", "line": "42"}, "connection": "mbf-dbmt5", "explain": null, "start_percent": 82.873, "width_percent": 9.551}, {"sql": "select `id`, `firstname`, `lastname`, `email`, `username`, `mt5_login`, `mt5_group`, `status`, `created_at` from `users` where `email` is not null and `email` != '' and `id` != 53630 order by `created_at` desc", "type": "query", "params": [], "bindings": ["", 53630], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 298}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.01261, "duration": 0.18878, "duration_str": "189ms", "memory": 0, "memory_str": null, "filename": "ManageUsersController.php:298", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FManageUsersController.php&line=298", "ajax": false, "filename": "ManageUsersController.php", "line": "298"}, "connection": "mbf-db", "explain": null, "start_percent": 92.423, "width_percent": 4.487}, {"sql": "select `id`, `firstname`, `lastname`, `email` from `users` where `users`.`id` = 10921 limit 1", "type": "query", "params": [], "bindings": [10921], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 303}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3188891, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "ManageUsersController.php:303", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 303}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FManageUsersController.php&line=303", "ajax": false, "filename": "ManageUsersController.php", "line": "303"}, "connection": "mbf-db", "explain": null, "start_percent": 96.911, "width_percent": 0.024}, {"sql": "select count(*) as aggregate from `transactions` where `user_id` = 53630", "type": "query", "params": [], "bindings": [53630], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Transaction.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Models\\Transaction.php", "line": 45}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 306}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.326737, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Transaction.php:45", "source": {"index": 16, "namespace": null, "name": "app/Models/Transaction.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Models\\Transaction.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FModels%2FTransaction.php&line=45", "ajax": false, "filename": "Transaction.php", "line": "45"}, "connection": "mbf-db", "explain": null, "start_percent": 96.935, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `users` where `ref_by` = 53630", "type": "query", "params": [], "bindings": [53630], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 331}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.336761, "duration": 0.00642, "duration_str": "6.42ms", "memory": 0, "memory_str": null, "filename": "ManageUsersController.php:331", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 331}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FManageUsersController.php&line=331", "ajax": false, "filename": "ManageUsersController.php", "line": "331"}, "connection": "mbf-db", "explain": null, "start_percent": 96.957, "width_percent": 0.153}, {"sql": "select count(*) as aggregate from `users` where `ref_by` = 53630", "type": "query", "params": [], "bindings": [53630], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 334}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.349536, "duration": 0.0116, "duration_str": "11.6ms", "memory": 0, "memory_str": null, "filename": "ManageUsersController.php:334", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 334}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FManageUsersController.php&line=334", "ajax": false, "filename": "ManageUsersController.php", "line": "334"}, "connection": "mbf-db", "explain": null, "start_percent": 97.11, "width_percent": 0.276}, {"sql": "select `id`, `firstname`, `lastname`, `ref_by`, `mt5_login`, `mt5_balance`, `ib_status`, `ib_type`, `mt5_group` from `users` where `ref_by` = 53630", "type": "query", "params": [], "bindings": [53630], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 1658}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 1638}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 337}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3685129, "duration": 0.00844, "duration_str": "8.44ms", "memory": 0, "memory_str": null, "filename": "ManageUsersController.php:1658", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 1658}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FManageUsersController.php&line=1658", "ajax": false, "filename": "ManageUsersController.php", "line": "1658"}, "connection": "mbf-db", "explain": null, "start_percent": 97.386, "width_percent": 0.201}, {"sql": "WITH RECURSIVE referral_tree AS (\nSELECT id, ref_by, 1 as level\nFROM users\nWHERE ref_by = 53630\nUNION ALL\nSELECT u.id, u.ref_by, rt.level + 1\nFROM users u\nINNER JOIN referral_tree rt ON u.ref_by = rt.id\nWHERE rt.level < 10\n)\nSELECT COUNT(*) as total_count FROM referral_tree", "type": "query", "params": [], "bindings": [53630], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 489}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 341}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.3833191, "duration": 0.01419, "duration_str": "14.19ms", "memory": 0, "memory_str": null, "filename": "ManageUsersController.php:489", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 489}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FManageUsersController.php&line=489", "ajax": false, "filename": "ManageUsersController.php", "line": "489"}, "connection": "mbf-db", "explain": null, "start_percent": 97.586, "width_percent": 0.337}, {"sql": "select count(*) as aggregate from `users` where `ref_by` = 53630 and `mt5_login` is not null", "type": "query", "params": [], "bindings": [53630], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 342}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.404638, "duration": 0.04735, "duration_str": "47.35ms", "memory": 0, "memory_str": null, "filename": "ManageUsersController.php:342", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ManageUsersController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\Admin\\ManageUsersController.php", "line": 342}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FManageUsersController.php&line=342", "ajax": false, "filename": "ManageUsersController.php", "line": "342"}, "connection": "mbf-db", "explain": null, "start_percent": 97.924, "width_percent": 1.125}, {"sql": "select count(*) as aggregate from `support_tickets` where `user_id` = 53630", "type": "query", "params": [], "bindings": [53630], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "view", "name": "components.user-detail.tickets", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/components/user-detail/tickets.blade.php", "line": 5}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.6113029, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "components.user-detail.tickets:5", "source": {"index": 16, "namespace": "view", "name": "components.user-detail.tickets", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/components/user-detail/tickets.blade.php", "line": 5}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fcomponents%2Fuser-detail%2Ftickets.blade.php&line=5", "ajax": false, "filename": "tickets.blade.php", "line": "5"}, "connection": "mbf-db", "explain": null, "start_percent": 99.049, "width_percent": 0.022}, {"sql": "select count(*) as aggregate from `users` where `status` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 63}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/app.blade.php", "line": 4}], "start": **********.643625, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:63", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 63}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FProviders%2FAppServiceProvider.php&line=63", "ajax": false, "filename": "AppServiceProvider.php", "line": "63"}, "connection": "mbf-db", "explain": null, "start_percent": 99.071, "width_percent": 0.023}, {"sql": "select count(*) as aggregate from `users` where `ev` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 64}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/app.blade.php", "line": 4}], "start": **********.650115, "duration": 0.0063, "duration_str": "6.3ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:64", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FProviders%2FAppServiceProvider.php&line=64", "ajax": false, "filename": "AppServiceProvider.php", "line": "64"}, "connection": "mbf-db", "explain": null, "start_percent": 99.094, "width_percent": 0.15}, {"sql": "select count(*) as aggregate from `users` where `sv` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 65}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/app.blade.php", "line": 4}], "start": **********.66136, "duration": 0.00642, "duration_str": "6.42ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:65", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FProviders%2FAppServiceProvider.php&line=65", "ajax": false, "filename": "AppServiceProvider.php", "line": "65"}, "connection": "mbf-db", "explain": null, "start_percent": 99.244, "width_percent": 0.153}, {"sql": "select count(*) as aggregate from `users` where `kv` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/app.blade.php", "line": 4}], "start": **********.672715, "duration": 0.00632, "duration_str": "6.32ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:66", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FProviders%2FAppServiceProvider.php&line=66", "ajax": false, "filename": "AppServiceProvider.php", "line": "66"}, "connection": "mbf-db", "explain": null, "start_percent": 99.396, "width_percent": 0.15}, {"sql": "select count(*) as aggregate from `users` where `kv` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 67}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/app.blade.php", "line": 4}], "start": **********.684072, "duration": 0.00615, "duration_str": "6.15ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:67", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 67}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FProviders%2FAppServiceProvider.php&line=67", "ajax": false, "filename": "AppServiceProvider.php", "line": "67"}, "connection": "mbf-db", "explain": null, "start_percent": 99.547, "width_percent": 0.146}, {"sql": "select count(*) as aggregate from `users` where `profile_request` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 68}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/app.blade.php", "line": 4}], "start": **********.695161, "duration": 0.00628, "duration_str": "6.28ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:68", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FProviders%2FAppServiceProvider.php&line=68", "ajax": false, "filename": "AppServiceProvider.php", "line": "68"}, "connection": "mbf-db", "explain": null, "start_percent": 99.693, "width_percent": 0.149}, {"sql": "select count(*) as aggregate from `support_tickets` where `status` in (0, 2)", "type": "query", "params": [], "bindings": [0, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 69}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/app.blade.php", "line": 4}], "start": **********.706506, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:69", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FProviders%2FAppServiceProvider.php&line=69", "ajax": false, "filename": "AppServiceProvider.php", "line": "69"}, "connection": "mbf-db", "explain": null, "start_percent": 99.842, "width_percent": 0.021}, {"sql": "select count(*) as aggregate from `deposits` where `method_code` >= 1000 and `status` = 2", "type": "query", "params": [], "bindings": [1000, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 70}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/app.blade.php", "line": 4}], "start": **********.7123852, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:70", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FProviders%2FAppServiceProvider.php&line=70", "ajax": false, "filename": "AppServiceProvider.php", "line": "70"}, "connection": "mbf-db", "explain": null, "start_percent": 99.863, "width_percent": 0.019}, {"sql": "select count(*) as aggregate from `withdrawals` where `status` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 71}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/app.blade.php", "line": 4}], "start": **********.7181742, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:71", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 71}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FProviders%2FAppServiceProvider.php&line=71", "ajax": false, "filename": "AppServiceProvider.php", "line": "71"}, "connection": "mbf-db", "explain": null, "start_percent": 99.882, "width_percent": 0.019}, {"sql": "select count(*) as aggregate from `p2p_trades` where `status` = 4", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/app.blade.php", "line": 4}], "start": **********.725106, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:72", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FProviders%2FAppServiceProvider.php&line=72", "ajax": false, "filename": "AppServiceProvider.php", "line": "72"}, "connection": "mbf-db", "explain": null, "start_percent": 99.901, "width_percent": 0.018}, {"sql": "select * from `admin_notifications` where `is_read` = 0 order by `id` desc limit 10", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 78}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}, {"index": 22, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/app.blade.php", "line": 5}], "start": **********.747053, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:78", "source": {"index": 15, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 78}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FProviders%2FAppServiceProvider.php&line=78", "ajax": false, "filename": "AppServiceProvider.php", "line": "78"}, "connection": "mbf-db", "explain": null, "start_percent": 99.919, "width_percent": 0.025}, {"sql": "select * from `users` where `users`.`id` in (10860)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 78}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}, {"index": 27, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/app.blade.php", "line": 5}], "start": **********.753705, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:78", "source": {"index": 20, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 78}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FProviders%2FAppServiceProvider.php&line=78", "ajax": false, "filename": "AppServiceProvider.php", "line": "78"}, "connection": "mbf-db", "explain": null, "start_percent": 99.944, "width_percent": 0.034}, {"sql": "select count(*) as aggregate from `admin_notifications` where `is_read` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 79}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}, {"index": 23, "namespace": "view", "name": "admin.layouts.app", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/admin/layouts/app.blade.php", "line": 5}], "start": **********.764207, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:79", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FProviders%2FAppServiceProvider.php&line=79", "ajax": false, "filename": "AppServiceProvider.php", "line": "79"}, "connection": "mbf-db", "explain": null, "start_percent": 99.978, "width_percent": 0.022}]}, "models": {"data": {"App\\Models\\User": {"value": 5047, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\AdminNotification": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FModels%2FAdminNotification.php&line=1", "ajax": false, "filename": "AdminNotification.php", "line": "?"}}, "App\\Models\\Wallet": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FModels%2FWallet.php&line=1", "ajax": false, "filename": "Wallet.php", "line": "?"}}, "App\\Models\\Currency": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}}, "count": 5074, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://localhost/mbf.mybrokerforex.com-********/admin/users/detail/53630", "action_name": "admin.users.detail", "controller_action": "App\\Http\\Controllers\\Admin\\ManageUsersController@detail", "uri": "GET admin/users/detail/{id}", "controller": "App\\Http\\Controllers\\Admin\\ManageUsersController@detail<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FManageUsersController.php&line=207\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin/users", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FAdmin%2FManageUsersController.php&line=207\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/ManageUsersController.php:207-365</a>", "middleware": "web, admin", "duration": "5.53s", "peak_memory": "42MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-644951125 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-644951125\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1775873530 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1775873530\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-460532241 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"73 characters\">https://localhost/mbf.mybrokerforex.com-********/admin/users/detail/53630</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">en-US,en;q=0.9,ms-MY;q=0.8,ms;q=0.7,zh-CN;q=0.6,zh;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ik8wb1AzdE1ZTExQcDlaQ252VXV3QXc9PSIsInZhbHVlIjoiRDFoUDBuN1dHUnlrRHpEdCtIbWIxa0FLOUdCV0lMRTJackE0UzJFSFIzamJ6Mi9nMVptbmVCazFibVQzWlN1cHprN0VMdHplQnJpSTJXdU55bERwM1pOYTVFS1ZZVWMxcng0dUcwblBzcTZYWlBmRGJmaWk0U25ZWWQ4UGlkbzEiLCJtYWMiOiIxNTE4OTQzNDFhMjJjOGE0MGNiZjFhNTQwZDAzMTY5OWM5MzlkYTA2NDdkZjc3MDA4OGVlN2M5YTYyYmQ1OGM3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Im9kNmZKQUp1azh6K2hzSEdyNVNuamc9PSIsInZhbHVlIjoicnVEVEpVb01tMVVMeDFKckY0TkdGSm9seTJ6MklWUVBReVlzQ2pudWZRU3h5RFZ4akRQWDAxSDQyWGlDazFmVUZLWVpaanc1K2FiUkpjUUdEQ2hmV1VqSDFENmhOb09kcC9ETm9HaFNudXBlNEJVK3c5RnVnbE51REQzdHF6ajgiLCJtYWMiOiJiYTQ2YTkzYWMzMjhkYzE3NWQxODQ0NTE4MTg1NTU5OWQwZTYyMTdlNjZiMjY5Y2RhMjRkMjI0NGNmMzM5ZDNkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-460532241\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-749284803 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6wydTXTQETk9uM050OalbDmHEZPTqQskjidNxWTA</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pKwSOwBxhh7a0b12K8rA1re0Q1SyO815L9Vl6qQN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-749284803\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1849290947 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 09:08:59 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1849290947\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-46135547 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6wydTXTQETk9uM050OalbDmHEZPTqQskjidNxWTA</span>\"\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"73 characters\">https://localhost/mbf.mybrokerforex.com-********/admin/users/detail/53630</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">notify</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>flasher::envelopes</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>53630</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01JZ2KY4B80C23KZ057D8YG962</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>notify</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">User details updated successfully</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-46135547\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://localhost/mbf.mybrokerforex.com-********/admin/users/detail/53630", "action_name": "admin.users.detail", "controller_action": "App\\Http\\Controllers\\Admin\\ManageUsersController@detail"}, "badge": null}}