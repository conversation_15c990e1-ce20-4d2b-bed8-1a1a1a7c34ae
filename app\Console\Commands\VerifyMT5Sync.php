<?php

namespace App\Console\Commands;

use App\Services\MT5SyncMonitoringService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class VerifyMT5Sync extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'mt5:verify-sync 
                            {--detailed : Show detailed verification results}
                            {--fix-issues : Attempt to fix detected issues}';

    /**
     * The console command description.
     */
    protected $description = 'Verify MT5 sync accuracy and data integrity';

    /**
     * The monitoring service instance
     */
    protected $monitoringService;

    /**
     * Create a new command instance.
     */
    public function __construct(MT5SyncMonitoringService $monitoringService)
    {
        parent::__construct();
        $this->monitoringService = $monitoringService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 MT5 SYNC VERIFICATION STARTING');
        $this->info('=====================================');

        $detailed = $this->option('detailed');
        $fixIssues = $this->option('fix-issues');

        // Run verification
        $verification = $this->monitoringService->verifySyncAccuracy();

        if (isset($verification['error'])) {
            $this->error('❌ Verification failed: ' . $verification['error']);
            return 1;
        }

        // Display summary
        $this->displaySummary($verification);

        if ($detailed) {
            $this->displayDetailedResults($verification);
        }

        // Check for critical issues
        $criticalIssues = $this->identifyCriticalIssues($verification);
        
        if (!empty($criticalIssues)) {
            $this->displayCriticalIssues($criticalIssues);
            
            if ($fixIssues) {
                $this->attemptToFixIssues($criticalIssues);
            }
        } else {
            $this->info('✅ No critical issues detected');
        }

        // Display recommendations
        $dashboard = $this->monitoringService->getSyncStatusDashboard();
        $this->displayRecommendations($dashboard['recommendations']);

        return 0;
    }

    /**
     * Display verification summary
     */
    private function displaySummary($verification)
    {
        $this->info("\n📊 SYNC VERIFICATION SUMMARY");
        $this->info("============================");

        $this->table(['Metric', 'Value'], [
            ['Verification Time', $verification['timestamp']],
            ['MT5 Total Users', number_format($verification['mt5_total_users'])],
            ['Local Total Users', number_format($verification['local_total_users'])],
            ['Local MT5 Users', number_format($verification['local_mt5_users'])],
            ['Sync Coverage', round(($verification['local_mt5_users'] / max($verification['mt5_total_users'], 1)) * 100, 2) . '%'],
        ]);

        // Data quality summary
        $nullData = $verification['null_data_check'];
        $this->info("\n📈 DATA QUALITY METRICS");
        $this->table(['Metric', 'Count', 'Status'], [
            ['Data Quality Score', round($nullData['data_quality_score'], 1) . '%', $this->getStatusIcon($nullData['data_quality_score'], 90)],
            ['NULL Issues', $nullData['total_null_issues'], $nullData['total_null_issues'] == 0 ? '✅' : '⚠️'],
            ['Duplicate Emails', $verification['duplicate_check']['duplicate_emails'], $verification['duplicate_check']['duplicate_emails'] == 0 ? '✅' : '❌'],
            ['Duplicate MT5 Logins', $verification['duplicate_check']['duplicate_mt5_logins'], $verification['duplicate_check']['duplicate_mt5_logins'] == 0 ? '✅' : '❌'],
        ]);

        // Sync lag status
        $syncLag = $verification['sync_lag'];
        $this->info("\n⏱️  SYNC FRESHNESS");
        if (isset($syncLag['lag_minutes'])) {
            $status = match($syncLag['status']) {
                'excellent' => '🟢 Excellent',
                'good' => '🟡 Good',
                'warning' => '🟠 Warning',
                'critical' => '🔴 Critical',
                default => '⚪ Unknown'
            };
            $this->line("Last Sync: {$syncLag['last_sync']}");
            $this->line("Lag: {$syncLag['lag_minutes']} minutes ({$status})");
        } else {
            $this->warn("No sync data available");
        }
    }

    /**
     * Display detailed verification results
     */
    private function displayDetailedResults($verification)
    {
        $this->info("\n🔍 DETAILED VERIFICATION RESULTS");
        $this->info("=================================");

        // NULL data breakdown
        $nullData = $verification['null_data_check'];
        $this->info("\n📋 NULL Data Analysis:");
        foreach ($nullData as $key => $value) {
            if ($key !== 'total_null_issues' && $key !== 'data_quality_score') {
                $this->line("  {$key}: {$value}");
            }
        }

        // Data integrity details
        if (isset($verification['data_integrity']) && !isset($verification['data_integrity']['error'])) {
            $integrity = $verification['data_integrity'];
            $this->info("\n🔒 Data Integrity Analysis:");
            $this->line("  Sample Size: {$integrity['sample_size']}");
            $this->line("  Checked Users: {$integrity['checked_users']}");
            $this->line("  Integrity Issues: {$integrity['integrity_issues']}");
            $this->line("  Integrity Score: {$integrity['integrity_score']}%");
        }

        // Performance metrics
        if (isset($verification['performance_metrics']) && !isset($verification['performance_metrics']['no_recent_data'])) {
            $perf = $verification['performance_metrics'];
            $this->info("\n⚡ Performance Metrics:");
            $this->line("  Avg Duration: {$perf['avg_duration_seconds']}s");
            $this->line("  Avg Rate: {$perf['avg_rate_users_per_second']} users/sec");
            $this->line("  Performance Trend: {$perf['performance_trend']}");
        }
    }

    /**
     * Identify critical issues that need immediate attention
     */
    private function identifyCriticalIssues($verification)
    {
        $issues = [];

        // High NULL data
        if ($verification['null_data_check']['total_null_issues'] > 100) {
            $issues[] = [
                'type' => 'high_null_data',
                'severity' => 'high',
                'description' => 'High number of NULL values in critical fields',
                'count' => $verification['null_data_check']['total_null_issues']
            ];
        }

        // Duplicate emails
        if ($verification['duplicate_check']['duplicate_emails'] > 0) {
            $issues[] = [
                'type' => 'duplicate_emails',
                'severity' => 'critical',
                'description' => 'Duplicate email addresses found',
                'count' => $verification['duplicate_check']['duplicate_emails']
            ];
        }

        // Duplicate MT5 logins
        if ($verification['duplicate_check']['duplicate_mt5_logins'] > 0) {
            $issues[] = [
                'type' => 'duplicate_mt5_logins',
                'severity' => 'critical',
                'description' => 'Duplicate MT5 login numbers found',
                'count' => $verification['duplicate_check']['duplicate_mt5_logins']
            ];
        }

        // Critical sync lag
        if (isset($verification['sync_lag']['status']) && $verification['sync_lag']['status'] === 'critical') {
            $issues[] = [
                'type' => 'sync_lag',
                'severity' => 'high',
                'description' => 'Sync lag is critical',
                'lag_minutes' => $verification['sync_lag']['lag_minutes']
            ];
        }

        // Low data integrity
        if (isset($verification['data_integrity']['integrity_score']) && 
            $verification['data_integrity']['integrity_score'] < 90) {
            $issues[] = [
                'type' => 'low_integrity',
                'severity' => 'high',
                'description' => 'Data integrity score is below acceptable threshold',
                'score' => $verification['data_integrity']['integrity_score']
            ];
        }

        return $issues;
    }

    /**
     * Display critical issues
     */
    private function displayCriticalIssues($issues)
    {
        $this->error("\n🚨 CRITICAL ISSUES DETECTED");
        $this->error("===========================");

        foreach ($issues as $issue) {
            $severity = match($issue['severity']) {
                'critical' => '🔴 CRITICAL',
                'high' => '🟠 HIGH',
                'medium' => '🟡 MEDIUM',
                default => '⚪ LOW'
            };

            $this->line("{$severity}: {$issue['description']}");
            
            if (isset($issue['count'])) {
                $this->line("  Count: {$issue['count']}");
            }
            if (isset($issue['lag_minutes'])) {
                $this->line("  Lag: {$issue['lag_minutes']} minutes");
            }
            if (isset($issue['score'])) {
                $this->line("  Score: {$issue['score']}%");
            }
        }
    }

    /**
     * Attempt to fix detected issues
     */
    private function attemptToFixIssues($issues)
    {
        $this->info("\n🔧 ATTEMPTING TO FIX ISSUES");
        $this->info("============================");

        foreach ($issues as $issue) {
            $this->line("Fixing: {$issue['description']}");

            switch ($issue['type']) {
                case 'sync_lag':
                    $this->info("  → Running immediate sync...");
                    $this->call('mt5:sync-users', ['--force' => true, '--fast' => true, '--limit' => 1000]);
                    break;

                case 'high_null_data':
                    $this->info("  → Running data cleanup...");
                    // Could implement a data cleanup command here
                    $this->warn("  → Manual review recommended for NULL data cleanup");
                    break;

                case 'duplicate_emails':
                case 'duplicate_mt5_logins':
                    $this->warn("  → Manual review required for duplicate resolution");
                    $this->line("  → Consider running: php artisan mt5:resolve-duplicates");
                    break;

                case 'low_integrity':
                    $this->info("  → Running integrity repair...");
                    $this->call('mt5:sync-users', ['--force' => true, '--limit' => 100]);
                    break;

                default:
                    $this->warn("  → No automatic fix available");
            }
        }
    }

    /**
     * Display recommendations
     */
    private function displayRecommendations($recommendations)
    {
        $this->info("\n💡 RECOMMENDATIONS");
        $this->info("==================");

        foreach ($recommendations as $recommendation) {
            $this->line("• {$recommendation}");
        }
    }

    /**
     * Get status icon based on score and threshold
     */
    private function getStatusIcon($score, $threshold)
    {
        return $score >= $threshold ? '✅' : ($score >= $threshold * 0.8 ? '⚠️' : '❌');
    }
}
