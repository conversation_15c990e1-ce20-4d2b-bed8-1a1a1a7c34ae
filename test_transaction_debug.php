<?php

/**
 * Debug script to test transaction creation and display
 * Run this script to verify transaction functionality
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== MT5 Trading Platform Transaction Debug ===\n\n";

// Test 1: Check existing transactions
echo "TEST 1: Checking existing transactions\n";
echo "=====================================\n";

$totalTransactions = \App\Models\Transaction::count();
echo "Total transactions in database: {$totalTransactions}\n";

// Check transactions by remark
$remarkCounts = \App\Models\Transaction::selectRaw('remark, COUNT(*) as count')
    ->groupBy('remark')
    ->orderBy('count', 'desc')
    ->get();

echo "\nTransactions by remark:\n";
foreach ($remarkCounts as $remark) {
    echo "  {$remark->remark}: {$remark->count}\n";
}

// Test 2: Check transactions with missing wallet relationships
echo "\nTEST 2: Checking transaction-wallet relationships\n";
echo "================================================\n";

$transactionsWithoutWallet = \App\Models\Transaction::whereNull('wallet_id')->count();
echo "Transactions without wallet_id: {$transactionsWithoutWallet}\n";

$transactionsWithInvalidWallet = \App\Models\Transaction::whereNotNull('wallet_id')
    ->whereDoesntHave('wallet')
    ->count();
echo "Transactions with invalid wallet_id: {$transactionsWithInvalidWallet}\n";

// Test 3: Check recent withdraw transactions
echo "\nTEST 3: Recent withdraw transactions\n";
echo "===================================\n";

$withdrawTransactions = \App\Models\Transaction::whereIn('remark', ['withdraw', 'withdraw_approved', 'withdraw_reject'])
    ->orderBy('created_at', 'desc')
    ->take(10)
    ->get();

echo "Recent withdraw transactions (last 10):\n";
foreach ($withdrawTransactions as $trx) {
    $walletInfo = $trx->wallet ? "{$trx->wallet->currency->symbol} ({$trx->wallet->name})" : "NO WALLET";
    echo "  ID: {$trx->id} | TRX: {$trx->trx} | Remark: {$trx->remark} | Amount: {$trx->amount} | Wallet: {$walletInfo} | Created: {$trx->created_at}\n";
}

// Test 4: Check specific user transactions
echo "\nTEST 4: User-specific transaction check\n";
echo "======================================\n";

// Get a user with transactions
$userWithTransactions = \App\Models\User::whereHas('transactions')->first();

if ($userWithTransactions) {
    echo "Testing with user: {$userWithTransactions->email} (ID: {$userWithTransactions->id})\n";
    
    $userTransactions = \App\Models\Transaction::where('user_id', $userWithTransactions->id)
        ->orderBy('created_at', 'desc')
        ->take(5)
        ->get();
    
    echo "User's recent transactions:\n";
    foreach ($userTransactions as $trx) {
        $walletInfo = $trx->wallet ? "{$trx->wallet->currency->symbol}" : "NO WALLET";
        echo "  {$trx->remark} | {$trx->trx_type}{$trx->amount} | {$walletInfo} | {$trx->created_at}\n";
    }
    
    // Test the controller query
    echo "\nTesting controller query for this user:\n";
    $controllerQuery = \App\Models\Transaction::where('user_id', $userWithTransactions->id)
        ->with('wallet.currency')
        ->orderBy('id', 'desc')
        ->take(5)
        ->get();
    
    echo "Controller query results:\n";
    foreach ($controllerQuery as $trx) {
        $walletInfo = $trx->wallet && $trx->wallet->currency ? 
            "{$trx->wallet->currency->symbol} ({$trx->wallet->name})" : 
            "MISSING WALLET/CURRENCY";
        echo "  {$trx->remark} | {$trx->trx_type}{$trx->amount} | {$walletInfo} | Details: {$trx->details}\n";
    }
} else {
    echo "No users with transactions found.\n";
}

// Test 5: Check wallet relationships
echo "\nTEST 5: Wallet relationship check\n";
echo "=================================\n";

$totalWallets = \App\Models\Wallet::count();
echo "Total wallets in database: {$totalWallets}\n";

$walletsWithCurrency = \App\Models\Wallet::whereHas('currency')->count();
echo "Wallets with valid currency: {$walletsWithCurrency}\n";

$walletsWithoutCurrency = \App\Models\Wallet::whereDoesntHave('currency')->count();
echo "Wallets without valid currency: {$walletsWithoutCurrency}\n";

// Test 6: Create a test transaction to verify functionality
echo "\nTEST 6: Test transaction creation\n";
echo "=================================\n";

try {
    // Get a user with a wallet
    $testUser = \App\Models\User::whereHas('wallets')->first();
    
    if ($testUser) {
        $testWallet = $testUser->wallets()->first();
        
        echo "Creating test transaction for user: {$testUser->email}\n";
        echo "Using wallet: {$testWallet->name} ({$testWallet->currency->symbol})\n";
        
        $testTransaction = new \App\Models\Transaction();
        $testTransaction->user_id = $testUser->id;
        $testTransaction->wallet_id = $testWallet->id;
        $testTransaction->amount = 10.00;
        $testTransaction->post_balance = $testWallet->balance;
        $testTransaction->charge = 0;
        $testTransaction->trx_type = '-';
        $testTransaction->details = 'Test withdraw transaction for debugging';
        $testTransaction->trx = 'TEST' . time();
        $testTransaction->remark = 'withdraw_test';
        $testTransaction->save();
        
        echo "✅ Test transaction created successfully with ID: {$testTransaction->id}\n";
        
        // Verify it can be retrieved with relationships
        $retrievedTransaction = \App\Models\Transaction::with('wallet.currency')
            ->find($testTransaction->id);
        
        if ($retrievedTransaction && $retrievedTransaction->wallet && $retrievedTransaction->wallet->currency) {
            echo "✅ Test transaction retrieved successfully with wallet relationship\n";
            echo "   Wallet: {$retrievedTransaction->wallet->currency->symbol} ({$retrievedTransaction->wallet->name})\n";
        } else {
            echo "❌ Test transaction missing wallet relationship\n";
        }
        
        // Clean up test transaction
        $testTransaction->delete();
        echo "🧹 Test transaction cleaned up\n";
        
    } else {
        echo "No users with wallets found for testing.\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Error creating test transaction: " . $e->getMessage() . "\n";
}

echo "\n=== Debug Complete ===\n";
