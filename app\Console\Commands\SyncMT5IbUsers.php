<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\IbCommissionIntegrationService;
use Illuminate\Support\Facades\Log;

class SyncMT5IbUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mt5:sync-ib-users
                            {--dry-run : Run without making changes}
                            {--force : Force sync even if user exists}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync existing MT5 IB users with local database';

    /**
     * Commission integration service
     */
    protected $commissionService;

    /**
     * Create a new command instance.
     */
    public function __construct(IbCommissionIntegrationService $commissionService)
    {
        parent::__construct();
        $this->commissionService = $commissionService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔄 Starting MT5 IB Users Synchronization');
        $this->info('==========================================');

        $dryRun = $this->option('dry-run');
        $force = $this->option('force');

        if ($dryRun) {
            $this->warn('🧪 DRY RUN MODE - No changes will be made');
        }

        try {
            $this->info("📊 Syncing MT5 IB users with local database...");
            
            $startTime = microtime(true);
            
            if ($dryRun) {
                $this->simulateSync();
            } else {
                $result = $this->commissionService->syncMT5IbUsers();
                $this->displayResults($result);
            }

            $endTime = microtime(true);
            $duration = round($endTime - $startTime, 2);
            
            $this->info("⏱️  Sync completed in {$duration} seconds");
            $this->info('✅ MT5 IB users sync finished successfully');

        } catch (\Exception $e) {
            $this->error('❌ Error during IB users sync: ' . $e->getMessage());
            Log::error('MT5 IB users sync error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }

        return 0;
    }

    /**
     * Display sync results
     */
    private function displayResults($result)
    {
        if ($result['success']) {
            $this->info("✅ Successfully processed {$result['total_processed']} MT5 IB users");
            $this->info("🆕 Created {$result['synced_new']} new users");
            $this->info("🔄 Updated {$result['updated_existing']} existing users");
            
            if ($result['synced_new'] > 0 || $result['updated_existing'] > 0) {
                $this->info("📈 Total changes made: " . ($result['synced_new'] + $result['updated_existing']));
            }
        } else {
            $this->error("❌ Sync failed: " . $result['error']);
        }
    }

    /**
     * Simulate sync for dry run mode
     */
    private function simulateSync()
    {
        $this->info('🧪 Simulating MT5 IB users sync...');
        
        $this->info("📊 Would check MT5 IB groups:");
        $this->line("   - real\\Affiliates");
        $this->line("   - real\\IB\\IB MAIN");
        $this->line("   - real\\IB\\IB SUB");
        $this->line("   - real\\Multi-IB\\Default");
        $this->line("   - real\\Multi-IB\\Level1-5");
        
        $this->info("🔍 Would process each MT5 IB user:");
        $this->line("   - Check if user exists in local database");
        $this->line("   - Create new user if not exists");
        $this->line("   - Update IB status based on MT5 group");
        $this->line("   - Set agent relationships");
        $this->line("   - Update MT5 login information");
        
        $this->warn('🧪 DRY RUN - No actual changes made');
    }
}
