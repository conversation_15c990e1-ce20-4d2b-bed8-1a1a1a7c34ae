@extends($activeTemplate . 'layouts.master')
@section('content')

<!-- Include OrgChart.js CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/orgchart@4.0.1/dist/css/jquery.orgchart.min.css" />

<style>
#orgChartContainer {
    min-height: 400px;
    border: 2px dashed #ddd;
    padding: 20px;
    background: #f8f9fa;
}
.debug-info {
    background: #e3f2fd;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 15px;
    font-size: 12px;
}
</style>

<div class="container-fluid">
    <div class="row justify-content-center mt-4">
        <div class="col-xl-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">@lang('Professional Network Tree')</h5>
                    <div class="tree-controls">
                        <button type="button" class="btn btn-primary btn-sm me-2" id="expandAllBtn">
                            <i class="las la-expand-arrows-alt"></i> @lang('Expand All')
                        </button>
                        <button type="button" class="btn btn-secondary btn-sm me-2" id="collapseAllBtn">
                            <i class="las la-compress-arrows-alt"></i> @lang('Collapse All')
                        </button>
                        <button type="button" class="btn btn-success btn-sm" id="exportTreeBtn">
                            <i class="las la-download"></i> @lang('Export')
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Debug Info -->
                    <div class="alert alert-info m-3" id="debugInfo">
                        <strong>🔧 Debug Info:</strong><br>
                        User: {{ $user->firstname }} {{ $user->lastname }} (ID: {{ $user->id }})<br>
                        IB Status: {{ $user->ib_status }} ({{ $user->isIb() ? 'Yes' : 'No' }})<br>
                        Direct Referrals: {{ $networkData['direct_referrals'] ?? 'N/A' }}<br>
                        Tree Data Available: {{ isset($networkData['tree_data']) ? 'Yes' : 'No' }}<br>
                        JavaScript Status: <span id="jsStatus">⏳ Checking...</span><br>
                        OrgChart.js: <span id="orgchartStatus">⏳ Checking...</span><br>
                        BACKEND_DATA: <span id="backendDataStatus">⏳ Checking...</span>
                    </div>
                    
                    <!-- Test Buttons -->
                    <div class="text-center mb-3">
                        <button class="btn btn-sm btn-primary" onclick="testOriginalTree()">🔄 Test Tree</button>
                        <button class="btn btn-sm btn-secondary" onclick="testOriginalFallback()">🔄 Test Fallback</button>
                        <button class="btn btn-sm btn-info" onclick="showOriginalDebug()">🔍 Show Debug</button>
                    </div>

                    <!-- Professional OrgChart Container -->
                    <div id="orgChartContainer" class="org-chart-wrapper">
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-3 text-muted">@lang('Loading network visualization...')</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{{-- Pass data to JavaScript safely --}}
<script>
window.networkDataJson = @json($networkData ?? []);
window.userDataJson = {
    id: {{ $user->id ?? 0 }},
    firstname: @json($user->firstname ?? ''),
    lastname: @json($user->lastname ?? ''),
    ib_type: @json($user->ib_type ?? 'master'),
    mt5_login: @json($user->mt5_login ?? 'N/A'),
    mt5_balance: {{ $user->mt5_balance ?? 0 }}
};
window.routesJson = {
    getReferrals: @json(route('user.partnership.get-referrals')),
    network: @json(route('user.partnership.network'))
};
console.log('✅ USER NETWORK: PHP Data passed successfully');
</script>

@push('script')
<script src="https://cdn.jsdelivr.net/npm/orgchart@4.0.1/dist/js/jquery.orgchart.min.js"></script>
<script>
console.log('✅ USER NETWORK: JavaScript starting...');

// Global variables
let orgChart;
let treeData;

// Create BACKEND_DATA
const BACKEND_DATA = {
    networkData: window.networkDataJson || [],
    user: {
        id: window.userDataJson.id || 0,
        name: (window.userDataJson.firstname || '') + ' ' + (window.userDataJson.lastname || ''),
        title: (window.userDataJson.ib_type || 'master') + ' IB',
        mt5_login: window.userDataJson.mt5_login || 'N/A',
        mt5_balance: (window.userDataJson.mt5_balance || 0).toFixed(2),
        node_type: 'master'
    },
    routes: {
        getReferrals: window.routesJson.getReferrals || '',
        network: window.routesJson.network || ''
    },
    translations: {
        expandAll: 'Expand All',
        collapseAll: 'Collapse All',
        export: 'Export'
    }
};

console.log('✅ USER NETWORK: BACKEND_DATA created:', BACKEND_DATA);

// Update debug status
$('#jsStatus').html('✅ Working');
$('#backendDataStatus').html('✅ Created');
$('#orgchartStatus').html(typeof $.fn.orgchart !== 'undefined' ? '✅ Loaded' : '❌ Missing');

// Working functions for test buttons
function testOriginalTree() {
    console.log('✅ USER NETWORK: Test tree button clicked');
    if (typeof initializeOrgChart === 'function' && typeof treeData !== 'undefined') {
        initializeOrgChart(treeData);
    } else {
        alert('Tree initialization function not available');
    }
}

function testOriginalFallback() {
    console.log('✅ USER NETWORK: Test fallback button clicked');
    if (typeof showFallbackTree === 'function' && typeof treeData !== 'undefined') {
        showFallbackTree(treeData);
    } else {
        alert('Fallback function not available');
    }
}

function showOriginalDebug() {
    console.log('✅ USER NETWORK: Debug button clicked');
    console.log('BACKEND_DATA:', typeof BACKEND_DATA !== 'undefined' ? BACKEND_DATA : 'UNDEFINED');
    console.log('treeData:', typeof treeData !== 'undefined' ? treeData : 'UNDEFINED');
    alert('Debug data logged to console. Check F12 → Console tab');
}

$(document).ready(function() {
    console.log('✅ USER NETWORK: Document ready started');
    
    // Create tree data
    treeData = BACKEND_DATA.networkData.tree_data || {
        id: BACKEND_DATA.user.id,
        name: BACKEND_DATA.user.name,
        title: BACKEND_DATA.user.title,
        children: []
    };
    
    console.log('✅ USER NETWORK: Tree data prepared:', treeData);
    
    // Initialize network tree
    if (typeof $.fn.orgchart !== 'undefined') {
        console.log('✅ USER NETWORK: OrgChart.js available, initializing...');
        initializeOrgChart(treeData);
    } else {
        console.log('⚠️ USER NETWORK: OrgChart.js not available, showing fallback');
        showFallbackTree(treeData);
    }
    
    // Initialize control buttons
    initializeControlButtons();
});

// Initialize OrgChart function
function initializeOrgChart(data) {
    console.log('✅ USER NETWORK: Initializing OrgChart with data:', data);
    const $container = $('#orgChartContainer');
    $container.empty();

    // Check if OrgChart library is loaded
    if (typeof $.fn.orgchart === 'undefined') {
        console.error('❌ USER NETWORK: OrgChart.js library not loaded');
        showFallbackTree(data);
        return;
    }

    try {
        orgChart = $container.orgchart({
            data: data,
            nodeContent: 'name',
            nodeId: 'id',
            direction: 't2b',
            visibleLevel: 2,
            pan: true,
            zoom: true,
            parentNodeSymbol: 'fa-users',
            toggleSiblingsResp: false,
            createNode: function($node, data) {
                // Apply node type classes for styling
                const nodeType = data.node_type || 'client';
                if (nodeType === 'master') {
                    $node.addClass('master-ib-node');
                } else if (nodeType === 'ib') {
                    $node.addClass('sub-ib-node');
                } else {
                    $node.addClass('client-node');
                }

                // Create enhanced node content with complete information
                const nodeHtml = `
                    <div class="title">${data.name}</div>
                    <div class="content">
                        <div style="margin-bottom: 4px;">
                            <strong>${data.title}</strong>
                        </div>
                        <div style="font-size: 11px; opacity: 0.9;">
                            <strong>MT5:</strong> ${data.mt5_login || 'N/A'}
                        </div>
                        <div style="font-size: 11px; opacity: 0.9;">
                            <strong>Balance:</strong> $${data.mt5_balance || '0.00'}
                        </div>
                        <div style="font-size: 11px; opacity: 0.9;">
                            <strong>Deposits:</strong> $${data.total_deposit || '0.00'} (${data.deposit_count || 0})
                        </div>
                        ${data.children_count ? `<div style="font-size: 11px; opacity: 0.9;"><strong>Referrals:</strong> ${data.children_count}</div>` : ''}
                    </div>
                `;
                $node.html(nodeHtml);
            }
        });

        console.log('✅ USER NETWORK: OrgChart initialized successfully');
        
        // Add success message
        setTimeout(() => {
            $container.prepend('<div class="alert alert-success">✅ Network tree loaded successfully!</div>');
        }, 500);
        
    } catch (error) {
        console.error('❌ USER NETWORK: Error initializing OrgChart:', error);
        showFallbackTree(data);
    }
}

// Show fallback tree function
function showFallbackTree(data) {
    console.log('✅ USER NETWORK: Showing fallback tree');
    const $container = $('#orgChartContainer');
    
    const fallbackHtml = `
        <div class="alert alert-warning">⚠️ Using fallback tree visualization</div>
        <div class="text-center">
            <h4>Network Tree (Fallback Mode)</h4>
            <div class="row justify-content-center">
                <div class="col-md-4">
                    <div class="card border-primary">
                        <div class="card-body text-center">
                            <h6 class="card-title">${data.name || 'User'}</h6>
                            <p class="card-text">
                                <small class="text-muted">MT5: ${data.mt5_login || 'N/A'}</small><br>
                                <small class="text-muted">ID: ${data.id || 'N/A'}</small>
                            </p>
                            <span class="badge bg-primary">${data.title || 'Master IB'}</span>
                        </div>
                    </div>
                </div>
            </div>
            ${data.children && data.children.length > 0 ? `
            <div class="row justify-content-center mt-3">
                ${data.children.map(child => `
                <div class="col-md-3">
                    <div class="card border-success">
                        <div class="card-body text-center">
                            <h6 class="card-title">${child.name}</h6>
                            <span class="badge bg-success">${child.title}</span>
                        </div>
                    </div>
                </div>
                `).join('')}
            </div>
            ` : ''}
        </div>
    `;
    
    $container.html(fallbackHtml);
    console.log('✅ USER NETWORK: Fallback tree displayed');
}

// Control button functions
function initializeControlButtons() {
    console.log('✅ USER NETWORK: Initializing control buttons');
    
    // Expand All button
    $('#expandAllBtn').off('click').on('click', function() {
        console.log('✅ USER NETWORK: Expand All clicked');
        if (orgChart && typeof orgChart.expandAllNodes === 'function') {
            try {
                orgChart.expandAllNodes();
                console.log('✅ USER NETWORK: All nodes expanded');
            } catch (error) {
                console.error('❌ USER NETWORK: Error expanding nodes:', error);
                // Fallback: manually expand nodes
                $('.orgchart .node .edge').each(function() {
                    if ($(this).hasClass('collapsed')) {
                        $(this).click();
                    }
                });
            }
        } else {
            // Expand fallback tree or manual expansion
            $('.children-fallback').addClass('show');
            $('.expand-fallback-btn i').removeClass('la-chevron-down').addClass('la-chevron-up');
            console.log('✅ USER NETWORK: Fallback tree expanded');
        }
    });
    
    // Collapse All button
    $('#collapseAllBtn').off('click').on('click', function() {
        console.log('✅ USER NETWORK: Collapse All clicked');
        if (orgChart && typeof orgChart.collapseAllNodes === 'function') {
            try {
                orgChart.collapseAllNodes();
                console.log('✅ USER NETWORK: All nodes collapsed');
            } catch (error) {
                console.error('❌ USER NETWORK: Error collapsing nodes:', error);
                // Fallback: reinitialize chart
                initializeOrgChart(treeData);
                console.log('✅ USER NETWORK: Chart reinitialized (collapsed)');
            }
        } else {
            // Collapse fallback tree
            $('.children-fallback').removeClass('show');
            $('.expand-fallback-btn i').removeClass('la-chevron-up').addClass('la-chevron-down');
            console.log('✅ USER NETWORK: Fallback tree collapsed');
        }
    });
    
    // Export button with improved functionality
    $('#exportTreeBtn').off('click').on('click', function() {
        console.log('✅ USER NETWORK: Export clicked');

        if (orgChart && typeof orgChart.export === 'function') {
            try {
                // Try OrgChart built-in export first
                orgChart.export('Partnership-Network-Tree', 'png');
                console.log('✅ USER NETWORK: Chart exported successfully');
            } catch (error) {
                console.error('❌ USER NETWORK: OrgChart export failed:', error);
                exportWithHtml2Canvas();
            }
        } else {
            // Fallback to html2canvas export
            exportWithHtml2Canvas();
        }
    });

    // HTML2Canvas export function
    function exportWithHtml2Canvas() {
        console.log('✅ USER NETWORK: Using html2canvas export');

        // Load html2canvas if not already loaded
        if (typeof html2canvas === 'undefined') {
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js';
            script.onload = function() {
                performHtml2CanvasExport();
            };
            document.head.appendChild(script);
        } else {
            performHtml2CanvasExport();
        }
    }

    function performHtml2CanvasExport() {
        const container = document.getElementById('orgChartContainer');
        if (!container) {
            console.error('❌ USER NETWORK: Container not found for export');
            alert('Export failed: Container not found');
            return;
        }

        html2canvas(container, {
            backgroundColor: '#ffffff',
            scale: 2,
            useCORS: true,
            allowTaint: true
        }).then(canvas => {
            const link = document.createElement('a');
            link.download = 'partnership-network-tree.png';
            link.href = canvas.toDataURL('image/png');
            link.click();
            console.log('✅ USER NETWORK: Export completed successfully');
        }).catch(error => {
            console.error('❌ USER NETWORK: html2canvas export failed:', error);
            alert('Export failed. Please try again.');
        });
    }
    
    console.log('✅ USER NETWORK: Control buttons initialized successfully');
}

}); // Close $(document).ready function

</script>
@endpush

@endsection
