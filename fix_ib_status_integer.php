<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔧 FIXING IB STATUS WITH CORRECT INTEGER VALUES\n";
echo "===============================================\n";

// Update all partner=1 users to have ib_status=1 (approved)
echo "\n🔧 Updating partner=1 users to ib_status=1 (approved):\n";

$affected = \DB::update("UPDATE users SET ib_status = 1 WHERE partner = 1");
echo "✅ Updated {$affected} users with partner=1\n";

// Verify the update
echo "\n🔍 Verifying database update:\n";
$users = \DB::select("SELECT email, partner, ib_status FROM users WHERE partner = 1 LIMIT 5");
foreach ($users as $user) {
    echo "   - {$user->email}: partner={$user->partner}, ib_status={$user->ib_status}\n";
}

// Test specific user
echo "\n🔍 Testing specific user: <EMAIL>\n";
$testUser = \App\Models\User::where('email', '<EMAIL>')->first();
if ($testUser) {
    echo "   Partner: {$testUser->partner}\n";
    echo "   IB Status: {$testUser->ib_status}\n";
    echo "   IB Type: {$testUser->ib_type}\n";
    echo "   isIb(): " . ($testUser->isIb() ? 'true' : 'false') . "\n";
    echo "   Can access partnership: " . ($testUser->isIb() ? 'YES' : 'NO') . "\n";
}

// Test all approved IBs
echo "\n🔍 Testing all approved IBs:\n";
$approvedIBs = \App\Models\User::where('partner', 1)->where('ib_status', 1)->get();
echo "Found {$approvedIBs->count()} approved IBs:\n";

foreach ($approvedIBs->take(10) as $ib) {
    echo "   - {$ib->email}: isIb() = " . ($ib->isIb() ? 'true' : 'false') . "\n";
}

// Check database status distribution
echo "\n📊 IB Status distribution after fix:\n";
$statusCounts = \DB::select("
    SELECT 
        CASE 
            WHEN partner = 0 THEN 'Regular User'
            WHEN partner = 1 AND ib_status = 1 THEN 'Approved IB'
            WHEN partner = 2 AND ib_status = 2 THEN 'Pending IB'
            WHEN partner = 3 AND ib_status = 3 THEN 'Rejected IB'
            ELSE 'Other'
        END as status_type,
        COUNT(*) as count
    FROM users 
    GROUP BY 
        CASE 
            WHEN partner = 0 THEN 'Regular User'
            WHEN partner = 1 AND ib_status = 1 THEN 'Approved IB'
            WHEN partner = 2 AND ib_status = 2 THEN 'Pending IB'
            WHEN partner = 3 AND ib_status = 3 THEN 'Rejected IB'
            ELSE 'Other'
        END
    ORDER BY count DESC
");

foreach ($statusCounts as $status) {
    echo "   - {$status->status_type}: {$status->count} users\n";
}

// Test the constants
echo "\n🔍 Testing User model constants:\n";
echo "   IB_STATUS_NONE: " . \App\Models\User::IB_STATUS_NONE . "\n";
echo "   IB_STATUS_PENDING: " . \App\Models\User::IB_STATUS_PENDING . "\n";
echo "   IB_STATUS_APPROVED: " . \App\Models\User::IB_STATUS_APPROVED . "\n";
echo "   IB_STATUS_REJECTED: " . \App\Models\User::IB_STATUS_REJECTED . "\n";

echo "\n✅ IB status integer fix completed!\n";
echo "\n🎯 VERIFICATION URLS:\n";
echo "   Admin Active IBs: /admin/ib_settings/activeIB\n";
echo "   User Partnership: /user/partnership/dashboard\n";
echo "   Test User Login: <EMAIL>\n";
