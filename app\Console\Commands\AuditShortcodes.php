<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\NotificationTemplate;

class AuditShortcodes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:audit-shortcodes {--missing : Show only missing shortcodes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Audit all email templates for shortcode usage and identify missing implementations';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('🔍 COMPREHENSIVE SHORTCODE AUDIT');
        $this->line('=====================================');

        $templates = NotificationTemplate::all();
        $allShortcodes = [];
        $templateShortcodes = [];

        // Scan all templates for shortcodes
        foreach ($templates as $template) {
            $content = $template->email_body . ' ' . $template->subj . ' ' . $template->sms_body;
            
            // Find all shortcodes in format {{shortcode}}
            preg_match_all('/\{\{([^}]+)\}\}/', $content, $matches);
            
            $foundShortcodes = array_unique($matches[1]);
            $templateShortcodes[$template->id] = [
                'name' => $template->name,
                'act' => $template->act,
                'shortcodes' => $foundShortcodes
            ];
            
            foreach ($foundShortcodes as $shortcode) {
                if (!in_array($shortcode, $allShortcodes)) {
                    $allShortcodes[] = $shortcode;
                }
            }
        }

        sort($allShortcodes);

        $this->line("\n📊 SHORTCODE USAGE SUMMARY:");
        $this->line("Total templates scanned: " . count($templates));
        $this->line("Unique shortcodes found: " . count($allShortcodes));

        // Get implemented shortcodes from ShortcodeService
        $testUser = (object) ['fullname' => 'Test', 'username' => 'test', 'email' => '<EMAIL>'];
        $implementedShortcodes = array_keys(\App\Services\ShortcodeService::getShortcodes($testUser, []));

        // Identify missing shortcodes
        $missingShortcodes = array_diff($allShortcodes, $implementedShortcodes);

        if ($this->option('missing')) {
            $this->line("\n❌ MISSING SHORTCODES (" . count($missingShortcodes) . "):");
            foreach ($missingShortcodes as $shortcode) {
                $this->line("  - {{" . $shortcode . "}}");
            }
        } else {
            $this->line("\n📋 ALL SHORTCODES FOUND:");
            foreach ($allShortcodes as $shortcode) {
                $status = in_array($shortcode, $implementedShortcodes) ? '✅' : '❌';
                $this->line("  $status {{" . $shortcode . "}}");
            }

            $this->line("\n❌ MISSING SHORTCODES (" . count($missingShortcodes) . "):");
            foreach ($missingShortcodes as $shortcode) {
                $this->line("  - {{" . $shortcode . "}}");
            }

            $this->line("\n📧 TEMPLATES WITH MISSING SHORTCODES:");
            foreach ($templateShortcodes as $templateId => $data) {
                $templateMissing = array_intersect($data['shortcodes'], $missingShortcodes);
                if (!empty($templateMissing)) {
                    $this->line("  Template {$templateId}: {$data['name']} ({$data['act']})");
                    foreach ($templateMissing as $missing) {
                        $this->line("    - {{" . $missing . "}}");
                    }
                }
            }
        }

        return 0;
    }
}
