@extends('admin.layouts.app')

@section('panel')
<div class="row">
    <div class="col-lg-12">
        <div class="card b-radius--10">
            <div class="card-header">
                <h5 class="card-title">@lang('Edit IB Group - ') {{ $ibGroup->name }}</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.ib.groups.update', $ibGroup->id) }}" method="POST">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Group Name') <span class="text-danger">*</span></label>
                                <input type="text" name="name" class="form-control" value="{{ old('name', $ibGroup->name) }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Commission Multiplier') <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" name="commission_multiplier" class="form-control" step="0.01" min="0" max="10" value="{{ old('commission_multiplier', $ibGroup->commission_multiplier) }}" required>
                                    <div class="input-group-text">x</div>
                                </div>
                                <small class="text-muted">@lang('Multiplier for base commission rates (e.g., 1.5 = 150% of base rate)')</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Maximum Levels') <span class="text-danger">*</span></label>
                                <input type="number" name="max_levels" class="form-control" min="1" max="10" value="{{ old('max_levels', $ibGroup->max_levels) }}" required>
                                <small class="text-muted">@lang('Maximum hierarchy levels allowed in this group')</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Status')</label>
                                <select name="status" class="form-control">
                                    <option value="1" {{ old('status', $ibGroup->status) == 1 ? 'selected' : '' }}>@lang('Active')</option>
                                    <option value="0" {{ old('status', $ibGroup->status) == 0 ? 'selected' : '' }}>@lang('Inactive')</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>@lang('Description')</label>
                        <textarea name="description" class="form-control" rows="4">{{ old('description', $ibGroup->description) }}</textarea>
                    </div>

                    <div class="form-group">
                        <label>@lang('Additional Rules') <small class="text-muted">(@lang('Optional - JSON format'))</small></label>
                        <textarea name="rules" class="form-control" rows="6" placeholder='{"min_clients": 10, "min_volume": 100, "regions": ["Asia", "Europe"]}'>{{ old('rules', $ibGroup->rules_json ? json_encode($ibGroup->rules_json, JSON_PRETTY_PRINT) : '') }}</textarea>
                        <small class="text-muted">@lang('Enter additional rules in JSON format for advanced configurations')</small>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn--primary h-45 w-100">@lang('Update IB Group')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Current Members -->
<div class="row mt-4">
    <div class="col-lg-12">
        <div class="card b-radius--10">
            <div class="card-header">
                <h5 class="card-title">@lang('Current Group Members')</h5>
                <small class="text-muted">@lang('Users currently assigned to this group')</small>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive--sm table-responsive">
                    <table class="table table--light style--two">
                        <thead>
                            <tr>
                                <th>@lang('User')</th>
                                <th>@lang('IB Type')</th>
                                <th>@lang('IB Status')</th>
                                <th>@lang('Joined Date')</th>
                                <th>@lang('Action')</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($ibGroup->users as $user)
                            <tr>
                                <td>
                                    <div>
                                        <strong>{{ $user->fullname }}</strong>
                                        <br>
                                        <small class="text-muted">{{ $user->username }}</small>
                                    </div>
                                </td>
                                <td>
                                    @if($user->ib_type)
                                        <span class="badge badge--{{ $user->ib_type == 'master' ? 'primary' : 'info' }}">
                                            {{ ucfirst($user->ib_type) }} IB
                                        </span>
                                    @else
                                        <span class="text-muted">N/A</span>
                                    @endif
                                </td>
                                <td>
                                    @if($user->ib_status == 'approved')
                                        <span class="badge badge--success">@lang('Approved')</span>
                                    @elseif($user->ib_status == 'pending')
                                        <span class="badge badge--warning">@lang('Pending')</span>
                                    @elseif($user->ib_status == 'rejected')
                                        <span class="badge badge--danger">@lang('Rejected')</span>
                                    @else
                                        <span class="text-muted">N/A</span>
                                    @endif
                                </td>
                                <td>{{ showDateTime($user->ib_approved_at ?? $user->created_at) }}</td>
                                <td>
                                    <a href="{{ route('admin.users.detail', $user->id) }}" class="btn btn-sm btn-outline--primary">
                                        <i class="las la-eye"></i> @lang('View')
                                    </a>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="5" class="text-center">@lang('No members in this group yet')</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('breadcrumb-plugins')
<div class="d-inline">
    <a href="{{ route('admin.ib.groups.show', $ibGroup->id) }}" class="btn btn--dark">
        <i class="las la-arrow-left"></i> @lang('Back to Group Details')
    </a>
</div>
@endpush

@push('script')
<script>
(function($) {
    "use strict";

    // JSON validation for rules field
    $('textarea[name="rules"]').on('blur', function() {
        const value = $(this).val().trim();
        if (value && value !== '') {
            try {
                JSON.parse(value);
                $(this).removeClass('is-invalid').addClass('is-valid');
            } catch (e) {
                $(this).removeClass('is-valid').addClass('is-invalid');
                alert('Invalid JSON format in Additional Rules field');
            }
        } else {
            $(this).removeClass('is-invalid is-valid');
        }
    });

})(jQuery);
</script>
@endpush
