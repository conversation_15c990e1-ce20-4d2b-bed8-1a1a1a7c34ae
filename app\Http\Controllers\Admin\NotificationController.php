<?php

namespace App\Http\Controllers\Admin;

use App\Constants\Status;
use App\Http\Controllers\Controller;
use App\Models\NotificationTemplate;
use App\Notify\Sms;
use App\Services\TemplateRestorationService;
use Illuminate\Http\Request;

class NotificationController extends Controller
{
    public function global(){
        $pageTitle = 'Global Template for Notification';
        return view('admin.notification.global_template',compact('pageTitle'));
    }

    public function globalUpdate(Request $request){
        $request->validate([
            'email_from' => 'required|email|string|max:40',
            'sms_from' => 'required|string|max:40',
            'email_template' => 'required',
            'sms_body' => 'required',
        ]);

        $general = gs();
        $general->email_from = $request->email_from;

        // Use the final email template from visual editor if available, otherwise use regular template
        $emailTemplate = $request->email_template_final ?: $request->email_template;
        $general->email_template = $emailTemplate;

        $general->sms_from = $request->sms_from;
        $general->sms_body = $request->sms_body;
        $general->save();

        $notify[] = ['success','Global notification settings updated successfully'];
        return back()->withNotify($notify);
    }

    public function templates(Request $request){
        $pageTitle = 'Notification Templates';

        // Get search and filter parameters
        $search = $request->get('search');
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');
        $perPage = $request->get('per_page', 15);

        // Validate sort parameters
        $allowedSortFields = ['name', 'subj', 'act', 'created_at', 'updated_at', 'email_status'];
        if (!in_array($sortBy, $allowedSortFields)) {
            $sortBy = 'name';
        }

        if (!in_array($sortOrder, ['asc', 'desc'])) {
            $sortOrder = 'asc';
        }

        // Build query
        $query = NotificationTemplate::query();

        // Apply search filter
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('subj', 'LIKE', "%{$search}%")
                  ->orWhere('act', 'LIKE', "%{$search}%")
                  ->orWhere('email_body', 'LIKE', "%{$search}%");
            });
        }

        // Apply sorting
        $query->orderBy($sortBy, $sortOrder);

        // Get paginated results
        $templates = $query->paginate($perPage)->withQueryString();

        // Get statistics
        $stats = [
            'total' => NotificationTemplate::count(),
            'active_email' => NotificationTemplate::where('email_status', 1)->count(),
            'active_sms' => NotificationTemplate::where('sms_status', 1)->count(),
            'inactive' => NotificationTemplate::where('email_status', 0)->where('sms_status', 0)->count(),
        ];

        return view('admin.notification.templates', compact('pageTitle', 'templates', 'stats', 'search', 'sortBy', 'sortOrder', 'perPage'));
    }

    public function templateEdit($id)
    {
        $template = NotificationTemplate::findOrFail($id);
        $pageTitle = $template->name;
        return view('admin.notification.edit', compact('pageTitle', 'template'));
    }

    public function templateUpdate(Request $request,$id){
        // ENHANCED DEBUG LOGGING FOR WINDOWS SERVER TROUBLESHOOTING
        \Log::info("=== TEMPLATE UPDATE DEBUG START ===");
        \Log::info("Template ID: {$id}");
        \Log::info("Request Method: " . $request->method());
        \Log::info("Request URL: " . $request->fullUrl());
        \Log::info("User Agent: " . $request->userAgent());
        \Log::info("Server Environment: " . PHP_OS . " - PHP " . PHP_VERSION);
        \Log::info("Content Type: " . $request->header('Content-Type', 'Not Set'));
        \Log::info("Content Length: " . $request->header('Content-Length', 'Not Set'));

        // CRITICAL: Log raw input data to identify corruption source
        \Log::info("Raw POST data keys: " . implode(', ', array_keys($request->all())));
        \Log::info("Email body field exists: " . ($request->has('email_body') ? 'YES' : 'NO'));
        \Log::info("Email body final field exists: " . ($request->has('email_body_final') ? 'YES' : 'NO'));

        $request->validate([
            'subject' => 'required|string|max:255',
            'email_body' => 'required',
            'sms_body' => 'required',
        ]);

        \Log::info("✅ Validation passed");

        $template = NotificationTemplate::findOrFail($id);
        \Log::info("✅ Template found - Current subject: " . $template->subj);
        \Log::info("✅ Template found - Current email_body length: " . strlen($template->email_body));

        $template->subj = $request->subject;
        \Log::info("✅ Subject updated to: " . $request->subject);

        // ENHANCED EMAIL BODY PROCESSING WITH BASE64 DECODING FOR WINDOWS SERVER
        \Log::info("=== ENHANCED EMAIL BODY PROCESSING WITH BASE64 SUPPORT ===");

        // Check for base64 encoded content first (Windows Server compatibility)
        if ($request->has('email_body_encoded') && !empty($request->input('email_body_encoded'))) {
            \Log::info("📦 Base64 encoded content detected");
            try {
                $encodedContent = $request->input('email_body_encoded');
                $decodedContent = base64_decode($encodedContent);

                // Verify the decoded content is valid
                if ($decodedContent !== false && !empty($decodedContent)) {
                    $emailBody = $decodedContent;
                    \Log::info("✅ Base64 content decoded successfully, length: " . strlen($emailBody));
                    \Log::info("📝 Decoded content preview: " . substr($emailBody, 0, 200));
                } else {
                    \Log::warning("⚠️ Base64 decoding failed, falling back to regular content");
                    $emailBody = $request->input('email_body_final') ?: $request->input('email_body');
                }
            } catch (\Exception $e) {
                \Log::error("❌ Base64 decoding error: " . $e->getMessage());
                $emailBody = $request->input('email_body_final') ?: $request->input('email_body');
            }
        } else {
            // Fallback to regular content processing
            \Log::info("📄 Using regular content processing");
            $emailBody = $request->input('email_body_final') ?: $request->input('email_body');
        }

        \Log::info("Email body source: " . ($request->has('email_body_final') ? 'email_body_final' : 'email_body'));
        \Log::info("Email body length: " . strlen((string)$emailBody));
        \Log::info("Email body preview (first 200 chars): " . substr((string)$emailBody, 0, 200));

        // CRITICAL: Minimal processing to prevent content loss on Windows Server
        if (!empty($emailBody)) {
            // Only apply essential Windows Server fixes
            // Remove BOM if present (common on Windows servers)
            $emailBody = preg_replace('/^\xEF\xBB\xBF/', '', $emailBody);

            // Normalize line endings (Windows vs Unix)
            $emailBody = str_replace(["\r\n", "\r"], "\n", $emailBody);

            // Remove any null bytes that might cause issues
            $emailBody = str_replace("\0", '', $emailBody);

            \Log::info("After minimal Windows cleanup - Email body length: " . strlen((string)$emailBody));
        }

        // CRITICAL: Use content as-is from editor - NO COMPLEX PROCESSING
        \Log::info("Template {$id}: Using content directly from editor with minimal cleanup");

        // Only provide default content if completely empty
        if (empty(trim($emailBody))) {
            \Log::info("Template {$id}: Content is completely empty, using default");
            $emailBody = '<p>Dear {{fullname}},</p><p>This is a notification from {{site_name}}.</p><p>Best regards,<br>MBFX Team</p>';
        }

        // Log final results
        \Log::info("=== FINAL CONTENT READY FOR SAVE ===");
        \Log::info("Final email body length: " . strlen((string)$emailBody));
        \Log::info("Final email body preview (first 300 chars): " . substr((string)$emailBody, 0, 300));

        // SKIP ALL CORRUPTION DETECTION AND COMPLEX PROCESSING
        // This was causing content duplication and corruption
        \Log::info("Template {$id}: Skipping all corruption detection and complex processing to prevent issues");

        // COMPREHENSIVE DATABASE SAVE DEBUG
        \Log::info("=== DATABASE SAVE OPERATION DEBUG ===");

        // Log before save
        \Log::info("Before save - Template email_body length: " . strlen($template->email_body));
        \Log::info("Before save - New email_body length: " . strlen($emailBody));
        \Log::info("Before save - Template dirty: " . json_encode($template->getDirty()));

        // Set all fields
        $template->email_body = $emailBody;
        $template->email_status = $request->email_status ? Status::ENABLE : Status::DISABLE;
        $template->sms_body = $request->sms_body;
        $template->sms_status = $request->sms_status ? Status::ENABLE : Status::DISABLE;

        // Log after setting fields but before save
        \Log::info("After setting fields - Template dirty: " . json_encode($template->getDirty()));
        \Log::info("After setting fields - Template email_body length: " . strlen($template->email_body));

        // Attempt to save with error handling
        try {
            $saveResult = $template->save();
            \Log::info("Save operation result: " . ($saveResult ? 'SUCCESS' : 'FAILED'));

            // Verify save by reloading from database
            $template->refresh();
            \Log::info("After refresh - Template email_body length: " . strlen($template->email_body));
            \Log::info("After refresh - Content matches: " . ($template->email_body === $emailBody ? 'YES' : 'NO'));

            if ($template->email_body !== $emailBody) {
                \Log::error("❌ CRITICAL: Database content does not match saved content!");
                \Log::error("Expected length: " . strlen($emailBody));
                \Log::error("Actual length: " . strlen($template->email_body));
                \Log::error("Expected preview: " . substr($emailBody, 0, 100));
                \Log::error("Actual preview: " . substr($template->email_body, 0, 100));
            }

        } catch (\Exception $e) {
            \Log::error("❌ CRITICAL: Database save failed with exception: " . $e->getMessage());
            \Log::error("Exception trace: " . $e->getTraceAsString());
            throw $e;
        }

        \Log::info("✅ Template {$id}: Database operation completed");
        \Log::info("=== TEMPLATE UPDATE DEBUG END ===");

        // Handle AJAX requests (for Windows Server compatibility)
        if ($request->ajax() || $request->wantsJson()) {
            \Log::info("📤 Returning AJAX response");
            return response()->json([
                'status' => 'success',
                'message' => 'Notification template updated successfully',
                'template' => [
                    'id' => $template->id,
                    'subject' => $template->subj,
                    'email_body' => $template->email_body,
                    'email_status' => $template->email_status,
                    'sms_status' => $template->sms_status
                ]
            ]);
        }

        // Regular form submission response
        $notify[] = ['success','Notification template updated successfully'];
        return back()->withNotify($notify);
    }

    public function templateCreate(Request $request){
        $request->validate([
            'act' => 'required|string|max:255|unique:notification_templates,act',
            'name' => 'required|string|max:255',
            'subj' => 'required|string|max:255',
            'email_body' => 'required',
            'sms_body' => 'required',
            'shortcodes' => 'nullable|json',
        ]);

        $template = new NotificationTemplate();
        $template->act = strtoupper($request->act);
        $template->name = $request->name;
        $template->subj = $request->subj;

        // Use the final email body from visual editor if available, otherwise use regular email body
        $emailBody = $request->email_body_final ?: $request->email_body;
        $template->email_body = $emailBody;

        $template->sms_body = $request->sms_body;
        $template->email_status = $request->email_status ? Status::ENABLE : Status::DISABLE;
        $template->sms_status = $request->sms_status ? Status::ENABLE : Status::DISABLE;

        // Handle shortcodes
        if ($request->shortcodes) {
            $shortcodes = json_decode($request->shortcodes, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $template->shortcodes = json_encode($shortcodes);
            } else {
                $template->shortcodes = json_encode([
                    'fullname' => 'Full name of the user',
                    'username' => 'Username of the user',
                    'site_name' => 'Name of the site'
                ]);
            }
        } else {
            $template->shortcodes = json_encode([
                'fullname' => 'Full name of the user',
                'username' => 'Username of the user',
                'site_name' => 'Name of the site'
            ]);
        }

        $template->save();

        $notify[] = ['success','Email template created successfully'];
        return back()->withNotify($notify);
    }

    public function emailSetting(){
        $pageTitle = 'Email Notification Settings';
        return view('admin.notification.email_setting', compact('pageTitle'));
    }

    public function emailSettingUpdate(Request $request)
    {
        $request->validate([
            'email_method' => 'required|in:php,smtp,sendgrid,mailjet',
            'host' => 'required_if:email_method,smtp',
            'port' => 'required_if:email_method,smtp',
            'username' => 'required_if:email_method,smtp',
            'password' => 'required_if:email_method,smtp',
            'enc' => 'required_if:email_method,smtp',
            'appkey' => 'required_if:email_method,sendgrid',
            'public_key' => 'required_if:email_method,mailjet',
            'secret_key' => 'required_if:email_method,mailjet',
        ], [
            'host.required_if' => ':attribute is required for SMTP configuration',
            'port.required_if' => ':attribute is required for SMTP configuration',
            'username.required_if' => ':attribute is required for SMTP configuration',
            'password.required_if' => ':attribute is required for SMTP configuration',
            'enc.required_if' => ':attribute is required for SMTP configuration',
            'appkey.required_if' => ':attribute is required for SendGrid configuration',
            'public_key.required_if' => ':attribute is required for Mailjet configuration',
            'secret_key.required_if' => ':attribute is required for Mailjet configuration',
        ]);
        if ($request->email_method == 'php') {
            $data['name'] = 'php';
        } else if ($request->email_method == 'smtp') {
            $request->merge(['name' => 'smtp']);
            $data = $request->only('name', 'host', 'port', 'enc', 'username', 'password', 'driver');
        } else if ($request->email_method == 'sendgrid') {
            $request->merge(['name' => 'sendgrid']);
            $data = $request->only('name', 'appkey');
        } else if ($request->email_method == 'mailjet') {
            $request->merge(['name' => 'mailjet']);
            $data = $request->only('name', 'public_key', 'secret_key');
        }
        $general = gs();
        $general->mail_config = $data;
        $general->save();
        $notify[] = ['success', 'Email settings updated successfully'];
        return back()->withNotify($notify);
    }

    public function emailTest(Request $request){
        $request->validate([
           'email' => 'required|email'
       ]);

       $general = gs();
       $config = $general->mail_config;
       $receiverName = explode('@', $request->email)[0];
       $subject = strtoupper($config->name).' Configuration Success';
       $message = 'Your email notification setting is configured successfully for '.$general->site_name;

       if ($general->en) {
           $user = [
               'username'=>$request->email,
               'email'=>$request->email,
               'fullname'=>$receiverName,
           ];
           notify($user,'DEFAULT',[
               'subject'=>$subject,
               'message'=>$message,
           ],['email'],false);
       }else{
           $notify[] = ['info', 'Please enable from general settings'];
           $notify[] = ['error', 'Your email notification is disabled'];
           return back()->withNotify($notify);
       }

       if (session('mail_error')) {
           $notify[] = ['error', session('mail_error')];
       }else{
           $notify[] = ['success', 'Email sent to ' . $request->email . ' successfully'];
       }

       return back()->withNotify($notify);
   }

   public  function smsSetting(){
        $pageTitle = 'SMS Notification Settings';
        return view('admin.notification.sms_setting', compact('pageTitle'));
    }


    public function smsSettingUpdate(Request $request){
        $request->validate([
            'sms_method' => 'required|in:clickatell,infobip,messageBird,nexmo,smsBroadcast,twilio,textMagic,custom',
            'clickatell_api_key' => 'required_if:sms_method,clickatell',
            'message_bird_api_key' => 'required_if:sms_method,messageBird',
            'nexmo_api_key' => 'required_if:sms_method,nexmo',
            'nexmo_api_secret' => 'required_if:sms_method,nexmo',
            'infobip_username' => 'required_if:sms_method,infobip',
            'infobip_password' => 'required_if:sms_method,infobip',
            'sms_broadcast_username' => 'required_if:sms_method,smsBroadcast',
            'sms_broadcast_password' => 'required_if:sms_method,smsBroadcast',
            'text_magic_username' => 'required_if:sms_method,textMagic',
            'apiv2_key' => 'required_if:sms_method,textMagic',
            'account_sid' => 'required_if:sms_method,twilio',
            'auth_token' => 'required_if:sms_method,twilio',
            'from' => 'required_if:sms_method,twilio',
            'custom_api_method' => 'required_if:sms_method,custom|in:get,post',
            'custom_api_url' => 'required_if:sms_method,custom',
        ]);

        $data = [
            'name'=>$request->sms_method,
            'clickatell'=>[
                'api_key'=>$request->clickatell_api_key,
            ],
            'infobip'=>[
                'username'=>$request->infobip_username,
                'password'=>$request->infobip_password,
            ],
            'message_bird'=>[
                'api_key'=>$request->message_bird_api_key,
            ],
            'nexmo'=>[
                'api_key'=>$request->nexmo_api_key,
                'api_secret'=>$request->nexmo_api_secret,
            ],
            'sms_broadcast'=>[
                'username'=>$request->sms_broadcast_username,
                'password'=>$request->sms_broadcast_password,
            ],
            'twilio'=>[
                'account_sid'=>$request->account_sid,
                'auth_token'=>$request->auth_token,
                'from'=>$request->from,
            ],
            'text_magic'=>[
                'username'=>$request->text_magic_username,
                'apiv2_key'=>$request->apiv2_key,
            ],
            'custom'=>[
                'method'=>$request->custom_api_method,
                'url'=>$request->custom_api_url,
                'headers'=>[
                    'name'=>$request->custom_header_name ?? [],
                    'value'=>$request->custom_header_value ?? [],
                ],
                'body'=>[
                    'name'=>$request->custom_body_name ?? [],
                    'value'=>$request->custom_body_value ?? [],
                ],
            ],
        ];
        $general = gs();
        $general->sms_config = $data;
        $general->save();
        $notify[] = ['success', 'Sms settings updated successfully'];
        return back()->withNotify($notify);
    }

    public function smsTest(Request $request){
        $request->validate(['mobile' => 'required']);
        $general = gs();
        if ($general->sn == 1) {
            $sendSms = new Sms;
            $sendSms->mobile = $request->mobile;
            $sendSms->receiverName = ' ';
            $sendSms->message = 'Your sms notification setting is configured successfully for '.$general->site_name;
            $sendSms->subject = ' ';
            $sendSms->send();
        }else{
            $notify[] = ['info', 'Please enable from general settings'];
            $notify[] = ['error', 'Your sms notification is disabled'];
            return back()->withNotify($notify);
        }

        if (session('sms_error')) {
            $notify[] = ['error', session('sms_error')];
        }else{
            $notify[] = ['success', 'SMS sent to ' . $request->mobile . 'successfully'];
        }

        return back()->withNotify($notify);
    }

    /**
     * Restore all email templates for Visual Builder compatibility
     */
    public function restoreAllTemplates()
    {
        try {
            $restorationService = new TemplateRestorationService();
            $results = $restorationService->restoreAllTemplates();

            // Also restore global template
            $globalRestored = $restorationService->restoreGlobalTemplate();

            $message = "Templates restored: {$results['restored']}, Errors: {$results['errors']}";
            if ($globalRestored) {
                $message .= ", Global template: Restored";
            }

            // Return JSON response for AJAX
            return response()->json([
                'success' => true,
                'message' => $message,
                'data' => [
                    'restored' => $results['restored'],
                    'errors' => $results['errors'],
                    'global_restored' => $globalRestored,
                    'templates' => $results['templates']
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Template restoration failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Restore a single email template
     */
    public function restoreTemplate($id)
    {
        try {
            $template = NotificationTemplate::findOrFail($id);
            $restorationService = new TemplateRestorationService();

            if ($restorationService->restoreTemplate($template)) {
                return response()->json([
                    'success' => true,
                    'message' => "Template '{$template->name}' restored successfully",
                    'data' => [
                        'template_id' => $template->id,
                        'template_name' => $template->name
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => "Failed to restore template '{$template->name}'"
                ], 500);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Template restoration failed: ' . $e->getMessage()
            ], 500);
        }
    }



    /**
     * Restore global template for Visual Builder compatibility
     */
    public function restoreGlobalTemplate()
    {
        try {
            $restorationService = new TemplateRestorationService();
            $restored = $restorationService->restoreGlobalTemplate();

            if ($restored) {
                return response()->json([
                    'success' => true,
                    'message' => 'Global template restored successfully',
                    'data' => [
                        'restored' => true
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to restore global template'
                ], 500);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Global template restoration failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send test email for template
     */
    public function sendTestEmail(Request $request)
    {
        $request->validate([
            'template_id' => 'required|exists:notification_templates,id',
            'test_email' => 'required|email'
        ]);

        try {
            $template = NotificationTemplate::findOrFail($request->template_id);
            $testEmail = $request->test_email;

            // Create sample data for testing
            $sampleData = [
                'fullname' => 'Test User',
                'username' => 'testuser',
                'email' => $testEmail,
                'site_name' => config('app.name', 'MBFX'),
                'site_url' => config('app.url'),
                'amount' => '1,000.00',
                'currency' => 'USD',
                'balance' => '5,000.00',
                'new_balance' => '6,000.00',
                'mt5_login' => '12345678',
                'mt5_group' => 'real\\MBFX\\B\\Sf\\Cp\\Fake',
                'leverage' => '500',
                'server_name' => 'MBFX-Live',
                'transaction_id' => 'TEST' . time(),
                'transaction_date' => now()->format('Y-m-d H:i:s'),
                'code' => '123456',
                'ip_address' => request()->ip(),
                'location' => 'Test Location'
            ];

            // Replace shortcodes with sample data
            $emailBody = $template->email_body;
            $emailSubject = $template->subj;

            foreach ($sampleData as $key => $value) {
                $emailBody = str_replace('{{' . $key . '}}', $value, $emailBody);
                $emailSubject = str_replace('{{' . $key . '}}', $value, $emailSubject);
            }

            // Send test email using the actual template action code
            $testUser = [
                'username' => 'testuser',
                'email' => $testEmail,
                'fullname' => 'Test User'
            ];

            // Use the actual template action code instead of DEFAULT
            notify($testUser, $template->act, $sampleData, ['email'], false);

            return response()->json([
                'success' => true,
                'message' => 'Test email sent successfully to ' . $testEmail
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send test email: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Preview email template (Simplified System)
     * Supports both GET and POST requests
     */
    public function templatePreview(Request $request, $id)
    {
        try {
            $template = NotificationTemplate::findOrFail($id);

            // Create sample data for shortcode replacement
            $sampleData = [
                'fullname' => 'John Doe',
                'username' => 'johndoe',
                'email' => '<EMAIL>',
                'site_name' => gs('site_name'),
                'site_url' => url('/'),
                'amount' => '100.00',
                'currency' => gs('cur_text'),
                'balance' => '1,500.00',
                'transaction_id' => 'TXN123456',
                'code' => '123456',
                'message' => 'This is a sample message for preview.',
                'mt5_login' => '12345678',
                'mt5_group' => 'real\\MBFX\\B\\Sf\\Cp\\Fake',
                'leverage' => '1:100',
                'new_balance' => '1,600.00',
                'transaction_date' => now()->format('Y-m-d H:i:s'),
                'reason' => 'Sample transaction',
                'ib_type' => 'Master IB',
                'referral_code' => 'REF1234',
                'server_name' => 'MT5-Live-Server',
                'approval_date' => now()->format('Y-m-d'),
                'commission_rate' => '50%',
                'deadline' => now()->addDays(7)->format('Y-m-d'),
                'ip_address' => '***********',
                'location' => 'New York, USA'
            ];

            // Use custom content from POST request if available, otherwise use template content
            $emailBody = $request->has('content') && $request->isMethod('post') 
                ? $request->input('content') 
                : $template->email_body;
                
            // Replace shortcodes in email body
            foreach ($sampleData as $key => $value) {
                $emailBody = str_replace('{{' . $key . '}}', $value, $emailBody);
            }

            // Get global email template
            $general = gs();
            $emailTemplate = $general->email_template;

            // Replace {{message}} in global template with processed email body
            $finalContent = str_replace('{{message}}', $emailBody, $emailTemplate);

            // Return clean HTML response for preview
            return response($finalContent, 200, [
                'Content-Type' => 'text/html; charset=utf-8',
                'Cache-Control' => 'no-cache, no-store, must-revalidate',
                'Pragma' => 'no-cache',
                'Expires' => '0'
            ]);

        } catch (\Exception $e) {
            $errorHtml = '
            <!DOCTYPE html>
            <html>
            <head>
                <title>Preview Error</title>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        padding: 40px;
                        text-align: center;
                        background: #f8f9fa;
                        margin: 0;
                    }
                    .error {
                        background: #fff;
                        padding: 30px;
                        border-radius: 8px;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        max-width: 500px;
                        margin: 0 auto;
                    }
                    .error h3 {
                        color: #dc3545;
                        margin-bottom: 15px;
                    }
                    .error p {
                        color: #6c757d;
                        margin: 0;
                    }
                    .error-details {
                        background: #f8f9fa;
                        padding: 15px;
                        border-radius: 5px;
                        margin-top: 15px;
                        font-size: 12px;
                        color: #666;
                        text-align: left;
                    }
                </style>
            </head>
            <body>
                <div class="error">
                    <h3>🚫 Preview Error</h3>
                    <p>Unable to load email template preview</p>
                    <div class="error-details">
                        <strong>Error:</strong> ' . htmlspecialchars($e->getMessage()) . '<br>
                        <strong>Template ID:</strong> ' . $id . '<br>
                        <strong>Time:</strong> ' . now()->format('Y-m-d H:i:s') . '
                    </div>
                </div>
            </body>
            </html>';

            return response($errorHtml, 500, ['Content-Type' => 'text/html; charset=utf-8']);
        }
    }

    /**
     * Check if content has proper HTML structure
     */
    private function hasProperHtmlStructure($content)
    {
        if (empty($content)) {
            return false;
        }

        $content = trim($content);

        // Check for DOCTYPE and basic HTML structure
        return strpos($content, '<!DOCTYPE html>') === 0 &&
               strpos($content, '<html') !== false &&
               strpos($content, '<head>') !== false &&
               strpos($content, '<body') !== false;
    }

    /**
     * Check if template has professional email structure
     */
    private function isProfessionalTemplate($content)
    {
        if (empty($content)) {
            return false;
        }

        $content = trim($content);

        // Check for professional email template indicators
        return $this->hasProperHtmlStructure($content) &&
               (strpos($content, 'email-container') !== false ||
                strpos($content, 'email-content') !== false ||
                strpos($content, 'MBFX') !== false ||
                strpos($content, 'max-width: 600px') !== false);
    }

    /**
     * Check if content has repeated/corrupted text
     */
    private function hasRepeatedContent($content)
    {
        // Remove HTML tags and check for repeated phrases
        $cleanContent = strip_tags($content);

        // Check for obvious repetitions like "Balance Added Successfully Balance Added Successfully"
        $words = explode(' ', $cleanContent);
        $wordCount = array_count_values($words);

        // If any word appears more than 3 times in a short content, it's likely corrupted
        foreach ($wordCount as $word => $count) {
            if (strlen($word) > 3 && $count > 3 && strlen($cleanContent) < 1000) {
                return true;
            }
        }

        // Check for repeated phrases
        if (preg_match('/(.{10,})\1{2,}/', $cleanContent)) {
            return true;
        }

        return false;
    }

    /**
     * Preserve professional structure while updating content
     */
    private function preserveProfessionalStructureWithNewContent($originalContent, $newContent)
    {
        // Extract the main content from the new content (remove any partial HTML structure)
        $extractedContent = $this->extractMainContentFromNewContent($newContent);

        // Try to replace the main content area in the original professional template
        if (preg_match('/<div[^>]*class="email-content"[^>]*>(.*?)<\/div>/s', $originalContent, $matches)) {
            // Replace the content inside the email-content div
            return preg_replace(
                '/<div([^>]*class="email-content"[^>]*)>(.*?)<\/div>/s',
                '<div$1>' . $extractedContent . '</div>',
                $originalContent,
                1
            );
        }

        // If no email-content div found, try to replace body content
        if (preg_match('/<body[^>]*>(.*?)<\/body>/s', $originalContent, $matches)) {
            // Find the main content area and replace it
            return preg_replace(
                '/(<body[^>]*>.*?<div[^>]*>)(.*?)(<\/div>.*?<\/body>)/s',
                '$1' . $extractedContent . '$3',
                $originalContent,
                1
            );
        }

        // If all else fails, wrap the new content in professional structure
        return $this->addMissingHtmlStructure($extractedContent);
    }

    /**
     * Extract main content from potentially corrupted new content
     * FIXED: Preserve formatting and whitespace
     */
    private function extractMainContentFromNewContent($content)
    {
        // Remove any partial HTML structure but preserve formatting
        $content = preg_replace('/<!DOCTYPE[^>]*>/i', '', $content);
        $content = preg_replace('/<\/?html[^>]*>/i', '', $content);
        $content = preg_replace('/<head>.*?<\/head>/s', '', $content);
        $content = preg_replace('/<\/?body[^>]*>/i', '', $content);

        // CRITICAL FIX: Only trim leading/trailing whitespace, preserve internal formatting
        $content = trim($content);

        // If content is empty or too short, provide default
        if (strlen(strip_tags($content)) < 10) {
            $content = '<p>Dear {{fullname}},</p><p>This is a notification from {{site_name}}.</p><p>Best regards,<br>MBFX Team</p>';
        }

        return $content;
    }

    /**
     * Add missing HTML structure while preserving user content
     */
    private function addMissingHtmlStructure($content)
    {
        // Clean the content first
        if (empty(trim($content))) {
            $content = '<p>Start editing your email template...</p>';
        }

        // Extract title from content if possible
        $title = 'Email Notification';
        if (preg_match('/<h[1-6][^>]*>(.*?)<\/h[1-6]>/i', $content, $matches)) {
            $title = strip_tags($matches[1]) ?: $title;
        } elseif (preg_match('/<title[^>]*>(.*?)<\/title>/i', $content, $matches)) {
            $title = $matches[1];
        }

        // If content already has some HTML structure, just add DOCTYPE
        if (strpos($content, '<html') !== false) {
            return '<!DOCTYPE html>' . "\n" . $content;
        }

        // Create professional email template structure with full width
        return '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>' . htmlspecialchars($title) . '</title>
    <style>
        body { margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4; }
        .email-container { width: 100%; margin: 0 auto; background-color: #ffffff; }
        .email-content { padding: 20px; }
        @media only screen and (max-width: 600px) {
            .email-container { width: 100% !important; }
            .email-content { padding: 15px !important; }
        }
    </style>
</head>
<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4;">
    <div class="email-container">
        <div class="email-content">
            ' . $content . '
        </div>
    </div>
</body>
</html>';
    }

    /**
     * Get template type based on action code
     */
    private function getTemplateType($actionCode)
    {
        // Map action codes to template types
        $typeMapping = [
            'ACCOUNT_VERIFICATION_REQUIRED' => 'ACCOUNT_VERIFICATION_REQUIRED',
            'BALANCE_ADD' => 'BALANCE_ADD',
            'BALANCE_SUB' => 'BALANCE_SUB',
            'DEPOSIT_COMPLETE' => 'DEPOSIT_COMPLETE',
            'DEPOSIT_REJECT' => 'DEPOSIT_REJECT',
            'WITHDRAW_REQUEST' => 'WITHDRAW_REQUEST',
            'WITHDRAW_APPROVE' => 'WITHDRAW_APPROVE',
            'WITHDRAW_REJECT' => 'WITHDRAW_REJECT',
            'KYC_APPROVE' => 'KYC_APPROVE',
            'KYC_REJECT' => 'KYC_REJECT',
            'MT5_ACCOUNT_CREATED' => 'MT5_ACCOUNT_CREATED',
            'MT5_PASSWORD_CHANGED' => 'MT5_PASSWORD_CHANGED',
            'IB_APPLICATION_APPROVED' => 'IB_APPLICATION_APPROVED',
            'IB_APPLICATION_REJECTED' => 'IB_APPLICATION_REJECTED',
            'USER_REGISTRATION' => 'USER_REGISTRATION',
            'PASS_RESET_CODE' => 'PASS_RESET_CODE',
            'PASS_RESET_DONE' => 'PASS_RESET_DONE',
            'EVER_CODE' => 'EVER_CODE',
            'SVER_CODE' => 'SVER_CODE',
            'SECURITY_ALERT' => 'SECURITY_ALERT'
        ];

        return $typeMapping[$actionCode] ?? 'DEFAULT';
    }



}
