<div class="row mb--20">
  <div class="col-lg-12">
    <div class="card mt-30">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0"><?php echo app('translator')->get('Transaction'); ?></h5>
      </div>
      

      <div class="card-body p-0 b-radius--10 ">
            <div class="card-body p-0">
                <div class="table-responsive--sm table-responsive">
                    <table class="table table--light style--two custom-data-table">
                        <thead>
                            <tr>
                                <th><?php echo app('translator')->get('Currency | Wallet'); ?></th>
                                <th><?php echo app('translator')->get('User'); ?></th>
                                <th><?php echo app('translator')->get('TRX'); ?></th>
                                <th><?php echo app('translator')->get('Transacted'); ?></th>
                                <th><?php echo app('translator')->get('Amount'); ?></th>
                                <th><?php echo app('translator')->get('MT5 Balance'); ?></th>
                                <th><?php echo app('translator')->get('Details'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $transactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $trx): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <div class="text-end text-lg-start">
                                            <span><?php echo e(@$trx->wallet->currency->symbol); ?></span>
                                            <br>
                                            <small><?php echo e(@$trx->wallet->name); ?> | <?php echo e(__(strToUpper(@$trx->wallet->type_text))); ?> </small> 
                                        </div>
                                    </td>
                                    <td>
                                        <span class="fw-bold"><?php echo e($trx->user->fullname); ?></span>
                                        <br>
                                        <span class="small"> <a href="<?php echo e(appendQuery('search',$trx->user->username)); ?>"><span>@</span><?php echo e($trx->user->username); ?></a> </span>
                                    </td>

                                    <td>
                                        <strong><?php echo e($trx->trx); ?></strong>
                                    </td>

                                    <td>
                                        <?php echo e(showDateTime($trx->created_at)); ?><br><?php echo e(diffForHumans($trx->created_at)); ?>

                                    </td>

                                    <td class="budget">
                                        <span class="fw-bold <?php if($trx->trx_type == '+'): ?>text--success <?php else: ?> text--danger <?php endif; ?>">
                                            <?php echo e($trx->trx_type); ?> <?php echo e(showAmount($trx->amount)); ?> <?php echo e(__(@$trx->wallet->currency->symbol)); ?>

                                        </span>
                                    </td>

                                    <td class="budget">
                                        <?php
                                            // Get real-time MT5 balance for the user
                                            $mt5Balance = 0;
                                            if ($trx->user) {
                                                $mt5Accounts = \App\Models\Mt5Users::getAccounts($trx->user->email);
                                                foreach ($mt5Accounts as $account) {
                                                    if (stripos($account->Group, 'real') !== false) {
                                                        $mt5Balance += $account->Balance ?? 0;
                                                    }
                                                }
                                            }
                                        ?>
                                        <?php echo e(showAmount($mt5Balance)); ?> <?php echo e(__(@$trx->wallet->currency->symbol)); ?>

                                    </td>

                                    <td><?php echo e(__($trx->details)); ?></td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td class="text-muted text-center" colspan="100%"><?php echo e(__($emptyMessage)); ?></td>
                                </tr>
                            <?php endif; ?>

                    </tbody>
                </table><!-- table end -->
            </div>
        </div>
        </div>
        <?php if($transactions->hasPages()): ?>
        <div class="card-footer py-4">
            <?php echo e(paginateLinks($transactions)); ?>

        </div>
        <?php endif; ?>
    </div><!-- card end -->
  </div>
</div>

<?php if (isset($component)) { $__componentOriginalbd5922df145d522b37bf664b524be380 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbd5922df145d522b37bf664b524be380 = $attributes; } ?>
<?php $component = App\View\Components\ConfirmationModal::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('confirmation-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\ConfirmationModal::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbd5922df145d522b37bf664b524be380)): ?>
<?php $attributes = $__attributesOriginalbd5922df145d522b37bf664b524be380; ?>
<?php unset($__attributesOriginalbd5922df145d522b37bf664b524be380); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbd5922df145d522b37bf664b524be380)): ?>
<?php $component = $__componentOriginalbd5922df145d522b37bf664b524be380; ?>
<?php unset($__componentOriginalbd5922df145d522b37bf664b524be380); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\mbf.mybrokerforex.com-31052025\resources\views/components/user-detail/transaction.blade.php ENDPATH**/ ?>