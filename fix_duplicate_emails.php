<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔧 FIXING DUPLICATE EMAIL CONSOLIDATION (ISSUE 3)\n";
echo "=================================================\n";

// Find duplicate emails
echo "\n🔍 Finding duplicate emails:\n";

$duplicates = \DB::select("
    SELECT email, COUNT(*) as count 
    FROM users 
    GROUP BY email 
    HAVING COUNT(*) > 1 
    ORDER BY count DESC
");

echo "Found " . count($duplicates) . " emails with duplicates:\n";

foreach (array_slice($duplicates, 0, 10) as $duplicate) {
    echo "   - {$duplicate->email}: {$duplicate->count} accounts\n";
}

// <NAME_EMAIL> case specifically
echo "\n🔍 Analyzing <EMAIL> (mentioned in issue):\n";

$mbfxUsers = \DB::select("
    SELECT id, email, created_at, partner, ib_status, kv 
    FROM users 
    WHERE email = '<EMAIL>' 
    ORDER BY created_at DESC
");

foreach ($mbfxUsers as $user) {
    echo "   - ID: {$user->id}, Created: {$user->created_at}, Partner: {$user->partner}, KYC: {$user->kv}\n";
}

// Check MT5 accounts for these users
echo "\n🔍 Checking MT5 <NAME_EMAIL>:\n";

$mt5Accounts = \DB::select("
    SELECT ua.User_Id, ua.Account, ua.created_at 
    FROM user_accounts ua 
    JOIN users u ON ua.User_Id = u.id 
    WHERE u.email = '<EMAIL>'
    ORDER BY ua.created_at DESC
");

foreach ($mt5Accounts as $account) {
    echo "   - User ID: {$account->User_Id}, MT5 Account: {$account->Account}, Created: {$account->created_at}\n";
}

// Strategy: Keep the most recent user record and transfer all MT5 accounts to it
echo "\n🔧 Consolidation Strategy:\n";
echo "1. Keep the most recent user record (latest created_at)\n";
echo "2. Transfer all MT5 accounts to the kept user\n";
echo "3. Update referral relationships\n";
echo "4. Delete duplicate user records\n";

// Process each duplicate email
$processedEmails = 0;
$consolidatedAccounts = 0;

foreach (array_slice($duplicates, 0, 5) as $duplicate) { // Process first 5 for testing
    $email = $duplicate->email;
    echo "\n🔧 Processing: {$email}\n";
    
    // Get all users with this email, ordered by creation date (newest first)
    $users = \DB::select("
        SELECT id, email, created_at, partner, ib_status, kv, ev, sv 
        FROM users 
        WHERE email = ? 
        ORDER BY created_at DESC
    ", [$email]);
    
    if (count($users) <= 1) {
        echo "   ⚠️ No duplicates found, skipping\n";
        continue;
    }
    
    $keepUser = $users[0]; // Keep the newest user
    $duplicateUsers = array_slice($users, 1); // Remove the rest
    
    echo "   ✅ Keeping user ID: {$keepUser->id} (created: {$keepUser->created_at})\n";
    echo "   🗑️ Removing " . count($duplicateUsers) . " duplicate users\n";
    
    // Transfer MT5 accounts to the kept user
    foreach ($duplicateUsers as $dupUser) {
        $transferredAccounts = \DB::update("
            UPDATE user_accounts 
            SET User_Id = ? 
            WHERE User_Id = ?
        ", [$keepUser->id, $dupUser->id]);
        
        if ($transferredAccounts > 0) {
            echo "   📦 Transferred {$transferredAccounts} MT5 accounts from user {$dupUser->id}\n";
            $consolidatedAccounts += $transferredAccounts;
        }
        
        // Update referral relationships
        $referralUpdates = \DB::update("
            UPDATE users 
            SET ref_by = ? 
            WHERE ref_by = ?
        ", [$keepUser->id, $dupUser->id]);
        
        if ($referralUpdates > 0) {
            echo "   🔗 Updated {$referralUpdates} referral relationships\n";
        }
        
        // Delete the duplicate user
        \DB::delete("DELETE FROM users WHERE id = ?", [$dupUser->id]);
        echo "   🗑️ Deleted duplicate user ID: {$dupUser->id}\n";
    }
    
    $processedEmails++;
}

echo "\n📊 Consolidation Summary:\n";
echo "   - Processed emails: {$processedEmails}\n";
echo "   - Consolidated MT5 accounts: {$consolidatedAccounts}\n";

// Verify the consolidation
echo "\n🔍 Verification - Checking remaining duplicates:\n";

$remainingDuplicates = \DB::select("
    SELECT email, COUNT(*) as count 
    FROM users 
    GROUP BY email 
    HAVING COUNT(*) > 1 
    ORDER BY count DESC
    LIMIT 5
");

if (count($remainingDuplicates) > 0) {
    echo "Remaining duplicates:\n";
    foreach ($remainingDuplicates as $duplicate) {
        echo "   - {$duplicate->email}: {$duplicate->count} accounts\n";
    }
} else {
    echo "✅ No duplicates found in processed emails!\n";
}

// Check <EMAIL> after consolidation
echo "\n🔍 Checking <EMAIL> after consolidation:\n";

$mbfxAfter = \DB::select("
    SELECT u.id, u.email, u.created_at, COUNT(ua.Account) as mt5_accounts
    FROM users u
    LEFT JOIN user_accounts ua ON u.id = ua.User_Id
    WHERE u.email = '<EMAIL>'
    GROUP BY u.id
");

foreach ($mbfxAfter as $user) {
    echo "   - User ID: {$user->id}, MT5 Accounts: {$user->mt5_accounts}, Created: {$user->created_at}\n";
}

echo "\n✅ Duplicate email consolidation completed!\n";
