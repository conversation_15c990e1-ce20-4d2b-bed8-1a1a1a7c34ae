<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\IbCommissionService;

class ProcessPendingCommissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ib:process-pending-commissions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process pending IB commissions and pay them to IB wallets';

    protected $ibCommissionService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->ibCommissionService = new IbCommissionService();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Processing pending IB commissions...');

        try {
            $processed = $this->ibCommissionService->processPendingCommissions();
            
            $this->info("Successfully processed {$processed} pending commissions");
            
            return 0;
            
        } catch (\Exception $e) {
            $this->error("Error processing commissions: " . $e->getMessage());
            return 1;
        }
    }
}
