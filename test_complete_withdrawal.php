<?php

/**
 * Test complete withdrawal workflow
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Testing Complete Withdrawal Workflow ===\n\n";

// Use the user we know has real MT5 accounts
$user = \App\Models\User::find(1); // <EMAIL>
if (!$user) {
    echo "❌ Test user not found\n";
    exit(1);
}

echo "Testing with user: {$user->email} (ID: {$user->id})\n";

// Ensure user has a wallet
$wallet = $user->wallets()->first();
if (!$wallet) {
    echo "❌ User has no wallets\n";
    exit(1);
}

echo "Using wallet: {$wallet->name} (Balance: \${$wallet->balance})\n";

// Check MT5 accounts
$mt5Accounts = \App\Models\Mt5Users::getAccounts($user->email);
echo "MT5 accounts: {$mt5Accounts->count()}\n";

$realAccounts = $mt5Accounts->filter(function ($account) {
    return stripos($account->Group, 'real') !== false;
});

if ($realAccounts->isEmpty()) {
    echo "❌ No real MT5 accounts found\n";
    exit(1);
}

$testAccount = $realAccounts->first();
echo "Test MT5 account: {$testAccount->Login} | Balance: \${$testAccount->Balance}\n\n";

// Test 1: Create a withdrawal method if none exists
echo "TEST 1: Checking withdrawal methods\n";
echo "===================================\n";

$withdrawMethod = \App\Models\WithdrawMethod::where('status', 1)->first();
if (!$withdrawMethod) {
    echo "❌ No active withdrawal methods found\n";
    exit(1);
}

echo "Using withdrawal method: {$withdrawMethod->name}\n\n";

// Test 2: Simulate withdrawal creation
echo "TEST 2: Creating test withdrawal\n";
echo "===============================\n";

$testAmount = 1.00; // $1 test withdrawal
echo "Creating withdrawal for \${$testAmount}\n";

// Check current balances before withdrawal
$walletBalanceBefore = $wallet->balance;
$mt5BalanceBefore = $testAccount->Balance;

echo "Balances before withdrawal:\n";
echo "  Wallet: \${$walletBalanceBefore}\n";
echo "  MT5: \${$mt5BalanceBefore}\n";

// Create withdrawal record
$withdraw = new \App\Models\Withdrawal();
$withdraw->method_id = $withdrawMethod->id;
$withdraw->user_id = $user->id;
$withdraw->wallet_id = $wallet->id;
$withdraw->amount = $testAmount;
$withdraw->currency = $wallet->currency->symbol;
$withdraw->rate = 1;
$withdraw->charge = 0;
$withdraw->final_amount = $testAmount;
$withdraw->after_charge = $testAmount;
$withdraw->trx = 'TEST' . time();
$withdraw->status = 0; // PAYMENT_PENDING
$withdraw->withdraw_information = json_encode([]);
$withdraw->save();

echo "✅ Withdrawal record created: TRX {$withdraw->trx}\n";

// Test 3: Test the deductBalanceFromMT5Accounts method directly
echo "\nTEST 3: Testing MT5 balance deduction\n";
echo "====================================\n";

// Use reflection to call the private method
$withdrawController = new \App\Http\Controllers\User\WithdrawController();
$reflection = new ReflectionClass($withdrawController);
$method = $reflection->getMethod('deductBalanceFromMT5Accounts');
$method->setAccessible(true);

$result = $method->invoke(null, $user, $testAmount, "Test withdrawal: " . $withdraw->trx);

echo "MT5 deduction result: " . ($result ? 'SUCCESS' : 'FAILED') . "\n";

// Check balances after MT5 deduction
$mt5AccountAfter = \App\Models\Mt5Users::getAccounts($user->email)->first();
echo "MT5 balance after deduction: \${$mt5AccountAfter->Balance}\n";

$expectedMT5Balance = $mt5BalanceBefore - $testAmount;
echo "Expected MT5 balance: \${$expectedMT5Balance}\n";

if (abs($mt5AccountAfter->Balance - $expectedMT5Balance) < 0.01) {
    echo "✅ MT5 balance deduction SUCCESSFUL\n";
} else {
    echo "❌ MT5 balance deduction FAILED\n";
}

// Test 4: Create transaction record
echo "\nTEST 4: Creating transaction record\n";
echo "==================================\n";

$transaction = new \App\Models\Transaction();
$transaction->user_id = $user->id;
$transaction->amount = $testAmount;
$transaction->post_balance = $wallet->balance - $testAmount; // Simulate wallet deduction
$transaction->charge = 0;
$transaction->trx_type = '-';
$transaction->details = "Test withdrawal of \${$testAmount} via {$withdrawMethod->name}";
$transaction->trx = $withdraw->trx;
$transaction->remark = 'withdraw';
$transaction->wallet_id = $wallet->id;
$transaction->save();

echo "✅ Transaction record created: ID {$transaction->id}\n";

// Test 5: Verify transaction appears in user history
echo "\nTEST 5: Checking transaction history\n";
echo "===================================\n";

$userTransactions = \App\Models\Transaction::where('user_id', $user->id)
    ->where('trx', $withdraw->trx)
    ->with('wallet.currency')
    ->get();

echo "Transactions found for TRX {$withdraw->trx}: {$userTransactions->count()}\n";

foreach ($userTransactions as $trx) {
    $walletInfo = $trx->wallet && $trx->wallet->currency ? 
        "{$trx->wallet->currency->symbol} ({$trx->wallet->name})" : 
        "NO WALLET";
    echo "  ID: {$trx->id} | Remark: {$trx->remark} | Amount: {$trx->trx_type}\${$trx->amount} | Wallet: {$walletInfo}\n";
}

// Test 6: Test controller query (same as used in UserController)
echo "\nTEST 6: Testing controller transaction query\n";
echo "===========================================\n";

$controllerTransactions = \App\Models\Transaction::where('user_id', $user->id)
    ->with('wallet.currency')
    ->orderBy('id', 'desc')
    ->take(5)
    ->get();

echo "Recent transactions from controller query:\n";
foreach ($controllerTransactions as $trx) {
    $walletInfo = $trx->wallet && $trx->wallet->currency ? 
        "{$trx->wallet->currency->symbol}" : 
        "NO WALLET";
    echo "  {$trx->remark} | {$trx->trx_type}\${$trx->amount} | {$walletInfo} | {$trx->created_at}\n";
}

// Cleanup: Add balance back to MT5 and delete test records
echo "\nCLEANUP: Restoring balances and removing test data\n";
echo "=================================================\n";

// Add balance back to MT5
$mt5Service = new \App\Services\MT5Service();
$refundResult = $mt5Service->addBalanceToUserAccounts($user, $testAmount, "Test cleanup refund");

if ($refundResult['success']) {
    echo "✅ MT5 balance refunded\n";
} else {
    echo "❌ MT5 balance refund failed\n";
}

// Delete test records
$transaction->delete();
$withdraw->delete();

echo "✅ Test records cleaned up\n";

echo "\n=== Complete Withdrawal Test Finished ===\n";
