<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Server Email Editor Diagnostic</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .diagnostic-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        .log-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background: #dc3545;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #c82333;
        }
        .asset-test {
            display: inline-block;
            margin: 5px;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="diagnostic-container">
        <h1>🔍 Live Server Email Editor Diagnostic Tool</h1>
        <p><strong>Purpose:</strong> Identify why the email template editor is not displaying correctly on Windows Server 2022 with PHP 8.4</p>
        
        <div class="test-section info">
            <h3>📋 Server Information</h3>
            <div id="server-info">Loading server information...</div>
        </div>

        <div class="test-section">
            <h3>🔗 Asset Loading Tests</h3>
            <button onclick="testAssetLoading()">Test Asset Loading</button>
            <div id="asset-results"></div>
        </div>

        <div class="test-section">
            <h3>📜 JavaScript Console Errors</h3>
            <button onclick="captureConsoleErrors()">Capture Console Errors</button>
            <div class="log-output" id="console-errors">Click button to capture console errors...</div>
        </div>

        <div class="test-section">
            <h3>🌐 Network Requests</h3>
            <button onclick="testNetworkRequests()">Test Network Requests</button>
            <div class="log-output" id="network-results">Click button to test network requests...</div>
        </div>

        <div class="test-section">
            <h3>🎯 DOM Element Detection</h3>
            <button onclick="testDOMElements()">Test DOM Elements</button>
            <div class="log-output" id="dom-results">Click button to test DOM elements...</div>
        </div>

        <div class="test-section">
            <h3>⚙️ JavaScript Environment</h3>
            <button onclick="testJSEnvironment()">Test JS Environment</button>
            <div class="log-output" id="js-env-results">Click button to test JavaScript environment...</div>
        </div>

        <div class="test-section">
            <h3>🔧 Recommended Fixes</h3>
            <div id="recommendations">Run diagnostics to get recommendations...</div>
        </div>
    </div>

    <script>
        // Global error capture
        let capturedErrors = [];
        let capturedNetworkErrors = [];
        
        // Override console.error to capture errors
        const originalConsoleError = console.error;
        console.error = function(...args) {
            capturedErrors.push({
                timestamp: new Date().toISOString(),
                message: args.join(' '),
                stack: new Error().stack
            });
            originalConsoleError.apply(console, args);
        };

        // Capture unhandled errors
        window.addEventListener('error', function(e) {
            capturedErrors.push({
                timestamp: new Date().toISOString(),
                message: e.message,
                filename: e.filename,
                lineno: e.lineno,
                colno: e.colno,
                stack: e.error ? e.error.stack : 'No stack trace'
            });
        });

        // Display server information
        function displayServerInfo() {
            const info = {
                'User Agent': navigator.userAgent,
                'Current URL': window.location.href,
                'Protocol': window.location.protocol,
                'Host': window.location.host,
                'Pathname': window.location.pathname,
                'Viewport': `${window.innerWidth}x${window.innerHeight}`,
                'Screen': `${screen.width}x${screen.height}`,
                'Local Time': new Date().toString(),
                'Timezone': Intl.DateTimeFormat().resolvedOptions().timeZone
            };
            
            let html = '<table style="width:100%; border-collapse: collapse;">';
            for (const [key, value] of Object.entries(info)) {
                html += `<tr><td style="border:1px solid #ddd; padding:5px; font-weight:bold;">${key}</td><td style="border:1px solid #ddd; padding:5px;">${value}</td></tr>`;
            }
            html += '</table>';
            
            document.getElementById('server-info').innerHTML = html;
        }

        // Test asset loading
        async function testAssetLoading() {
            const assets = [
                '/assets/admin/css/simple-email-editor.css',
                '/assets/admin/js/simple-email-editor.js',
                '/assets/admin/css/bootstrap.min.css',
                '/assets/admin/js/bootstrap.bundle.min.js',
                '/assets/admin/js/jquery-3.6.0.min.js'
            ];
            
            let results = '<h4>Asset Loading Results:</h4>';
            
            for (const asset of assets) {
                try {
                    const response = await fetch(asset, { method: 'HEAD' });
                    const status = response.status;
                    const contentType = response.headers.get('content-type') || 'unknown';
                    const contentLength = response.headers.get('content-length') || 'unknown';
                    
                    const statusClass = status === 200 ? 'success' : 'error';
                    results += `<div class="asset-test ${statusClass}">${asset} - ${status} (${contentType}, ${contentLength} bytes)</div>`;
                } catch (error) {
                    results += `<div class="asset-test error">${asset} - ERROR: ${error.message}</div>`;
                }
            }
            
            document.getElementById('asset-results').innerHTML = results;
        }

        // Capture console errors
        function captureConsoleErrors() {
            let output = 'Captured Console Errors:\n\n';
            
            if (capturedErrors.length === 0) {
                output += 'No console errors captured yet.\n';
            } else {
                capturedErrors.forEach((error, index) => {
                    output += `${index + 1}. [${error.timestamp}]\n`;
                    output += `   Message: ${error.message}\n`;
                    if (error.filename) output += `   File: ${error.filename}:${error.lineno}:${error.colno}\n`;
                    if (error.stack) output += `   Stack: ${error.stack.split('\n')[1] || 'No stack'}\n`;
                    output += '\n';
                });
            }
            
            document.getElementById('console-errors').textContent = output;
        }

        // Test network requests
        async function testNetworkRequests() {
            let output = 'Network Request Tests:\n\n';
            
            // Test critical endpoints
            const endpoints = [
                '/admin/notification/template/test',
                '/admin/notification/template/preview/1',
                '/admin/setting/notification/templates'
            ];
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint, { 
                        method: 'GET',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    });
                    output += `${endpoint}: ${response.status} ${response.statusText}\n`;
                } catch (error) {
                    output += `${endpoint}: ERROR - ${error.message}\n`;
                }
            }
            
            document.getElementById('network-results').textContent = output;
        }

        // Test DOM elements
        function testDOMElements() {
            const elements = [
                'html-editor-textarea',
                'visual-editor-content',
                'email_body',
                'email_body_final',
                'template-form',
                'visual-tab',
                'html-tab'
            ];
            
            let output = 'DOM Element Detection:\n\n';
            
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    output += `✅ ${id}: Found (${element.tagName})\n`;
                } else {
                    output += `❌ ${id}: Not found\n`;
                }
            });
            
            // Test by name attribute
            const nameElements = ['email_body', 'template_id'];
            output += '\nElements by name attribute:\n';
            nameElements.forEach(name => {
                const elements = document.getElementsByName(name);
                output += `${name}: ${elements.length} found\n`;
            });
            
            document.getElementById('dom-results').textContent = output;
        }

        // Test JavaScript environment
        function testJSEnvironment() {
            let output = 'JavaScript Environment Test:\n\n';
            
            // Test global variables
            const globals = ['jQuery', '$', 'bootstrap', 'tinymce', 'templateData'];
            output += 'Global Variables:\n';
            globals.forEach(global => {
                output += `${global}: ${typeof window[global]}\n`;
            });
            
            // Test functions
            output += '\nEmail Editor Functions:\n';
            const functions = [
                'initializeSimpleEditor',
                'initializeEditorToggle',
                'syncEditorContent',
                'showToast'
            ];
            functions.forEach(func => {
                output += `${func}: ${typeof window[func]}\n`;
            });
            
            // Test event listeners
            output += '\nDOM Ready State: ' + document.readyState + '\n';
            output += 'DOMContentLoaded fired: ' + (document.readyState !== 'loading') + '\n';
            
            document.getElementById('js-env-results').textContent = output;
        }

        // Generate recommendations
        function generateRecommendations() {
            let recommendations = '<h4>🔧 Recommended Fixes Based on Diagnostics:</h4><ul>';
            
            // Check if assets are loading
            const assetResults = document.getElementById('asset-results').innerHTML;
            if (assetResults.includes('error') || assetResults.includes('404')) {
                recommendations += '<li><strong>Asset Loading Issue:</strong> Check web.config rewrite rules and ensure static files are served correctly</li>';
            }
            
            // Check for JavaScript errors
            if (capturedErrors.length > 0) {
                recommendations += '<li><strong>JavaScript Errors:</strong> Fix console errors before proceeding</li>';
            }
            
            // Check for missing DOM elements
            const domResults = document.getElementById('dom-results').textContent;
            if (domResults.includes('❌')) {
                recommendations += '<li><strong>Missing DOM Elements:</strong> Ensure the email template view is loading correctly</li>';
            }
            
            recommendations += '</ul>';
            
            // Add specific server fixes
            recommendations += '<h4>🖥️ Windows Server 2022 Specific Fixes:</h4><ul>';
            recommendations += '<li>Ensure IIS has proper MIME types for .css and .js files</li>';
            recommendations += '<li>Check if URL Rewrite module is properly configured</li>';
            recommendations += '<li>Verify PHP 8.4 is handling Laravel routes correctly</li>';
            recommendations += '<li>Test with browser developer tools network tab</li>';
            recommendations += '</ul>';
            
            document.getElementById('recommendations').innerHTML = recommendations;
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            displayServerInfo();
            
            // Auto-run some tests after a delay
            setTimeout(() => {
                testAssetLoading();
                testDOMElements();
                testJSEnvironment();
                generateRecommendations();
            }, 1000);
        });
    </script>
</body>
</html>