# 🔧 WINDOWS 2022 SERVER EMAIL TEMPLATE DISPLAY FIX

## 📋 **PROBLEM ANALYSIS**

The email template editor works perfectly on localhost but displays incorrectly on Windows 2022 Server with PHP 8.4. This is caused by **server-specific configuration issues** that prevent proper asset loading and JavaScript execution.

---

## 🚨 **CRITICAL ISSUES IDENTIFIED**

### **1. INCORRECT WEB.CONFIG REWRITE RULES**
**Problem**: The current web.config has a faulty rewrite rule that redirects ALL requests to `/`, breaking CSS/JS asset loading.

**Current Broken Rule**:
```xml
<action type="Rewrite" url="/" />
```

**Impact**: 
- CSS files return HTML instead of CSS content
- JavaScript files return HTML instead of JS content
- Email template editor appears unstyled and non-functional

### **2. MISSING STATIC FILE HANDLING**
**Problem**: Windows IIS doesn't properly serve static assets without explicit configuration.

### **3. MISSING MIME TYPE CONFIGURATIONS**
**Problem**: IIS may not recognize .css and .js file extensions properly.

---

## ✅ **COMPLETE SOLUTION**

### **STEP 1: Fix web.config (CRITICAL)**

Replace the entire `web.config` file with this corrected version:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>
    <rewrite>
      <rules>
        <!-- Static Files Rule - Allow direct access to assets -->
        <rule name="Static Files" stopProcessing="true">
          <match url="^(assets|css|js|images|fonts)/.*" />
          <action type="None" />
        </rule>
        
        <!-- Laravel Routes Rule -->
        <rule name="Laravel Routes" stopProcessing="true">
          <match url="^(.*)$" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
          </conditions>
          <action type="Rewrite" url="index.php" />
        </rule>
      </rules>
    </rewrite>
    
    <!-- Static Content Configuration -->
    <staticContent>
      <remove fileExtension=".css" />
      <mimeMap fileExtension=".css" mimeType="text/css" />
      <remove fileExtension=".js" />
      <mimeMap fileExtension=".js" mimeType="application/javascript" />
      <remove fileExtension=".json" />
      <mimeMap fileExtension=".json" mimeType="application/json" />
      <remove fileExtension=".woff" />
      <mimeMap fileExtension=".woff" mimeType="font/woff" />
      <remove fileExtension=".woff2" />
      <mimeMap fileExtension=".woff2" mimeType="font/woff2" />
    </staticContent>
    
    <!-- Default Documents -->
    <defaultDocument>
      <files>
        <clear />
        <add value="index.php" />
        <add value="index.html" />
      </files>
    </defaultDocument>
    
    <!-- Security Settings -->
    <security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="30000000" maxUrl="1000" maxQueryString="10000" />
        <fileExtensions>
          <add fileExtension=".env" allowed="false" />
        </fileExtensions>
      </requestFiltering>
    </security>
    
    <!-- Error Pages -->
    <httpErrors errorMode="Detailed" />
    
    <!-- PHP Handler -->
    <handlers accessPolicy="Read, Execute, Script">
      <remove name="PHP84_via_FastCGI" />
      <add name="PHP84_via_FastCGI" path="*.php" verb="*" modules="FastCgiModule" scriptProcessor="C:\Program Files\PHP\v8.4\php-cgi.exe" resourceType="File" requireAccess="Script" />
    </handlers>
    
  </system.webServer>
  
  <system.web>
    <compilation tempDirectory="C:\Inetpub\vhosts\mybrokerforex.com\tmp" />
  </system.web>
</configuration>
```

### **STEP 2: Verify Asset Paths**

Ensure the following files exist and are accessible:

```
✅ /assets/admin/css/simple-email-editor.css
✅ /assets/admin/js/simple-email-editor.js
✅ /assets/admin/js/app.js
✅ /assets/global/css/bootstrap.min.css
✅ /assets/global/js/jquery-3.7.1.min.js
```

### **STEP 3: Test Asset Loading**

After applying the web.config fix, test these URLs directly:

```
https://yourdomain.com/assets/admin/css/simple-email-editor.css
https://yourdomain.com/assets/admin/js/simple-email-editor.js
```

**Expected Result**: Should return actual CSS/JS content, not HTML.

### **STEP 4: Clear Server Cache**

On Windows 2022 Server:

1. **Restart IIS**:
   ```cmd
   iisreset
   ```

2. **Clear Browser Cache** completely

3. **Clear Laravel Cache**:
   ```cmd
   php artisan cache:clear
   php artisan config:clear
   php artisan view:clear
   ```

### **STEP 5: Verify PHP Configuration**

Ensure PHP 8.4 is properly configured:

```ini
; In php.ini
max_execution_time = 300
memory_limit = 512M
post_max_size = 50M
upload_max_filesize = 50M
```

---

## 🧪 **TESTING PROCEDURE**

### **1. Test Asset Loading**
```bash
# Test CSS loading
curl -I https://yourdomain.com/assets/admin/css/simple-email-editor.css
# Should return: Content-Type: text/css

# Test JS loading  
curl -I https://yourdomain.com/assets/admin/js/simple-email-editor.js
# Should return: Content-Type: application/javascript
```

### **2. Test Email Template Editor**
1. Navigate to: `/admin/notification/template/edit/1`
2. **Expected Results**:
   - ✅ Page loads with proper styling
   - ✅ Shortcode buttons are visible and styled
   - ✅ Visual/HTML editor tabs work
   - ✅ Editor content is editable
   - ✅ No JavaScript errors in console

### **3. Browser Console Check**
Open browser DevTools (F12) and check:
- ✅ No 404 errors for CSS/JS files
- ✅ No JavaScript errors
- ✅ Console shows: "✅ Simple Email Editor Initialized Successfully"

---

## 📁 **FILES TO DEPLOY TO LIVE SERVER**

### **1. CRITICAL - Replace web.config**
```
File: /web.config
Action: Replace entire file with corrected version above
Priority: HIGHEST
```

### **2. Verify Asset Files Exist**
```
/assets/admin/css/simple-email-editor.css
/assets/admin/js/simple-email-editor.js
/assets/admin/js/app.js (with null check fix)
```

### **3. Verify View File**
```
/resources/views/admin/notification/edit.blade.php
```

---

## 🚀 **DEPLOYMENT STEPS**

### **Step 1: Backup Current web.config**
```cmd
copy web.config web.config.backup
```

### **Step 2: Deploy New web.config**
```cmd
# Upload the corrected web.config file
```

### **Step 3: Restart Services**
```cmd
iisreset
```

### **Step 4: Test Immediately**
```
1. Open: https://yourdomain.com/assets/admin/css/simple-email-editor.css
2. Verify: Returns CSS content (not HTML)
3. Open: https://yourdomain.com/admin/notification/template/edit/1
4. Verify: Page displays correctly with styling
```

---

## 🔍 **TROUBLESHOOTING**

### **If Assets Still Don't Load**

1. **Check IIS Logs**:
   ```
   C:\inetpub\logs\LogFiles\W3SVC1\
   ```

2. **Verify File Permissions**:
   - Assets folder: Read permissions for IIS_IUSRS
   - PHP files: Read/Execute permissions

3. **Test Direct Asset Access**:
   ```
   https://yourdomain.com/assets/admin/css/simple-email-editor.css
   ```

### **If JavaScript Errors Persist**

1. **Check Console for Specific Errors**
2. **Verify jQuery is Loading First**
3. **Check for Path Case Sensitivity Issues**

---

## ✅ **SUCCESS INDICATORS**

After applying the fix, you should see:

1. **✅ Proper CSS Loading**: Email template editor has correct styling
2. **✅ JavaScript Functionality**: Editor tabs work, shortcodes clickable
3. **✅ No Console Errors**: Browser console shows successful initialization
4. **✅ Asset URLs Work**: Direct access to CSS/JS files returns proper content
5. **✅ Template Saving**: Can edit and save templates successfully

---

## 📞 **IMMEDIATE ACTION REQUIRED**

**PRIORITY 1**: Replace web.config with corrected version
**PRIORITY 2**: Restart IIS
**PRIORITY 3**: Test asset loading
**PRIORITY 4**: Verify email template editor functionality

This fix will resolve the display issues and restore full functionality to the email template editor on Windows 2022 Server.