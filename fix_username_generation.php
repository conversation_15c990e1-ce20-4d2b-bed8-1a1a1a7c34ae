<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔧 FIXING USERNAME GENERATION FORMAT (ISSUE 6)\n";
echo "==============================================\n";

// Find users with poorly formatted usernames
echo "\n🔍 Finding users with poorly formatted usernames:\n";

$badUsernames = \DB::select("
    SELECT id, firstname, lastname, email, username 
    FROM users 
    WHERE username LIKE '%_%' 
    AND username NOT LIKE '@%_01' 
    AND username NOT LIKE '@%_02' 
    AND username NOT LIKE '@%_03'
    AND username NOT LIKE '@%_04'
    AND username NOT LIKE '@%_05'
    ORDER BY id DESC 
    LIMIT 20
");

echo "Found " . count($badUsernames) . " users with poorly formatted usernames:\n";

foreach (array_slice($badUsernames, 0, 10) as $user) {
    echo "   - ID: {$user->id}, Name: {$user->firstname} {$user->lastname}, Email: {$user->email}, Username: {$user->username}\n";
}

// Function to generate proper username
function generateProperUsername($firstname, $email) {
    // Clean firstname - remove spaces, special characters, convert to lowercase
    $cleanFirstname = strtolower(preg_replace('/[^a-zA-Z0-9]/', '', $firstname));
    
    // If firstname is empty or too short, use email prefix
    if (strlen($cleanFirstname) < 2) {
        $emailParts = explode('@', $email);
        $cleanFirstname = strtolower(preg_replace('/[^a-zA-Z0-9]/', '', $emailParts[0]));
    }
    
    // Limit to reasonable length
    $cleanFirstname = substr($cleanFirstname, 0, 15);
    
    $baseUsername = '@' . $cleanFirstname;
    
    // Check if username exists
    $existingCount = \DB::scalar("SELECT COUNT(*) FROM users WHERE username LIKE ?", [$baseUsername . '%']);
    
    if ($existingCount == 0) {
        return $baseUsername;
    } else {
        // Find the next available number
        for ($i = 1; $i <= 99; $i++) {
            $numberedUsername = $baseUsername . '_' . str_pad($i, 2, '0', STR_PAD_LEFT);
            $exists = \DB::scalar("SELECT COUNT(*) FROM users WHERE username = ?", [$numberedUsername]);
            if ($exists == 0) {
                return $numberedUsername;
            }
        }
        
        // Fallback if all numbers are taken
        return $baseUsername . '_' . str_pad(rand(10, 99), 2, '0', STR_PAD_LEFT);
    }
}

// Test the username generation function
echo "\n🧪 Testing username generation:\n";

$testCases = [
    ['firstname' => 'Abdul Rehman', 'email' => '<EMAIL>'],
    ['firstname' => 'John', 'email' => '<EMAIL>'],
    ['firstname' => 'Muhammad Ali', 'email' => '<EMAIL>'],
    ['firstname' => 'Test User', 'email' => '<EMAIL>']
];

foreach ($testCases as $test) {
    $newUsername = generateProperUsername($test['firstname'], $test['email']);
    echo "   - '{$test['firstname']}' + '{$test['email']}' → '{$newUsername}'\n";
}

// Fix usernames for users with bad formats
echo "\n🔧 Fixing usernames for users with bad formats:\n";

$fixedCount = 0;
$duplicateCount = 0;

foreach ($badUsernames as $user) {
    $newUsername = generateProperUsername($user->firstname, $user->email);
    
    // Check if this would create a duplicate
    $existingUser = \DB::select("SELECT id FROM users WHERE username = ? AND id != ?", [$newUsername, $user->id]);
    
    if (count($existingUser) > 0) {
        echo "   - Skipping {$user->username} → {$newUsername} (would create duplicate)\n";
        $duplicateCount++;
        continue;
    }
    
    // Update the username
    $affected = \DB::update("UPDATE users SET username = ? WHERE id = ?", [$newUsername, $user->id]);
    
    if ($affected > 0) {
        echo "   - Fixed: {$user->username} → {$newUsername} (ID: {$user->id})\n";
        $fixedCount++;
    }
}

// Fix specific user mentioned in the issue
echo "\n🔧 Fixing specific user mentioned in issue:\n";

$specificUser = \DB::select("SELECT id, firstname, lastname, email, username FROM users WHERE email = '<EMAIL>'")[0] ?? null;

if ($specificUser) {
    echo "Found user: {$specificUser->firstname} {$specificUser->lastname} ({$specificUser->email}) - Current username: {$specificUser->username}\n";
    
    $newUsername = generateProperUsername($specificUser->firstname, $specificUser->email);
    
    $affected = \DB::update("UPDATE users SET username = ? WHERE id = ?", [$newUsername, $specificUser->id]);
    
    if ($affected > 0) {
        echo "✅ Fixed: {$specificUser->username} → {$newUsername}\n";
    }
} else {
    echo "❌ <NAME_EMAIL> not found\n";
}

// Check for users without @ prefix
echo "\n🔍 Checking users without @ prefix:\n";

$noAtUsers = \DB::select("
    SELECT id, firstname, lastname, email, username 
    FROM users 
    WHERE username NOT LIKE '@%' 
    AND username IS NOT NULL 
    AND username != ''
    ORDER BY id DESC 
    LIMIT 10
");

echo "Found " . count($noAtUsers) . " users without @ prefix:\n";

foreach ($noAtUsers as $user) {
    $newUsername = generateProperUsername($user->firstname, $user->email);
    
    $affected = \DB::update("UPDATE users SET username = ? WHERE id = ?", [$newUsername, $user->id]);
    
    if ($affected > 0) {
        echo "   - Fixed: {$user->username} → {$newUsername} (ID: {$user->id})\n";
        $fixedCount++;
    }
}

// Summary
echo "\n📊 Username Fix Summary:\n";
echo "========================\n";
echo "   - Total usernames fixed: {$fixedCount}\n";
echo "   - Duplicates skipped: {$duplicateCount}\n";

// Verify the fixes
echo "\n🔍 Verification - Checking username formats:\n";

$usernameStats = \DB::select("
    SELECT 
        CASE 
            WHEN username LIKE '@%' AND username NOT LIKE '%_%' THEN 'Proper format (@name)'
            WHEN username LIKE '@%_%' AND username LIKE '@%_[0-9][0-9]' THEN 'Proper numbered (@name_01)'
            WHEN username LIKE '@%_%' THEN 'Improper numbered'
            WHEN username LIKE '@%' THEN 'Proper simple'
            ELSE 'No @ prefix'
        END as format_type,
        COUNT(*) as count
    FROM users 
    WHERE username IS NOT NULL AND username != ''
    GROUP BY 
        CASE 
            WHEN username LIKE '@%' AND username NOT LIKE '%_%' THEN 'Proper format (@name)'
            WHEN username LIKE '@%_%' AND username LIKE '@%_[0-9][0-9]' THEN 'Proper numbered (@name_01)'
            WHEN username LIKE '@%_%' THEN 'Improper numbered'
            WHEN username LIKE '@%' THEN 'Proper simple'
            ELSE 'No @ prefix'
        END
    ORDER BY count DESC
");

foreach ($usernameStats as $stat) {
    echo "   - {$stat->format_type}: {$stat->count} users\n";
}

echo "\n✅ Username generation format fix completed!\n";
