<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class MT5SyncMonitoringService
{
    /**
     * Verify sync accuracy and data integrity
     */
    public function verifySyncAccuracy()
    {
        Log::info('🔍 Starting MT5 Sync Verification');
        
        try {
            $verification = [
                'timestamp' => now(),
                'mt5_total_users' => $this->getMT5UserCount(),
                'local_total_users' => $this->getLocalUserCount(),
                'local_mt5_users' => $this->getLocalMT5UserCount(),
                'null_data_check' => $this->checkNullData(),
                'duplicate_check' => $this->checkDuplicates(),
                'sync_lag' => $this->calculateSyncLag(),
                'data_integrity' => $this->checkDataIntegrity(),
                'performance_metrics' => $this->getPerformanceMetrics()
            ];
            
            // Store verification results
            Cache::put('mt5_sync_verification', $verification, 3600); // 1 hour
            
            // Log critical issues
            $this->logCriticalIssues($verification);
            
            return $verification;
            
        } catch (\Exception $e) {
            Log::error('MT5 Sync Verification Failed', ['error' => $e->getMessage()]);
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Get MT5 database user count
     */
    private function getMT5UserCount()
    {
        try {
            return DB::connection('mbf-dbmt5')
                ->table('mt5_users')
                ->whereNotNull('Email')
                ->where('Email', '!=', '')
                ->where('Group', 'not like', '%admin%')
                ->where('Group', 'not like', '%manager%')
                ->count();
        } catch (\Exception $e) {
            Log::error('Failed to get MT5 user count', ['error' => $e->getMessage()]);
            return 0;
        }
    }

    /**
     * Get local database user count
     */
    private function getLocalUserCount()
    {
        return User::count();
    }

    /**
     * Get local users with MT5 data count
     */
    private function getLocalMT5UserCount()
    {
        return User::whereNotNull('mt5_login')->count();
    }

    /**
     * Check for NULL data in critical fields
     */
    private function checkNullData()
    {
        $nullChecks = [
            'null_firstnames' => User::whereNull('firstname')->count(),
            'null_lastnames' => User::whereNull('lastname')->count(),
            'null_emails' => User::whereNull('email')->count(),
            'null_mt5_logins' => User::whereNotNull('mt5_synced_at')->whereNull('mt5_login')->count(),
            'null_mt5_groups' => User::whereNotNull('mt5_login')->whereNull('mt5_group')->count(),
            'empty_emails' => User::where('email', '')->count(),
            'invalid_emails' => User::where('email', 'not like', '%@%')->count()
        ];
        
        $totalNullIssues = array_sum($nullChecks);
        $nullChecks['total_null_issues'] = $totalNullIssues;
        $nullChecks['data_quality_score'] = $totalNullIssues > 0 ? 
            max(0, 100 - ($totalNullIssues / $this->getLocalUserCount() * 100)) : 100;
        
        return $nullChecks;
    }

    /**
     * Check for duplicate data
     */
    private function checkDuplicates()
    {
        return [
            'duplicate_emails' => User::select('email')
                ->groupBy('email')
                ->havingRaw('COUNT(*) > 1')
                ->count(),
            'duplicate_mt5_logins' => User::select('mt5_login')
                ->whereNotNull('mt5_login')
                ->groupBy('mt5_login')
                ->havingRaw('COUNT(*) > 1')
                ->count(),
            'duplicate_usernames' => User::select('username')
                ->groupBy('username')
                ->havingRaw('COUNT(*) > 1')
                ->count()
        ];
    }

    /**
     * Calculate sync lag (how recent is the data)
     */
    private function calculateSyncLag()
    {
        $lastSync = User::whereNotNull('mt5_synced_at')
            ->max('mt5_synced_at');
        
        if (!$lastSync) {
            return ['lag_minutes' => null, 'status' => 'no_sync_data'];
        }
        
        $lagMinutes = Carbon::parse($lastSync)->diffInMinutes(now());
        
        return [
            'last_sync' => $lastSync,
            'lag_minutes' => $lagMinutes,
            'status' => $lagMinutes <= 2 ? 'excellent' : 
                       ($lagMinutes <= 5 ? 'good' : 
                       ($lagMinutes <= 15 ? 'warning' : 'critical'))
        ];
    }

    /**
     * Check data integrity between MT5 and local
     */
    private function checkDataIntegrity()
    {
        try {
            // Sample check: Compare random MT5 users with local data
            $sampleSize = 10;
            $mt5Sample = DB::connection('mbf-dbmt5')
                ->table('mt5_users')
                ->whereNotNull('Email')
                ->where('Email', '!=', '')
                ->inRandomOrder()
                ->limit($sampleSize)
                ->get(['Login', 'Email', 'FirstName', 'LastName', 'Balance', 'Group']);
            
            $integrityIssues = 0;
            $checkedUsers = 0;
            
            foreach ($mt5Sample as $mt5User) {
                $localUser = User::where('mt5_login', $mt5User->Login)->first();
                
                if ($localUser) {
                    $checkedUsers++;
                    
                    // Check critical field mismatches
                    if ($localUser->email !== $mt5User->Email ||
                        $localUser->firstname !== $mt5User->FirstName ||
                        $localUser->lastname !== $mt5User->LastName ||
                        abs($localUser->mt5_balance - $mt5User->Balance) > 0.01) {
                        $integrityIssues++;
                    }
                }
            }
            
            return [
                'sample_size' => $sampleSize,
                'checked_users' => $checkedUsers,
                'integrity_issues' => $integrityIssues,
                'integrity_score' => $checkedUsers > 0 ? 
                    round((($checkedUsers - $integrityIssues) / $checkedUsers) * 100, 2) : 0
            ];
            
        } catch (\Exception $e) {
            Log::error('Data integrity check failed', ['error' => $e->getMessage()]);
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Get performance metrics
     */
    private function getPerformanceMetrics()
    {
        $recentSyncs = Cache::get('mt5_sync_performance', []);
        
        if (empty($recentSyncs)) {
            return ['no_recent_data' => true];
        }
        
        $avgDuration = collect($recentSyncs)->avg('duration');
        $avgRate = collect($recentSyncs)->avg('rate');
        $lastSync = collect($recentSyncs)->last();
        
        return [
            'avg_duration_seconds' => round($avgDuration, 2),
            'avg_rate_users_per_second' => round($avgRate, 2),
            'last_sync_performance' => $lastSync,
            'performance_trend' => $this->calculatePerformanceTrend($recentSyncs)
        ];
    }

    /**
     * Calculate performance trend
     */
    private function calculatePerformanceTrend($syncs)
    {
        if (count($syncs) < 2) {
            return 'insufficient_data';
        }
        
        $recent = array_slice($syncs, -5); // Last 5 syncs
        $rates = collect($recent)->pluck('rate')->toArray();
        
        $trend = 0;
        for ($i = 1; $i < count($rates); $i++) {
            $trend += $rates[$i] - $rates[$i-1];
        }
        
        return $trend > 0 ? 'improving' : ($trend < 0 ? 'declining' : 'stable');
    }

    /**
     * Log critical issues that need attention
     */
    private function logCriticalIssues($verification)
    {
        $criticalIssues = [];
        
        // Check for high NULL data
        if ($verification['null_data_check']['total_null_issues'] > 100) {
            $criticalIssues[] = "High NULL data count: " . $verification['null_data_check']['total_null_issues'];
        }
        
        // Check for duplicates
        if ($verification['duplicate_check']['duplicate_emails'] > 0) {
            $criticalIssues[] = "Duplicate emails found: " . $verification['duplicate_check']['duplicate_emails'];
        }
        
        // Check sync lag
        if (isset($verification['sync_lag']['status']) && $verification['sync_lag']['status'] === 'critical') {
            $criticalIssues[] = "Sync lag critical: " . $verification['sync_lag']['lag_minutes'] . " minutes";
        }
        
        // Check data integrity
        if (isset($verification['data_integrity']['integrity_score']) && 
            $verification['data_integrity']['integrity_score'] < 90) {
            $criticalIssues[] = "Low data integrity score: " . $verification['data_integrity']['integrity_score'] . "%";
        }
        
        if (!empty($criticalIssues)) {
            Log::warning('MT5 Sync Critical Issues Detected', [
                'issues' => $criticalIssues,
                'verification_data' => $verification
            ]);
        }
    }

    /**
     * Store performance metrics for trending
     */
    public function storePerformanceMetrics($duration, $rate, $processed, $created, $updated, $errors)
    {
        $metrics = Cache::get('mt5_sync_performance', []);
        
        $metrics[] = [
            'timestamp' => now(),
            'duration' => $duration,
            'rate' => $rate,
            'processed' => $processed,
            'created' => $created,
            'updated' => $updated,
            'errors' => $errors
        ];
        
        // Keep only last 50 sync records
        if (count($metrics) > 50) {
            $metrics = array_slice($metrics, -50);
        }
        
        Cache::put('mt5_sync_performance', $metrics, 86400); // 24 hours
    }

    /**
     * Get sync status dashboard data
     */
    public function getSyncStatusDashboard()
    {
        $verification = Cache::get('mt5_sync_verification', []);
        $performance = Cache::get('mt5_sync_performance', []);
        
        return [
            'last_verification' => $verification,
            'recent_performance' => array_slice($performance, -10), // Last 10 syncs
            'system_health' => $this->calculateSystemHealth($verification),
            'recommendations' => $this->generateRecommendations($verification)
        ];
    }

    /**
     * Calculate overall system health score
     */
    private function calculateSystemHealth($verification)
    {
        if (empty($verification)) {
            return ['score' => 0, 'status' => 'unknown'];
        }
        
        $scores = [];
        
        // Data quality score
        if (isset($verification['null_data_check']['data_quality_score'])) {
            $scores[] = $verification['null_data_check']['data_quality_score'];
        }
        
        // Integrity score
        if (isset($verification['data_integrity']['integrity_score'])) {
            $scores[] = $verification['data_integrity']['integrity_score'];
        }
        
        // Sync freshness score
        if (isset($verification['sync_lag']['status'])) {
            $syncScore = match($verification['sync_lag']['status']) {
                'excellent' => 100,
                'good' => 85,
                'warning' => 60,
                'critical' => 20,
                default => 0
            };
            $scores[] = $syncScore;
        }
        
        $avgScore = empty($scores) ? 0 : array_sum($scores) / count($scores);
        
        $status = $avgScore >= 90 ? 'excellent' :
                 ($avgScore >= 75 ? 'good' :
                 ($avgScore >= 50 ? 'warning' : 'critical'));
        
        return [
            'score' => round($avgScore, 1),
            'status' => $status,
            'component_scores' => $scores
        ];
    }

    /**
     * Generate recommendations based on verification results
     */
    private function generateRecommendations($verification)
    {
        $recommendations = [];
        
        if (empty($verification)) {
            return ['Run sync verification to get recommendations'];
        }
        
        // NULL data recommendations
        if (isset($verification['null_data_check']['total_null_issues']) && 
            $verification['null_data_check']['total_null_issues'] > 50) {
            $recommendations[] = "High NULL data detected. Run data cleanup command.";
        }
        
        // Duplicate recommendations
        if (isset($verification['duplicate_check']['duplicate_emails']) && 
            $verification['duplicate_check']['duplicate_emails'] > 0) {
            $recommendations[] = "Duplicate emails found. Review and merge duplicate accounts.";
        }
        
        // Performance recommendations
        if (isset($verification['performance_metrics']['avg_rate_users_per_second']) && 
            $verification['performance_metrics']['avg_rate_users_per_second'] < 50) {
            $recommendations[] = "Sync performance is slow. Consider optimizing database or increasing server resources.";
        }
        
        // Sync lag recommendations
        if (isset($verification['sync_lag']['status']) && 
            in_array($verification['sync_lag']['status'], ['warning', 'critical'])) {
            $recommendations[] = "Sync lag detected. Check scheduler and database connectivity.";
        }
        
        return empty($recommendations) ? ['System is running optimally'] : $recommendations;
    }
}
