{{-- Master IB Network Tree Component --}}
@if($user->ibChildren->count() > 0 && $level < $maxDepth)
    @foreach($user->ibChildren as $child)
    <div class="network-node {{ $child->isIb() ? ($child->ib_type == 'master' ? 'master-ib-node' : 'sub-ib-node') : 'client-node' }}" 
         style="margin-left: {{ $level * 30 }}px;">
        
        <div class="d-flex align-items-center justify-content-between p-3">
            <div class="network-user-info flex-grow-1">
                <div class="d-flex align-items-center">
                    <div class="network-level me-3">
                        <span class="badge badge--{{ $child->isIb() ? ($child->ib_type == 'master' ? 'primary' : 'success') : 'secondary' }}">
                            L{{ $level + 1 }}
                        </span>
                    </div>
                    
                    <div class="user-details">
                        @php
                            $ibIndicator = '';
                            if ($child->isIb()) {
                                if ($child->ib_type === 'master') {
                                    $ibIndicator = ' (M)';
                                } elseif ($child->ib_type === 'sub') {
                                    $ibIndicator = ' (S)';
                                }
                            } else {
                                $ibIndicator = ' (C)';
                            }
                        @endphp
                        <h6 class="mb-1">
                            <strong>{{ $child->fullname }}{{ $ibIndicator }}</strong>
                            <small class="text-muted">({{ $child->username }})</small>
                        </h6>
                        
                        <div class="user-meta d-flex align-items-center gap-2">
                            @if($child->isIb())
                                <span class="badge badge--{{ $child->ib_type == 'master' ? 'primary' : 'info' }} badge-sm">
                                    {{ ucfirst($child->ib_type) }} IB
                                </span>
                                
                                @if($child->ibGroup)
                                    <span class="badge badge--warning badge-sm">{{ $child->ibGroup->name }}</span>
                                @endif

                                <span class="badge badge--{{ $child->ib_status == 'approved' ? 'success' : 'warning' }} badge-sm">
                                    {{ ucfirst($child->ib_status) }}
                                </span>
                            @else
                                <span class="badge badge--secondary badge-sm">Client</span>
                            @endif
                            
                            <small class="text-muted">
                                <i class="las la-calendar"></i>
                                {{ $child->created_at->format('M d, Y') }}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="network-stats text-end">
                <div class="stats-row d-flex gap-3">
                    <div class="stat-item">
                        <small class="text-muted d-block">Referrals</small>
                        <span class="badge badge--info">{{ $child->ibChildren->count() }}</span>
                    </div>
                    
                    @if($child->isIb())
                    <div class="stat-item">
                        <small class="text-muted d-block">Commissions</small>
                        <span class="text--success fw-bold">
                            {{ showAmount($child->ibCommissionsEarned->sum('commission_amount')) }}
                        </span>
                    </div>
                    
                    <div class="stat-item">
                        <small class="text-muted d-block">Trades</small>
                        <span class="badge badge--primary">{{ $child->ibCommissionsEarned->count() }}</span>
                    </div>
                    @endif
                </div>
                
                <div class="network-actions mt-2">
                    @if($child->isIb())
                        <button class="btn btn-sm btn-outline--success" onclick="viewSubIbDetails({{ $child->id }})" title="View IB Details">
                            <i class="las la-chart-line"></i>
                        </button>
                        
                        <button class="btn btn-sm btn-outline--primary" onclick="manageCommissions({{ $child->id }})" title="Manage Commissions">
                            <i class="las la-dollar-sign"></i>
                        </button>
                    @endif
                    
                    <button class="btn btn-sm btn-outline--info" onclick="contactUser({{ $child->id }})" title="Contact">
                        <i class="las la-envelope"></i>
                    </button>
                </div>
            </div>
        </div>
        
        {{-- Performance Indicator --}}
        @if($child->isIb())
        <div class="performance-indicator">
            @php
                $totalCommissions = $child->ibCommissionsEarned->sum('commission_amount');
                $performanceLevel = $totalCommissions > 1000 ? 'high' : ($totalCommissions > 100 ? 'medium' : 'low');
            @endphp
            <div class="performance-bar">
                <div class="performance-fill performance-{{ $performanceLevel }}" 
                     style="width: {{ min(100, ($totalCommissions / 1000) * 100) }}%"></div>
            </div>
            <small class="text-muted">Performance: {{ ucfirst($performanceLevel) }}</small>
        </div>
        @endif
        
        {{-- Connection Line for Children --}}
        @if($child->ibChildren->count() > 0 && $level + 1 < $maxDepth)
            <div class="network-connector text-center">
                <i class="las la-arrow-down text-muted"></i>
            </div>
        @endif
        
        {{-- Recursive Call for Children --}}
        @if($child->ibChildren->count() > 0 && $level + 1 < $maxDepth)
            @include('templates.basic.user.ib.partials.master-network-tree', [
                'user' => $child,
                'level' => $level + 1,
                'maxDepth' => $maxDepth
            ])
        @elseif($child->ibChildren->count() > 0 && $level + 1 >= $maxDepth)
            <div class="network-more" style="margin-left: {{ ($level + 1) * 30 }}px;">
                <div class="alert alert-info">
                    <i class="las la-info-circle"></i>
                    <strong>{{ $child->ibChildren->count() }}</strong> more referrals available
                    <a href="{{ route('user.ib.hierarchy') }}" class="btn btn-sm btn--primary ms-2">
                        View Full Hierarchy
                    </a>
                </div>
            </div>
        @endif
    </div>
    @endforeach
@elseif($level == 0 && $user->ibChildren->count() == 0)
    <div class="text-center py-5">
        <i class="las la-users text-muted" style="font-size: 4rem;"></i>
        <h5 class="text-muted mt-3">No Network Members Yet</h5>
        <p class="text-muted">Start building your network by sharing your referral link</p>
        <a href="{{ route('user.ib.referral_link') }}" class="btn btn--primary">
            <i class="las la-share"></i> Get Referral Link
        </a>
    </div>
@endif

<style>
.network-node {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    margin-bottom: 15px;
    position: relative;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.network-node:hover {
    background: #e9ecef;
    border-color: #dee2e6;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.network-node.sub-ib-node {
    background: #f8fff9;
    border-color: #28a745;
}

.network-node.master-ib-node {
    background: #f0f8ff;
    border-color: #007bff;
}

.network-node.client-node {
    background: #fff9f0;
    border-color: #ffc107;
}

.network-connector {
    margin: 10px 0;
    font-size: 1.2rem;
}

.stats-row {
    min-width: 200px;
}

.stat-item {
    text-align: center;
    min-width: 60px;
}

.network-actions .btn {
    margin-left: 3px;
}

.performance-indicator {
    margin-top: 10px;
    padding: 0 15px 10px;
}

.performance-bar {
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 5px;
}

.performance-fill {
    height: 100%;
    transition: width 0.3s ease;
}

.performance-fill.performance-high {
    background: linear-gradient(90deg, #28a745, #20c997);
}

.performance-fill.performance-medium {
    background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.performance-fill.performance-low {
    background: linear-gradient(90deg, #6c757d, #adb5bd);
}

.network-more .alert {
    margin-bottom: 0;
    padding: 15px;
}

@media (max-width: 768px) {
    .network-node {
        margin-left: 0 !important;
        padding: 15px 10px;
    }
    
    .network-user-info .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
    }
    
    .network-stats {
        margin-top: 15px;
        text-align: left !important;
    }
    
    .stats-row {
        flex-direction: column;
        gap: 10px !important;
    }
    
    .stat-item {
        text-align: left;
    }
}
</style>

<script>
function viewSubIbDetails(userId) {
    // Implementation for viewing Sub IB details
    window.open(`/user/ib/sub-ib-details/${userId}`, '_blank');
}

function manageCommissions(userId) {
    // Implementation for managing commissions
    window.open(`/user/ib/manage-commissions/${userId}`, '_blank');
}

function contactUser(userId) {
    // Implementation for contacting user
    alert('Contact functionality - User ID: ' + userId);
}
</script>
