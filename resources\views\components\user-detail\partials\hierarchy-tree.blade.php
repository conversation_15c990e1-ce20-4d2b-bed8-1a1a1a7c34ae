{{-- Recursive Hierarchy Tree Component --}}
@if($user->ibChildren->count() > 0 && $level < $maxDepth)
    @foreach($user->ibChildren as $child)
    <div class="hierarchy-node {{ $child->isIb() ? ($child->ib_type == 'master' ? 'master-ib' : 'ib-node') : '' }}" 
         style="margin-left: {{ $level * 25 }}px;">
        
        <div class="d-flex align-items-center justify-content-between">
            <div class="hierarchy-user-info">
                <div class="d-flex align-items-center">
                    <div class="hierarchy-level me-2">
                        <span class="badge badge--{{ $child->isIb() ? 'success' : 'secondary' }}">
                            L{{ $level + 1 }}
                        </span>
                    </div>
                    
                    <div class="user-details">
                        <h6 class="mb-0">
                            <strong>{{ $child->fullname }}</strong>
                            <small class="text-muted">({{ $child->username }})</small>
                        </h6>
                        
                        <div class="user-meta">
                            @if($child->isIb())
                                <span class="badge badge--{{ $child->ib_type == 'master' ? 'primary' : 'info' }} badge-sm">
                                    {{ ucfirst($child->ib_type) }} IB
                                </span>
                                
                                @if($child->ibGroup)
                                    <span class="badge badge--warning badge-sm">{{ $child->ibGroup->name }}</span>
                                @endif
                            @else
                                <span class="badge badge--secondary badge-sm">Client</span>
                            @endif
                            
                            <small class="text-muted ms-2">
                                <i class="las la-calendar"></i>
                                {{ $child->created_at->format('M d, Y') }}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="hierarchy-stats">
                @if($child->isIb())
                    <div class="stats-grid text-end">
                        <div class="stat-item">
                            <small class="text-muted">Referrals</small>
                            <br>
                            <span class="badge badge--info">{{ $child->ibChildren->count() }}</span>
                        </div>
                        
                        <div class="stat-item ms-3">
                            <small class="text-muted">Commissions</small>
                            <br>
                            <span class="text--success fw-bold">
                                {{ showAmount($child->ibCommissionsEarned->sum('commission_amount')) }}
                            </span>
                        </div>
                    </div>
                @else
                    <div class="text-end">
                        <small class="text-muted">Client</small>
                        <br>
                        <span class="badge badge--light">{{ $child->ibChildren->count() }} referrals</span>
                    </div>
                @endif
                
                <div class="hierarchy-actions mt-2">
                    <a href="{{ route('admin.users.detail', $child->id) }}" 
                       class="btn btn-sm btn-outline--primary" 
                       title="View Details">
                        <i class="las la-eye"></i>
                    </a>
                    
                    @if($child->isIb())
                        <a href="{{ route('admin.ib.hierarchy', $child->id) }}" 
                           class="btn btn-sm btn-outline--success" 
                           title="View IB Hierarchy">
                            <i class="las la-sitemap"></i>
                        </a>
                    @endif
                </div>
            </div>
        </div>
        
        {{-- Connection Line for Children --}}
        @if($child->ibChildren->count() > 0 && $level + 1 < $maxDepth)
            <div class="hierarchy-connector">
                <i class="las la-arrow-down text-muted"></i>
            </div>
        @endif
        
        {{-- Recursive Call for Children --}}
        @if($child->ibChildren->count() > 0 && $level + 1 < $maxDepth)
            @include('components.user-detail.partials.hierarchy-tree', [
                'user' => $child,
                'level' => $level + 1,
                'maxDepth' => $maxDepth
            ])
        @elseif($child->ibChildren->count() > 0 && $level + 1 >= $maxDepth)
            <div class="hierarchy-more" style="margin-left: {{ ($level + 1) * 25 }}px;">
                <small class="text-muted">
                    <i class="las la-ellipsis-h"></i>
                    {{ $child->ibChildren->count() }} more referrals (max depth reached)
                </small>
            </div>
        @endif
    </div>
    @endforeach
@elseif($level == 0 && $user->ibChildren->count() == 0)
    <div class="text-center py-4">
        <i class="las la-users text-muted" style="font-size: 3rem;"></i>
        <h6 class="text-muted mt-2">No Direct Referrals</h6>
        <p class="text-muted">This user has not referred anyone yet.</p>
    </div>
@endif

<style>
.hierarchy-node {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    position: relative;
    transition: all 0.3s ease;
}

.hierarchy-node:hover {
    background: #e9ecef;
    border-color: #dee2e6;
}

.hierarchy-node.ib-node {
    background: #f8fff9;
    border-color: #28a745;
}

.hierarchy-node.master-ib {
    background: #f0f8ff;
    border-color: #007bff;
}

.hierarchy-connector {
    text-align: center;
    margin: 5px 0;
}

.stats-grid {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.stat-item {
    text-align: center;
}

.hierarchy-actions .btn {
    margin-left: 5px;
}

.hierarchy-more {
    padding: 10px;
    background: #f1f3f4;
    border-radius: 5px;
    margin-bottom: 10px;
}

@media (max-width: 768px) {
    .hierarchy-node {
        margin-left: 0 !important;
        padding: 10px;
    }
    
    .hierarchy-user-info .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
    }
    
    .hierarchy-stats {
        margin-top: 10px;
    }
}
</style>
