<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RebateRule extends Model
{
    use HasFactory;

    protected $fillable = [
        'ib_group_id',
        'symbol_group_id',
        'symbol',
        'rebate_per_lot',
        'min_volume',
        'max_volume',
        'status'
    ];

    protected $casts = [
        'rebate_per_lot' => 'decimal:2',
        'min_volume' => 'decimal:2',
        'max_volume' => 'decimal:2',
        'status' => 'boolean'
    ];

    /**
     * Get the IB group this rule belongs to
     */
    public function ibGroup()
    {
        return $this->belongsTo(IbGroup::class);
    }

    /**
     * Get the symbol group this rule belongs to
     */
    public function symbolGroup()
    {
        return $this->belongsTo(SymbolGroup::class);
    }

    /**
     * Scope for active rules
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Scope for specific symbol
     */
    public function scopeForSymbol($query, $symbol)
    {
        return $query->where(function($q) use ($symbol) {
            $q->where('symbol', $symbol)
              ->orWhereHas('symbolGroup', function($sg) use ($symbol) {
                  $sg->where('symbols_json', 'like', '%"' . $symbol . '"%');
              });
        });
    }

    /**
     * Scope for IB group
     */
    public function scopeForIbGroup($query, $ibGroupId)
    {
        return $query->where('ib_group_id', $ibGroupId);
    }

    /**
     * Get rebate rule for specific symbol and IB group
     */
    public static function getRebateForSymbol($symbol, $ibGroupId)
    {
        // First try to find specific symbol rule
        $rule = self::active()
            ->forIbGroup($ibGroupId)
            ->where('symbol', $symbol)
            ->first();

        if ($rule) {
            return $rule;
        }

        // Then try to find symbol group rule
        $rule = self::active()
            ->forIbGroup($ibGroupId)
            ->whereHas('symbolGroup', function($q) use ($symbol) {
                $q->where('symbols_json', 'like', '%"' . $symbol . '"%');
            })
            ->first();

        return $rule;
    }

    /**
     * Check if volume is within rule limits
     */
    public function isVolumeValid($volume)
    {
        if ($volume < $this->min_volume) {
            return false;
        }

        if ($this->max_volume && $volume > $this->max_volume) {
            return false;
        }

        return true;
    }

    /**
     * Calculate rebate for given volume
     */
    public function calculateRebate($volume)
    {
        if (!$this->isVolumeValid($volume)) {
            return 0;
        }

        return $volume * $this->rebate_per_lot;
    }

    /**
     * Get display name for the rule
     */
    public function getDisplayNameAttribute()
    {
        if ($this->symbol) {
            return $this->symbol;
        }

        return $this->symbolGroup ? $this->symbolGroup->name : 'Unknown';
    }
}
