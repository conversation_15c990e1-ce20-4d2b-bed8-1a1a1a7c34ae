<?php
/**
 * Direct Template Update Test
 * This script tests the template update functionality directly
 */

// Include Laravel bootstrap
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🔧 DIRECT TEMPLATE UPDATE TEST\n";
echo "==============================\n\n";

try {
    // Get a test template
    $template = \App\Models\NotificationTemplate::first();
    if (!$template) {
        echo "❌ No templates found in database\n";
        exit;
    }
    
    echo "✅ Found template ID: {$template->id}\n";
    echo "✅ Current subject: {$template->subj}\n";
    echo "✅ Current email body length: " . strlen($template->email_body) . "\n";
    echo "✅ Current updated_at: {$template->updated_at}\n\n";
    
    // Store original content
    $originalContent = $template->email_body;
    $originalUpdatedAt = $template->updated_at;
    
    // Create test content
    $testContent = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Email</title>
</head>
<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4;">
    <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
        <div style="padding: 20px;">
            <h1 style="color: #dc3545;">TEST UPDATE - ' . date('Y-m-d H:i:s') . '</h1>
            <p>Dear {{fullname}},</p>
            <p>This is a test email template update performed at ' . date('Y-m-d H:i:s') . '</p>
            <p>If you can see this content after refresh, the template save functionality is working correctly.</p>
            <p>Best regards,<br>MBFX Team</p>
        </div>
    </div>
</body>
</html>';
    
    echo "🔄 Attempting to update template with test content...\n";
    echo "🔄 Test content length: " . strlen($testContent) . "\n\n";
    
    // Update the template
    $template->email_body = $testContent;
    $template->subj = 'TEST UPDATE - ' . date('Y-m-d H:i:s');
    
    // Check if template is dirty (has changes)
    $dirtyFields = $template->getDirty();
    echo "🔍 Dirty fields before save: " . json_encode(array_keys($dirtyFields)) . "\n";
    
    // Attempt to save
    $saveResult = $template->save();
    echo "🔍 Save result: " . ($saveResult ? 'SUCCESS' : 'FAILED') . "\n";
    
    // Refresh from database
    $template->refresh();
    
    echo "\n✅ After save and refresh:\n";
    echo "✅ Subject: {$template->subj}\n";
    echo "✅ Email body length: " . strlen($template->email_body) . "\n";
    echo "✅ Updated_at: {$template->updated_at}\n";
    echo "✅ Updated_at changed: " . ($template->updated_at != $originalUpdatedAt ? 'YES' : 'NO') . "\n";
    echo "✅ Content changed: " . ($template->email_body != $originalContent ? 'YES' : 'NO') . "\n";
    
    if ($template->email_body != $originalContent) {
        echo "✅ Content preview: " . substr($template->email_body, 0, 100) . "...\n";
    }
    
    echo "\n🔍 Database connection info:\n";
    $connection = \DB::connection();
    echo "🔍 Driver: " . $connection->getDriverName() . "\n";
    echo "🔍 Database: " . $connection->getDatabaseName() . "\n";
    
    // Test a simple query
    $count = \App\Models\NotificationTemplate::count();
    echo "🔍 Total templates in database: {$count}\n";
    
    // Check if we can perform another update
    echo "\n🔄 Testing second update...\n";
    $template->subj = 'SECOND TEST - ' . date('Y-m-d H:i:s');
    $secondSave = $template->save();
    echo "🔍 Second save result: " . ($secondSave ? 'SUCCESS' : 'FAILED') . "\n";
    
    $template->refresh();
    echo "✅ After second save - Subject: {$template->subj}\n";
    
    echo "\n✅ DIRECT TEMPLATE UPDATE TEST COMPLETED\n";
    echo "📋 If both saves succeeded, the database operations are working correctly.\n";
    echo "📋 The issue might be in the web form submission or JavaScript.\n";
    
} catch (\Exception $e) {
    echo "❌ CRITICAL ERROR: " . $e->getMessage() . "\n";
    echo "❌ File: " . $e->getFile() . "\n";
    echo "❌ Line: " . $e->getLine() . "\n";
    echo "❌ Trace: " . $e->getTraceAsString() . "\n";
}

echo "\n🔧 ADDITIONAL CHECKS:\n";
echo "===================\n";

// Check file permissions
$files = [
    'storage/logs/laravel.log',
    'storage/framework/cache',
    'storage/framework/sessions',
    'storage/framework/views'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        echo "📁 {$file}: " . substr(sprintf('%o', $perms), -4) . "\n";
    } else {
        echo "❌ {$file}: NOT FOUND\n";
    }
}

// Check Laravel configuration
echo "\n🔧 Laravel Configuration:\n";
echo "🔧 APP_ENV: " . env('APP_ENV', 'not set') . "\n";
echo "🔧 APP_DEBUG: " . (env('APP_DEBUG', false) ? 'true' : 'false') . "\n";
echo "🔧 DB_CONNECTION: " . env('DB_CONNECTION', 'not set') . "\n";
echo "🔧 DB_DATABASE: " . env('DB_DATABASE', 'not set') . "\n";

// Check PHP configuration
echo "\n🔧 PHP Configuration:\n";
echo "🔧 PHP Version: " . PHP_VERSION . "\n";
echo "🔧 Memory Limit: " . ini_get('memory_limit') . "\n";
echo "🔧 Max Execution Time: " . ini_get('max_execution_time') . "s\n";
echo "🔧 Post Max Size: " . ini_get('post_max_size') . "\n";
echo "🔧 Upload Max Filesize: " . ini_get('upload_max_filesize') . "\n";

echo "\n📋 NEXT STEPS:\n";
echo "==============\n";
echo "1. If direct update worked: Issue is in web form/JavaScript\n";
echo "2. If direct update failed: Issue is in database/Laravel config\n";
echo "3. Check the debug page: /debug-template-save.html\n";
echo "4. Monitor Laravel logs during web form submission\n";
echo "5. Check browser network tab during form submission\n";

?>
