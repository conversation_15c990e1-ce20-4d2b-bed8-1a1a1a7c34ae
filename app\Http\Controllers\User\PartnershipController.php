<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\User; // MISSING IMPORT FIX
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class PartnershipController extends Controller
{
    /**
     * Partnership Network Dashboard
     */
    public function network(Request $request)
    {
        // CRITICAL FIX: Load user with deposits to prevent N+1 queries
        $user = Auth::user()->load([
            'deposits' => function($query) {
                $query->where('status', 1)->select('user_id', 'amount', 'created_at')->limit(50);
            }
        ]);
        $pageTitle = 'Partnership Network';

        // Check if user is approved IB
        if (!$user->isIb()) {
            $notify[] = ['error', 'You are not an approved IB partner'];
            return redirect()->route('user.home')->withNotify($notify);
        }

        // FIXED: Remove pagination limits to show ALL users in network hierarchy
        // Get MT5 commission data
        $mt5CommissionData = $this->getMT5CommissionData($user->mt5_login, 365);

        // Get complete referral network data (no pagination limits)
        $networkData = $this->getCompleteNetworkData($user);

        // Get recent commission activity
        $recentActivity = $this->getRecentCommissionActivity($user->mt5_login, 10);

        // Return JSON for AJAX requests
        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'data' => $networkData
            ]);
        }

        return view('templates.basic.user.partnership.network', compact(
            'pageTitle',
            'user',
            'mt5CommissionData',
            'networkData',
            'recentActivity'
        ));
    }

    /**
     * Clean Network Test Page
     */
    public function networkClean(Request $request)
    {
        $user = Auth::user();
        $pageTitle = 'Partnership Network (Clean Test)';

        // Simple test data
        $networkData = [
            'direct_referrals' => 3,
            'total_referrals' => 5,
            'tree_data' => [
                'id' => $user->id,
                'name' => $user->firstname . ' ' . $user->lastname,
                'title' => 'Master IB',
                'children' => []
            ]
        ];

        return view('templates.basic.user.partnership.network_clean', compact(
            'pageTitle',
            'user',
            'networkData'
        ));
    }

    /**
     * Simple Network Test Page
     */
    public function networkSimple(Request $request)
    {
        $user = Auth::user();
        $pageTitle = 'Partnership Network (Simple Working Version)';

        // Simple test data
        $networkData = [
            'direct_referrals' => 3,
            'total_referrals' => 5,
            'tree_data' => [
                'id' => $user->id,
                'name' => $user->firstname . ' ' . $user->lastname,
                'title' => 'Master IB',
                'children' => []
            ]
        ];

        return view('templates.basic.user.partnership.network_simple', compact(
            'pageTitle',
            'user',
            'networkData'
        ));
    }

    /**
     * Working Network Page
     */
    public function networkWorking(Request $request)
    {
        $user = Auth::user();
        $pageTitle = 'Partnership Network (Working Version)';

        // Get real network data
        $mt5CommissionData = $this->getMT5CommissionData($user->mt5_login);
        $networkData = $this->getNetworkData($user);
        $recentActivity = $this->getRecentCommissionActivity($user->mt5_login);

        return view('templates.basic.user.partnership.network_working', compact(
            'pageTitle',
            'user',
            'mt5CommissionData',
            'networkData',
            'recentActivity'
        ));
    }

    /**
     * Get referrals for a specific user (AJAX) - Completely optimized to eliminate N+1 queries
     */
    public function getReferrals(Request $request)
    {
        $userId = $request->get('user_id');
        $user = Auth::user();

        // Security check - ensure user can only access their own network
        if (!$this->canAccessUserNetwork($user, $userId)) {
            return response()->json(['success' => false, 'message' => 'Access denied'], 403);
        }

        // Get direct referrals with ZERO N+1 queries - use direct query without relationships
        $referrals = \App\Models\User::where('ref_by', $userId)
            ->withCount('referrals as has_referrals_count')
            ->select('id', 'firstname', 'lastname', 'email', 'mt5_login', 'mt5_balance', 'ib_status', 'ib_type', 'created_at')
            ->get()
            ->map(function($referral) {
                return [
                    'id' => $referral->id,
                    'firstname' => $referral->firstname,
                    'lastname' => $referral->lastname,
                    'email' => $referral->email,
                    'mt5_login' => $referral->mt5_login,
                    'mt5_balance' => $referral->mt5_balance ?? 0,
                    'is_ib' => $referral->ib_status == 1, // Direct property check
                    'ib_type' => $referral->ib_type,
                    'has_referrals' => $referral->has_referrals_count > 0,
                    'created_at' => $referral->created_at->format('M d, Y')
                ];
            });

        return response()->json([
            'success' => true,
            'referrals' => $referrals
        ]);
    }

    /**
     * Check if user can access another user's network data
     */
    private function canAccessUserNetwork($currentUser, $targetUserId)
    {
        // User can access their own network
        if ($currentUser->id == $targetUserId) {
            return true;
        }

        // Check if target user is in current user's network (recursive check)
        return $this->isInNetwork($currentUser->id, $targetUserId);
    }

    /**
     * Check if target user is in the network of root user
     */
    private function isInNetwork($rootUserId, $targetUserId, $depth = 0, $maxDepth = 10)
    {
        if ($depth >= $maxDepth) {
            return false;
        }

        // Get direct referrals of root user
        $directReferrals = \App\Models\User::where('ref_by', $rootUserId)->pluck('id');

        if ($directReferrals->contains($targetUserId)) {
            return true;
        }

        // Check recursively in sub-networks
        foreach ($directReferrals as $referralId) {
            if ($this->isInNetwork($referralId, $targetUserId, $depth + 1, $maxDepth)) {
                return true;
            }
        }

        return false;
    }

    /**
     * TASK 3 FIX: Get real-time commission data from ib_commissions table
     */
    private function getMT5CommissionData($mt5Login, $days = 365)
    {
        if (!$mt5Login) {
            return [
                'total_commission' => 0,
                'commission_count' => 0,
                'pending_commission' => 0,
                'paid_commission' => 0,
                'recent_commissions' => collect(),
                'monthly_commissions' => [],
                'period_days' => $days
            ];
        }

        try {
            // Find user by MT5 login
            $user = User::where('mt5_login', $mt5Login)->first();

            if (!$user) {
                return [
                    'total_commission' => 0,
                    'commission_count' => 0,
                    'pending_commission' => 0,
                    'paid_commission' => 0,
                    'recent_commissions' => collect(),
                    'monthly_commissions' => [],
                    'period_days' => $days
                ];
            }

            $startDate = Carbon::now()->subDays($days);

            // TASK 3 FIX: Get real-time commission data from ib_commissions table
            $commissions = DB::table('ib_commissions')
                ->where('to_ib_user_id', $user->id)
                ->where('created_at', '>=', $startDate)
                ->orderBy('created_at', 'desc')
                ->get();

            $totalCommission = $commissions->sum('commission_amount');
            $paidCommission = $commissions->where('status', 'paid')->sum('commission_amount');
            $pendingCommission = $commissions->where('status', 'pending')->sum('commission_amount');
            $commissionCount = $commissions->count();

            // Group by month for chart
            $monthlyCommissions = $commissions->groupBy(function($commission) {
                return Carbon::parse($commission->created_at)->format('Y-m');
            })->map(function($monthCommissions) {
                return [
                    'month' => Carbon::parse($monthCommissions->first()->created_at)->format('M Y'),
                    'total' => $monthCommissions->sum('commission_amount'),
                    'count' => $monthCommissions->count()
                ];
            })->values();

            // Format recent commissions for display
            $recentCommissions = $commissions->take(10)->map(function($commission) {
                return (object)[
                    'Deal' => $commission->trade_id,
                    'Time' => $commission->created_at,
                    'Commission' => $commission->commission_amount,
                    'Comment' => $commission->notes ?? "Level {$commission->level} commission"
                ];
            });

            return [
                'total_commission' => $totalCommission,
                'commission_count' => $commissionCount,
                'pending_commission' => $pendingCommission,
                'paid_commission' => $paidCommission,
                'recent_commissions' => $recentCommissions,
                'monthly_commissions' => $monthlyCommissions,
                'period_days' => $days
            ];

        } catch (\Exception $e) {
            \Log::error("Failed to get real-time commission data for user", [
                'mt5_login' => $mt5Login,
                'error' => $e->getMessage()
            ]);

            return [
                'total_commission' => 0,
                'commission_count' => 0,
                'pending_commission' => 0,
                'paid_commission' => 0,
                'recent_commissions' => collect(),
                'monthly_commissions' => [],
                'period_days' => $days
            ];
        }
    }

    /**
     * Get COMPLETE network data for IB with SYSTEM PAGINATION - FIXED
     */
    private function getCompleteNetworkData($user)
    {
        $startTime = microtime(true);

        // FIXED: Use Laravel pagination system like other parts of the application
        $directReferralsPaginated = \App\Models\User::where('ref_by', $user->id)
            ->select('id', 'firstname', 'lastname', 'email', 'username', 'mobile', 'country_code', 'created_at', 'status', 'ref_by', 'ib_status', 'ib_type', 'mt5_login', 'mt5_balance', 'mt5_group')
            ->orderBy('created_at', 'desc')
            ->paginate(10); // Use Laravel's built-in pagination with 10 per page

        $totalDirectReferrals = $directReferralsPaginated->total();

        // Get all referrals for tree (not paginated)
        $allDirectReferrals = \App\Models\User::where('ref_by', $user->id)
            ->select('id', 'firstname', 'lastname', 'email', 'username', 'mobile', 'country_code', 'created_at', 'status', 'ref_by', 'ib_status', 'ib_type', 'mt5_login', 'mt5_balance', 'mt5_group')
            ->orderBy('created_at', 'desc')
            ->get();

        // PERFORMANCE FIX: Use optimized count queries instead of recursive methods
        $totalReferrals = $this->getTotalReferralsCountOptimized($user->id);
        $activeReferrals = $allDirectReferrals->where('mt5_login', '!=', null)->count();

        // PERFORMANCE FIX: Build lightweight tree structure
        $completeNetworkTree = $this->buildLightweightNetworkTree($user, $allDirectReferrals);

        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000;

        \Log::info("Network data loaded", [
            'user_id' => $user->id,
            'direct_referrals' => $totalDirectReferrals,
            'total_referrals' => $totalReferrals,
            'execution_time_ms' => round($executionTime, 2)
        ]);

        return [
            'direct_referrals' => $totalDirectReferrals,
            'total_referrals' => $totalReferrals,
            'active_referrals' => $activeReferrals,
            'tree_data' => $completeNetworkTree,
            'direct_referrals_data' => $directReferralsPaginated, // FIXED: Use paginated data
            'pagination' => null, // No longer needed - Laravel handles it
            'performance' => [
                'execution_time_ms' => round($executionTime, 2),
                'queries_optimized' => true
            ]
        ];
    }

    /**
     * Get network data for IB with complete N+1 optimization (LEGACY - kept for compatibility)
     */
    private function getNetworkData($user, $page = 1, $perPage = 15)
    {
        // Get paginated direct referrals with COMPLETE eager loading to eliminate ALL N+1 queries
        $directReferralsQuery = \App\Models\User::where('ref_by', $user->id)
            ->withCount('referrals as children_count')
            ->with([
                'referrer' => function($query) {
                    $query->select('id', 'firstname', 'lastname', 'email');
                },
                'referrals' => function($query) {
                    $query->select('id', 'firstname', 'lastname', 'ref_by', 'mt5_login', 'mt5_balance', 'ib_status', 'ib_type')
                          ->withCount('referrals as children_count')
                          ->with('referrer:id,firstname,lastname,email');
                },
                'deposits' => function($query) {
                    $query->where('status', 1)->select('user_id', 'amount', 'created_at');
                }
            ])
            ->select('id', 'firstname', 'lastname', 'email', 'ref_by', 'mt5_login', 'mt5_balance', 'ib_status', 'ib_type', 'created_at');

        $directReferralsPaginated = $directReferralsQuery->paginate($perPage, ['*'], 'page', $page);

        // Use direct count query instead of relationship count to avoid N+1
        $totalDirectReferrals = \App\Models\User::where('ref_by', $user->id)->count();

        // Get total network size efficiently
        $totalReferrals = $this->getTotalReferralsCountOptimized($user->id);

        // Build optimized tree structure for JSON
        $treeData = $this->buildOptimizedTreeData($user, $directReferralsPaginated->items());

        // Calculate stats without additional queries using loaded data
        $activeReferrals = 0;
        $ibReferrals = 0;

        foreach ($directReferralsPaginated->items() as $referral) {
            if ($referral->mt5_login && $referral->mt5_balance > 0) {
                $activeReferrals++;
            }
            if ($referral->ib_status == 1) {
                $ibReferrals++;
            }
        }

        return [
            'direct_referrals' => $totalDirectReferrals,
            'total_referrals' => $totalReferrals,
            'active_referrals' => $activeReferrals,
            'ib_referrals' => $ibReferrals,
            'tree_data' => $treeData,
            'direct_referrals_list' => $directReferralsPaginated,
            'pagination' => [
                'current_page' => $directReferralsPaginated->currentPage(),
                'last_page' => $directReferralsPaginated->lastPage(),
                'per_page' => $directReferralsPaginated->perPage(),
                'total' => $directReferralsPaginated->total(),
                'has_more' => $directReferralsPaginated->hasMorePages()
            ]
        ];
    }

    /**
     * Get total referrals count optimized with single query
     * Performance: O(1) instead of O(n) recursive queries
     */
    private function getTotalReferralsCountOptimized($userId)
    {
        try {
            // Use recursive CTE for efficient counting (MySQL 8.0+)
            $sql = "
                WITH RECURSIVE referral_tree AS (
                    SELECT id, ref_by, 1 as level
                    FROM users
                    WHERE ref_by = ?

                    UNION ALL

                    SELECT u.id, u.ref_by, rt.level + 1
                    FROM users u
                    INNER JOIN referral_tree rt ON u.ref_by = rt.id
                    WHERE rt.level < 10
                )
                SELECT COUNT(*) as total_count FROM referral_tree
            ";

            $result = \DB::select($sql, [$userId]);
            return $result[0]->total_count ?? 0;
        } catch (\Exception $e) {
            // Fallback for older MySQL versions
            \Log::warning("CTE query failed, using fallback method", ['error' => $e->getMessage()]);
            return $this->getTotalReferralsCountFallback($userId);
        }
    }

    /**
     * Fallback method for MySQL < 8.0
     */
    private function getTotalReferralsCountFallback($userId, $depth = 0, $maxDepth = 5)
    {
        if ($depth >= $maxDepth) return 0;

        $directCount = \App\Models\User::where('ref_by', $userId)->count();
        $indirectCount = 0;

        $directReferrals = \App\Models\User::where('ref_by', $userId)->pluck('id');
        foreach ($directReferrals as $referralId) {
            $indirectCount += $this->getTotalReferralsCountFallback($referralId, $depth + 1, $maxDepth);
        }

        return $directCount + $indirectCount;
    }

    /**
     * Build optimized tree data for OrgChart.js - FIXED N+1 queries
     */
    private function buildOptimizedTreeData($rootUser, $directReferrals)
    {
        // CRITICAL FIX: Ensure root user has deposits loaded to prevent N+1 queries
        if (!$rootUser->relationLoaded('deposits')) {
            $rootUser->load(['deposits' => function($query) {
                $query->where('status', 1)->select('user_id', 'amount', 'created_at')->limit(50);
            }]);
        }

        // Calculate root user deposits from loaded relationship
        $rootUserDeposits = $rootUser->deposits ? $rootUser->deposits->sum('amount') : 0;

        $treeData = [
            'id' => $rootUser->id,
            'name' => $rootUser->firstname . ' ' . $rootUser->lastname,
            'title' => ucfirst($rootUser->ib_type ?? 'master') . ' IB',
            'mt5_login' => $rootUser->mt5_login ?: 'N/A',
            'mt5_balance' => number_format($rootUser->mt5_balance ?? 0, 2),
            'total_deposit' => number_format($rootUserDeposits, 2),
            'deposit_count' => $rootUser->deposits ? $rootUser->deposits->count() : 0,
            'node_type' => 'master',
            'ib_status' => $rootUser->ib_status ?? 1, // TASK 1 FIX: Add ib_status for JavaScript
            'ib_type' => $rootUser->ib_type ?? 'master', // TASK 1 FIX: Add ib_type for JavaScript
            'level' => 0, // Root level
            'children' => []
        ];

        foreach ($directReferrals as $referral) {
            // Direct property access to prevent N+1 queries
            $isIb = $referral->ib_status == 1;

            // Calculate deposits from loaded relationship (already loaded in getNetworkData)
            $referralDeposits = $referral->deposits ? $referral->deposits->sum('amount') : 0;
            $depositCount = $referral->deposits ? $referral->deposits->count() : 0;

            $childData = [
                'id' => $referral->id,
                'name' => $referral->firstname . ' ' . $referral->lastname,
                'title' => $isIb ? ucfirst($referral->ib_type ?? 'sub') . ' IB' : 'Client',
                'mt5_login' => $referral->mt5_login ?: 'N/A',
                'mt5_balance' => number_format($referral->mt5_balance ?? 0, 2),
                'total_deposit' => number_format($referralDeposits, 2),
                'deposit_count' => $depositCount,
                'node_type' => $isIb ? 'ib' : 'client',
                'ib_status' => $referral->ib_status ?? 0, // TASK 1 FIX: Add ib_status for JavaScript
                'ib_type' => $referral->ib_type ?? null, // TASK 1 FIX: Add ib_type for JavaScript
                'level' => 1, // First level referrals
                'children_count' => $referral->children_count ?? 0,
                'has_children' => ($referral->children_count ?? 0) > 0
            ];

            $treeData['children'][] = $childData;
        }

        return $treeData;
    }

    /**
     * Build referral tree for visualization with eager loading
     */
    private function buildReferralTree($user, $maxDepth = 3, $currentDepth = 0)
    {
        if ($currentDepth >= $maxDepth) return [];

        $tree = [];

        // Use eager loading to prevent N+1 queries
        $referrals = $user->referrals()
            ->with(['referrals' => function($query) use ($maxDepth, $currentDepth) {
                if ($currentDepth + 1 < $maxDepth) {
                    $query->select('id', 'firstname', 'lastname', 'ref_by', 'mt5_login', 'mt5_balance', 'ib_status', 'ib_type');
                }
            }])
            ->select('id', 'firstname', 'lastname', 'ref_by', 'mt5_login', 'mt5_balance', 'ib_status', 'ib_type', 'mt5_group')
            ->get();

        foreach ($referrals as $referral) {
            $tree[] = [
                'user' => $referral,
                'level' => $currentDepth + 1,
                'is_ib' => $referral->isIb(),
                'ib_type' => $referral->ib_type,
                'mt5_login' => $referral->mt5_login,
                'mt5_balance' => $referral->mt5_balance ?? 0,
                'mt5_group' => $referral->mt5_group,
                'children' => $this->buildReferralTree($referral, $maxDepth, $currentDepth + 1)
            ];
        }

        return $tree;
    }

    /**
     * Get recent commission activity
     */
    private function getRecentCommissionActivity($mt5Login, $limit = 10)
    {
        if (!$mt5Login) {
            return collect();
        }

        try {
            return DB::connection('mbf-dbmt5')
                ->table('mt5_deals_2025')
                ->where('Login', $mt5Login)
                ->where('Action', 18) // Commission action
                ->select('Deal', 'Time', 'Profit as Commission', 'Comment')
                ->orderBy('Time', 'desc')
                ->limit($limit)
                ->get();

        } catch (\Exception $e) {
            \Log::error("Failed to get recent commission activity", [
                'mt5_login' => $mt5Login,
                'error' => $e->getMessage()
            ]);
            
            return collect();
        }
    }

    /**
     * Get total deposits for MT5 account
     */
    private function getTotalDeposits($mt5Login)
    {
        if (!$mt5Login || $mt5Login === 'N/A') {
            return 0;
        }

        try {
            // Get deposits from MT5 deals table (Action = 2 for deposits)
            $totalDeposits = DB::connection('mbf-dbmt5')
                ->table('mt5_deals_2025')
                ->where('Login', $mt5Login)
                ->where('Action', 2) // Deposit action
                ->where('Profit', '>', 0) // Positive amounts only
                ->sum('Profit');

            return $totalDeposits ?? 0;

        } catch (\Exception $e) {
            \Log::error("Failed to get total deposits", [
                'mt5_login' => $mt5Login,
                'error' => $e->getMessage()
            ]);

            return 0;
        }
    }

    /**
     * Build COMPLETE network tree (all levels) for user dashboard
     */
    private function buildCompleteNetworkTree($rootUser, $maxLevels = 5)
    {
        // Load root user deposits if not already loaded
        if (!$rootUser->relationLoaded('deposits')) {
            $rootUser->load(['deposits' => function($query) {
                $query->where('status', 1)->select('user_id', 'amount', 'created_at')->limit(50);
            }]);
        }

        $rootUserDeposits = $rootUser->deposits ? $rootUser->deposits->sum('amount') : 0;

        $treeData = [
            'id' => $rootUser->id,
            'name' => $rootUser->firstname . ' ' . $rootUser->lastname,
            'title' => $rootUser->ib_status == 1 ? ucfirst($rootUser->ib_type ?? 'master') . ' IB' : 'Client',
            'mt5_login' => $rootUser->mt5_login ?: 'N/A',
            'mt5_balance' => number_format($rootUser->mt5_balance ?? 0, 2),
            'total_deposit' => number_format($rootUserDeposits, 2),
            'deposit_count' => $rootUser->deposits ? $rootUser->deposits->count() : 0,
            'node_type' => $rootUser->ib_status == 1 ? 'master' : 'client',
            'level' => 0,
            'children' => []
        ];

        // Load ALL referrals recursively (no limits)
        $treeData['children'] = $this->loadAllReferralsRecursive($rootUser->id, 1, $maxLevels);

        return $treeData;
    }

    /**
     * Recursively load ALL referrals at all levels (no pagination)
     */
    private function loadAllReferralsRecursive($userId, $currentLevel, $maxLevels)
    {
        if ($currentLevel > $maxLevels) {
            return [];
        }

        // Load ALL direct referrals (no pagination limit)
        $referrals = \App\Models\User::where('ref_by', $userId)
            ->with(['deposits' => function($query) {
                $query->where('status', 1)->select('user_id', 'amount', 'created_at')->limit(50);
            }])
            ->select('id', 'firstname', 'lastname', 'ref_by', 'mt5_login', 'mt5_balance', 'ib_status', 'ib_type', 'mt5_group')
            ->get(); // Get ALL, no limits

        $children = [];

        foreach ($referrals as $referral) {
            $isIb = $referral->ib_status == 1;
            $referralDeposits = $referral->deposits ? $referral->deposits->sum('amount') : 0;
            $depositCount = $referral->deposits ? $referral->deposits->count() : 0;

            $childData = [
                'id' => $referral->id,
                'name' => $referral->firstname . ' ' . $referral->lastname,
                'title' => $isIb ? ucfirst($referral->ib_type ?? 'sub') . ' IB' : 'Client',
                'mt5_login' => $referral->mt5_login ?: 'N/A',
                'mt5_balance' => number_format($referral->mt5_balance ?? 0, 2),
                'total_deposit' => number_format($referralDeposits, 2),
                'deposit_count' => $depositCount,
                'node_type' => $isIb ? 'ib' : 'client',
                'level' => $currentLevel,
                'children' => []
            ];

            // Recursively load children for this referral
            $childData['children'] = $this->loadAllReferralsRecursive($referral->id, $currentLevel + 1, $maxLevels);

            $children[] = $childData;
        }

        return $children;
    }

    /**
     * Get total referrals count (all levels)
     */
    private function getTotalReferralsCount($userId)
    {
        $directCount = \App\Models\User::where('ref_by', $userId)->count();

        $indirectCount = \App\Models\User::whereIn('ref_by', function($query) use ($userId) {
            $query->select('id')->from('users')->where('ref_by', $userId);
        })->count();

        return $directCount + $indirectCount;
    }

    /**
     * Build lightweight network tree for performance (no deep recursion)
     */
    private function buildLightweightNetworkTree($rootUser, $directReferrals)
    {
        $treeData = [
            'id' => $rootUser->id,
            'name' => $rootUser->firstname . ' ' . $rootUser->lastname,
            'title' => $rootUser->ib_status == 1 ? ucfirst($rootUser->ib_type ?? 'master') . ' IB' : 'Client',
            'mt5_login' => $rootUser->mt5_login ?: 'N/A',
            'mt5_balance' => number_format($rootUser->mt5_balance ?? 0, 2),
            'total_deposit' => '0.00', // Skip expensive deposit calculation for performance
            'deposit_count' => 0,
            'node_type' => $rootUser->ib_status == 1 ? 'master' : 'client',
            'ib_status' => $rootUser->ib_status ?? 0, // TASK 1 FIX: Add ib_status for JavaScript
            'ib_type' => $rootUser->ib_type ?? null, // TASK 1 FIX: Add ib_type for JavaScript
            'level' => 0,
            'children' => []
        ];

        // TASK 2 FIX: Add complete hierarchy (not just direct referrals)
        $treeData['children'] = $this->buildCompleteUserNetworkTree($rootUser->id, 1, 5);

        return $treeData;
    }

    /**
     * TASK 2 FIX: Build complete user network tree (all levels) like admin network
     */
    private function buildCompleteUserNetworkTree($userId, $currentLevel, $maxLevels)
    {
        if ($currentLevel > $maxLevels) {
            return [];
        }

        // Load ALL direct referrals (no pagination limit)
        $referrals = User::where('ref_by', $userId)
            ->select('id', 'firstname', 'lastname', 'ref_by', 'mt5_login', 'mt5_balance', 'ib_status', 'ib_type', 'mt5_group')
            ->get();

        $children = [];

        foreach ($referrals as $referral) {
            $isIb = $referral->ib_status == 1;

            $childData = [
                'id' => $referral->id,
                'name' => $referral->firstname . ' ' . $referral->lastname,
                'title' => $isIb ? ucfirst($referral->ib_type ?? 'sub') . ' IB' : 'Client',
                'mt5_login' => $referral->mt5_login ?: 'N/A',
                'mt5_balance' => number_format($referral->mt5_balance ?? 0, 2),
                'total_deposit' => '0.00', // Skip for performance
                'deposit_count' => 0,
                'node_type' => $isIb ? 'ib' : 'client',
                'ib_status' => $referral->ib_status ?? 0, // TASK 1 FIX: Add ib_status for JavaScript
                'ib_type' => $referral->ib_type ?? null, // TASK 1 FIX: Add ib_type for JavaScript
                'level' => $currentLevel,
                'children' => []
            ];

            // TASK 2 FIX: Recursively load children for complete hierarchy
            $childData['children'] = $this->buildCompleteUserNetworkTree($referral->id, $currentLevel + 1, $maxLevels);

            $children[] = $childData;
        }

        return $children;
    }

    /**
     * TASK 1: Get real-time commission data for user partnership page
     */
    public function getRealtimeCommissionData()
    {
        try {
            $user = auth()->user();

            if (!$user->isIb()) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not an IB'
                ]);
            }

            // Get real-time commission data
            $commissionData = $this->getMT5CommissionData($user->mt5_login, 365);

            // Get updated user balance
            $user->refresh();

            return response()->json([
                'success' => true,
                'total_commission' => $commissionData['total_commission'],
                'paid_commission' => $commissionData['paid_commission'],
                'pending_commission' => $commissionData['pending_commission'],
                'mt5_balance' => $user->mt5_balance,
                'commission_count' => $commissionData['commission_count']
            ]);

        } catch (\Exception $e) {
            \Log::error("Failed to get real-time commission data for user", [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch commission data'
            ]);
        }
    }
}
