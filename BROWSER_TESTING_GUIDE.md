# Browser Testing Guide - MT5 Withdrawal & Transaction History

## Overview
This guide provides step-by-step browser testing procedures to verify that the MT5 withdrawal and transaction history fixes are working correctly.

## Pre-Testing Setup

### 1. Test User Requirements
For proper testing, you need a user account with:
- ✅ **Real MT5 Account** (not demo) - Group name contains "real\"
- ✅ **Sufficient MT5 Balance** (at least $10 for testing)
- ✅ **Laravel Wallet** (USD wallet created)
- ✅ **Active Withdrawal Method** available

### 2. Recommended Test User
Based on our analysis, use this test user:
- **Email**: <EMAIL>
- **MT5 Account**: 87654 (Group: real\MBFX\B\Sf\Cp\Fake)
- **MT5 Balance**: $19,575.38
- **Laravel User ID**: 1

## Browser Testing Procedures

### Test 1: Verify MT5 Balance Deduction During Withdrawal

#### Steps:
1. **Login** as test user: <EMAIL>
2. **Navigate** to withdrawal page: `/user/withdraw`
3. **Note Current MT5 Balance** (should be $19,575.38)
4. **Submit Withdrawal Request** for $5.00
5. **Expected Results**:
   - ✅ Withdrawal status: "Pending"
   - ✅ MT5 balance immediately reduced by $5.00 (now $19,570.38)
   - ✅ Success message displayed
   - ✅ No error messages

#### Verification Queries:
```sql
-- Check withdrawal record
SELECT * FROM withdrawals WHERE user_id = 1 ORDER BY id DESC LIMIT 1;

-- Check MT5 balance
SELECT Login, Balance FROM `mbf-dbmt5`.mt5_users WHERE Login = 87654;

-- Check transaction record
SELECT * FROM transactions WHERE user_id = 1 AND remark = 'withdraw' ORDER BY id DESC LIMIT 1;
```

### Test 2: Verify Transaction History Display

#### Steps:
1. **Navigate** to transaction history: `/user/transactions`
2. **Check for Withdraw Transactions**:
   - Look for transactions with remark "withdraw"
   - Verify amount shows as negative (e.g., "-$5.00")
   - Verify proper wallet information displayed
3. **Test Transaction Filtering**:
   - Use filter dropdown to select "Withdraw"
   - Verify only withdraw transactions appear
4. **Expected Results**:
   - ✅ All withdraw transactions visible
   - ✅ Proper amount formatting
   - ✅ Correct wallet information
   - ✅ Filtering works correctly

### Test 3: Admin Withdrawal Approval (No Duplicate Deduction)

#### Steps:
1. **Login as Admin**
2. **Navigate** to pending withdrawals: `/admin/withdraw/pending`
3. **Note Current MT5 Balance** (should already be deducted)
4. **Approve the Withdrawal**
5. **Expected Results**:
   - ✅ Withdrawal status: "Approved"
   - ✅ MT5 balance remains the same (no additional deduction)
   - ✅ New transaction created with remark "withdraw_approved"

#### Verification:
```sql
-- Check withdrawal status
SELECT * FROM withdrawals WHERE user_id = 1 ORDER BY id DESC LIMIT 1;

-- Verify MT5 balance unchanged
SELECT Login, Balance FROM `mbf-dbmt5`.mt5_users WHERE Login = 87654;

-- Check approval transaction
SELECT * FROM transactions WHERE user_id = 1 AND remark = 'withdraw_approved' ORDER BY id DESC LIMIT 1;
```

### Test 4: Admin Withdrawal Rejection (Balance Refund)

#### Steps:
1. **Create Another Withdrawal** for $3.00
2. **Verify MT5 Balance Deducted** immediately
3. **Admin Rejects the Withdrawal**
4. **Expected Results**:
   - ✅ Withdrawal status: "Rejected"
   - ✅ MT5 balance refunded (increased by $3.00)
   - ✅ New transaction created with remark "withdraw_reject"
   - ✅ User can see refund in transaction history

### Test 5: Complete Transaction History Verification

#### Steps:
1. **Navigate** to `/user/transactions`
2. **Verify All Transaction Types Appear**:
   - `withdraw` - Initial withdrawal submission
   - `withdraw_approved` - Admin approved withdrawal
   - `withdraw_reject` - Admin rejected withdrawal (refund)
   - `deposit` - Deposit transactions
   - `internal_transfer` - Internal transfers
3. **Test Pagination and Filtering**
4. **Expected Results**:
   - ✅ All transaction types visible
   - ✅ Proper chronological order
   - ✅ Correct amount signs (+ for credits, - for debits)
   - ✅ Proper wallet information

## Troubleshooting Common Issues

### Issue: "No real MT5 accounts found"
**Solution**: User has only demo accounts. Use a user with real accounts (Group contains "real\").

### Issue: "Insufficient balance"
**Solution**: User's MT5 account has insufficient balance. Use test user with adequate balance.

### Issue: "No wallets found"
**Solution**: User doesn't have Laravel wallets. Create a wallet or use a user with existing wallets.

### Issue: Transactions not appearing
**Possible Causes**:
1. **Browser Cache**: Clear browser cache and refresh
2. **Database Connection**: Check if transactions are actually created in database
3. **Wallet Relationship**: Verify transaction has proper wallet_id

### Issue: MT5 balance not deducting
**Possible Causes**:
1. **Python Script Path**: Check .env PYTHON_EXE and PYTHON_SCRIPT settings
2. **MT5 Database Connection**: Verify mbf-dbmt5 connection works
3. **Account Type**: Ensure using real accounts, not demo accounts

## Success Criteria Checklist

### Functional Requirements
- [ ] ✅ MT5 balance deducted immediately on withdrawal submission
- [ ] ✅ No duplicate MT5 deduction during admin approval
- [ ] ✅ MT5 balance refunded on withdrawal rejection
- [ ] ✅ All withdraw transactions appear in user history
- [ ] ✅ Transaction filtering works correctly
- [ ] ✅ Proper transaction details and formatting

### Technical Requirements
- [ ] ✅ Page load times under 3 seconds
- [ ] ✅ No JavaScript errors in browser console
- [ ] ✅ Proper error handling for edge cases
- [ ] ✅ Responsive design works on mobile/desktop

### User Experience
- [ ] ✅ Clear status messages and notifications
- [ ] ✅ Intuitive transaction history display
- [ ] ✅ Proper balance updates in real-time
- [ ] ✅ Professional UI with black/red theme colors

## Browser Console Debugging

### Check for JavaScript Errors
1. Open browser Developer Tools (F12)
2. Go to Console tab
3. Refresh the page
4. Look for any red error messages

### Network Tab Verification
1. Go to Network tab in Developer Tools
2. Submit withdrawal or load transaction history
3. Check for failed AJAX requests (red status codes)
4. Verify API responses contain expected data

## Database Verification Queries

### Check Recent Withdrawals
```sql
SELECT w.*, u.email, wm.name as method_name 
FROM withdrawals w 
JOIN users u ON w.user_id = u.id 
JOIN withdraw_methods wm ON w.method_id = wm.id 
ORDER BY w.id DESC LIMIT 10;
```

### Check Recent Transactions
```sql
SELECT t.*, u.email, w.name as wallet_name, c.symbol 
FROM transactions t 
JOIN users u ON t.user_id = u.id 
LEFT JOIN wallets w ON t.wallet_id = w.id 
LEFT JOIN currencies c ON w.currency_id = c.id 
ORDER BY t.id DESC LIMIT 20;
```

### Check MT5 Account Balances
```sql
SELECT Login, Email, `Group`, Balance 
FROM `mbf-dbmt5`.mt5_users 
WHERE Email IN ('<EMAIL>') 
ORDER BY Balance DESC;
```

## Final Verification

After completing all tests, verify:
1. **No Breaking Changes**: Existing functionality still works
2. **Performance**: All pages load within acceptable time limits
3. **Data Integrity**: All balances and transactions are accurate
4. **User Experience**: Interface is intuitive and professional

## Contact Information

If issues persist after following this guide:
1. Check Laravel logs: `storage/logs/laravel.log`
2. Check web server error logs
3. Verify database connections and permissions
4. Review MT5 integration configuration
