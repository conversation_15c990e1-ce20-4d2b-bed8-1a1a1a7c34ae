{{-- Master IB Network Table Component --}}
@foreach($users as $networkUser)
<tr>
    <td>
        <div style="padding-left: {{ ($networkUser['level'] - 1) * 25 }}px;">
            <div class="d-flex align-items-center">
                <div class="user-avatar me-2">
                    @if($networkUser['user']->isIb())
                        @if($networkUser['user']->ib_type == 'master')
                            <i class="las la-crown text-primary" title="Master IB"></i>
                        @else
                            <i class="las la-user-tie text-success" title="Sub IB"></i>
                        @endif
                    @else
                        <i class="las la-user text-muted" title="Client"></i>
                    @endif
                </div>
                <div>
                    <strong>{{ $networkUser['user']->fullname }}</strong>
                    <br>
                    <small class="text-muted">{{ $networkUser['user']->username }}</small>
                    <br>
                    <small class="text-info">{{ $networkUser['user']->email }}</small>
                </div>
            </div>
        </div>
    </td>
    
    <td>
        <span class="badge badge--info">Level {{ $networkUser['level'] }}</span>
    </td>
    
    <td>
        @if($networkUser['user']->isIb())
            <div class="d-flex flex-column">
                <span class="badge badge--{{ $networkUser['user']->ib_type == 'master' ? 'primary' : 'success' }} mb-1">
                    {{ ucfirst($networkUser['user']->ib_type) }} IB
                </span>
                <span class="badge badge--{{ $networkUser['user']->ib_status == 'approved' ? 'success' : 'warning' }} badge-sm">
                    {{ ucfirst($networkUser['user']->ib_status) }}
                </span>
            </div>
        @else
            <span class="badge badge--secondary">Client</span>
        @endif
    </td>
    
    <td>
        @if($networkUser['user']->ibGroup)
            <div>
                <span class="badge badge--warning">{{ $networkUser['user']->ibGroup->name }}</span>
                <br>
                <small class="text-muted">{{ $networkUser['user']->ibGroup->commission_multiplier }}x multiplier</small>
            </div>
        @else
            <span class="text-muted">No Group</span>
        @endif
    </td>
    
    <td>
        @if($networkUser['user']->isIb())
            <div class="commission-summary">
                <div class="d-flex justify-content-between">
                    <span class="text-muted">Total:</span>
                    <strong class="text--success">
                        {{ showAmount($networkUser['user']->ibCommissionsEarned->sum('commission_amount')) }}
                    </strong>
                </div>
                
                @php
                    $paidCommissions = $networkUser['user']->ibCommissionsEarned->where('status', 'paid')->sum('commission_amount');
                    $pendingCommissions = $networkUser['user']->ibCommissionsEarned->where('status', 'pending')->sum('commission_amount');
                @endphp
                
                <div class="d-flex justify-content-between">
                    <span class="text-muted">Paid:</span>
                    <span class="text--primary">{{ showAmount($paidCommissions) }}</span>
                </div>
                
                @if($pendingCommissions > 0)
                <div class="d-flex justify-content-between">
                    <span class="text-muted">Pending:</span>
                    <span class="text--warning">{{ showAmount($pendingCommissions) }}</span>
                </div>
                @endif
                
                <small class="text-muted">
                    {{ $networkUser['user']->ibCommissionsEarned->count() }} trades
                </small>
            </div>
        @else
            <span class="text-muted">N/A</span>
        @endif
    </td>
    
    <td>
        <div class="referral-info">
            <div class="d-flex justify-content-between">
                <span class="text-muted">Direct:</span>
                <span class="badge badge--info">{{ $networkUser['user']->ibChildren->count() }}</span>
            </div>
            
            <div class="d-flex justify-content-between">
                <span class="text-muted">Total:</span>
                <span class="badge badge--primary">{{ $networkUser['user']->getAllReferralsCount() }}</span>
            </div>
            
            @if($networkUser['user']->isIb())
            <div class="d-flex justify-content-between">
                <span class="text-muted">Active:</span>
                <span class="badge badge--success">
                    {{ $networkUser['user']->ibChildren->where('ib_status', 'approved')->count() }}
                </span>
            </div>
            @endif
        </div>
    </td>
    
    <td>
        <div class="status-info">
            <div class="mb-1">
                <strong>{{ $networkUser['user']->created_at->format('M d, Y') }}</strong>
            </div>
            <small class="text-muted">{{ $networkUser['user']->created_at->diffForHumans() }}</small>
            
            @if($networkUser['user']->isIb() && $networkUser['user']->ib_approved_at)
                <br>
                <small class="text-success">
                    IB since {{ $networkUser['user']->ib_approved_at->format('M d, Y') }}
                </small>
            @endif
            
            {{-- Performance Indicator --}}
            @if($networkUser['user']->isIb())
                @php
                    $totalCommissions = $networkUser['user']->ibCommissionsEarned->sum('commission_amount');
                    $performanceLevel = $totalCommissions > 1000 ? 'high' : ($totalCommissions > 100 ? 'medium' : 'low');
                    $performanceColor = $performanceLevel == 'high' ? 'success' : ($performanceLevel == 'medium' ? 'warning' : 'secondary');
                @endphp
                <br>
                <span class="badge badge--{{ $performanceColor }} badge-sm">
                    {{ ucfirst($performanceLevel) }} Performer
                </span>
            @endif
        </div>
    </td>
    
    <td>
        <div class="dropdown">
            <button class="btn btn-sm btn-outline--primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                Actions
            </button>
            <ul class="dropdown-menu">
                @if($networkUser['user']->isIb())
                    <li>
                        <a class="dropdown-item" href="#" onclick="viewSubIbPerformance({{ $networkUser['user']->id }})">
                            <i class="las la-chart-line"></i> View Performance
                        </a>
                    </li>
                    
                    <li>
                        <a class="dropdown-item" href="#" onclick="manageSubIbCommissions({{ $networkUser['user']->id }})">
                            <i class="las la-dollar-sign"></i> Manage Commissions
                        </a>
                    </li>
                    
                    <li>
                        <a class="dropdown-item" href="#" onclick="viewSubIbHierarchy({{ $networkUser['user']->id }})">
                            <i class="las la-sitemap"></i> View Sub-Network
                        </a>
                    </li>
                    
                    <li><hr class="dropdown-divider"></li>
                @endif
                
                <li>
                    <a class="dropdown-item" href="#" onclick="sendMessageToUser({{ $networkUser['user']->id }})">
                        <i class="las la-envelope"></i> Send Message
                    </a>
                </li>
                
                <li>
                    <a class="dropdown-item" href="#" onclick="viewUserActivity({{ $networkUser['user']->id }})">
                        <i class="las la-history"></i> View Activity
                    </a>
                </li>
                
                @if($networkUser['user']->isIb() && $networkUser['user']->ib_status == 'approved')
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item text-warning" href="#" onclick="adjustCommissionSettings({{ $networkUser['user']->id }})">
                            <i class="las la-cog"></i> Adjust Settings
                        </a>
                    </li>
                @endif
            </ul>
        </div>
    </td>
</tr>
@endforeach

@if(count($users) == 0)
<tr>
    <td colspan="8" class="text-center py-5">
        <i class="las la-users text-muted" style="font-size: 3rem;"></i>
        <br>
        <h5 class="text-muted mt-3">No Network Members</h5>
        <p class="text-muted">Start building your network by sharing your referral link</p>
        <a href="{{ route('user.ib.referral_link') }}" class="btn btn--primary">
            <i class="las la-share"></i> Get Referral Link
        </a>
    </td>
</tr>
@endif

<script>
function viewSubIbPerformance(userId) {
    // Implementation for viewing Sub IB performance
    window.open(`/user/ib/sub-ib-performance/${userId}`, '_blank');
}

function manageSubIbCommissions(userId) {
    // Implementation for managing Sub IB commissions
    window.open(`/user/ib/manage-sub-ib-commissions/${userId}`, '_blank');
}

function viewSubIbHierarchy(userId) {
    // Implementation for viewing Sub IB hierarchy
    window.open(`/user/ib/sub-ib-hierarchy/${userId}`, '_blank');
}

function sendMessageToUser(userId) {
    // Implementation for sending message to user
    alert('Send message functionality - User ID: ' + userId);
}

function viewUserActivity(userId) {
    // Implementation for viewing user activity
    window.open(`/user/ib/user-activity/${userId}`, '_blank');
}

function adjustCommissionSettings(userId) {
    // Implementation for adjusting commission settings
    if (confirm('Are you sure you want to adjust commission settings for this Sub IB?')) {
        alert('Adjust commission settings functionality - User ID: ' + userId);
    }
}
</script>

<style>
.commission-summary {
    min-width: 150px;
    font-size: 12px;
}

.referral-info {
    min-width: 100px;
    font-size: 12px;
}

.status-info {
    min-width: 120px;
    font-size: 12px;
}

.user-avatar {
    width: 30px;
    text-align: center;
    font-size: 1.2rem;
}

.dropdown-menu {
    min-width: 200px;
}

.dropdown-item {
    padding: 8px 16px;
    font-size: 14px;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

.dropdown-item i {
    width: 16px;
    margin-right: 8px;
}

@media (max-width: 768px) {
    .table-responsive {
        font-size: 11px;
    }
    
    .commission-summary,
    .referral-info,
    .status-info {
        min-width: auto;
        font-size: 10px;
    }
    
    .dropdown-menu {
        min-width: 150px;
    }
    
    .user-avatar {
        font-size: 1rem;
    }
}
</style>
