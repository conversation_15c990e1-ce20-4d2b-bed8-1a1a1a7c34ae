<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SyncCommissions extends Command
{
    protected $signature = 'commissions:sync 
                            {--days=30 : Number of days to sync}
                            {--user-id= : Sync for specific user ID}
                            {--mt5-login= : Sync for specific MT5 login}
                            {--dry-run : Show what would be synced without saving}
                            {--force : Force sync even if recently synced}';

    protected $description = 'Sync commission data from MT5 to local database';

    public function handle()
    {
        $days = $this->option('days');
        $userId = $this->option('user-id');
        $mt5Login = $this->option('mt5-login');
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');

        $this->info('🔄 Starting Commission Sync...');
        $this->info('=====================================');

        if ($dryRun) {
            $this->warn('🧪 DRY RUN MODE - No changes will be made');
        }

        try {
            $startDate = Carbon::now()->subDays($days);
            $syncedCount = 0;
            $errorCount = 0;

            // Get users to sync
            $usersQuery = User::where('partner', 1); // Approved IBs only

            if ($userId) {
                $usersQuery->where('id', $userId);
            }

            if ($mt5Login) {
                $usersQuery->where('mt5_login', $mt5Login);
            }

            $users = $usersQuery->whereNotNull('mt5_login')->get();

            $this->info("📊 Found {$users->count()} IB users to sync");

            foreach ($users as $user) {
                $this->line("🔄 Syncing commissions for {$user->email} (MT5: {$user->mt5_login})");

                try {
                    $synced = $this->syncUserCommissions($user, $startDate, $dryRun, $force);
                    $syncedCount += $synced;
                    
                    if ($synced > 0) {
                        $this->info("  ✅ Synced {$synced} commission records");
                    } else {
                        $this->line("  ℹ️ No new commissions found");
                    }

                } catch (\Exception $e) {
                    $errorCount++;
                    $this->error("  ❌ Failed: " . $e->getMessage());
                    Log::error("Commission sync failed for user {$user->id}", [
                        'user_id' => $user->id,
                        'mt5_login' => $user->mt5_login,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            $this->info('');
            $this->info('🎉 Commission Sync Completed!');
            $this->info("✅ Synced {$syncedCount} commission records");
            $this->info("❌ Errors: {$errorCount}");

            if (!$dryRun && $syncedCount > 0) {
                $this->info('✅ All changes have been saved to the database');
            }

        } catch (\Exception $e) {
            $this->error('💥 Commission sync failed: ' . $e->getMessage());
            Log::error('Commission Sync Failed', ['error' => $e->getMessage()]);
            return 1;
        }

        return 0;
    }

    /**
     * Sync commissions for a specific user
     */
    private function syncUserCommissions($user, $startDate, $dryRun = false, $force = false)
    {
        // Check if recently synced (unless forced)
        if (!$force && $user->last_commission_sync && 
            Carbon::parse($user->last_commission_sync)->diffInHours(now()) < 1) {
            return 0;
        }

        // Get commission deals from MT5
        $commissionDeals = DB::connection('mbf-dbmt5')
            ->table('mt5_deals_2025')
            ->where('Login', $user->mt5_login)
            ->where('Action', 18) // Commission action
            ->where('Time', '>=', $startDate)
            ->select('Deal', 'Login', 'Time', 'Profit as Commission', 'Comment', 'Symbol')
            ->orderBy('Time', 'desc')
            ->get();

        if ($commissionDeals->isEmpty()) {
            return 0;
        }

        $syncedCount = 0;

        foreach ($commissionDeals as $deal) {
            // Check if commission already exists
            $existingCommission = DB::table('ib_commissions')
                ->where('user_id', $user->id)
                ->where('mt5_deal_id', $deal->Deal)
                ->first();

            if ($existingCommission) {
                continue; // Skip if already synced
            }

            if (!$dryRun) {
                // Insert new commission record
                DB::table('ib_commissions')->insert([
                    'user_id' => $user->id,
                    'mt5_login' => $user->mt5_login,
                    'mt5_deal_id' => $deal->Deal,
                    'commission_amount' => $deal->Commission,
                    'symbol' => $deal->Symbol ?? '',
                    'comment' => $deal->Comment ?? '',
                    'deal_time' => Carbon::parse($deal->Time),
                    'status' => 'pending', // Pending approval
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }

            $syncedCount++;
        }

        if (!$dryRun && $syncedCount > 0) {
            // Update user's last sync time
            $user->update([
                'last_commission_sync' => now(),
                'total_commission_earned' => $this->calculateTotalCommission($user->id)
            ]);
        }

        return $syncedCount;
    }

    /**
     * Calculate total commission for user
     */
    private function calculateTotalCommission($userId)
    {
        return DB::table('ib_commissions')
            ->where('user_id', $userId)
            ->where('status', 'approved')
            ->sum('commission_amount');
    }
}
