<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\IbLevel;
use App\Models\IbCommission;
use App\Services\MultiLevelIbCommissionService;
use App\Services\MT5ManagerService; // TASK 3 FIX: Add MT5 service for balance updates
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class CommissionController extends Controller
{
    /**
     * Display commission overview
     */
    public function index(Request $request)
    {
        $pageTitle = 'Commission Management';

        // Get commission statistics
        $stats = $this->getCommissionStats();

        // Get recent commissions with pagination
        $commissions = DB::table('ib_commissions')
            ->join('users', 'ib_commissions.to_ib_user_id', '=', 'users.id')
            ->select(
                'ib_commissions.*',
                'users.firstname',
                'users.lastname',
                'users.email'
            )
            ->orderBy('ib_commissions.created_at', 'desc')
            ->paginate(25);

        return view('admin.commissions.index', compact(
            'pageTitle',
            'stats',
            'commissions'
        ));
    }

    /**
     * Display pending commissions
     */
    public function pending(Request $request)
    {
        $pageTitle = 'Pending Commissions';

        $commissions = DB::table('ib_commissions')
            ->join('users', 'ib_commissions.to_ib_user_id', '=', 'users.id')
            ->where('ib_commissions.status', 'pending')
            ->select(
                'ib_commissions.*',
                'users.firstname',
                'users.lastname',
                'users.email'
            )
            ->orderBy('ib_commissions.created_at', 'desc')
            ->paginate(25);

        return view('admin.commissions.pending', compact(
            'pageTitle',
            'commissions'
        ));
    }

    /**
     * Approve commission
     */
    public function approve(Request $request, $id)
    {
        try {
            $commission = DB::table('ib_commissions')->where('id', $id)->first();

            if (!$commission) {
                $notify[] = ['error', 'Commission not found'];
                return back()->withNotify($notify);
            }

            if ($commission->status !== 'pending') {
                $notify[] = ['error', 'Commission is not pending approval'];
                return back()->withNotify($notify);
            }

            // Update commission status
            DB::table('ib_commissions')
                ->where('id', $id)
                ->update([
                    'status' => 'paid',
                    'paid_at' => now(),
                    'processed_at' => now(),
                    'updated_at' => now()
                ]);

            // TASK 3 FIX: Update user's total commission AND MT5 balance
            $user = User::find($commission->to_ib_user_id);
            if ($user) {
                // Update local commission earnings
                $totalCommission = DB::table('ib_commissions')
                    ->where('to_ib_user_id', $user->id)
                    ->where('status', 'paid')
                    ->sum('commission_amount');

                $user->update(['commission_earnings' => $totalCommission]);

                // TASK 3 FIX: Update MT5 account balance
                if ($user->mt5_login) {
                    $this->updateMT5Balance($user->mt5_login, $commission->commission_amount, "Commission approval - Deal {$commission->trade_id}");
                }
            }

            $notify[] = ['success', 'Commission approved successfully and MT5 balance updated'];
            return back()->withNotify($notify);

        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to approve commission: ' . $e->getMessage()];
            return back()->withNotify($notify);
        }
    }

    /**
     * Reject commission
     */
    public function reject(Request $request, $id)
    {
        try {
            $commission = DB::table('ib_commissions')->where('id', $id)->first();

            if (!$commission) {
                $notify[] = ['error', 'Commission not found'];
                return back()->withNotify($notify);
            }

            if ($commission->status !== 'pending') {
                $notify[] = ['error', 'Commission is not pending approval'];
                return back()->withNotify($notify);
            }

            // Update commission status
            DB::table('ib_commissions')
                ->where('id', $id)
                ->update([
                    'status' => 'cancelled',
                    'processed_at' => now(),
                    'notes' => $request->input('reason', 'No reason provided'),
                    'updated_at' => now()
                ]);

            $notify[] = ['success', 'Commission rejected successfully'];
            return back()->withNotify($notify);

        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to reject commission: ' . $e->getMessage()];
            return back()->withNotify($notify);
        }
    }

    /**
     * Bulk approve commissions
     */
    public function bulkApprove(Request $request)
    {
        $request->validate([
            'commission_ids' => 'required|array',
            'commission_ids.*' => 'integer|exists:ib_commissions,id'
        ]);

        try {
            $commissionIds = $request->input('commission_ids');

            // Update commissions
            $updated = DB::table('ib_commissions')
                ->whereIn('id', $commissionIds)
                ->where('status', 'pending')
                ->update([
                    'status' => 'paid',
                    'paid_at' => now(),
                    'processed_at' => now(),
                    'updated_at' => now()
                ]);

            // TASK 3 FIX: Get commission details before updating for MT5 balance updates
            $commissionsToApprove = DB::table('ib_commissions')
                ->whereIn('id', $commissionIds)
                ->where('status', 'pending')
                ->get();

            // Update user totals AND MT5 balances
            $userIds = DB::table('ib_commissions')
                ->whereIn('id', $commissionIds)
                ->pluck('to_ib_user_id')
                ->unique();

            foreach ($userIds as $userId) {
                $user = User::find($userId);
                if (!$user) continue;

                // Update local commission earnings
                $totalCommission = DB::table('ib_commissions')
                    ->where('to_ib_user_id', $userId)
                    ->where('status', 'paid')
                    ->sum('commission_amount');

                $user->update(['commission_earnings' => $totalCommission]);

                // TASK 3 FIX: Update MT5 balance for this user's approved commissions
                if ($user->mt5_login) {
                    $userCommissions = $commissionsToApprove->where('to_ib_user_id', $userId);
                    $totalAmount = $userCommissions->sum('commission_amount');

                    if ($totalAmount > 0) {
                        $this->updateMT5Balance($user->mt5_login, $totalAmount, "Bulk commission approval - {$userCommissions->count()} commissions");
                    }
                }
            }

            $notify[] = ['success', "Successfully approved {$updated} commissions and updated MT5 balances"];
            return back()->withNotify($notify);

        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to approve commissions: ' . $e->getMessage()];
            return back()->withNotify($notify);
        }
    }

    /**
     * Show commission levels configuration
     */
    public function levels()
    {
        $pageTitle = 'Commission Levels Configuration';
        $levels = IbLevel::orderBy('level')->get();

        return view('admin.commissions.levels', compact('pageTitle', 'levels'));
    }

    /**
     * Update commission levels
     */
    public function updateLevels(Request $request)
    {
        $request->validate([
            'levels' => 'required|array',
            'levels.*.commission_percent' => 'required|numeric|min:0|max:100',
            'levels.*.max_commission_percent' => 'required|numeric|min:0|max:100',
        ]);

        try {
            DB::beginTransaction();

            foreach ($request->levels as $levelId => $levelData) {
                IbLevel::where('id', $levelId)->update([
                    'commission_percent' => $levelData['commission_percent'],
                    'max_commission_percent' => $levelData['max_commission_percent'],
                    'status' => isset($levelData['status']) ? 1 : 0,
                    'description' => $levelData['description'] ?? null,
                    'updated_at' => now()
                ]);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Commission levels updated successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Failed to update commission levels: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reset commission levels to default
     */
    public function resetLevels()
    {
        try {
            DB::beginTransaction();

            // Clear existing levels
            IbLevel::truncate();

            // Create default levels
            $defaultLevels = [
                [
                    'level' => 1,
                    'name' => 'Level 1 - Master IB',
                    'commission_percent' => 50.00,
                    'max_commission_percent' => 80.00,
                    'description' => 'Direct IB - highest commission rate',
                    'status' => 1
                ],
                [
                    'level' => 2,
                    'name' => 'Level 2 - Sub IB',
                    'commission_percent' => 30.00,
                    'max_commission_percent' => 60.00,
                    'description' => 'Second level IB under Master IB',
                    'status' => 1
                ],
                [
                    'level' => 3,
                    'name' => 'Level 3 - Basic IB',
                    'commission_percent' => 20.00,
                    'max_commission_percent' => 40.00,
                    'description' => 'Third level IB in hierarchy',
                    'status' => 1
                ]
            ];

            foreach ($defaultLevels as $level) {
                IbLevel::create($level);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Commission levels reset to default successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Failed to reset commission levels: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Sync commissions from MT5
     */
    public function syncFromMT5(Request $request)
    {
        try {
            $days = $request->input('days', 30);

            // Run the sync command
            \Artisan::call('commissions:sync', [
                '--days' => $days,
                '--force' => true
            ]);

            $output = \Artisan::output();

            $notify[] = ['success', 'Commission sync completed successfully'];
            return back()->withNotify($notify);

        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to sync commissions: ' . $e->getMessage()];
            return back()->withNotify($notify);
        }
    }

    /**
     * Process real-time commission from MT5 trade
     */
    public function processRealTimeCommission(Request $request)
    {
        $request->validate([
            'deal_id' => 'required|string',
            'mt5_login' => 'required|string',
            'symbol' => 'required|string',
            'volume' => 'required|numeric|min:0',
            'profit' => 'required|numeric',
            'commission' => 'numeric|nullable',
            'time' => 'required|date'
        ]);

        try {
            $commissionService = new MultiLevelIbCommissionService();

            $tradeData = [
                'deal_id' => $request->input('deal_id'),
                'mt5_login' => $request->input('mt5_login'),
                'symbol' => $request->input('symbol'),
                'volume' => $request->input('volume'),
                'profit' => $request->input('profit'),
                'commission' => $request->input('commission', 0),
                'time' => $request->input('time')
            ];

            $result = $commissionService->processMultiLevelCommission($tradeData);

            if ($result) {
                // Get created commission records
                $commissions = IbCommission::where('trade_id', $tradeData['deal_id'])->get();

                return response()->json([
                    'success' => true,
                    'message' => 'Commission processed successfully',
                    'commissions_created' => $commissions->count(),
                    'total_amount' => $commissions->sum('commission_amount'),
                    'commissions' => $commissions->map(function($commission) {
                        $ib = User::find($commission->to_ib_user_id);
                        return [
                            'level' => $commission->level,
                            'ib_name' => $ib->fullname,
                            'amount' => $commission->commission_amount,
                            'rate' => $commission->commission_rate
                        ];
                    })
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Commission processing failed'
                ], 400);
            }

        } catch (\Exception $e) {
            Log::error('Real-time commission processing failed', [
                'error' => $e->getMessage(),
                'trade_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Commission processing failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process batch commissions from recent MT5 trades
     */
    public function processBatchCommissions(Request $request)
    {
        $request->validate([
            'hours' => 'integer|min:1|max:168', // Max 1 week
            'force_reprocess' => 'boolean'
        ]);

        try {
            $hours = $request->input('hours', 24);
            $forceReprocess = $request->input('force_reprocess', false);

            $commissionService = new MultiLevelIbCommissionService();

            // Get recent profitable trades from MT5
            $recentTrades = DB::connection('mbf-dbmt5')
                ->table('mt5_deals_2025')
                ->where('Time', '>=', Carbon::now()->subHours($hours))
                ->where('Profit', '>', 0) // Only profitable trades
                ->where('Entry', 1) // Closed trades
                ->orderBy('Time', 'desc')
                ->get();

            $processed = 0;
            $skipped = 0;
            $failed = 0;

            foreach ($recentTrades as $trade) {
                // Check if already processed (unless force reprocess)
                if (!$forceReprocess) {
                    $existing = IbCommission::where('mt5_deal_id', $trade->Deal)->first();
                    if ($existing) {
                        $skipped++;
                        continue;
                    }
                }

                $tradeData = [
                    'deal_id' => $trade->Deal,
                    'mt5_login' => $trade->Login,
                    'symbol' => $trade->Symbol,
                    'volume' => $trade->Volume / 100, // Convert to lots
                    'profit' => $trade->Profit,
                    'commission' => $trade->Commission ?? 0,
                    'time' => $trade->Time
                ];

                if ($commissionService->processMultiLevelCommission($tradeData)) {
                    $processed++;
                } else {
                    $failed++;
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Batch commission processing completed',
                'stats' => [
                    'total_trades' => $recentTrades->count(),
                    'processed' => $processed,
                    'skipped' => $skipped,
                    'failed' => $failed
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Batch commission processing failed', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Batch processing failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get commission statistics
     */
    private function getCommissionStats()
    {
        $today = Carbon::today();
        $thisMonth = Carbon::now()->startOfMonth();
        $lastMonth = Carbon::now()->subMonth()->startOfMonth();

        return [
            'total_pending' => DB::table('ib_commissions')
                ->where('status', 'pending')
                ->count(),

            'total_approved' => DB::table('ib_commissions')
                ->where('status', 'paid') // Use 'paid' status for approved commissions
                ->count(),

            'total_amount_pending' => DB::table('ib_commissions')
                ->where('status', 'pending')
                ->sum('commission_amount'),

            'total_amount_approved' => DB::table('ib_commissions')
                ->where('status', 'paid') // Use 'paid' status for approved commissions
                ->sum('commission_amount'),

            'today_commissions' => DB::table('ib_commissions')
                ->whereDate('created_at', $today)
                ->count(),

            'this_month_amount' => DB::table('ib_commissions')
                ->where('status', 'paid') // Use 'paid' status for approved commissions
                ->where('created_at', '>=', $thisMonth)
                ->sum('commission_amount'),

            'last_month_amount' => DB::table('ib_commissions')
                ->where('status', 'paid') // Use 'paid' status for approved commissions
                ->where('created_at', '>=', $lastMonth)
                ->where('created_at', '<', $thisMonth)
                ->sum('commission_amount')
        ];
    }

    /**
     * TASK 3 FIX: Update MT5 account balance when commission is approved
     */
    private function updateMT5Balance($mt5Login, $amount, $comment)
    {
        try {
            // CRITICAL BUG FIX: Use the correct MT5 Python integration script from environment
            $pythonExe = env('PYTHON_EXE', 'python');
            $pythonScript = env('PYTHON_SCRIPT', base_path('python/mt5manager.py'));

            // CRITICAL BUG FIX: Use correct command format for mt5manager.py add_balance
            $command = sprintf(
                '%s %s add_balance --login %s --amount %s --comment %s',
                escapeshellarg($pythonExe),
                escapeshellarg($pythonScript),
                escapeshellarg($mt5Login),
                escapeshellarg($amount),
                escapeshellarg($comment)
            );

            // Execute the Python script
            $output = shell_exec($command . ' 2>&1');

            Log::info("MT5 balance update executed", [
                'mt5_login' => $mt5Login,
                'amount' => $amount,
                'comment' => $comment,
                'output' => $output
            ]);

            // Update local MT5 balance as well
            User::where('mt5_login', $mt5Login)->increment('mt5_balance', $amount);

            return true;

        } catch (\Exception $e) {
            Log::error("Failed to update MT5 balance", [
                'mt5_login' => $mt5Login,
                'amount' => $amount,
                'error' => $e->getMessage()
            ]);

            // Still update local balance even if MT5 update fails
            User::where('mt5_login', $mt5Login)->increment('mt5_balance', $amount);

            return false;
        }
    }
}
