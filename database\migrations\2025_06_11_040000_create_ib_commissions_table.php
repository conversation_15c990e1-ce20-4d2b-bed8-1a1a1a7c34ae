<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('ib_commissions')) {
            Schema::create('ib_commissions', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('user_id');
                $table->string('mt5_login', 50);
                $table->string('mt5_deal_id', 50)->unique();
                $table->decimal('commission_amount', 15, 2);
                $table->string('symbol', 20)->nullable();
                $table->text('comment')->nullable();
                $table->timestamp('deal_time');
                $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending');
                $table->timestamp('approved_at')->nullable();
                $table->unsignedBigInteger('approved_by')->nullable();
                $table->timestamp('rejected_at')->nullable();
                $table->unsignedBigInteger('rejected_by')->nullable();
                $table->text('rejection_reason')->nullable();
                $table->timestamps();

                // Indexes for performance
                $table->index(['user_id', 'status']);
                $table->index(['mt5_login', 'deal_time']);
                $table->index(['status', 'created_at']);
                $table->index('deal_time');

                // Foreign key constraints
                $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
                $table->foreign('approved_by')->references('id')->on('admins')->onDelete('set null');
                $table->foreign('rejected_by')->references('id')->on('admins')->onDelete('set null');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ib_commissions');
    }
};
