<!doctype html>
<html lang="<?php echo e(config('app.locale')); ?>" itemscope itemtype="http://schema.org/WebPage">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title> <?php echo e($general->siteName(__($pageTitle))); ?></title>
    <?php echo $__env->make('partials.seo', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <link href="<?php echo e(asset('assets/global/css/bootstrap.min.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('assets/global/css/all.min.css')); ?>" rel="stylesheet">
    <link rel="stylesheet" href="<?php echo e(asset('assets/global/css/line-awesome.min.css')); ?>" />

    <link rel="stylesheet" href="<?php echo e(asset($activeTemplateTrue . 'dashboard/css/icomoon.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset($activeTemplateTrue . 'dashboard/css/main.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset($activeTemplateTrue . 'css/custom.css')); ?>">


    <?php echo $__env->yieldPushContent('style-lib'); ?>
    <?php echo $__env->yieldPushContent('style'); ?>

    <link rel="stylesheet" href="<?php echo e(asset($activeTemplateTrue . 'css/color.php')); ?>?color=<?php echo e($general->base_color); ?>">

    
    <?php if(session('app')): ?>
        <style>
            .dashboard-header,
            .dashboardBodyNav {
                display: none !important;
            }
        </style>
    <?php endif; ?>

</head>

<body>
    <?php if(!request()->routeIs('user.home')): ?>
        <div class="preloader">
            <div class="loader-p"></div>
        </div>
    <?php endif; ?>

    <div class="body-overlay"></div>
    <div class="sidebar-overlay"></div>
    <a class="scroll-top"><i class="fas fa-angle-double-up"></i></a>

    <div class="dashboard-fluid position-relative">
        <div class="dashboard__inner">
            <?php echo $__env->make($activeTemplate . 'partials.user_sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <div class="dashboard__right">
                <?php echo $__env->make($activeTemplate . 'partials.user_topbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <div class="dashboard-body">
                    <div class="d-flex justify-content-between mb-3 align-items-center dashboardBodyNav">
                        <div class="dashboard-body__bar d-xl-none d-inline-block">
                            <button class="dashboard-sidebar-filter__button">
                                <i class="las la-bars"></i>
                            </button>
                        </div>
                        <?php if(request()->routeIs('user.home')): ?>
                            <div class="dashboard-body__bar style ">
                                <span class="dashboard-body__bar-two-icon toggle-dashboard-right"><i class="fas fa-bars"></i></span>
                            </div>
                        <?php endif; ?>

                        <?php if(request()->routeIs('user.p2p*')): ?>
                            <div class="p2p-sidebar__menu">
                                <span class="p2p-sidebar__menu-icon">
                                    <i class="fas fa-bars"></i>
                                </span>
                            </div>
                        <?php endif; ?>

                    </div>
                    <?php echo $__env->yieldPushContent('topContent'); ?>
                    <?php echo $__env->yieldContent('content'); ?>
                </div>
            </div>
        </div>
    </div>

    <script src="<?php echo e(asset('assets/global/js/jquery-3.7.1.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/global/js/bootstrap.bundle.min.js')); ?>"></script>
    <script src="<?php echo e(asset($activeTemplateTrue . 'dashboard/js/main.js')); ?>"></script>

    <script>
        window.allow_decimal = "<?php echo e($general->allow_decimal_after_number); ?>";
    </script>

    <?php echo $__env->yieldPushContent('script-lib'); ?>
    <?php echo $__env->make('partials.notify', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('partials.plugins', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->yieldPushContent('script'); ?>

    <script>
        (function($) {
            "use strict";

            var inputElements = $('[type=text],[type=password],select,textarea');
            $.each(inputElements, function(index, element) {
                element = $(element);
                element.closest('.form-group').find('label').attr('for', element.attr('name'));
                element.attr('id', element.attr('name'))
            });

            $.each($('input, select, textarea'), function(i, element) {
                if (element.hasAttribute('required')) {
                    $(element).closest('.form-group').find('label').addClass('required');
                }
            });

            $('.showFilterBtn').on('click', function() {
                $('.responsive-filter-card').slideToggle();
            });

            Array.from(document.querySelectorAll('table')).forEach(table => {
                let heading = table.querySelectorAll('thead tr th');
                Array.from(table.querySelectorAll('tbody tr')).forEach((row) => {
                    if (row.querySelectorAll('td').length > 1) {
                        Array.from(row.querySelectorAll('td')).forEach((colum, i) => {
                            colum.setAttribute('data-label', heading[i].innerText)
                        });
                    }
                });
            });


            <?php if(session('app')): ?>
                $('.btn--base').each(function() {
                    var isInForm = $(this).closest('form').length > 0;
                    if (isInForm) {
                        $(this).closest('form').submit(function() {
                            let html = `<span class="spinner-border spinner-border-sm" role="status"></span>`;
                            $(this).find('.btn--base').attr('disabled', true).html(html);
                        });
                    } else {
                        $(this).on('click', function() {
                            let html = `<span class="spinner-border spinner-border-sm" role="status"></span>`;
                            $(this).attr('disabled', true).html(html);
                        });
                    }
                });
            <?php endif; ?>

        })(jQuery);
    </script>
</body>

</html>
<?php /**PATH C:\xampp\htdocs\mbf.mybrokerforex.com-31052025\resources\views/templates/basic/layouts/master.blade.php ENDPATH**/ ?>