<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AccountLevel extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'image',
        'platform_group_default',
        'trading_server_live',
        'enable_separate_swap_free',
        'platform_group_swap_free',
        'leverage_options',
        'country_restrictions',
        'tags',
        'status'
    ];

    protected $casts = [
        'leverage_options' => 'array',
        'country_restrictions' => 'array',
        'tags' => 'array',
        'enable_separate_swap_free' => 'boolean',
        'status' => 'boolean'
    ];

    /**
     * Scope for active account levels
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Scope for inactive account levels
     */
    public function scopeInactive($query)
    {
        return $query->where('status', false);
    }

    /**
     * Get users with this account level
     */
    public function users()
    {
        return $this->hasMany(User::class, 'account_level_id');
    }

    /**
     * Get rebate rules for this account level
     */
    public function rebateRules()
    {
        return $this->belongsToMany(RebateRule::class, 'rebate_rule_account_types', 'account_type_id', 'rebate_rule_id');
    }

    /**
     * Toggle account level status
     */
    public function toggleStatus()
    {
        $this->status = !$this->status;
        $this->save();
        return $this;
    }

    /**
     * Get formatted leverage options
     */
    public function getFormattedLeverageOptionsAttribute()
    {
        if (empty($this->leverage_options)) {
            return 'Not specified';
        }
        
        return implode(', ', array_map(function($leverage) {
            return '1:' . $leverage;
        }, $this->leverage_options));
    }

    /**
     * Get formatted country restrictions
     */
    public function getFormattedCountryRestrictionsAttribute()
    {
        if (empty($this->country_restrictions)) {
            return 'All countries';
        }
        
        return implode(', ', $this->country_restrictions);
    }

    /**
     * Get formatted tags
     */
    public function getFormattedTagsAttribute()
    {
        if (empty($this->tags)) {
            return '';
        }
        
        return implode(', ', $this->tags);
    }

    /**
     * Check if country is allowed
     */
    public function isCountryAllowed($country)
    {
        if (empty($this->country_restrictions)) {
            return true;
        }
        
        return in_array($country, $this->country_restrictions);
    }

    /**
     * Check if leverage is available
     */
    public function isLeverageAvailable($leverage)
    {
        if (empty($this->leverage_options)) {
            return false;
        }
        
        return in_array($leverage, $this->leverage_options);
    }

    /**
     * Get image URL using system's standard getImage helper
     */
    public function getImageUrlAttribute()
    {
        if ($this->image) {
            // Use system's standard getImage helper function
            return getImage('assets/images/account_levels/' . $this->image, '100x100');
        }

        // Return default placeholder using system's helper
        return getImage('assets/images/default.png', '100x100');
    }

    /**
     * Get all active account levels for dropdown
     */
    public static function getActiveForDropdown()
    {
        return self::active()->pluck('name', 'id');
    }

    /**
     * Search account levels
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', '%' . $search . '%')
              ->orWhere('platform_group_default', 'like', '%' . $search . '%')
              ->orWhere('trading_server_live', 'like', '%' . $search . '%');
        });
    }
}
