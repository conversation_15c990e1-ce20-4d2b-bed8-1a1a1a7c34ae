<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\AccountLevel;

echo "=== ACCOUNT LEVELS DATABASE CHECK ===\n\n";

$levels = AccountLevel::all();

echo "Found " . $levels->count() . " account levels:\n\n";

foreach($levels as $level) {
    echo "ID: " . $level->id . "\n";
    echo "Name: " . $level->name . "\n";
    echo "Image (DB): " . ($level->image ?? 'NULL') . "\n";
    echo "Image URL: " . $level->image_url . "\n";
    
    // Check if file exists
    if ($level->image) {
        $storagePath = storage_path('app/public/account_levels/' . $level->image);
        $publicPath = public_path('storage/account_levels/' . $level->image);
        
        echo "Storage Path: " . $storagePath . "\n";
        echo "Storage Exists: " . (file_exists($storagePath) ? "YES" : "NO") . "\n";
        echo "Public Path: " . $publicPath . "\n";
        echo "Public Exists: " . (file_exists($publicPath) ? "YES" : "NO") . "\n";
    }
    
    echo "---\n\n";
}

?>
