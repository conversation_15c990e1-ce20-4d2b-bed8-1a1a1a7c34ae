<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Template Save Functionality Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: #dc3545; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .test-section h3 { color: #dc3545; margin-top: 0; }
        .test-button { background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .test-button:hover { background: #c82333; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .test-url { background: #f8f9fa; padding: 10px; border-left: 4px solid #dc3545; margin: 10px 0; font-family: monospace; }
        .code { background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; margin: 10px 0; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .checklist { list-style: none; padding: 0; }
        .checklist li { padding: 8px 0; border-bottom: 1px solid #eee; }
        .checklist li:before { content: "☐ "; color: #dc3545; font-weight: bold; }
        .checklist li.checked:before { content: "✅ "; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Template Save Functionality Test</h1>
            <p>Comprehensive testing to ensure Visual Builder edits are properly saved</p>
        </div>

        <!-- Test Instructions -->
        <div class="test-section">
            <h3><span class="status-indicator status-warning"></span>Test Instructions</h3>
            <p>Follow these steps to test the template save functionality:</p>
            
            <div class="test-url">
                <strong>Test URL:</strong> https://localhost/mbf.mybrokerforex.com-31052025/admin/notification/template/edit/1
            </div>
            
            <ol>
                <li><strong>Open Template Editor:</strong> Navigate to any email template edit page</li>
                <li><strong>Check Browser Console:</strong> Open Developer Tools (F12) and check Console tab</li>
                <li><strong>Edit Content:</strong> Make changes in the Visual Builder editor</li>
                <li><strong>Save Template:</strong> Click "Update Template" button</li>
                <li><strong>Verify Save:</strong> Reload page and check if changes persisted</li>
                <li><strong>Check Logs:</strong> Review Laravel logs for debugging information</li>
            </ol>
        </div>

        <!-- Browser Console Tests -->
        <div class="test-section">
            <h3><span class="status-indicator status-info"></span>Browser Console Tests</h3>
            <p>Run these commands in your browser console to test the functionality:</p>
            
            <div class="code">
// 1. Check if Visual Builder is initialized
console.log('Visual Builder Instance:', typeof visualBuilderInstance);

// 2. Check if template data is loaded
console.log('Template Data:', window.templateData);

// 3. Test form field sync
if (visualBuilderInstance && visualBuilderInstance.updateFormFields) {
    visualBuilderInstance.updateFormFields();
    console.log('✅ Form fields updated');
} else {
    console.log('❌ Visual Builder not available');
}

// 4. Check form field values
const emailBodyField = document.querySelector('textarea[name="email_body"]');
const emailBodyFinalField = document.getElementById('email_body_final');
console.log('Email Body Field:', emailBodyField ? emailBodyField.value.length + ' chars' : 'NOT FOUND');
console.log('Email Body Final Field:', emailBodyFinalField ? emailBodyFinalField.value.length + ' chars' : 'NOT FOUND');

// 5. Test form submission preparation
if (typeof prepareFormSubmission === 'function') {
    prepareFormSubmission();
    console.log('✅ Form submission prepared');
} else {
    console.log('❌ prepareFormSubmission function not found');
}
            </div>
            
            <button class="test-button" onclick="runConsoleTests()">Run Console Tests</button>
            <div id="consoleTestResults" class="test-result" style="display: none;"></div>
        </div>

        <!-- Manual Testing Checklist -->
        <div class="test-section">
            <h3><span class="status-indicator status-warning"></span>Manual Testing Checklist</h3>
            <p>Complete this checklist to verify all functionality:</p>
            
            <ul class="checklist">
                <li id="check1">Open template edit page without errors</li>
                <li id="check2">Visual Builder loads with existing template content</li>
                <li id="check3">Can edit content in Visual Builder</li>
                <li id="check4">Form fields sync when content changes</li>
                <li id="check5">Click "Update Template" button</li>
                <li id="check6">Success message appears after save</li>
                <li id="check7">Reload page and verify changes persisted</li>
                <li id="check8">Template structure remains intact (no corruption)</li>
                <li id="check9">MBFX logo and formatting preserved</li>
                <li id="check10">Laravel logs show successful update</li>
            </ul>
            
            <button class="test-button" onclick="markAllChecked()">Mark All Checked</button>
            <button class="test-button" onclick="resetChecklist()">Reset Checklist</button>
        </div>

        <!-- Debugging Information -->
        <div class="test-section">
            <h3><span class="status-indicator status-info"></span>Debugging Information</h3>
            <p>Key areas to check if save functionality is not working:</p>
            
            <div class="info">
                <h4>1. JavaScript Console Errors:</h4>
                <ul>
                    <li>Check for Visual Builder initialization errors</li>
                    <li>Verify prepareFormSubmission function exists</li>
                    <li>Look for form field sync errors</li>
                </ul>
                
                <h4>2. Laravel Logs (storage/logs/laravel.log):</h4>
                <ul>
                    <li>Template update debugging information</li>
                    <li>Content length comparisons</li>
                    <li>Corruption detection messages</li>
                </ul>
                
                <h4>3. Network Tab (Developer Tools):</h4>
                <ul>
                    <li>Check POST request to template update endpoint</li>
                    <li>Verify email_body_final field is being sent</li>
                    <li>Look for 200 response status</li>
                </ul>
                
                <h4>4. Form Field Values:</h4>
                <ul>
                    <li>email_body textarea should contain updated content</li>
                    <li>email_body_final hidden field should match</li>
                    <li>original_email_body should contain original content</li>
                </ul>
            </div>
        </div>

        <!-- Expected Behavior -->
        <div class="test-section">
            <h3><span class="status-indicator status-success"></span>Expected Behavior</h3>
            <p>After implementing the fixes, you should see:</p>
            
            <div class="success">
                <h4>✅ Successful Template Editing:</h4>
                <ul>
                    <li><strong>Content Preservation:</strong> Template structure remains intact</li>
                    <li><strong>Edit Persistence:</strong> Changes are saved and persist after reload</li>
                    <li><strong>No Corruption:</strong> No repeated text or broken formatting</li>
                    <li><strong>Form Sync:</strong> Visual Builder content syncs to form fields</li>
                    <li><strong>Proper Logging:</strong> Debug information appears in Laravel logs</li>
                </ul>
                
                <h4>🔧 Key Fixes Implemented:</h4>
                <ul>
                    <li><strong>prepareFormSubmission():</strong> Syncs Visual Builder content before save</li>
                    <li><strong>updateFormFields():</strong> Real-time form field synchronization</li>
                    <li><strong>email_body_final:</strong> Hidden field captures Visual Builder content</li>
                    <li><strong>Enhanced Logging:</strong> Detailed debugging information</li>
                    <li><strong>Content Validation:</strong> Prevents corruption during save</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function runConsoleTests() {
            const resultDiv = document.getElementById('consoleTestResults');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = '🔄 Running console tests... Check browser console for detailed results.';
            
            // Run the tests
            setTimeout(() => {
                let results = [];
                
                // Test 1: Visual Builder instance
                if (typeof visualBuilderInstance !== 'undefined') {
                    results.push('✅ Visual Builder instance found');
                } else {
                    results.push('❌ Visual Builder instance not found');
                }
                
                // Test 2: Template data
                if (typeof window.templateData !== 'undefined') {
                    results.push('✅ Template data loaded');
                } else {
                    results.push('❌ Template data not found');
                }
                
                // Test 3: Form fields
                const emailBodyField = document.querySelector('textarea[name="email_body"]');
                const emailBodyFinalField = document.getElementById('email_body_final');
                
                if (emailBodyField) {
                    results.push('✅ Email body field found');
                } else {
                    results.push('❌ Email body field not found');
                }
                
                if (emailBodyFinalField) {
                    results.push('✅ Email body final field found');
                } else {
                    results.push('❌ Email body final field not found');
                }
                
                // Test 4: Functions
                if (typeof prepareFormSubmission === 'function') {
                    results.push('✅ prepareFormSubmission function found');
                } else {
                    results.push('❌ prepareFormSubmission function not found');
                }
                
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = '<h4>Console Test Results:</h4><ul><li>' + results.join('</li><li>') + '</li></ul>';
            }, 1000);
        }
        
        function markAllChecked() {
            const checklistItems = document.querySelectorAll('.checklist li');
            checklistItems.forEach(item => {
                item.classList.add('checked');
            });
        }
        
        function resetChecklist() {
            const checklistItems = document.querySelectorAll('.checklist li');
            checklistItems.forEach(item => {
                item.classList.remove('checked');
            });
        }
        
        // Auto-run initial check
        window.onload = function() {
            console.log('🔧 Template Save Functionality Test Suite Loaded');
            console.log('📋 Use this page to test template editing and saving functionality');
        };
    </script>
</body>
</html>
