<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\NotificationTemplate;

class TestSpecificTemplates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test-specific {templates : Comma-separated template IDs} {--email=<EMAIL>}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test email delivery for specific template IDs';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $templateIds = explode(',', $this->argument('templates'));
        $testEmail = $this->option('email');
        
        $this->info('🧪 TESTING SPECIFIC TEMPLATES');
        $this->line('==============================');
        $this->line("Template IDs: " . implode(', ', $templateIds));
        $this->line("Test Email: {$testEmail}");
        $this->line('');

        foreach ($templateIds as $templateId) {
            $templateId = trim($templateId);
            
            try {
                $template = NotificationTemplate::findOrFail($templateId);
                $this->line("Testing Template {$templateId}: {$template->name} ({$template->act})");
                
                // Create test user
                $testUser = (object) [
                    'id' => 1,
                    'fullname' => 'Test User',
                    'username' => 'testuser',
                    'email' => $testEmail,
                    'referral_code' => 'REF123456',
                    'created_at' => now()
                ];

                // Get comprehensive shortcodes
                $shortcodes = \App\Services\ShortcodeService::getShortcodes($testUser, [
                    'amount' => '1000.00',
                    'currency' => 'USD',
                    'new_balance' => '5000.00',
                    'transaction_id' => 'TXN' . time() . $templateId,
                    'mt5_login' => '12345678',
                    'ib_type' => 'Master IB',
                    'commission_rate' => '50%',
                    'approval_date' => now()->format('Y-m-d H:i:s'),
                    'rejection_reason' => 'Additional documentation required',
                    'template_id' => $templateId
                ]);

                // Send test email
                notify($testUser, $template->act, $shortcodes, ['email'], false);
                
                $this->info("  ✅ SUCCESS: Email sent successfully");
                
                // Small delay
                sleep(1);
                
            } catch (\Exception $e) {
                $this->error("  ❌ FAILED: {$e->getMessage()}");
                $this->line("  📋 Stack trace: " . $e->getTraceAsString());
            }
            
            $this->line('');
        }

        $this->info('🎉 Testing completed! Check your email inbox.');
        return 0;
    }
}
