# Multi-Level IB System - Quick Reference Guide

## 🚀 Quick Start

### Commission Rates
- **Master IB**: 50%
- **Sub-IB**: 30% 
- **Level 3 Sub-IB**: 20%

### Key URLs
- **Admin IB Management**: `/admin/ib/manage`
- **Admin Commissions**: `/admin/commissions`
- **User Partnership**: `/user/partnership/network`
- **IB Application**: `/user/ib/apply`

---

## 📊 Commission Calculation Quick Examples

### Master IB → Client
```
Client Profit: $10
Master IB Commission: $10 × 50% = $5.00
```

### Master IB → Sub-IB → Client  
```
Client Profit: $10
Sub-IB Commission: $10 × 30% = $3.00
Master IB Commission: $10 × 50% = $5.00
Total: $8.00
```

### 3-Level Hierarchy
```
Client Profit: $10
Level 3 Sub-IB: $10 × 20% = $2.00
Sub-IB: $10 × 30% = $3.00
Master IB: $10 × 50% = $5.00
Total: $10.00
```

---

## 🔧 Admin Quick Actions

### Approve IB Application
1. Go to `/admin/ib/pending`
2. Click "Review" on application
3. Check KYC status and documents
4. Click "Approve" or "Reject"
5. Add admin comments
6. Submit decision

### Approve Commission Batch
1. Go to `/admin/commissions/pending`
2. Select commissions to approve
3. Click "Approve Selected"
4. Confirm MT5 balance updates
5. Commissions automatically marked as 'paid'

### Check IB Performance
1. Go to `/admin/ib/manage`
2. Click on IB name for details
3. View "Partnership" tab for network
4. Check commission history and earnings

---

## 🛠️ Technical Quick Commands

### Commission Processing
```php
// Process commission for specific trade
$service = new MultiLevelIbCommissionService();
$result = $service->processMultiLevelCommission($tradeData);

// Force reprocess commission
php artisan commission:reprocess --deal-id=12345
```

### MT5 Balance Update
```python
# Add balance to MT5 account
python mt5manager.py add_balance --login 878046 --amount 5.00 --comment "Commission"

# Get current balance
python mt5manager.py get_balance --login 878046
```

### Database Queries
```sql
-- Check IB hierarchy
SELECT id, firstname, lastname, ib_type, ref_by, ib_parent_id 
FROM users WHERE ib_status = 1;

-- Check commission records
SELECT * FROM ib_commissions 
WHERE to_ib_user_id = 10921 
ORDER BY created_at DESC;

-- Check MT5 balances
SELECT mt5_login, mt5_balance, commission_earnings 
FROM users WHERE ib_status = 1;
```

---

## 🚨 Troubleshooting Quick Fixes

### Commission Not Processing
```bash
# Check logs
tail -f storage/logs/commission.log

# Verify IB hierarchy
php artisan tinker
>>> User::find(10921)->getAllReferrals()

# Reprocess failed commission
php artisan commission:reprocess --deal-id=FAILED_DEAL_ID
```

### MT5 Balance Update Failed
```bash
# Test Python integration
python python/mt5manager.py get_balance --login 878046

# Check environment variables
php artisan tinker
>>> env('PYTHON_EXE')
>>> env('PYTHON_SCRIPT')

# Manual balance update
php artisan mt5:update-balance --user-id=10921 --amount=5.00
```

### Duplicate Commission Error
```sql
-- Find duplicates
SELECT trade_id, COUNT(*) as count 
FROM ib_commissions 
GROUP BY trade_id 
HAVING count > 1;

-- Remove duplicates (keep latest)
DELETE c1 FROM ib_commissions c1
INNER JOIN ib_commissions c2 
WHERE c1.id < c2.id AND c1.trade_id = c2.trade_id;
```

---

## 📱 User Interface Quick Guide

### Master IB Dashboard Actions
- **View Network**: See all referred clients and Sub-IBs
- **Approve Sub-IB**: Review and approve Sub-IB applications
- **Generate Referral Link**: Create new referral links
- **Check Commissions**: View real-time commission earnings
- **Download Reports**: Export commission and client reports

### Sub-IB Dashboard Actions  
- **View Parent IB**: See referring Master IB details
- **Manage Clients**: View and manage own referred clients
- **Track Commissions**: Monitor commission earnings
- **Invite Clients**: Generate own referral links
- **Apply for Upgrade**: Request higher IB level

### Admin Dashboard Actions
- **Review Applications**: Approve/reject IB applications
- **Manage Commissions**: Process commission payments
- **Monitor Performance**: Track IB and system performance
- **Configure Rates**: Set commission rates and rules
- **Generate Reports**: Create compliance and financial reports

---

## 🔐 Security Checklist

### Daily Security Tasks
- [ ] Review commission processing logs
- [ ] Check for unusual commission patterns
- [ ] Verify MT5 balance synchronization
- [ ] Monitor failed login attempts
- [ ] Review admin activity logs

### Weekly Security Tasks
- [ ] Reconcile CRM and MT5 balances
- [ ] Review IB application approvals
- [ ] Check system error logs
- [ ] Verify backup integrity
- [ ] Update security patches

### Monthly Security Tasks
- [ ] Generate compliance reports
- [ ] Review user access permissions
- [ ] Conduct security audit
- [ ] Update documentation
- [ ] Review and update security policies

---

## 📞 Support Contacts

### Technical Issues
- **Commission Processing**: Development Team
- **MT5 Integration**: System Administrator  
- **Database Issues**: Database Administrator
- **UI/UX Issues**: Frontend Team

### Business Issues
- **IB Applications**: Business Operations
- **Commission Disputes**: Finance Team
- **Compliance**: Compliance Officer
- **General Support**: Customer Service

---

## 📚 Additional Resources

### Documentation Files
- `MULTI_LEVEL_IB_SYSTEM_COMPLETE_README.md` - Complete system documentation
- `IB_System_README.md` - Basic IB system guide
- `COMPLETE_IB_TESTING_PLAN.md` - Testing procedures
- `MULTI_LEVEL_IB_SYSTEM_README.md` - Multi-level specific guide

### Configuration Files
- `config/ib.php` - IB system configuration
- `.env` - Environment variables
- `database/migrations/` - Database schema
- `python/mt5manager.py` - MT5 integration script

### Log Files
- `storage/logs/commission.log` - Commission processing logs
- `storage/logs/mt5_integration.log` - MT5 balance update logs
- `storage/logs/ib_system.log` - General IB system logs
- `storage/logs/laravel.log` - Application logs

---

**Quick Reference Version**: 2.0  
**Last Updated**: June 2025  
**For Complete Documentation**: See `MULTI_LEVEL_IB_SYSTEM_COMPLETE_README.md`
