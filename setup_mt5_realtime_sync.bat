@echo off
REM MT5 Real-time Sync Setup for Windows Server 2022 / Plesk
REM This script sets up automated 1-minute MT5 database synchronization

echo ========================================
echo MT5 REAL-TIME SYNC SETUP FOR PLESK
echo ========================================
echo.

REM Set variables for Plesk environment - Updated for PHP 8.4
set PHP_PATH="C:\Program Files (x86)\Plesk\Additional\PleskPHP84\php.exe"
set PROJECT_PATH="C:\Inetpub\vhosts\mybrokerforex.com\mbf.mybrokerforex.com"
set ARTISAN_PATH="%PROJECT_PATH%\artisan"
set LOG_PATH="%PROJECT_PATH%\storage\logs\mt5_sync.log"

echo PHP Path: %PHP_PATH%
echo Project Path: %PROJECT_PATH%
echo Artisan Path: %ARTISAN_PATH%
echo Log Path: %LOG_PATH%
echo.

REM Test PHP and Laravel setup
echo Testing PHP and Laravel setup...
%PHP_PATH% %ARTISAN_PATH% --version
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: PHP or Laravel not working correctly
    pause
    exit /b 1
)
echo ✅ PHP and Laravel working correctly
echo.

REM Test MT5 sync command
echo Testing MT5 sync command...
%PHP_PATH% %ARTISAN_PATH% mt5:sync-users --dry-run --limit=10 --force
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: MT5 sync command failed
    pause
    exit /b 1
)
echo ✅ MT5 sync command working correctly
echo.

REM Create the sync script
echo Creating MT5 sync script...
set SYNC_SCRIPT="%PROJECT_PATH%\mt5_sync_runner.bat"

echo @echo off > %SYNC_SCRIPT%
echo REM MT5 Real-time Sync Runner >> %SYNC_SCRIPT%
echo REM Runs every minute via Windows Task Scheduler >> %SYNC_SCRIPT%
echo. >> %SYNC_SCRIPT%
echo set PHP_PATH="C:\Program Files (x86)\Plesk\Additional\PleskPHP84\php.exe" >> %SYNC_SCRIPT%
echo set PROJECT_PATH="C:\Inetpub\vhosts\mybrokerforex.com\mbf.mybrokerforex.com" >> %SYNC_SCRIPT%
echo set ARTISAN_PATH="%%PROJECT_PATH%%\artisan" >> %SYNC_SCRIPT%
echo set LOG_PATH="%%PROJECT_PATH%%\storage\logs\mt5_sync.log" >> %SYNC_SCRIPT%
echo. >> %SYNC_SCRIPT%
echo REM Change to project directory >> %SYNC_SCRIPT%
echo cd /d %%PROJECT_PATH%% >> %SYNC_SCRIPT%
echo. >> %SYNC_SCRIPT%
echo REM Run MT5 sync with enhanced settings >> %SYNC_SCRIPT%
echo %%PHP_PATH%% %%ARTISAN_PATH%% mt5:sync-users --fast --force --limit=1000 ^>^> %%LOG_PATH%% 2^>^&1 >> %SYNC_SCRIPT%
echo. >> %SYNC_SCRIPT%
echo REM Log completion >> %SYNC_SCRIPT%
echo echo [%%DATE%% %%TIME%%] MT5 sync completed ^>^> %%LOG_PATH%% >> %SYNC_SCRIPT%

echo ✅ Sync script created: %SYNC_SCRIPT%
echo.

REM Create Windows Task Scheduler XML
echo Creating Windows Task Scheduler configuration...
set TASK_XML="%PROJECT_PATH%\MT5_RealTime_Sync_Task.xml"

echo ^<?xml version="1.0" encoding="UTF-16"?^> > %TASK_XML%
echo ^<Task version="1.2" xmlns="http://schemas.microsoft.com/windows/2004/02/mit/task"^> >> %TASK_XML%
echo   ^<RegistrationInfo^> >> %TASK_XML%
echo     ^<Description^>MT5 Real-time Database Synchronization - Runs every minute^</Description^> >> %TASK_XML%
echo     ^<Author^>MBFX System^</Author^> >> %TASK_XML%
echo   ^</RegistrationInfo^> >> %TASK_XML%
echo   ^<Triggers^> >> %TASK_XML%
echo     ^<TimeTrigger^> >> %TASK_XML%
echo       ^<Repetition^> >> %TASK_XML%
echo         ^<Interval^>PT1M^</Interval^> >> %TASK_XML%
echo         ^<StopAtDurationEnd^>false^</StopAtDurationEnd^> >> %TASK_XML%
echo       ^</Repetition^> >> %TASK_XML%
echo       ^<StartBoundary^>2025-06-15T00:00:00^</StartBoundary^> >> %TASK_XML%
echo       ^<Enabled^>true^</Enabled^> >> %TASK_XML%
echo     ^</TimeTrigger^> >> %TASK_XML%
echo   ^</Triggers^> >> %TASK_XML%
echo   ^<Principals^> >> %TASK_XML%
echo     ^<Principal id="Author"^> >> %TASK_XML%
echo       ^<UserId^>S-1-5-18^</UserId^> >> %TASK_XML%
echo       ^<RunLevel^>HighestAvailable^</RunLevel^> >> %TASK_XML%
echo     ^</Principal^> >> %TASK_XML%
echo   ^</Principals^> >> %TASK_XML%
echo   ^<Settings^> >> %TASK_XML%
echo     ^<MultipleInstancesPolicy^>IgnoreNew^</MultipleInstancesPolicy^> >> %TASK_XML%
echo     ^<DisallowStartIfOnBatteries^>false^</DisallowStartIfOnBatteries^> >> %TASK_XML%
echo     ^<StopIfGoingOnBatteries^>false^</StopIfGoingOnBatteries^> >> %TASK_XML%
echo     ^<AllowHardTerminate^>true^</AllowHardTerminate^> >> %TASK_XML%
echo     ^<StartWhenAvailable^>true^</StartWhenAvailable^> >> %TASK_XML%
echo     ^<RunOnlyIfNetworkAvailable^>false^</RunOnlyIfNetworkAvailable^> >> %TASK_XML%
echo     ^<IdleSettings^> >> %TASK_XML%
echo       ^<StopOnIdleEnd^>false^</StopOnIdleEnd^> >> %TASK_XML%
echo       ^<RestartOnIdle^>false^</RestartOnIdle^> >> %TASK_XML%
echo     ^</IdleSettings^> >> %TASK_XML%
echo     ^<AllowStartOnDemand^>true^</AllowStartOnDemand^> >> %TASK_XML%
echo     ^<Enabled^>true^</Enabled^> >> %TASK_XML%
echo     ^<Hidden^>false^</Hidden^> >> %TASK_XML%
echo     ^<RunOnlyIfIdle^>false^</RunOnlyIfIdle^> >> %TASK_XML%
echo     ^<WakeToRun^>false^</WakeToRun^> >> %TASK_XML%
echo     ^<ExecutionTimeLimit^>PT5M^</ExecutionTimeLimit^> >> %TASK_XML%
echo     ^<Priority^>7^</Priority^> >> %TASK_XML%
echo   ^</Settings^> >> %TASK_XML%
echo   ^<Actions Context="Author"^> >> %TASK_XML%
echo     ^<Exec^> >> %TASK_XML%
echo       ^<Command^>%PROJECT_PATH%\mt5_sync_runner.bat^</Command^> >> %TASK_XML%
echo       ^<WorkingDirectory^>%PROJECT_PATH%^</WorkingDirectory^> >> %TASK_XML%
echo     ^</Exec^> >> %TASK_XML%
echo   ^</Actions^> >> %TASK_XML%
echo ^</Task^> >> %TASK_XML%

echo ✅ Task XML created: %TASK_XML%
echo.

REM Copy the pre-created files to project directory
echo Copying pre-created sync files...
if exist "MT5_RealTime_Sync_Task.xml" (
    copy "MT5_RealTime_Sync_Task.xml" %TASK_XML%
    echo ✅ Copied MT5_RealTime_Sync_Task.xml to project directory
) else (
    echo ⚠️  MT5_RealTime_Sync_Task.xml not found in current directory
)

if exist "mt5_sync_runner.bat" (
    copy "mt5_sync_runner.bat" %SYNC_SCRIPT%
    echo ✅ Copied mt5_sync_runner.bat to project directory
) else (
    echo ⚠️  mt5_sync_runner.bat not found in current directory
)
echo.

REM Instructions for manual setup
echo ========================================
echo MANUAL SETUP INSTRUCTIONS
echo ========================================
echo.
echo 1. IMPORT WINDOWS TASK:
echo    - Open Task Scheduler (taskschd.msc)
echo    - Right-click "Task Scheduler Library"
echo    - Select "Import Task..."
echo    - Browse to: %TASK_XML%
echo    - Click "OK" to import
echo.
echo 2. VERIFY TASK SETTINGS:
echo    - Task Name: MT5_RealTime_Sync
echo    - Trigger: Every 1 minute
echo    - Action: Run %SYNC_SCRIPT%
echo    - User: SYSTEM (highest privileges)
echo.
echo 3. TEST THE TASK:
echo    - Right-click the imported task
echo    - Select "Run"
echo    - Check logs: %LOG_PATH%
echo.
echo 4. MONITOR SYNC:
echo    - Check Laravel logs: storage/logs/laravel.log
echo    - Check sync logs: storage/logs/mt5_sync.log
echo    - Monitor admin dashboard for sync statistics
echo.
echo ========================================
echo ALTERNATIVE COMMAND LINE SETUP
echo ========================================
echo.
echo Run this command as Administrator to create the task:
echo.
echo schtasks /create /tn "MT5_RealTime_Sync" /tr "%SYNC_SCRIPT%" /sc minute /mo 1 /ru SYSTEM /rl HIGHEST /f
echo.
echo ========================================
echo TESTING COMMANDS
echo ========================================
echo.
echo Manual sync test:
echo %PHP_PATH% %ARTISAN_PATH% mt5:sync-users --fast --force --limit=1000
echo.
echo Dry run test:
echo %PHP_PATH% %ARTISAN_PATH% mt5:sync-users --dry-run --limit=100
echo.
echo Check sync status:
echo %PHP_PATH% %ARTISAN_PATH% tinker --execute="echo 'Last sync: ' . \Cache::get('mt5_sync_stats.last_sync', 'Never');"
echo.
echo ========================================
echo SETUP COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo Next steps:
echo 1. Import the task using Task Scheduler
echo 2. Test the task manually
echo 3. Monitor logs for successful sync
echo 4. Verify real-time sync in admin dashboard
echo.
pause
