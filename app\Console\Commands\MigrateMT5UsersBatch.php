<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\MT5UserMigrationService;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MigrateMT5UsersBatch extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mt5:migrate-users-batch
                            {--batch-size=500 : Number of users to process per batch}
                            {--clear : Clear existing users table before migration}
                            {--start-from=0 : Start from specific offset}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate MT5 users in batches to avoid memory issues';

    /**
     * Migration service
     */
    protected $migrationService;

    /**
     * Create a new command instance.
     */
    public function __construct(MT5UserMigrationService $migrationService)
    {
        parent::__construct();
        $this->migrationService = $migrationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Starting Batch MT5 Users Migration');
        $this->info('=====================================');

        $batchSize = (int) $this->option('batch-size');
        $clear = $this->option('clear');
        $startFrom = (int) $this->option('start-from');

        if ($clear && $startFrom === 0) {
            $this->warn('⚠️  WARNING: This will DELETE ALL existing users!');
            if (!$this->confirm('Are you sure you want to continue?')) {
                $this->info('Migration cancelled.');
                return 0;
            }
            $this->clearDatabase();
        }

        try {
            // Get total count
            $totalUsers = DB::connection('mbf-dbmt5')->table('mt5_users')->count();
            $this->info("📊 Total MT5 users to migrate: {$totalUsers}");
            $this->info("📦 Batch size: {$batchSize}");
            $this->info("🎯 Starting from offset: {$startFrom}");

            $totalMigrated = 0;
            $totalUpdated = 0;
            $totalErrors = 0;
            $offset = $startFrom;

            $progressBar = $this->output->createProgressBar($totalUsers - $startFrom);
            $progressBar->start();

            while ($offset < $totalUsers) {
                $this->info("\n📦 Processing batch: " . ($offset + 1) . " to " . min($offset + $batchSize, $totalUsers));
                
                $result = $this->processBatch($offset, $batchSize);
                
                $totalMigrated += $result['migrated'];
                $totalUpdated += $result['updated'];
                $totalErrors += $result['errors'];
                
                $progressBar->advance($result['processed']);
                
                $offset += $batchSize;
                
                // Memory cleanup
                gc_collect_cycles();
                
                // Small delay to prevent overwhelming the database
                usleep(100000); // 0.1 second
            }

            $progressBar->finish();

            $this->info("\n\n🎉 Migration Completed!");
            $this->info("========================");
            $this->info("✅ Total Processed: " . ($totalUsers - $startFrom));
            $this->info("🆕 New Users Created: {$totalMigrated}");
            $this->info("🔄 Existing Users Updated: {$totalUpdated}");
            $this->info("❌ Errors: {$totalErrors}");

            // Setup referral relationships
            $this->info("\n🔗 Setting up referral relationships...");
            $referralCount = $this->migrationService->setupReferralRelationships();
            $this->info("✅ Set up {$referralCount} referral relationships");

            return 0;

        } catch (\Exception $e) {
            $this->error('❌ Error during migration: ' . $e->getMessage());
            Log::error('MT5 batch migration error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
    }

    /**
     * Process a single batch of users
     */
    private function processBatch($offset, $batchSize)
    {
        try {
            $mt5Users = DB::connection('mbf-dbmt5')
                ->table('mt5_users')
                ->select('*')
                ->orderBy('Login')
                ->offset($offset)
                ->limit($batchSize)
                ->get();

            $migrated = 0;
            $updated = 0;
            $errors = 0;

            foreach ($mt5Users as $mt5User) {
                try {
                    if (empty($mt5User->Email)) {
                        continue; // Skip users without email
                    }

                    $result = $this->migrateSingleUser($mt5User);
                    if ($result === 'created') {
                        $migrated++;
                    } elseif ($result === 'updated') {
                        $updated++;
                    }
                } catch (\Exception $e) {
                    $errors++;
                    $this->error("Failed to migrate MT5 user {$mt5User->Login}: " . $e->getMessage());
                    Log::error("Failed to migrate MT5 user {$mt5User->Login}", [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                        'user_data' => [
                            'Login' => $mt5User->Login,
                            'Email' => $mt5User->Email,
                            'FirstName' => $mt5User->FirstName,
                            'LastName' => $mt5User->LastName,
                            'Group' => $mt5User->Group
                        ]
                    ]);
                }
            }

            return [
                'processed' => $mt5Users->count(),
                'migrated' => $migrated,
                'updated' => $updated,
                'errors' => $errors
            ];

        } catch (\Exception $e) {
            Log::error("Batch processing error at offset {$offset}", [
                'error' => $e->getMessage()
            ]);
            
            return [
                'processed' => 0,
                'migrated' => 0,
                'updated' => 0,
                'errors' => $batchSize
            ];
        }
    }

    /**
     * Migrate single user
     */
    private function migrateSingleUser($mt5User)
    {
        // Check if user already exists
        $existingUser = User::where('email', $mt5User->Email)->first();

        if ($existingUser) {
            // Update existing user with MT5 data
            $this->updateUserWithMT5Data($existingUser, $mt5User);
            return 'updated';
        } else {
            // Create new user from MT5 data
            $this->createUserFromMT5Data($mt5User);
            return 'created';
        }
    }

    /**
     * Create user from MT5 data
     */
    private function createUserFromMT5Data($mt5User)
    {
        // Use the migration service method
        $reflection = new \ReflectionClass($this->migrationService);
        $method = $reflection->getMethod('createUserFromMT5Data');
        $method->setAccessible(true);
        
        return $method->invoke($this->migrationService, $mt5User);
    }

    /**
     * Update user with MT5 data
     */
    private function updateUserWithMT5Data($user, $mt5User)
    {
        // Use the migration service method
        $reflection = new \ReflectionClass($this->migrationService);
        $method = $reflection->getMethod('updateUserWithMT5Data');
        $method->setAccessible(true);
        
        return $method->invoke($this->migrationService, $user, $mt5User);
    }

    /**
     * Clear database
     */
    private function clearDatabase()
    {
        $this->info("🗑️  Clearing existing database...");
        
        // Disable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        
        // Clear related tables first
        DB::table('ib_commissions')->truncate();
        DB::table('transactions')->truncate();
        DB::table('deposits')->truncate();
        DB::table('withdrawals')->truncate();
        
        // Clear users table
        DB::table('users')->truncate();
        
        // Re-enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        
        $this->info("✅ Database cleared successfully");
    }
}
