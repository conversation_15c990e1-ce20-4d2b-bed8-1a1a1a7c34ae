<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use Illuminate\Http\Request;

echo "=== TESTING REAL DIRECT REFERRAL ISSUES ===\n\n";

// Test 1: Check if the AJAX endpoint actually works
echo "🔍 TEST 1: AJAX MT5 Accounts Endpoint\n";
echo "=====================================\n";

try {
    $request = new Request();
    $request->headers->set('X-Requested-With', 'XMLHttpRequest');
    
    $controller = new \App\Http\Controllers\Admin\ManageUsersController();
    $response = $controller->getAllMT5Accounts($request);
    $data = json_decode($response->getContent(), true);
    
    if ($data['success']) {
        echo "✅ AJAX endpoint working: " . count($data['accounts']) . " MT5 accounts\n";
        echo "✅ Sample accounts:\n";
        foreach(array_slice($data['accounts'], 0, 3) as $account) {
            echo "  - MT5: {$account['mt5_login']} (User: {$account['user_name']})\n";
        }
    } else {
        echo "❌ AJAX endpoint failed: " . $data['message'] . "\n";
    }
} catch (Exception $e) {
    echo "❌ AJAX endpoint error: " . $e->getMessage() . "\n";
}

// Test 2: Check form submission validation
echo "\n🔍 TEST 2: Form Submission Validation\n";
echo "=====================================\n";

// Simulate form submission
$testUser = User::find(11178); // sufyan aslam
$potentialReferral = User::where('id', '!=', $testUser->id)->first();

echo "Test scenario:\n";
echo "  - Adding referral to: {$testUser->fullname} (ID: {$testUser->id})\n";
echo "  - Referral user: {$potentialReferral->fullname} (ID: {$potentialReferral->id})\n";

// Test the validation rules
$validationRules = [
    'referral_user' => 'required|exists:users,id',
    'assign_as_sub_ib' => 'nullable|boolean',
    'search_method' => 'nullable|in:user,mt5,email',
    'mt5_login' => 'nullable|string',
    'email_search' => 'nullable|email'
];

echo "\n✅ Controller validation rules:\n";
foreach($validationRules as $field => $rule) {
    echo "  - {$field}: {$rule}\n";
}

// Test if the referral user exists
$referralUserExists = User::where('id', $potentialReferral->id)->exists();
echo "\n✅ Referral user exists in database: " . ($referralUserExists ? 'YES' : 'NO') . "\n";

// Test 3: Check MT5 search functionality
echo "\n🔍 TEST 3: MT5 Search Functionality\n";
echo "===================================\n";

try {
    $searchRequest = new Request();
    $searchRequest->merge(['mt5_id' => '878046']);
    $searchRequest->headers->set('X-Requested-With', 'XMLHttpRequest');
    
    $searchResponse = $controller->searchByMT5($searchRequest);
    $searchData = json_decode($searchResponse->getContent(), true);
    
    if ($searchData['success']) {
        echo "✅ MT5 search working: " . count($searchData['users']) . " results\n";
        foreach($searchData['users'] as $user) {
            echo "  - Found: MT5 {$user['mt5_login']} (User: {$user['user_name']})\n";
        }
    } else {
        echo "❌ MT5 search failed\n";
    }
} catch (Exception $e) {
    echo "❌ MT5 search error: " . $e->getMessage() . "\n";
}

// Test 4: Check if routes are properly registered
echo "\n🔍 TEST 4: Route Registration Check\n";
echo "===================================\n";

$routes = [
    'admin.users.mt5.accounts.all' => 'GET admin/users/get-all-mt5-accounts',
    'admin.users.search.mt5' => 'POST admin/users/search-by-mt5',
    'admin.users.referral.add' => 'POST admin/users/detail/{id}/add-referral'
];

foreach($routes as $name => $path) {
    try {
        $url = route($name, ['id' => 11178]);
        echo "✅ Route '{$name}' exists: {$url}\n";
    } catch (Exception $e) {
        echo "❌ Route '{$name}' missing: " . $e->getMessage() . "\n";
    }
}

// Test 5: Check actual form data structure
echo "\n🔍 TEST 5: Form Data Structure\n";
echo "==============================\n";

echo "Expected form fields:\n";
echo "  - referral_user: (required) ID of user to add as referral\n";
echo "  - assign_as_sub_ib: (optional) boolean for Sub-IB assignment\n";
echo "  - search_method: (optional) user|mt5|email\n";
echo "  - _token: CSRF token\n";

echo "\nForm submission URL: " . route('admin.users.referral.add', $testUser->id) . "\n";

// Test 6: Check if there are any JavaScript conflicts
echo "\n🔍 TEST 6: Potential JavaScript Issues\n";
echo "======================================\n";

echo "Potential issues to check in browser:\n";
echo "1. Check browser console for JavaScript errors\n";
echo "2. Verify jQuery and Select2 are loaded\n";
echo "3. Check if CSRF token is properly set\n";
echo "4. Verify AJAX requests are being made\n";
echo "5. Check if form fields are properly populated\n";

echo "\n🎯 DEBUGGING STEPS:\n";
echo "===================\n";
echo "1. Open browser developer tools (F12)\n";
echo "2. Go to user detail page: https://localhost/mbf.mybrokerforex.com-********/admin/users/detail/11178\n";
echo "3. Click Direct Referrals tab → Add Referral button\n";
echo "4. Click 'MT5 Account' tab and check console for AJAX calls\n";
echo "5. Select a user and check console logs\n";
echo "6. Try to submit form and check console for errors\n";
echo "7. Check Network tab for failed AJAX requests\n";

echo "\n🔧 IMMEDIATE FIXES TO TRY:\n";
echo "===========================\n";
echo "1. Clear browser cache (Ctrl+F5)\n";
echo "2. Check if Select2 CSS/JS is loaded\n";
echo "3. Verify CSRF token in form\n";
echo "4. Check if routes are accessible\n";

echo "\n🚨 CRITICAL DEBUGGING REQUIRED:\n";
echo "================================\n";
echo "The issue might be:\n";
echo "1. Browser caching old JavaScript\n";
echo "2. CSRF token mismatch\n";
echo "3. AJAX endpoints not accessible\n";
echo "4. JavaScript library conflicts\n";
echo "5. Form field name mismatch\n";

echo "\nPlease check browser console and network tab for actual errors!\n";
