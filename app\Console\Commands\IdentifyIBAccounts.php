<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class IdentifyIBAccounts extends Command
{
    protected $signature = 'mt5:identify-ib-accounts 
                            {--dry-run : Show what would be done without making changes}
                            {--sync-commissions : Also sync commission data from MT5}
                            {--limit=1000 : Number of users to process}';

    protected $description = 'Identify and mark IB accounts based on MT5 groups and commission data';

    public function handle()
    {
        $dryRun = $this->option('dry-run');
        $syncCommissions = $this->option('sync-commissions');
        $limit = (int) $this->option('limit');

        $this->info('🔍 Starting IB Account Identification...');
        $this->info('=====================================');

        if ($dryRun) {
            $this->warn('🧪 DRY RUN MODE - No changes will be made');
        }

        try {
            // Define IB group patterns
            $ibGroupPatterns = [
                'real\\Affiliates' => 'affiliate',
                'real\\Multi-IB\\' => 'multi_ib',
                'real\\IB\\' => 'ib',
                'real\\Partner' => 'partner',
                '1:1' => 'one_to_one'
            ];

            $this->info("🎯 Processing up to {$limit} users for IB identification");

            $processed = 0;
            $identified = 0;
            $commissionsUpdated = 0;
            $errors = 0;

            // Process users in batches
            User::whereNotNull('mt5_login')
                ->whereNotNull('mt5_group')
                ->chunk(100, function ($users) use ($ibGroupPatterns, $dryRun, $syncCommissions, &$processed, &$identified, &$commissionsUpdated, &$errors) {
                    
                    foreach ($users as $user) {
                        try {
                            $wasIB = $user->is_ib_account;
                            $isIB = false;
                            $ibType = null;

                            // Check MT5 group for IB patterns
                            foreach ($ibGroupPatterns as $pattern => $type) {
                                if (strpos($user->mt5_group, $pattern) !== false) {
                                    $isIB = true;
                                    $ibType = $type;
                                    break;
                                }
                            }

                            // Check for commission earnings if not identified by group
                            if (!$isIB && $syncCommissions) {
                                $commissionEarnings = $this->getCommissionEarnings($user->mt5_login);
                                if ($commissionEarnings > 0) {
                                    $isIB = true;
                                    $ibType = 'commission_earner';
                                    
                                    if (!$dryRun) {
                                        $user->commission_earnings = $commissionEarnings;
                                        $user->last_commission_sync = now();
                                        $commissionsUpdated++;
                                    }
                                }
                            }

                            // Update IB status if changed
                            if ($isIB !== $wasIB || ($isIB && $user->ib_type !== $ibType)) {
                                if (!$dryRun) {
                                    $user->is_ib_account = $isIB;
                                    $user->ib_type = $ibType;
                                    $user->save();
                                }

                                if ($isIB && !$wasIB) {
                                    $identified++;
                                    $this->line("✅ Identified IB: {$user->email} (MT5: {$user->mt5_login}) - Type: {$ibType}");
                                }
                            }

                            $processed++;

                            if ($processed % 100 === 0) {
                                $this->info("📈 Processed: {$processed} | Identified: {$identified} | Errors: {$errors}");
                            }

                        } catch (\Exception $e) {
                            $errors++;
                            $this->error("❌ Error processing user {$user->id}: " . $e->getMessage());
                            Log::error('IB Identification Error', [
                                'user_id' => $user->id,
                                'error' => $e->getMessage()
                            ]);
                        }
                    }
                });

            // Summary
            $this->info('');
            $this->info('🎉 IB Identification Completed!');
            $this->table(['Metric', 'Count'], [
                ['Users Processed', $processed],
                ['IB Accounts Identified', $identified],
                ['Commission Data Updated', $commissionsUpdated],
                ['Errors', $errors]
            ]);

            // Show IB statistics
            $this->showIBStatistics();

            if (!$dryRun) {
                $this->info('✅ All changes have been saved to the database');
            } else {
                $this->warn('🧪 DRY RUN - No actual changes were made');
            }

        } catch (\Exception $e) {
            $this->error('💥 IB identification failed: ' . $e->getMessage());
            Log::error('IB Identification Command Failed', ['error' => $e->getMessage()]);
            return 1;
        }

        return 0;
    }

    /**
     * Get commission earnings for an MT5 account
     */
    private function getCommissionEarnings($mt5Login)
    {
        try {
            $commissions = DB::connection('mbf-dbmt5')
                ->table('mt5_deals_2025')
                ->where('Login', $mt5Login)
                ->where('Action', 18) // Commission action
                ->sum('Profit');

            return $commissions ?? 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Show IB statistics
     */
    private function showIBStatistics()
    {
        $this->info('');
        $this->info('📊 IB Account Statistics:');

        $ibStats = User::where('is_ib_account', true)
            ->select('ib_type', DB::raw('COUNT(*) as count'))
            ->groupBy('ib_type')
            ->orderBy('count', 'desc')
            ->get();

        $totalIBs = $ibStats->sum('count');
        $totalUsers = User::count();

        $this->table(['IB Type', 'Count', 'Percentage'], 
            $ibStats->map(function ($stat) use ($totalIBs) {
                $percentage = $totalIBs > 0 ? round(($stat->count / $totalIBs) * 100, 2) : 0;
                return [
                    $stat->ib_type ?? 'Unknown',
                    $stat->count,
                    $percentage . '%'
                ];
            })->toArray()
        );

        $this->info("Total IB Accounts: {$totalIBs} out of {$totalUsers} users (" . 
                   round(($totalIBs / $totalUsers) * 100, 2) . "%)");
    }
}
