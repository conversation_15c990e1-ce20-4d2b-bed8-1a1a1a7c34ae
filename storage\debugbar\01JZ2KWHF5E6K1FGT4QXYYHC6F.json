{"__meta": {"id": "01JZ2KWHF5E6K1FGT4QXYYHC6F", "datetime": "2025-07-01 09:08:02", "utime": **********.150481, "method": "GET", "uri": "/mbf.mybrokerforex.com-********/user/dashboard", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751360875.405102, "end": **********.150507, "duration": 6.745404958724976, "duration_str": "6.75s", "measures": [{"label": "Booting", "start": 1751360875.405102, "relative_start": 0, "end": **********.006641, "relative_end": **********.006641, "duration": 0.***************, "duration_str": "602ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.006662, "relative_start": 0.****************, "end": **********.15051, "relative_end": 3.0994415283203125e-06, "duration": 6.***************, "duration_str": "6.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.074654, "relative_start": 0.****************, "end": **********.08796, "relative_end": **********.08796, "duration": 0.013305902481079102, "duration_str": "13.31ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.614108, "relative_start": 4.***************, "end": **********.137737, "relative_end": **********.137737, "duration": 2.****************, "duration_str": "2.52s", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: templates.basic.user.dashboard", "start": **********.619433, "relative_start": 4.****************, "end": **********.619433, "relative_end": **********.619433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.layouts.master", "start": **********.580061, "relative_start": 5.***************, "end": **********.580061, "relative_end": **********.580061, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.seo", "start": **********.959276, "relative_start": 5.554173946380615, "end": **********.959276, "relative_end": **********.959276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.partials.user_sidebar", "start": 1751360881.105574, "relative_start": 5.700471878051758, "end": 1751360881.105574, "relative_end": 1751360881.105574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.basic.partials.user_topbar", "start": 1751360881.904098, "relative_start": 6.498996019363403, "end": 1751360881.904098, "relative_end": 1751360881.904098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.notify", "start": **********.077043, "relative_start": 6.671941041946411, "end": **********.077043, "relative_end": **********.077043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.plugins", "start": **********.080709, "relative_start": 6.675606966018677, "end": **********.080709, "relative_end": **********.080709, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 29325312, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.12", "Environment": "production", "Debug Mode": "Enabled", "URL": "localhost/mbf.mybrokerforex.com-********", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 7, "nb_templates": 7, "templates": [{"name": "templates.basic.user.dashboard", "param_count": null, "params": [], "start": **********.61926, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/templates/basic/user/dashboard.blade.phptemplates.basic.user.dashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fuser%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}}, {"name": "templates.basic.layouts.master", "param_count": null, "params": [], "start": **********.579913, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/templates/basic/layouts/master.blade.phptemplates.basic.layouts.master", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}}, {"name": "partials.seo", "param_count": null, "params": [], "start": **********.959128, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/partials/seo.blade.phppartials.seo", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fpartials%2Fseo.blade.php&line=1", "ajax": false, "filename": "seo.blade.php", "line": "?"}}, {"name": "templates.basic.partials.user_sidebar", "param_count": null, "params": [], "start": 1751360881.105419, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/templates/basic/partials/user_sidebar.blade.phptemplates.basic.partials.user_sidebar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Fuser_sidebar.blade.php&line=1", "ajax": false, "filename": "user_sidebar.blade.php", "line": "?"}}, {"name": "templates.basic.partials.user_topbar", "param_count": null, "params": [], "start": 1751360881.903942, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/templates/basic/partials/user_topbar.blade.phptemplates.basic.partials.user_topbar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Ftemplates%2Fbasic%2Fpartials%2Fuser_topbar.blade.php&line=1", "ajax": false, "filename": "user_topbar.blade.php", "line": "?"}}, {"name": "partials.notify", "param_count": null, "params": [], "start": **********.076886, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/partials/notify.blade.phppartials.notify", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fpartials%2Fnotify.blade.php&line=1", "ajax": false, "filename": "notify.blade.php", "line": "?"}}, {"name": "partials.plugins", "param_count": null, "params": [], "start": **********.080561, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/partials/plugins.blade.phppartials.plugins", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fresources%2Fviews%2Fpartials%2Fplugins.blade.php&line=1", "ajax": false, "filename": "plugins.blade.php", "line": "?"}}]}, "queries": {"count": 16, "nb_statements": 14, "nb_visible_statements": 16, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 2.9045399999999995, "accumulated_duration_str": "2.9s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "vendor/beyondcode/laravel-query-detector/src/QueryDetector.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetector.php", "line": 25}, {"index": 10, "namespace": null, "name": "vendor/beyondcode/laravel-query-detector/src/QueryDetectorMiddleware.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetectorMiddleware.php", "line": 30}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21}], "start": **********.072642, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "QueryDetector.php:25", "source": {"index": 9, "namespace": null, "name": "vendor/beyondcode/laravel-query-detector/src/QueryDetector.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetector.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fvendor%2Fbeyondcode%2Flaravel-query-detector%2Fsrc%2FQueryDetector.php&line=25", "ajax": false, "filename": "QueryDetector.php", "line": "25"}, "connection": "mbf-db", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 53630 limit 1", "type": "query", "params": [], "bindings": [53630], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.375793, "duration": 0.00698, "duration_str": "6.98ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mbf-db", "explain": null, "start_percent": 0, "width_percent": 0.24}, {"sql": "select `id`, `market_id`, `coin_id` from `coin_pairs` where exists (select * from `market_data` where `coin_pairs`.`id` = `market_data`.`pair_id`)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\User\\UserController.php", "line": 39}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.530571, "duration": 0.03356, "duration_str": "33.56ms", "memory": 0, "memory_str": null, "filename": "UserController.php:39", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\User\\UserController.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FUser%2FUserController.php&line=39", "ajax": false, "filename": "UserController.php", "line": "39"}, "connection": "mbf-db", "explain": null, "start_percent": 0.24, "width_percent": 1.155}, {"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 8, "namespace": null, "name": "app/Models/Mt5Users.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Models\\Mt5Users.php", "line": 56}, {"index": 9, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\User\\UserController.php", "line": 42}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.637409, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "Mt5Users.php:56", "source": {"index": 8, "namespace": null, "name": "app/Models/Mt5Users.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Models\\Mt5Users.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FModels%2FMt5Users.php&line=56", "ajax": false, "filename": "Mt5Users.php", "line": "56"}, "connection": "mbf-dbmt5", "explain": null, "start_percent": 1.396, "width_percent": 0}, {"sql": "select * from `mt5_users` where `Email` = '<EMAIL>' and (`Group` like '%real%' or `Group` like '%Real%') limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>", "%real%", "%Real%"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Mt5Users.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Models\\Mt5Users.php", "line": 63}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\User\\UserController.php", "line": 42}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.637881, "duration": 2.77075, "duration_str": "2.77s", "memory": 0, "memory_str": null, "filename": "Mt5Users.php:63", "source": {"index": 14, "namespace": null, "name": "app/Models/Mt5Users.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Models\\Mt5Users.php", "line": 63}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FModels%2FMt5Users.php&line=63", "ajax": false, "filename": "Mt5Users.php", "line": "63"}, "connection": "mbf-dbmt5", "explain": null, "start_percent": 1.396, "width_percent": 95.394}, {"sql": "select count(*) as aggregate from `orders` where `user_id` = 53630 and `status` = 0", "type": "query", "params": [], "bindings": [53630, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\User\\UserController.php", "line": 87}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4156122, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "UserController.php:87", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\User\\UserController.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FUser%2FUserController.php&line=87", "ajax": false, "filename": "UserController.php", "line": "87"}, "connection": "mbf-db", "explain": null, "start_percent": 96.79, "width_percent": 0.057}, {"sql": "select count(*) as aggregate from `orders` where `user_id` = 53630 and `status` = 1", "type": "query", "params": [], "bindings": [53630, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\User\\UserController.php", "line": 88}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.42254, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "UserController.php:88", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\User\\UserController.php", "line": 88}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FUser%2FUserController.php&line=88", "ajax": false, "filename": "UserController.php", "line": "88"}, "connection": "mbf-db", "explain": null, "start_percent": 96.847, "width_percent": 0.027}, {"sql": "select count(*) as aggregate from `orders` where `user_id` = 53630 and `status` = 9", "type": "query", "params": [], "bindings": [53630, 9], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\User\\UserController.php", "line": 89}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.428653, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "UserController.php:89", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\User\\UserController.php", "line": 89}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FUser%2FUserController.php&line=89", "ajax": false, "filename": "UserController.php", "line": "89"}, "connection": "mbf-db", "explain": null, "start_percent": 96.874, "width_percent": 0.028}, {"sql": "select count(*) as aggregate from `trades` where `trader_id` = 53630", "type": "query", "params": [], "bindings": [53630], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\User\\UserController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.435589, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "UserController.php:90", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\User\\UserController.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FUser%2FUserController.php&line=90", "ajax": false, "filename": "UserController.php", "line": "90"}, "connection": "mbf-db", "explain": null, "start_percent": 96.902, "width_percent": 0.025}, {"sql": "select * from `orders` where `user_id` = 53630 order by `id` desc limit 10", "type": "query", "params": [], "bindings": [53630], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\User\\UserController.php", "line": 92}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.441481, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "UserController.php:92", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\User\\UserController.php", "line": 92}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FUser%2FUserController.php&line=92", "ajax": false, "filename": "UserController.php", "line": "92"}, "connection": "mbf-db", "explain": null, "start_percent": 96.927, "width_percent": 0.03}, {"sql": "select * from `transactions` where `user_id` = 53630 order by `id` desc limit 10", "type": "query", "params": [], "bindings": [53630], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\User\\UserController.php", "line": 93}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.499258, "duration": 0.036789999999999996, "duration_str": "36.79ms", "memory": 0, "memory_str": null, "filename": "UserController.php:93", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\User\\UserController.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FUser%2FUserController.php&line=93", "ajax": false, "filename": "UserController.php", "line": "93"}, "connection": "mbf-db", "explain": null, "start_percent": 96.957, "width_percent": 1.267}, {"sql": "select * from `formsib` where `user_id` = 53630 limit 1", "type": "query", "params": [], "bindings": [53630], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\User\\UserController.php", "line": 95}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.5749881, "duration": 0.02943, "duration_str": "29.43ms", "memory": 0, "memory_str": null, "filename": "UserController.php:95", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/User/UserController.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Controllers\\User\\UserController.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FUser%2FUserController.php&line=95", "ajax": false, "filename": "UserController.php", "line": "95"}, "connection": "mbf-db", "explain": null, "start_percent": 98.223, "width_percent": 1.013}, {"sql": "select * from `frontends` where `tempname` = 'basic' and `data_keys` = 'kyc_content.content' order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["basic", "kyc_content.content"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Helpers\\helpers.php", "line": 328}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.5504901, "duration": 0.01557, "duration_str": "15.57ms", "memory": 0, "memory_str": null, "filename": "helpers.php:328", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Helpers\\helpers.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=328", "ajax": false, "filename": "helpers.php", "line": "328"}, "connection": "mbf-db", "explain": null, "start_percent": 99.237, "width_percent": 0.536}, {"sql": "select * from `frontends` where `data_keys` = 'seo.data' limit 1", "type": "query", "params": [], "bindings": ["seo.data"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 90}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}, {"index": 23, "namespace": "view", "name": "templates.basic.layouts.master", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\resources\\views/templates/basic/layouts/master.blade.php", "line": 8}], "start": **********.948166, "duration": 0.00481, "duration_str": "4.81ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:90", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Providers\\AppServiceProvider.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FProviders%2FAppServiceProvider.php&line=90", "ajax": false, "filename": "AppServiceProvider.php", "line": "90"}, "connection": "mbf-db", "explain": null, "start_percent": 99.773, "width_percent": 0.166}, {"sql": "select * from `extensions` where `act` = 'tawk-chat' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["tawk-chat", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Helpers\\helpers.php", "line": 105}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.125658, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "helpers.php:105", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Helpers\\helpers.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=105", "ajax": false, "filename": "helpers.php", "line": "105"}, "connection": "mbf-db", "explain": null, "start_percent": 99.938, "width_percent": 0.034}, {"sql": "select * from `extensions` where `act` = 'google-analytics' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["google-analytics", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Helpers\\helpers.php", "line": 105}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.131673, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "helpers.php:105", "source": {"index": 16, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "C:\\xampp\\htdocs\\mbf.mybrokerforex.com-********\\app\\Http\\Helpers\\helpers.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=105", "ajax": false, "filename": "helpers.php", "line": "105"}, "connection": "mbf-db", "explain": null, "start_percent": 99.972, "width_percent": 0.028}]}, "models": {"data": {"App\\Models\\Frontend": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FModels%2FFrontend.php&line=1", "ajax": false, "filename": "Frontend.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://localhost/mbf.mybrokerforex.com-********/user/dashboard", "action_name": "user.home", "controller_action": "App\\Http\\Controllers\\User\\UserController@home", "uri": "GET user/dashboard", "controller": "App\\Http\\Controllers\\User\\UserController@home<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FUser%2FUserController.php&line=32\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\User", "prefix": "/user", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fmbf.mybrokerforex.com-********%2Fapp%2FHttp%2FControllers%2FUser%2FUserController.php&line=32\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/User/UserController.php:32-102</a>", "middleware": "web, maintenance, auth, check.status, registration.complete", "duration": "6.75s", "peak_memory": "30MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1487345642 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1487345642\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1993354366 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1993354366\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-460053556 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"60 characters\">https://localhost/mbf.mybrokerforex.com-********/admin/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">en-US,en;q=0.9,ms-MY;q=0.8,ms;q=0.7,zh-CN;q=0.6,zh;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InFOOXEwUk96RXU3ckdPak9DNjBzNXc9PSIsInZhbHVlIjoiREUrRlZxQTFqdWpWejdwWU9nMWFjaXRRZDJ5SzQzSStKZ29aUlhaT1lCK3RFcXIzNi9WLzVCWS9ldTBWeERBaURkOUNBSkRxVVhHTHA0RW5TaVNIWWduM3BNbGhlUXZac3JBTGdtYldSckNtSDZPT2NURFNHWVU4S1Y3d0g4c24iLCJtYWMiOiJiMzA1OTY3Y2RmYzIxMzkyNjI5MzBiOTAzOGZhYTNkNWVmNTFmMTIxMWRjNzkzMmZiODFlMzI3MTI1ZTNmMTQzIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InNuQlZ4Uk91Y1NlWDJtajM1MUNhTmc9PSIsInZhbHVlIjoiV253b0FBenRvMm9obVFyZUkxZDFHVVFvM08xemtRRTJnQ2tuMmdEOStWd2hSVzZueFVqRVZCbEVYeFFYbGdpbEtiT2Q4bmJ4VzJLTHduaHFwUW5JRm1BMVZBanVjdTNJWEVRQW9yclZQYVlCa2gzTTJOMVNMT3VHWEdzdFBUdVEiLCJtYWMiOiIxZTRhYjVmOGIxNTJhZDExZDA2MzM2MTNiNWNkMzA0MjkwZGY2NmZjNjAwYTllZjdhNDAwOGFmOTQ0YjJlZDRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-460053556\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1456690863 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6wydTXTQETk9uM050OalbDmHEZPTqQskjidNxWTA</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pKwSOwBxhh7a0b12K8rA1re0Q1SyO815L9Vl6qQN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1456690863\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1644463386 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 09:07:59 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1644463386\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1695598269 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6wydTXTQETk9uM050OalbDmHEZPTqQskjidNxWTA</span>\"\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"72 characters\">https://localhost/mbf.mybrokerforex.com-********/admin/users/login/53630</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>flasher::envelopes</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01JZ2KWATTFQPJKWS3MRFAQ67V</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>53630</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1695598269\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://localhost/mbf.mybrokerforex.com-********/user/dashboard", "action_name": "user.home", "controller_action": "App\\Http\\Controllers\\User\\UserController@home"}, "badge": null}}