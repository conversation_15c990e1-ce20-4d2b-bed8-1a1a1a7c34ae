<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use Carbon\Carbon;

class FixRegistrationDates extends Command
{
    protected $signature = 'fix:registration-dates {--limit=1000 : Number of users to process} {--dry-run : Show what would be changed without making changes}';
    protected $description = 'Fix user registration dates to use original MT5 registration dates instead of sync dates';

    public function handle()
    {
        $limit = $this->option('limit');
        $dryRun = $this->option('dry-run');
        
        $this->info('🔧 FIXING USER REGISTRATION DATES');
        $this->info('=================================');
        
        if ($dryRun) {
            $this->warn('🧪 DRY RUN MODE - No changes will be made');
        }
        
        // Find users where created_at doesn't match mt5_registration
        $usersToFix = User::whereNotNull('mt5_registration')
                         ->whereNotNull('mt5_login')
                         ->where(function($query) {
                             // Find users where created_at is significantly different from mt5_registration
                             $query->whereRaw('ABS(TIMESTAMPDIFF(DAY, created_at, mt5_registration)) > 1')
                                   ->orWhereNull('created_at')
                                   ->orWhere('created_at', '0000-00-00 00:00:00');
                         })
                         ->limit($limit)
                         ->get(['id', 'email', 'created_at', 'mt5_registration', 'mt5_login']);

        $this->info("📊 Found {$usersToFix->count()} users with incorrect registration dates");
        
        if ($usersToFix->isEmpty()) {
            $this->info('✅ No users need registration date fixes');
            return;
        }

        $this->info('');
        $this->info('📋 Sample users to fix:');
        $this->table(['Email', 'Current created_at', 'MT5 Registration', 'Difference'], 
            $usersToFix->take(5)->map(function($user) {
                $createdAt = $user->created_at ? Carbon::parse($user->created_at)->format('Y-m-d H:i:s') : 'NULL';
                $mt5Registration = $user->mt5_registration ? Carbon::parse($user->mt5_registration)->format('Y-m-d H:i:s') : 'NULL';
                
                $difference = 'N/A';
                if ($user->created_at && $user->mt5_registration) {
                    $diff = Carbon::parse($user->created_at)->diffInDays(Carbon::parse($user->mt5_registration));
                    $difference = "{$diff} days";
                }
                
                return [
                    'email' => $user->email,
                    'created_at' => $createdAt,
                    'mt5_registration' => $mt5Registration,
                    'difference' => $difference
                ];
            })->toArray()
        );

        if ($dryRun) {
            $this->info('');
            $this->warn('🧪 DRY RUN - Would fix ' . $usersToFix->count() . ' users');
            return;
        }

        $this->info('');
        $this->info('🔄 Fixing registration dates...');
        
        $fixed = 0;
        $errors = 0;
        
        foreach ($usersToFix as $user) {
            try {
                // Update created_at to match mt5_registration
                User::where('id', $user->id)
                    ->update([
                        'created_at' => $user->mt5_registration,
                        'updated_at' => now()
                    ]);
                
                $fixed++;
                
                if ($fixed % 100 == 0) {
                    $this->line("   ✅ Fixed {$fixed} users...");
                }
                
            } catch (\Exception $e) {
                $errors++;
                $this->error("   ❌ Error fixing user {$user->email}: " . $e->getMessage());
            }
        }

        $this->info('');
        $this->info('📊 REGISTRATION DATE FIX RESULTS:');
        $this->info('=================================');
        $this->line("✅ Successfully fixed: {$fixed} users");
        $this->line("❌ Errors: {$errors} users");
        
        if ($fixed > 0) {
            $this->info('✅ Registration dates have been corrected!');
            $this->info('   Users now show their original MT5 registration dates.');
        }

        // Verify the fix
        $this->info('');
        $this->info('🔍 VERIFICATION:');
        $this->info('===============');
        
        $verificationUsers = User::whereNotNull('mt5_registration')
                                ->whereNotNull('mt5_login')
                                ->limit(5)
                                ->get(['email', 'created_at', 'mt5_registration']);
        
        $this->table(['Email', 'created_at', 'mt5_registration', 'Match'], 
            $verificationUsers->map(function($user) {
                $createdAt = Carbon::parse($user->created_at);
                $mt5Registration = Carbon::parse($user->mt5_registration);
                $diffMinutes = abs($createdAt->diffInMinutes($mt5Registration));
                $match = $diffMinutes <= 5 ? '✅ Yes' : '❌ No';
                
                return [
                    'email' => $user->email,
                    'created_at' => $createdAt->format('Y-m-d H:i:s'),
                    'mt5_registration' => $mt5Registration->format('Y-m-d H:i:s'),
                    'match' => $match
                ];
            })->toArray()
        );
    }
}
