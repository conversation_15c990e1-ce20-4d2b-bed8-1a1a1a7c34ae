@foreach($nodes as $node)
<tr>
    <td>
        <div style="margin-left: {{ ($node['level'] - 1) * 20 }}px;">
            <strong>{{ $node['fullname'] }}</strong>
            <br>
            <small class="text-muted">{{ $node['username'] }}</small>
        </div>
    </td>
    <td>
        <span class="badge badge--info">Level {{ $node['level'] }}</span>
    </td>
    <td>
        <span class="badge badge--{{ $node['ib_type'] == 'master' ? 'success' : 'primary' }}">
            {{ ucfirst($node['ib_type']) }} IB
        </span>
    </td>
    <td>{{ $node['total_clients'] }}</td>
    <td>{{ $node['active_clients'] }}</td>
    <td>
        <span class="badge badge--{{ $node['ib_status'] == 'approved' ? 'success' : 'warning' }}">
            {{ ucfirst($node['ib_status']) }}
        </span>
    </td>
</tr>

@if(isset($node['children']) && count($node['children']) > 0)
    @include('templates.basic.user.ib.partials.hierarchy_table', ['nodes' => $node['children']])
@endif
@endforeach
