<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Real-time Orders</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        .real-time-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #28a745;
            margin-right: 5px;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .log-entry {
            font-family: monospace;
            font-size: 12px;
            padding: 2px 5px;
            margin: 1px 0;
            border-left: 3px solid #ccc;
        }
        .log-success { border-left-color: #28a745; background-color: #d4edda; }
        .log-error { border-left-color: #dc3545; background-color: #f8d7da; }
        .log-warning { border-left-color: #ffc107; background-color: #fff3cd; }
        .log-info { border-left-color: #17a2b8; background-color: #d1ecf1; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>🔧 Real-time Orders Debug</h1>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5>Real-time Orders Data</h5>
                        <div>
                            <span class="real-time-indicator"></span>
                            <small class="text-muted">Live Updates</small>
                        </div>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary" onclick="testRealTimeEndpoint()">Test Real-time Endpoint</button>
                        <button class="btn btn-success" onclick="startAutoRefresh()">Start Auto Refresh</button>
                        <button class="btn btn-danger" onclick="stopAutoRefresh()">Stop Auto Refresh</button>
                        
                        <div class="mt-3">
                            <h6>Orders Table:</h6>
                            <div class="table-responsive">
                                <table class="table table-sm" id="ordersTable">
                                    <thead>
                                        <tr>
                                            <th>Time</th>
                                            <th>Symbol</th>
                                            <th>Profit</th>
                                            <th>Commission</th>
                                            <th>Fee</th>
                                            <th>Ticket</th>
                                            <th>Volume</th>
                                            <th>Price</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td colspan="8" class="text-center text-muted">Click "Test Real-time Endpoint" to load data</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6>Debug Log</h6>
                    </div>
                    <div class="card-body" style="height: 400px; overflow-y: auto;">
                        <div id="debugLog"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6>Raw Response Data</h6>
                    </div>
                    <div class="card-body">
                        <pre id="rawResponse" class="bg-dark text-light p-3 rounded" style="height: 300px; overflow-y: auto;">No data yet...</pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let autoRefreshInterval = null;
        
        function addLog(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `<div class="log-entry log-${type}">[${timestamp}] ${message}</div>`;
            $('#debugLog').prepend(logEntry);
        }
        
        function testRealTimeEndpoint() {
            addLog('info', 'Testing real-time endpoint...');

            // Test the working test endpoint (no authentication required)
            $.ajax({
                url: '/mbf.mybrokerforex.com-31052025/test-mt5-realtime/873475',
                type: 'GET',
                dataType: 'json',
                timeout: 15000,
                beforeSend: function() {
                    $('.real-time-indicator').css('background-color', '#ffc107');
                },
                success: function(response) {
                    addLog('success', `Endpoint responded successfully`);
                    $('#rawResponse').text(JSON.stringify(response, null, 2));

                    if (response.success && response.data && response.data.success) {
                        const orderData = response.data.data || [];
                        const count = response.data.count || 0;
                        addLog('success', `Found ${count} positions for account ${response.login}`);
                        updateOrdersTable(orderData);
                        $('.real-time-indicator').css('background-color', '#28a745');
                    } else {
                        addLog('error', `API returned error: ${response.error || response.message || 'Unknown error'}`);
                        $('.real-time-indicator').css('background-color', '#dc3545');
                    }
                },
                error: function(xhr, status, error) {
                    addLog('error', `AJAX Error: ${status} - ${error}`);
                    addLog('error', `Status Code: ${xhr.status}`);
                    addLog('error', `Response: ${xhr.responseText.substring(0, 200)}...`);
                    $('#rawResponse').text(`Error: ${xhr.status} ${error}\n\nResponse:\n${xhr.responseText}`);
                    $('.real-time-indicator').css('background-color', '#dc3545');
                }
            });
        }
        
        function updateOrdersTable(orders) {
            const tbody = $('#ordersTable tbody');
            
            if (!orders || orders.length === 0) {
                tbody.html('<tr><td colspan="8" class="text-center text-muted">No orders found</td></tr>');
                return;
            }
            
            let html = '';
            orders.forEach(order => {
                const timeSetup = order.TimeSetup ? new Date(order.TimeSetup * 1000).toLocaleString() : 'N/A';
                const profit = parseFloat(order.Profit || 0).toFixed(2);
                const volume = parseFloat(order.Volume || 0);
                const volumeDisplay = volume > 1000 ? (volume / 100000).toFixed(3) + ' lot' : volume.toFixed(2);
                
                html += `
                    <tr>
                        <td>${timeSetup}</td>
                        <td><strong>${order.Symbol || 'N/A'}</strong></td>
                        <td class="${profit >= 0 ? 'text-success' : 'text-danger'}">${profit}</td>
                        <td>${parseFloat(order.Commission || 0).toFixed(2)}</td>
                        <td>${parseFloat(order.Fee || 0).toFixed(2)}</td>
                        <td>${order.Ticket || order.Order || 'N/A'}</td>
                        <td>${volumeDisplay}</td>
                        <td>${order.PriceOpen || 0} → ${order.PriceCurrent || 0}</td>
                    </tr>
                `;
            });
            
            tbody.html(html);
            addLog('success', `Updated table with ${orders.length} orders`);
        }
        
        function startAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
            
            addLog('info', 'Starting auto-refresh every 30 seconds');
            autoRefreshInterval = setInterval(testRealTimeEndpoint, 30000);
            
            // Initial load
            testRealTimeEndpoint();
        }
        
        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                addLog('warning', 'Auto-refresh stopped');
            }
        }
        
        // Test on page load
        $(document).ready(function() {
            addLog('info', 'Debug page loaded');
            
            // Test if we can access the endpoint
            setTimeout(() => {
                addLog('info', 'Testing endpoint accessibility...');
                testRealTimeEndpoint();
            }, 1000);
        });
    </script>
</body>
</html>
