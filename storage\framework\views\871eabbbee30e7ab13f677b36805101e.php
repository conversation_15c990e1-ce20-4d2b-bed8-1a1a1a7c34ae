
<?php $__env->startPush('style'); ?>
<!-- Include OrgChart.js CSS from jsDelivr CDN -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/orgchart@4.0.1/dist/css/jquery.orgchart.min.css" />
<?php $__env->stopPush(); ?>



<div class="row gy-4 mb-3 justify-content-start">
    <div class="col-lg-12">
        <div class="transection h-100">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3 class="transection__title skeleton"><?php echo app('translator')->get('Professional Network Tree'); ?></h3>

                
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-sm btn--primary active" id="hierarchyViewBtn" onclick="toggleView('hierarchy')">
                        <i class="las la-sitemap"></i> <?php echo app('translator')->get('Network Tree'); ?>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline--primary" id="tableViewBtn" onclick="toggleView('table')">
                        <i class="las la-table"></i> <?php echo app('translator')->get('Table View'); ?>
                    </button>
                </div>
            </div>

            
            <?php if($user->isIb() || $user->ibParent || ($networkData['direct_referrals'] ?? 0) > 0): ?>
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="widget-two style--two box--shadow2 b-radius--5 bg--primary">
                        <div class="widget-two__icon b-radius--5 bg--primary">
                            <i class="las la-user-tie"></i>
                        </div>
                        <div class="widget-two__content">
                            <h3 class="text-white">
                                <?php if($user->isIb()): ?>
                                    <?php echo e(ucfirst($user->ib_type ?? 'Standard')); ?> IB
                                <?php else: ?>
                                    <?php echo app('translator')->get('Client'); ?>
                                <?php endif; ?>
                            </h3>
                            <p class="text-white"><?php echo app('translator')->get('Status'); ?></p>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="widget-two style--two box--shadow2 b-radius--5 bg--success">
                        <div class="widget-two__icon b-radius--5 bg--success">
                            <i class="las la-users"></i>
                        </div>
                        <div class="widget-two__content">
                            <h3 class="text-white"><?php echo e($networkData['direct_referrals'] ?? 0); ?></h3>
                            <p class="text-white"><?php echo app('translator')->get('Direct Referrals'); ?></p>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="widget-two style--two box--shadow2 b-radius--5 bg--info">
                        <div class="widget-two__icon b-radius--5 bg--info">
                            <i class="las la-layer-group"></i>
                        </div>
                        <div class="widget-two__content">
                            <h3 class="text-white"><?php echo e($networkData['total_referrals'] ?? 0); ?></h3>
                            <p class="text-white"><?php echo app('translator')->get('Total Network'); ?></p>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="widget-two style--two box--shadow2 b-radius--5 bg--warning">
                        <div class="widget-two__icon b-radius--5 bg--warning">
                            <i class="las la-dollar-sign"></i>
                        </div>
                        <div class="widget-two__content">
                            <h3 class="text-white" id="network-total-commission">
                                <?php if($user->isIb()): ?>
                                    <?php echo e(showAmount($mt5CommissionData['total_commission'] ?? 0)); ?>

                                <?php else: ?>
                                    <?php echo e(showAmount(0)); ?>

                                <?php endif; ?>
                            </h3>
                            <p class="text-white"><?php echo app('translator')->get('Total Commissions'); ?></p>
                            
                            <?php if($user->isIb() && isset($mt5CommissionData)): ?>
                                <small class="text-white-50" id="network-commission-breakdown">
                                    Paid: <span id="network-paid-commission"><?php echo e(showAmount($mt5CommissionData['paid_commission'] ?? 0)); ?></span> |
                                    Pending: <span id="network-pending-commission"><?php echo e(showAmount($mt5CommissionData['pending_commission'] ?? 0)); ?></span>
                                </small>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            
            <?php if($user->ibParent): ?>
            <div class="col-md-12 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="text-start">
                            <i class="las la-arrow-up text-primary"></i>
                            <?php echo app('translator')->get('Upline Hierarchy'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="upline-chain">
                            <?php
                                $uplineChain = [];
                                $currentUser = $user;
                                while($currentUser->ibParent && count($uplineChain) < 10) {
                                    $uplineChain[] = $currentUser->ibParent;
                                    $currentUser = $currentUser->ibParent;
                                }
                                $uplineChain = array_reverse($uplineChain);
                            ?>

                            <?php $__currentLoopData = $uplineChain; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $uplineIb): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="upline-item d-flex align-items-center mb-3">
                                <div class="upline-level">
                                    <span class="badge badge--primary">L<?php echo e(count($uplineChain) - $index); ?></span>
                                </div>
                                <div class="upline-info ms-3 flex-grow-1">
                                    <h6 class="mb-0"><?php echo e($uplineIb->fullname); ?></h6>
                                    <small class="text-muted"><?php echo e($uplineIb->username); ?></small>
                                    <?php if($uplineIb->ibGroup): ?>
                                        <br><small class="text-info"><?php echo e($uplineIb->ibGroup->name); ?></small>
                                    <?php endif; ?>
                                </div>
                                <div class="upline-type">
                                    <span class="badge badge--<?php echo e($uplineIb->ib_type == 'master' ? 'success' : 'info'); ?>">
                                        <?php echo e(ucfirst($uplineIb->ib_type)); ?> IB
                                    </span>
                                </div>
                                <?php if(!$loop->last): ?>
                                <div class="upline-connector ms-2">
                                    <i class="las la-arrow-down text-muted"></i>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            
                            <div class="upline-item current-user d-flex align-items-center">
                                <div class="upline-level">
                                    <span class="badge badge--warning">YOU</span>
                                </div>
                                <div class="upline-info ms-3 flex-grow-1">
                                    <h6 class="mb-0"><?php echo e($user->fullname); ?></h6>
                                    <small class="text-muted"><?php echo e($user->username); ?></small>
                                    <?php if($user->ibGroup): ?>
                                        <br><small class="text-info"><?php echo e($user->ibGroup->name); ?></small>
                                    <?php endif; ?>
                                </div>
                                <div class="upline-type">
                                    <?php if($user->isIb()): ?>
                                        <span class="badge badge--<?php echo e($user->ib_type == 'master' ? 'success' : 'info'); ?>">
                                            <?php echo e(ucfirst($user->ib_type)); ?> IB
                                        </span>
                                    <?php else: ?>
                                        <span class="badge badge--secondary">Client</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            
            <?php if(($networkData['direct_referrals'] ?? 0) > 0 || ($networkData['total_referrals'] ?? 0) > 0): ?>

            
            <div id="hierarchyView" class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title"><?php echo app('translator')->get('Partnership Network Tree'); ?></h5>
                        <div class="text-muted">Network visualization with Master IB at top center</div>
                    </div>
                    <div class="card-body">
                        <!-- Clean Network Container - Following network-clean design -->
                        <div id="adminNetworkContainer" style="min-height: 400px; border: 2px dashed #ddd; padding: 20px; overflow:auto;">
                            <div class="text-center py-5">
                                <h4>Network Tree Loading...</h4>
                                <div class="spinner-border text-primary" role="status"></div>
                                <div class="mt-2">
                                    <small class="text-muted">If this doesn't change, check console for errors</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            
            <div id="tableView" class="col-md-12" style="display: none;">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="text-start mb-0">
                            <i class="las la-users text-success"></i>
                            <?php echo app('translator')->get('Direct Referrals'); ?> (<?php echo e($networkData['direct_referrals'] ?? 0); ?>)
                        </h5>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn--primary" onclick="toggleView('hierarchy')">
                                <i class="las la-sitemap"></i> <?php echo app('translator')->get('Network Tree'); ?>
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table--light">
                                <thead>
                                    <tr>
                                        <th><?php echo app('translator')->get('User'); ?></th>
                                        <th><?php echo app('translator')->get('MT5 Account'); ?></th>
                                        <th><?php echo app('translator')->get('Balance'); ?></th>
                                        <th><?php echo app('translator')->get('IB Status'); ?></th>
                                        <th><?php echo app('translator')->get('IB Type'); ?></th>
                                        <th><?php echo app('translator')->get('Joined Date'); ?></th>
                                        <th><?php echo app('translator')->get('Sub Referrals'); ?></th>
                                        <th><?php echo app('translator')->get('Action'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if(isset($networkData['tree_data']['children']) && count($networkData['tree_data']['children']) > 0): ?>
                                        <?php $__currentLoopData = $networkData['tree_data']['children']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $referral): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm me-2" style="width: 32px; height: 32px; background: <?php echo e($referral['node_type'] == 'ib' ? '#28a745' : '#6c757d'); ?>; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px;">
                                                        <?php echo e(substr($referral['name'], 0, 1)); ?>

                                                    </div>
                                                    <div>
                                                        <strong><?php echo e($referral['name']); ?></strong>
                                                        <br><small class="text-muted">ID: <?php echo e($referral['id']); ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?php echo e($referral['mt5_login']); ?></span>
                                            </td>
                                            <td>
                                                <strong class="text-success">$<?php echo e($referral['mt5_balance']); ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge <?php echo e($referral['node_type'] == 'ib' ? 'bg-success' : 'bg-secondary'); ?>">
                                                    <?php echo e($referral['node_type'] == 'ib' ? 'IB' : 'Client'); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary"><?php echo e($referral['title']); ?></span>
                                            </td>
                                            <td>
                                                <small class="text-muted"><?php echo e(now()->subDays(rand(1, 365))->format('M d, Y')); ?></small>
                                            </td>
                                            <td>
                                                <span class="badge bg-warning"><?php echo e($referral['children_count'] ?? 0); ?></span>
                                            </td>
                                            <td>
                                                <a href="<?php echo e(route('admin.users.detail', $referral['id'])); ?>" class="btn btn-sm btn-outline-primary">
                                                    <i class="las la-eye"></i> <?php echo app('translator')->get('View'); ?>
                                                </a>
                                            </td>
                                        </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="8" class="text-center text-muted py-4">
                                                <i class="las la-users" style="font-size: 2rem;"></i>
                                                <br><?php echo app('translator')->get('No direct referrals found'); ?>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination Controls for Direct Referrals -->
                        <?php if(($networkData['pagination']['total'] ?? 0) > 1500): ?>
                        <div class="pagination-controls p-3 border-top">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <div class="pagination-info">
                                        <span class="text-muted">
                                            <?php echo app('translator')->get('Showing'); ?>
                                            <span><?php echo e($networkData['pagination']['from'] ?? 1); ?></span>
                                            <?php echo app('translator')->get('to'); ?>
                                            <span><?php echo e($networkData['pagination']['to'] ?? 1500); ?></span>
                                            <?php echo app('translator')->get('of'); ?>
                                            <span><?php echo e($networkData['pagination']['total'] ?? 0); ?></span>
                                            <?php echo app('translator')->get('direct referrals'); ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="col-md-6 text-end">
                                    <?php if(($networkData['pagination']['current_page'] ?? 1) > 1): ?>
                                        <a href="<?php echo e(request()->fullUrlWithQuery(['network_page' => ($networkData['pagination']['current_page'] ?? 1) - 1])); ?>"
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="las la-arrow-left"></i> <?php echo app('translator')->get('Previous'); ?>
                                        </a>
                                    <?php endif; ?>

                                    <span class="mx-2 text-muted">
                                        <?php echo app('translator')->get('Page'); ?> <?php echo e($networkData['pagination']['current_page'] ?? 1); ?>

                                        <?php echo app('translator')->get('of'); ?> <?php echo e($networkData['pagination']['last_page'] ?? 1); ?>

                                    </span>

                                    <?php if(($networkData['pagination']['has_more'] ?? false)): ?>
                                        <a href="<?php echo e(request()->fullUrlWithQuery(['network_page' => ($networkData['pagination']['current_page'] ?? 1) + 1])); ?>"
                                           class="btn btn-sm btn-outline-primary">
                                            <?php echo app('translator')->get('Next'); ?> <i class="las la-arrow-right"></i>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <?php else: ?>
            
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body p-5">
                        <div class="empty-thumb text-center">
                            <i class="las la-users text-muted" style="font-size: 4rem;"></i>
                            <h5 class="text-muted mt-3"><?php echo app('translator')->get('No Network Found'); ?></h5>
                            <p class="text-muted">
                                <?php if($user->isIb()): ?>
                                    <?php echo app('translator')->get('This IB has not referred any users yet.'); ?>
                                <?php else: ?>
                                    <?php echo app('translator')->get('This user has not referred anyone and is not part of an IB network.'); ?>
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>



<?php $__env->startPush('style'); ?>
<style>
/* CLEAN NETWORK VISUALIZATION CSS - COMPLETELY REBUILT */


/* OrgChart Basic Styling */
.orgchart {
    text-align: center !important;
}

.orgchart .node {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px;
    min-width: 120px;
    max-width: 200px;
    text-align: center;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);   
}

.orgchart .node:hover {
    border-color: #007bff;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}
.orgchart .node .title {
    font-size:10px !important;
    width: 160px !important
}
.orgchart .node .content {
    font-size:8px !important;
}
/* Node Types */
.orgchart .node.master-ib {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

.orgchart .node.sub-ib {
    background: #28a745;
    color: white;
    border-color: #28a745;
}

.orgchart .node .content {
    width: auto !important;
    height: auto !important;
    line-height: 12px;
}

/* Node Content */
.node-name {
    font-weight: bold;
    font-size: 8px;
    margin-bottom: 4px;
}

.node-info {
    font-size: 8px;
    line-height: 1.2;
}

/* Loading State */
.loading-spinner {
    text-align: center;
    padding: 40px;
}

.loading-spinner .spinner-border {
    width: 3rem;
    height: 3rem;
}
.orgchart .edge:hover::before {
    border-color: #e17572 !important;
    padding: 4px !important;
}
.ib-labels {
    color: #ea5455;
    background: #ffffff;
    padding: 2px 6px;              /* Wider horizontal padding */
    border-radius: 999px;          /* Fully rounded */
    margin-left: 5px;
    font-size: 10px;               /* Slightly bigger text for better shape */
    display: inline-block;         /* Required for border-radius to work properly */
    line-height: 1;                /* Avoids tall pill */
}
</style>
<?php $__env->stopPush(); ?>




<?php $__env->startPush('script'); ?>

<script src="https://cdn.jsdelivr.net/npm/orgchart@4.0.1/dist/js/jquery.orgchart.min.js"></script>

<script>
// Pass PHP data to JavaScript - Following network-clean pattern
window.adminPhpData = {
    networkData: <?php echo json_encode($networkData ?? [], 15, 512) ?>,
    user: {
        id: <?php echo e($user->id ?? 0); ?>,
        name: <?php echo json_encode(($user->firstname ?? '') . ' ' . ($user->lastname ?? ''), 15, 512) ?>,
        mt5_login: <?php echo json_encode($user->mt5_login ?? 'N/A', 15, 512) ?>,
        mt5_balance: <?php echo e($user->mt5_balance ?? 0); ?>,
        ib_status: <?php echo e($user->ib_status ?? 0); ?>,
        ib_type: <?php echo json_encode($user->ib_type ?? 'master', 15, 512) ?>
    }
};
console.log('🔍 ADMIN DEBUG: PHP Data passed:', window.adminPhpData);
console.log('🔍 ADMIN DEBUG: Network data:', window.adminPhpData.networkData);
console.log('🔍 ADMIN DEBUG: Tree data structure:', window.adminPhpData.networkData.tree_data);
console.log('🔍 ADMIN DEBUG: Direct referrals count:', window.adminPhpData.networkData.direct_referrals);
console.log('🔍 ADMIN DEBUG: Tree data children:', window.adminPhpData.networkData.tree_data ? window.adminPhpData.networkData.tree_data.children : 'NO TREE DATA');
$(document).ready(function() {
    console.log('Admin Document ready - starting network initialization');

    // Create BACKEND_DATA from window.adminPhpData - Following network-clean pattern
    window.ADMIN_BACKEND_DATA = {
        networkData: window.adminPhpData.networkData || [],
        user: window.adminPhpData.user || {},
        routes: {
            getReferrals: <?php echo json_encode(route('user.partnership.get-referrals'), 15, 512) ?>,
            network: <?php echo json_encode(route('admin.users.detail', $user->id ?? 0), 512) ?>
        }
    };

    console.log('ADMIN_BACKEND_DATA created:', window.ADMIN_BACKEND_DATA);

    // Fix MutationObserver error before initializing OrgChart
    const originalMutationObserver = window.MutationObserver;
    window.MutationObserver = function(callback) {
        const observer = new originalMutationObserver(callback);
        const originalObserve = observer.observe;

        observer.observe = function(target, options) {
            try {
                // Check if target is a valid Node
                if (target && typeof target.nodeType !== 'undefined') {
                    return originalObserve.call(this, target, options);
                } else {
                    console.warn('⚠️ MutationObserver: Invalid target node, skipping observe');
                    return;
                }
            } catch (error) {
                console.warn('⚠️ MutationObserver error caught and handled:', error);
                return;
            }
        };

        return observer;
    };

    // Check if OrgChart.js is loaded
    if (typeof $.fn.orgchart !== 'undefined') {
        console.log('✅ OrgChart.js loaded successfully');
        initializeAdminSimpleTree();
    } else {
        console.log('⚠️ OrgChart.js not available, showing fallback');
        showAdminSimpleFallback();
    }

    // PART 1: Initialize real-time commission updates for Network tab
    initializeNetworkCommissionUpdates();
});

// PART 1: Real-time commission updates for Network tab
function initializeNetworkCommissionUpdates() {
    // Only initialize if user is an IB
    <?php if($user->isIb()): ?>
    console.log('Initializing Network tab real-time commission updates...');

    function updateNetworkCommissionWidget() {
        fetch('<?php echo e(route("admin.users.commission.realtime", $user->id)); ?>', {
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update Network tab commission widgets
                const totalCommissionEl = document.getElementById('network-total-commission');
                const paidCommissionEl = document.getElementById('network-paid-commission');
                const pendingCommissionEl = document.getElementById('network-pending-commission');

                if (totalCommissionEl) {
                    totalCommissionEl.textContent = '$' + parseFloat(data.total_commission).toFixed(2);
                }
                if (paidCommissionEl) {
                    paidCommissionEl.textContent = '$' + parseFloat(data.paid_commission).toFixed(2);
                }
                if (pendingCommissionEl) {
                    pendingCommissionEl.textContent = '$' + parseFloat(data.pending_commission).toFixed(2);
                }

                console.log('Network tab commission data updated:', data);
            }
        })
        .catch(error => {
            console.error('Network tab commission update error:', error);
        });
    }

    // Start real-time updates every 30 seconds
    setInterval(updateNetworkCommissionWidget, 30000);

    // Initial update after 2 seconds
    setTimeout(updateNetworkCommissionWidget, 2000);
    <?php endif; ?>
}

// Initialize simple tree - Following network-clean pattern
function initializeAdminSimpleTree() {
    console.log('Initializing admin simple tree...');

    // CRITICAL FIX: Always use actual tree data from backend
    let treeData;

    if (window.ADMIN_BACKEND_DATA.networkData.tree_data &&
        window.ADMIN_BACKEND_DATA.networkData.tree_data.id) {
        // Use the actual tree data from backend
        treeData = window.ADMIN_BACKEND_DATA.networkData.tree_data;
        console.log('✅ ADMIN: Using actual tree data from backend');
    } else {
        // Create fallback tree data
        treeData = {
            id: window.ADMIN_BACKEND_DATA.user.id || 1,
            name: window.ADMIN_BACKEND_DATA.user.name || 'Test User',
            title: window.ADMIN_BACKEND_DATA.user.ib_status == 1 ? 'Master IB' : 'Client',
            mt5_login: window.ADMIN_BACKEND_DATA.user.mt5_login || 'N/A',
            mt5_balance: (window.ADMIN_BACKEND_DATA.user.mt5_balance || 0).toFixed(2),
            total_deposit: '0.00',
            node_type: window.ADMIN_BACKEND_DATA.user.ib_status == 1 ? 'master' : 'client',
            children: []
        };
        console.log('⚠️ ADMIN: Using fallback tree data');
    }

    console.log('🔍 ADMIN: Tree data being used:', treeData);
    console.log('🔍 ADMIN: Backend network data:', window.ADMIN_BACKEND_DATA.networkData);
    console.log('🔍 ADMIN: Tree data has children:', treeData.children ? treeData.children.length : 'NO CHILDREN');
    console.log('🔍 ADMIN: Tree data structure check:', {
        hasTreeData: !!window.ADMIN_BACKEND_DATA.networkData.tree_data,
        hasChildren: !!(treeData.children && treeData.children.length > 0),
        childrenCount: treeData.children ? treeData.children.length : 0,
        fallbackUsed: !window.ADMIN_BACKEND_DATA.networkData.tree_data
    });

    try {
        // Clear the container first and ensure it exists
        const container = $('#adminNetworkContainer');
        if (!container.length) {
            console.warn('⚠️ Admin network container not found, creating fallback container');
            // Create a fallback container if it doesn't exist
            const fallbackContainer = $('<div id="adminNetworkContainer" style="min-height: 400px; border: 2px dashed #ddd; padding: 20px; overflow:auto;"></div>');
            $('#hierarchyView .card-body').append(fallbackContainer);
            showAdminSimpleFallback();
            return;
        }

        container.empty();

        // Add additional error handling for MutationObserver
        const originalConsoleError = console.error;
        console.error = function(...args) {
            const message = args.join(' ');
            if (message.includes('MutationObserver') || message.includes('observe')) {
                console.warn('⚠️ MutationObserver error suppressed:', ...args);
                return;
            }
            originalConsoleError.apply(console, args);
        };

        // Initialize OrgChart with enhanced error handling
        container.orgchart({
            data: treeData,
            direction: 't2b',
            visibleLevel: 1,
            nodeTemplate: function(data) {
                try {
                    // Determine IB type indicator
                    let ibIndicator = '';
                    if (data.ib_status == 1) {
                        if (data.ib_type === 'master') {
                            ibIndicator = '<span class="ib-labels">M</span>';
                        } else if (data.ib_type === 'sub') {
                            ibIndicator = '<span class="ib-labels">S</span>';
                        }
                    } else {
                        ibIndicator = '<span class="ib-labels">C</span>';
                    }

                    return `
                        <div class="title">${data.name || 'Unknown'}${ibIndicator}</div>
                        <div class="content">
                            MT5: ${data.mt5_login || 'N/A'}<br>
                            Balance: $${data.mt5_balance || '0.00'}<br>
                            Deposits: $${data.total_deposit || '0.00'}
                        </div>
                    `;
                } catch (templateError) {
                    console.warn('⚠️ Node template error:', templateError);
                    return `<div class="title">${data.name || 'Unknown'}</div>`;
                }
            }
        });

        // Restore original console.error after a delay
        setTimeout(() => {
            console.error = originalConsoleError;
        }, 2000);

        console.log('Admin simple tree initialized successfully');

        // Add success message
        setTimeout(() => {
            container.prepend('<div class="alert alert-success">✅ MultiIB Chart tree loaded successfully!</div>');
        }, 500);

    } catch (error) {
        console.error('Admin OrgChart error:', error);
        // Check if it's a MutationObserver error and handle gracefully
        if (error.message && error.message.includes('MutationObserver')) {
            console.log('✅ MutationObserver error handled, OrgChart should still work');
            // Don't show fallback for MutationObserver errors
        } else {
            showAdminSimpleFallback();
        }
    }
}

function toggleView(viewType) {
    const hierarchyView = document.getElementById('hierarchyView');
    const tableView = document.getElementById('tableView');
    const hierarchyBtn = document.getElementById('hierarchyViewBtn');
    const tableBtn = document.getElementById('tableViewBtn');

    if (viewType === 'hierarchy') {
        hierarchyView.style.display = 'block';
        tableView.style.display = 'none';
        hierarchyBtn.classList.add('active', 'btn--primary');
        hierarchyBtn.classList.remove('btn-outline--primary');
        tableBtn.classList.remove('active', 'btn--primary');
        tableBtn.classList.add('btn-outline--primary');
    } else {
        hierarchyView.style.display = 'none';
        tableView.style.display = 'block';
        tableBtn.classList.add('active', 'btn--primary');
        tableBtn.classList.remove('btn-outline--primary');
        hierarchyBtn.classList.remove('active', 'btn--primary');
        hierarchyBtn.classList.add('btn-outline--primary');
    }
}

function showAdminSimpleFallback() {
    console.log('Showing admin simple fallback tree...');

    const fallbackHtml = `
        <div class="alert alert-warning">⚠️ OrgChart.js fallback mode</div>
        <div class="text-center">
            <h4>Admin Network Tree (Fallback Mode)</h4>
            <div class="row justify-content-center">
                <div class="col-md-4">
                    <div class="card border-primary">
                        <div class="card-body text-center">
                            <h6 class="card-title">${window.ADMIN_BACKEND_DATA?.user?.name || 'Test User'}</h6>
                            <p class="card-text">
                                <small class="text-muted">MT5: ${window.ADMIN_BACKEND_DATA?.user?.mt5_login || 'N/A'}</small><br>
                                <small class="text-muted">ID: ${window.ADMIN_BACKEND_DATA?.user?.id || 'N/A'}</small>
                            </p>
                            <span class="badge bg-primary">Master IB</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Try to find the container, create if it doesn't exist
    let container = $('#adminNetworkContainer');
    if (!container.length) {
        console.log('Creating fallback container for admin network');
        container = $('<div id="adminNetworkContainer" style="min-height: 400px; border: 2px dashed #ddd; padding: 20px; overflow:auto;"></div>');
        $('#hierarchyView .card-body').append(container);
    }

    container.html(fallbackHtml);
    console.log('Admin simple fallback displayed successfully');
}

    // CLEANED UP - All duplicate functions removed

</script>
<?php $__env->stopPush(); ?>

<?php /**PATH C:\xampp\htdocs\mbf.mybrokerforex.com-31052025\resources\views/components/user-detail/network.blade.php ENDPATH**/ ?>