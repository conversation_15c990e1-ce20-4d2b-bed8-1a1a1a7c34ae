<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class SyncMT5UsersToLocal extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mt5:sync-users
                            {--limit=1000 : Number of users to process per batch (minimum 1000 for performance)}
                            {--dry-run : Run without making changes}
                            {--force : Force sync even if user exists}
                            {--filter= : Filter users by group (e.g., real)}
                            {--replace-all : Delete all existing users and import all MT5 users}
                            {--fast : Use fast bulk operations for better performance}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync MT5 users from Ireland database to local CRM database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔄 Starting Enhanced MT5 User Synchronization...');
        $this->info('🎯 Ireland Server → Same Server Real-time Sync');

        $limit = max(1000, $this->option('limit') ?: 1000); // Enforce minimum 1000 for performance
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');
        $filter = $this->option('filter');
        $replaceAll = $this->option('replace-all');
        $fast = $this->option('fast');

        if ($dryRun) {
            $this->warn('🧪 DRY RUN MODE - No changes will be made');
        }

        // Store sync start time for performance tracking
        Cache::put('sync_start_time', microtime(true), 3600);

        try {
            // Enhanced connection test with timeout
            $this->info('📡 Testing connection to Ireland MT5 database (same server)...');
            $connection = DB::connection('mbf-dbmt5');

            // Test with timeout
            $startTime = microtime(true);
            $totalUsers = $connection->table('mt5_users')->count();
            $connectionTime = round((microtime(true) - $startTime) * 1000, 2);

            $this->info("✅ Connection successful! Found {$totalUsers} users in MT5 database");
            $this->info("⚡ Connection time: {$connectionTime}ms");

            // Enhanced query with duplicate email handling
            $query = $connection->table('mt5_users')
                ->whereNotNull('Email')
                ->where('Email', '!=', '')
                ->where('Email', 'not like', '%@company.com') // Exclude test accounts
                ->where('Group', 'not like', '%admin%')
                ->where('Group', 'not like', '%manager%')
                ->where('Group', 'not like', '%Dead Trading%'); // Exclude dead accounts

            if ($filter) {
                $query->where('Group', 'like', "%{$filter}%");
            }

            $filteredCount = $query->count();
            $this->info("📊 Found {$filteredCount} users matching criteria");

            // Check for duplicate emails and warn
            $duplicateCount = $connection->table('mt5_users')
                ->select('Email', DB::raw('COUNT(*) as count'))
                ->whereNotNull('Email')
                ->where('Email', '!=', '')
                ->groupBy('Email')
                ->having('count', '>', 1)
                ->count();

            if ($duplicateCount > 0) {
                $this->warn("⚠️  Found {$duplicateCount} emails with multiple MT5 accounts - will consolidate");
            }

            // Handle replace-all option
            if ($replaceAll) {
                $this->warn('⚠️  REPLACE ALL MODE: This will delete ALL existing users and import all MT5 users!');
                if (!$this->confirm('Are you absolutely sure you want to delete all existing users and replace with MT5 data?')) {
                    $this->info('❌ Replace operation cancelled');
                    return 0;
                }

                if (!$dryRun) {
                    $this->info('🗑️  Deleting all existing users...');
                    $deletedCount = User::count();

                    // Disable foreign key checks temporarily
                    DB::statement('SET FOREIGN_KEY_CHECKS=0;');
                    User::truncate();
                    DB::statement('SET FOREIGN_KEY_CHECKS=1;');

                    $this->info("✅ Deleted {$deletedCount} existing users");
                }

                // Remove filter for replace-all to import all users
                $query = $connection->table('mt5_users')
                    ->whereNotNull('Email')
                    ->where('Email', '!=', '')
                    ->where('Group', 'not like', '%admin%')
                    ->where('Group', 'not like', '%manager%');

                $filteredCount = $query->count();
                $this->info("📊 Will import {$filteredCount} MT5 users");
            }

            // Skip confirmation if --force flag is used (for automated scheduler)
            if (!$force && !$this->confirm('Do you want to proceed with the synchronization?')) {
                $this->info('❌ Synchronization cancelled');
                return 0;
            }

            $processed = 0;
            $created = 0;
            $updated = 0;
            $errors = 0;

            if ($fast) {
                // FAST BULK MODE - Optimized for high performance
                $this->info("🚀 Using FAST BULK MODE for maximum performance");
                $result = $this->fastBulkSync($query, $dryRun, $force, $replaceAll);
                $processed = $result['processed'];
                $created = $result['created'];
                $updated = $result['updated'];
                $errors = $result['errors'];
            } else {
                // SAFE MODE - Process in chunks
                $query->orderBy('Login')->chunk($limit, function ($mt5Users) use (&$processed, &$created, &$updated, &$errors, $dryRun, $force, $replaceAll) {
                    foreach ($mt5Users as $mt5User) {
                        try {
                            $result = $this->syncUser($mt5User, $dryRun, $force, $replaceAll);

                            if ($result === 'created') {
                                $created++;
                            } elseif ($result === 'updated') {
                                $updated++;
                            }

                            $processed++;

                            if ($processed % 100 === 0) {
                                $this->info("📈 Processed: {$processed} | Created: {$created} | Updated: {$updated} | Errors: {$errors}");
                            }

                        } catch (\Exception $e) {
                            $errors++;
                            Log::error('MT5 User Sync Error', [
                                'mt5_login' => $mt5User->Login,
                                'error' => $e->getMessage()
                            ]);

                            $this->error("❌ Error syncing user {$mt5User->Login}: " . $e->getMessage());
                        }
                    }
                });
            }

            // Final summary
            $this->info('');
            $this->info('🎉 Synchronization completed!');
            $this->table(['Metric', 'Count'], [
                ['Total Processed', $processed],
                ['Users Created', $created],
                ['Users Updated', $updated],
                ['Errors', $errors]
            ]);

            if (!$dryRun) {
                $this->info('✅ All changes have been saved to the database');

                // TRIGGER AUTO-SYNC: Update CRM cache and notify admin interface
                $this->triggerCRMAutoSync($processed, $created, $updated, $errors, $dryRun);
            } else {
                $this->warn('🧪 DRY RUN - No actual changes were made');
            }

        } catch (\Exception $e) {
            $this->error('💥 Synchronization failed: ' . $e->getMessage());
            Log::error('MT5 Sync Command Failed', ['error' => $e->getMessage()]);
            return 1;
        }

        return 0;
    }

    /**
     * Trigger CRM auto-sync after MT5 sync completion
     */
    private function triggerCRMAutoSync($processed, $created, $updated, $errors, $dryRun = false)
    {
        try {
            $this->info("🔄 TRIGGERING CRM AUTO-SYNC...");

            // Update sync statistics in cache for real-time display
            $syncStats = [
                'last_sync' => now(),
                'processed' => $processed,
                'created' => $created,
                'updated' => $updated,
                'errors' => $errors,
                'total_users' => User::count(),
                'mt5_users' => User::whereNotNull('mt5_login')->count(),
                'sync_status' => $errors > 0 ? 'warning' : 'success'
            ];

            Cache::put('mt5_sync_stats', $syncStats, 3600); // Cache for 1 hour

            // Clear user list cache to force refresh
            Cache::forget('admin_users_list');
            Cache::forget('admin_users_count');

            // Store performance metrics for monitoring
            if (app()->bound('App\Services\MT5SyncMonitoringService')) {
                $monitoringService = app('App\Services\MT5SyncMonitoringService');
                $duration = microtime(true) - (Cache::get('sync_start_time', microtime(true)));
                $rate = $processed > 0 ? $processed / max($duration, 1) : 0;

                $monitoringService->storePerformanceMetrics(
                    $duration, $rate, $processed, $created, $updated, $errors
                );
            }

            $this->info("✅ CRM auto-sync triggered successfully");

        } catch (\Exception $e) {
            $this->warn("⚠️ CRM auto-sync trigger failed: " . $e->getMessage());
            Log::warning('CRM Auto-sync trigger failed', ['error' => $e->getMessage()]);
        }

        // ISSUE 1 & 4 PERMANENT FIX: Always consolidate duplicates and ensure latest data
        if (!$dryRun) {
            $this->info("🔄 Ensuring latest data and consolidating duplicate email accounts...");

            // First, ensure we have the latest data from MT5
            $this->ensureLatestMT5Data();

            // Then consolidate duplicates permanently
            $consolidationResult = $this->consolidateDuplicateEmailsPermanent();
            $this->info("✅ Consolidated {$consolidationResult['consolidated']} duplicate email groups");

            if ($consolidationResult['errors'] > 0) {
                $this->warn("⚠️ {$consolidationResult['errors']} consolidation errors occurred");
            }

            // Update sync statistics for monitoring
            $this->updateSyncStatistics($processed, $created, $updated, $errors);
        }
    }

    /**
     * Sync individual MT5 user to local database - SAFE VERSION
     */
    private function syncUser($mt5User, $dryRun = false, $force = false, $replaceAll = false)
    {
        // In replace-all mode, always create new users (table was already truncated)
        if ($replaceAll) {
            $userData = $this->mapMt5UserToLocal($mt5User);

            if ($dryRun) {
                $this->line("🧪 Would create user: {$mt5User->Email} (MT5: {$mt5User->Login})");
                return 'created';
            }

            User::create($userData);
            return 'created';
        }

        // SAFE SYNC MODE - NEVER MIX USER DATA

        // Step 1: Check if user exists by EXACT mt5_login match (REAL MT5 LOGIN)
        $existingUserByMt5Login = User::where('mt5_login', $mt5User->Login)->first();

        if ($existingUserByMt5Login) {
            // Found exact match by MT5 login - UPDATE this user's MT5 data
            return $this->updateUserMt5Data($existingUserByMt5Login, $mt5User, $dryRun);
        }

        // Step 2: Check if user exists by email (but no mt5_login set)
        $existingUserByEmail = User::where('email', $mt5User->Email)
            ->whereNull('mt5_login')
            ->first();

        if ($existingUserByEmail) {
            // Found user by email with no MT5 data - UPDATE and set MT5 data
            return $this->updateUserMt5Data($existingUserByEmail, $mt5User, $dryRun);
        }

        // Step 3: Check if email exists but with different MT5 login (CONFLICT)
        $conflictUser = User::where('email', $mt5User->Email)
            ->whereNotNull('mt5_login')
            ->where('mt5_login', '!=', $mt5User->Login)
            ->first();

        if ($conflictUser) {
            // CONFLICT: Same email but different MT5 login - SKIP to prevent data corruption
            $this->warn("⚠️  CONFLICT: Email {$mt5User->Email} exists with different MT5 login ({$conflictUser->mt5_login} vs {$mt5User->Login}). Skipping to prevent data corruption.");
            return 'skipped';
        }

        // Step 4: No existing user found - CREATE new user
        $userData = $this->mapMt5UserToLocal($mt5User);

        if ($dryRun) {
            $this->line("🧪 Would create user: {$mt5User->Email} (MT5: {$mt5User->Login})");
            return 'created';
        }

        User::create($userData);
        return 'created';
    }

    /**
     * Update ONLY MT5 fields for existing user - CRITICAL FIX FOR ISSUE 1
     * NEVER overwrite Laravel user data (firstname, lastname, etc.)
     */
    private function updateUserMt5Data($user, $mt5User, $dryRun = false)
    {
        // CRITICAL FIX: Only update MT5-specific fields, PRESERVE all Laravel user data
        $updateData = [
            // PRESERVE Laravel user fields - DO NOT overwrite with MT5 data
            // 'firstname' => REMOVED - Keep existing Laravel data
            // 'lastname' => REMOVED - Keep existing Laravel data
            // 'mobile' => REMOVED - Keep existing Laravel data
            // 'country_code' => REMOVED - Keep existing Laravel data
            // 'address' => REMOVED - Keep existing Laravel data

            // MT5 Core Fields - Use REAL MT5 Login from Ireland database
            'mt5_login' => $mt5User->Login, // Store REAL MT5 login from Ireland database
            'mt5_timestamp' => $mt5User->Timestamp,
            'mt5_group' => $mt5User->Group,
            'mt5_cert_serial_number' => $mt5User->CertSerialNumber,
            'mt5_rights' => $mt5User->Rights,
            'mt5_registration' => $this->parseTimestamp($mt5User->Registration),
            'mt5_last_access' => $this->parseTimestamp($mt5User->LastAccess),
            'mt5_last_pass_change' => $this->parseTimestamp($mt5User->LastPassChange),

            // MT5 Personal Information
            'mt5_first_name' => $mt5User->FirstName,
            'mt5_last_name' => $mt5User->LastName,
            'mt5_middle_name' => $mt5User->MiddleName,
            'mt5_company' => $mt5User->Company,
            'mt5_account' => $mt5User->Account,
            'mt5_country' => $mt5User->Country,
            'mt5_language' => $mt5User->Language,
            'mt5_client_id' => $mt5User->ClientID,

            // MT5 Address Information
            'mt5_city' => $mt5User->City,
            'mt5_state' => $mt5User->State,
            'mt5_zip_code' => $mt5User->ZipCode,
            'mt5_address' => $mt5User->Address,
            'mt5_phone' => $mt5User->Phone,
            'mt5_email' => $mt5User->Email,
            'mt5_id' => $mt5User->ID,
            'mt5_status' => $mt5User->Status,
            'mt5_comment' => $mt5User->Comment,
            'mt5_color' => $mt5User->Color,
            'mt5_phone_password' => $mt5User->PhonePassword,

            // MT5 Trading Information
            'mt5_leverage' => $mt5User->Leverage,
            'mt5_agent' => $mt5User->Agent,
            'mt5_trade_accounts' => $mt5User->TradeAccounts,
            'mt5_limit_positions' => $mt5User->LimitPositions,
            'mt5_limit_orders' => $mt5User->LimitOrders,
            'mt5_lead_campaign' => $mt5User->LeadCampaign,
            'mt5_lead_source' => $mt5User->LeadSource,
            'mt5_timestamp_trade' => $mt5User->TimestampTrade,

            // MT5 Financial Information
            'mt5_balance' => $mt5User->Balance,
            'mt5_credit' => $mt5User->Credit,
            'mt5_equity' => $mt5User->Balance + $mt5User->Credit,
            'mt5_interest_rate' => $mt5User->InterestRate,
            'mt5_commission_daily' => $mt5User->CommissionDaily,
            'mt5_commission_monthly' => $mt5User->CommissionMonthly,
            'mt5_balance_prev_day' => $mt5User->BalancePrevDay,
            'mt5_balance_prev_month' => $mt5User->BalancePrevMonth,
            'mt5_equity_prev_day' => $mt5User->EquityPrevDay,
            'mt5_equity_prev_month' => $mt5User->EquityPrevMonth,

            // MT5 Additional Information
            'mt5_name' => $mt5User->Name,
            'mt5_mqid' => $mt5User->MQID,
            'mt5_last_ip' => $mt5User->LastIP,
            'mt5_api_data' => $mt5User->ApiData,
            'mt5_currency' => $this->extractCurrencyFromGroup($mt5User->Group),
            'mt5_synced_at' => now(),

            // CRITICAL FIX: PRESERVE Laravel user data - DO NOT overwrite created_at
            // 'created_at' => REMOVED - Keep original Laravel registration date
            'updated_at' => now(),

            // CRITICAL FIX: PRESERVE IB status and account type data
            // DO NOT overwrite: partner, ib_status, ib_type, ib_group_id, etc.
        ];

        if ($dryRun) {
            $this->line("🧪 Would update ONLY MT5 fields for user: {$user->email}");
            return 'updated';
        }

        // CRITICAL FIX: Use selective update to preserve existing Laravel data
        $user->update($updateData);

        // Log the selective update for verification
        Log::info('MT5 Selective Update Applied', [
            'user_id' => $user->id,
            'email' => $user->email,
            'mt5_login' => $mt5User->Login,
            'preserved_fields' => 'firstname, lastname, mobile, country_code, address, created_at, IB status'
        ]);

        return 'updated';
    }

    /**
     * Map MT5 user data to local user structure
     */
    private function mapMt5UserToLocal($mt5User)
    {
        return [
            // Basic user info (mapped from MT5 fields)
            'firstname' => $mt5User->FirstName ?: 'Unknown',
            'lastname' => $mt5User->LastName ?: 'User',
            'username' => $this->generateUsername($mt5User),
            'email' => $mt5User->Email,
            'password' => Hash::make('password123'), // Default password
            'country_code' => $this->getCountryCode($mt5User->Country),
            'mobile' => $this->formatMobile($mt5User->Phone),
            'address' => $this->formatAddress($mt5User),
            'status' => 1, // Active
            'ev' => 1, // Email verified
            'sv' => 1, // SMS verified
            'kv' => 1, // KYC verified
            'profile_complete' => 1,

            // MT5 Core Fields - Use REAL MT5 Login from Ireland database
            'mt5_login' => $mt5User->Login, // Store REAL MT5 login from Ireland database
            'mt5_timestamp' => $mt5User->Timestamp,
            'mt5_group' => $mt5User->Group,
            'mt5_cert_serial_number' => $mt5User->CertSerialNumber,
            'mt5_rights' => $mt5User->Rights,
            'mt5_registration' => $this->parseTimestamp($mt5User->Registration),
            'mt5_last_access' => $this->parseTimestamp($mt5User->LastAccess),
            'mt5_last_pass_change' => $this->parseTimestamp($mt5User->LastPassChange),

            // MT5 Personal Information
            'mt5_first_name' => $mt5User->FirstName,
            'mt5_last_name' => $mt5User->LastName,
            'mt5_middle_name' => $mt5User->MiddleName,
            'mt5_company' => $mt5User->Company,
            'mt5_account' => $mt5User->Account,
            'mt5_country' => $mt5User->Country,
            'mt5_language' => $mt5User->Language,
            'mt5_client_id' => $mt5User->ClientID,

            // MT5 Address Information
            'mt5_city' => $mt5User->City,
            'mt5_state' => $mt5User->State,
            'mt5_zip_code' => $mt5User->ZipCode,
            'mt5_address' => $mt5User->Address,
            'mt5_phone' => $mt5User->Phone,
            'mt5_email' => $mt5User->Email,
            'mt5_id' => $mt5User->ID,
            'mt5_status' => $mt5User->Status,
            'mt5_comment' => $mt5User->Comment,
            'mt5_color' => $mt5User->Color,
            'mt5_phone_password' => $mt5User->PhonePassword,

            // MT5 Trading Information
            'mt5_leverage' => $mt5User->Leverage,
            'mt5_agent' => $mt5User->Agent,
            'mt5_trade_accounts' => $mt5User->TradeAccounts,
            'mt5_limit_positions' => $mt5User->LimitPositions,
            'mt5_limit_orders' => $mt5User->LimitOrders,
            'mt5_lead_campaign' => $mt5User->LeadCampaign,
            'mt5_lead_source' => $mt5User->LeadSource,
            'mt5_timestamp_trade' => $mt5User->TimestampTrade,

            // MT5 Financial Information
            'mt5_balance' => $mt5User->Balance,
            'mt5_credit' => $mt5User->Credit,
            'mt5_equity' => $mt5User->Balance + $mt5User->Credit, // Calculated
            'mt5_interest_rate' => $mt5User->InterestRate,
            'mt5_commission_daily' => $mt5User->CommissionDaily,
            'mt5_commission_monthly' => $mt5User->CommissionMonthly,
            'mt5_balance_prev_day' => $mt5User->BalancePrevDay,
            'mt5_balance_prev_month' => $mt5User->BalancePrevMonth,
            'mt5_equity_prev_day' => $mt5User->EquityPrevDay,
            'mt5_equity_prev_month' => $mt5User->EquityPrevMonth,

            // MT5 Additional Information
            'mt5_name' => $mt5User->Name,
            'mt5_mqid' => $mt5User->MQID,
            'mt5_last_ip' => $mt5User->LastIP,
            'mt5_api_data' => $mt5User->ApiData,
            'mt5_currency' => $this->extractCurrencyFromGroup($mt5User->Group),
            'mt5_synced_at' => now(),

            // CRITICAL FIX: Use MT5 Registration date as created_at for correct user registration dates
            'created_at' => $this->parseTimestamp($mt5User->Registration) ?: now(),
            'updated_at' => now(),
        ];
    }

    // REMOVED: generateMt5Login() function - We now use REAL MT5 Login from Ireland database

    /**
     * Generate unique username from MT5 data - IMPROVED VERSION
     * FIXED: Issue 3 - Clean username generation without weird suffixes
     */
    private function generateUsername($mt5User)
    {
        // Priority 1: Use full name if available
        if (!empty($mt5User->FirstName) && !empty($mt5User->LastName)) {
            $base = strtolower(trim($mt5User->FirstName . $mt5User->LastName));
            $base = preg_replace('/[^a-z0-9]/', '', $base);

            if (strlen($base) >= 3) {
                $username = '@' . $base;
                $counter = 1;

                while (User::where('username', $username)->exists()) {
                    $username = '@' . $base . $counter;
                    $counter++;
                }

                return $username;
            }
        }

        // Priority 2: Use first name only if last name is empty
        if (!empty($mt5User->FirstName)) {
            $base = strtolower(trim($mt5User->FirstName));
            $base = preg_replace('/[^a-z0-9]/', '', $base);

            if (strlen($base) >= 3) {
                $username = '@' . $base;
                $counter = 1;

                while (User::where('username', $username)->exists()) {
                    $username = '@' . $base . $counter;
                    $counter++;
                }

                return $username;
            }
        }

        // Priority 3: Use email prefix if name is not available
        if (!empty($mt5User->Email) && strpos($mt5User->Email, '@') !== false) {
            $emailPrefix = strtolower(explode('@', $mt5User->Email)[0]);
            $emailPrefix = preg_replace('/[^a-z0-9]/', '', $emailPrefix);

            if (strlen($emailPrefix) >= 3) {
                $username = '@' . $emailPrefix;
                $counter = 1;

                while (User::where('username', $username)->exists()) {
                    $username = '@' . $emailPrefix . $counter;
                    $counter++;
                }

                return $username;
            }
        }

        // Fallback: Use MT5 login number
        $username = '@user' . $mt5User->Login;
        $counter = 1;

        while (User::where('username', $username)->exists()) {
            $username = '@user' . $mt5User->Login . $counter;
            $counter++;
        }

        return $username;
    }

    /**
     * Extract currency from MT5 group name
     */
    private function extractCurrencyFromGroup($group)
    {
        if (strpos($group, 'USD') !== false) return 'USD';
        if (strpos($group, 'EUR') !== false) return 'EUR';
        if (strpos($group, 'GBP') !== false) return 'GBP';
        return 'USD'; // Default
    }

    /**
     * Parse MT5 timestamp to Carbon - Enhanced for MySQL compatibility
     */
    private function parseTimestamp($timestamp)
    {
        if (empty($timestamp) || $timestamp === '1900-01-01 00:00:00' || $timestamp === '0000-00-00 00:00:00') {
            return null;
        }

        try {
            $carbon = Carbon::parse($timestamp);
            // Return MySQL-compatible datetime string
            return $carbon->format('Y-m-d H:i:s');
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Get country code from country name
     */
    private function getCountryCode($country)
    {
        if (empty($country)) {
            return 'PK'; // Default to Pakistan
        }

        $countryCodes = [
            'PK' => 'PK',
            'Pakistan' => 'PK',
            'United States' => 'US',
            'United Kingdom' => 'GB',
            'Germany' => 'DE',
            'France' => 'FR',
            'Guatemala' => 'GT',
            'India' => 'IN',
            'Bangladesh' => 'BD',
            'Malaysia' => 'MY',
            'Singapore' => 'SG',
            'Indonesia' => 'ID',
            'Thailand' => 'TH',
            'Philippines' => 'PH',
            'Vietnam' => 'VN',
            'China' => 'CN',
            'Japan' => 'JP',
            'South Korea' => 'KR',
            'Australia' => 'AU',
            'Canada' => 'CA',
            'Brazil' => 'BR',
            'Mexico' => 'MX',
            'Argentina' => 'AR',
            'Chile' => 'CL',
            'Colombia' => 'CO',
            'Peru' => 'PE',
            'Venezuela' => 'VE',
            'South Africa' => 'ZA',
            'Nigeria' => 'NG',
            'Egypt' => 'EG',
            'Morocco' => 'MA',
            'Kenya' => 'KE',
            'Ghana' => 'GH',
            'Turkey' => 'TR',
            'Russia' => 'RU',
            'Ukraine' => 'UA',
            'Poland' => 'PL',
            'Czech Republic' => 'CZ',
            'Hungary' => 'HU',
            'Romania' => 'RO',
            'Bulgaria' => 'BG',
            'Croatia' => 'HR',
            'Serbia' => 'RS',
            'Bosnia and Herzegovina' => 'BA',
            'Albania' => 'AL',
            'Greece' => 'GR',
            'Italy' => 'IT',
            'Spain' => 'ES',
            'Portugal' => 'PT',
            'Netherlands' => 'NL',
            'Belgium' => 'BE',
            'Switzerland' => 'CH',
            'Austria' => 'AT',
            'Sweden' => 'SE',
            'Norway' => 'NO',
            'Denmark' => 'DK',
            'Finland' => 'FI',
            'Ireland' => 'IE',
            'Iceland' => 'IS',
        ];

        // Try exact match first
        if (isset($countryCodes[$country])) {
            return $countryCodes[$country];
        }

        // Try case-insensitive match
        foreach ($countryCodes as $name => $code) {
            if (strcasecmp($name, $country) === 0) {
                return $code;
            }
        }

        return 'PK'; // Default to Pakistan
    }

    /**
     * Format mobile number from MT5 phone data
     */
    private function formatMobile($phone)
    {
        if (empty($phone)) {
            return null;
        }

        // Remove any non-numeric characters except +
        $phone = preg_replace('/[^\d+]/', '', $phone);

        // Remove leading + if present
        $phone = ltrim($phone, '+');

        return $phone ?: null;
    }

    /**
     * Format address from MT5 user data
     */
    private function formatAddress($mt5User)
    {
        return json_encode([
            'address' => $mt5User->Address ?: '',
            'city' => $mt5User->City ?: '',
            'state' => $mt5User->State ?: '',
            'zip' => $mt5User->ZipCode ?: '',
            'country' => $mt5User->Country ?: '',
        ]);
    }

    /**
     * Fast bulk synchronization with optimized performance
     * Processes 1000+ users per batch with minimal memory usage
     */
    private function fastBulkSync($query, $dryRun = false, $force = false, $replaceAll = false)
    {
        $this->info("🚀 FAST BULK SYNC: Starting high-performance synchronization");

        $processed = 0;
        $created = 0;
        $updated = 0;
        $errors = 0;
        $batchSize = 1000; // Process 1000 users per batch

        $startTime = microtime(true);

        try {
            if ($replaceAll && !$dryRun) {
                // For replace-all mode, use ultra-fast bulk insert
                $this->info("🗑️ REPLACE-ALL MODE: Using ultra-fast bulk operations");

                // Get all MT5 users in chunks and bulk insert
                $query->orderBy('Login')->chunk($batchSize, function ($mt5Users) use (&$processed, &$created, &$errors, $dryRun) {
                    $bulkData = [];

                    foreach ($mt5Users as $mt5User) {
                        try {
                            $userData = $this->mapMt5UserToLocal($mt5User);
                            $bulkData[] = $userData;
                            $processed++;
                        } catch (\Exception $e) {
                            $errors++;
                            Log::error('Fast Bulk Sync Error', [
                                'mt5_login' => $mt5User->Login,
                                'error' => $e->getMessage()
                            ]);
                        }
                    }

                    if (!empty($bulkData) && !$dryRun) {
                        // Bulk insert with ignore duplicates
                        User::insertOrIgnore($bulkData);
                        $created += count($bulkData);
                    }

                    $this->info("📈 BULK: Processed {$processed} users, Created {$created}, Errors {$errors}");
                });

            } else {
                // Smart sync mode - optimized for updates and new users
                $this->info("🧠 SMART SYNC MODE: Optimized for mixed operations");

                // First, get all existing users with MT5 logins for fast lookup
                $existingUsers = User::whereNotNull('mt5_login')
                    ->pluck('mt5_login', 'id')
                    ->toArray();

                $existingEmails = User::whereNull('mt5_login')
                    ->pluck('email', 'id')
                    ->toArray();

                $this->info("📊 Found " . count($existingUsers) . " users with MT5 logins, " . count($existingEmails) . " without");

                $query->orderBy('Login')->chunk($batchSize, function ($mt5Users) use (&$processed, &$created, &$updated, &$errors, $dryRun, $force, $existingUsers, $existingEmails) {
                    $bulkInserts = [];
                    $bulkUpdates = [];

                    foreach ($mt5Users as $mt5User) {
                        try {
                            $processed++;

                            // Check if user exists by MT5 login
                            $existingUserId = array_search($mt5User->Login, $existingUsers);

                            if ($existingUserId !== false) {
                                // User exists with this MT5 login - prepare for bulk update
                                $updateData = $this->prepareBulkUpdateData($mt5User);
                                $updateData['id'] = $existingUserId;
                                $bulkUpdates[] = $updateData;
                                $updated++;

                            } else {
                                // Check if user exists by email (without MT5 login)
                                $emailUserId = array_search($mt5User->Email, $existingEmails);

                                if ($emailUserId !== false) {
                                    // User exists by email - update with MT5 data
                                    $updateData = $this->prepareBulkUpdateData($mt5User);
                                    $updateData['id'] = $emailUserId;
                                    $bulkUpdates[] = $updateData;
                                    $updated++;

                                    // Remove from email lookup to prevent duplicates
                                    unset($existingEmails[$emailUserId]);

                                } else {
                                    // New user - prepare for bulk insert
                                    $userData = $this->mapMt5UserToLocal($mt5User);
                                    $bulkInserts[] = $userData;
                                    $created++;
                                }
                            }

                        } catch (\Exception $e) {
                            $errors++;
                            Log::error('Fast Bulk Sync Error', [
                                'mt5_login' => $mt5User->Login,
                                'error' => $e->getMessage()
                            ]);
                        }
                    }

                    // Execute bulk operations
                    if (!$dryRun) {
                        // Bulk insert new users
                        if (!empty($bulkInserts)) {
                            User::insertOrIgnore($bulkInserts);
                        }

                        // Bulk update existing users
                        if (!empty($bulkUpdates)) {
                            $this->executeBulkUpdates($bulkUpdates);
                        }
                    }

                    $this->info("📈 SMART: Processed {$processed} | Created {$created} | Updated {$updated} | Errors {$errors}");
                });
            }

            $endTime = microtime(true);
            $duration = round($endTime - $startTime, 2);
            $rate = $processed > 0 ? round($processed / $duration, 2) : 0;

            $this->info("⚡ FAST BULK SYNC COMPLETED in {$duration}s ({$rate} users/sec)");

            return [
                'processed' => $processed,
                'created' => $created,
                'updated' => $updated,
                'errors' => $errors,
                'duration' => $duration,
                'rate' => $rate
            ];

        } catch (\Exception $e) {
            $this->error("💥 Fast Bulk Sync Failed: " . $e->getMessage());
            Log::error('Fast Bulk Sync Failed', ['error' => $e->getMessage()]);

            return [
                'processed' => $processed,
                'created' => $created,
                'updated' => $updated,
                'errors' => $errors + 1
            ];
        }
    }

    /**
     * Prepare data for bulk update operations - COMPREHENSIVE FIELD MAPPING
     */
    private function prepareBulkUpdateData($mt5User)
    {
        return [
            // Basic CRM fields
            'firstname' => $mt5User->FirstName ?: 'Unknown',
            'lastname' => $mt5User->LastName ?: 'User',
            'mobile' => $this->formatMobile($mt5User->Phone),
            'country_code' => $this->getCountryCode($mt5User->Country),
            'address' => $this->formatAddress($mt5User),

            // MT5 Core Fields - COMPLETE MAPPING FOR UPDATES
            'mt5_login' => $mt5User->Login,
            'mt5_timestamp' => $mt5User->Timestamp,
            'mt5_group' => $mt5User->Group ?: '',
            'mt5_cert_serial_number' => $mt5User->CertSerialNumber,
            'mt5_rights' => $mt5User->Rights,
            'mt5_registration' => $this->parseTimestamp($mt5User->Registration),
            'mt5_last_access' => $this->parseTimestamp($mt5User->LastAccess),
            'mt5_last_pass_change' => $this->parseTimestamp($mt5User->LastPassChange),

            // MT5 Personal Information
            'mt5_first_name' => $mt5User->FirstName,
            'mt5_last_name' => $mt5User->LastName,
            'mt5_middle_name' => $mt5User->MiddleName,
            'mt5_company' => $mt5User->Company,
            'mt5_account' => $mt5User->Account,
            'mt5_country' => $mt5User->Country,
            'mt5_language' => $mt5User->Language,
            'mt5_client_id' => $mt5User->ClientID,

            // MT5 Address Information
            'mt5_city' => $mt5User->City,
            'mt5_state' => $mt5User->State,
            'mt5_zip_code' => $mt5User->ZipCode,
            'mt5_address' => $mt5User->Address,
            'mt5_phone' => $mt5User->Phone,
            'mt5_email' => $mt5User->Email,
            'mt5_id' => $mt5User->ID,
            'mt5_status' => $mt5User->Status,
            'mt5_comment' => $mt5User->Comment,
            'mt5_color' => $mt5User->Color,
            'mt5_phone_password' => $mt5User->PhonePassword,

            // MT5 Trading Information
            'mt5_leverage' => intval($mt5User->Leverage ?: 100),
            'mt5_agent' => $mt5User->Agent,
            'mt5_trade_accounts' => $mt5User->TradeAccounts,
            'mt5_limit_positions' => $mt5User->LimitPositions,
            'mt5_limit_orders' => $mt5User->LimitOrders,
            'mt5_lead_campaign' => $mt5User->LeadCampaign,
            'mt5_lead_source' => $mt5User->LeadSource,
            'mt5_timestamp_trade' => $mt5User->TimestampTrade,

            // MT5 Financial Information
            'mt5_balance' => floatval($mt5User->Balance ?: 0),
            'mt5_credit' => floatval($mt5User->Credit ?: 0),
            'mt5_equity' => floatval(($mt5User->Balance ?: 0) + ($mt5User->Credit ?: 0)),
            'mt5_interest_rate' => $mt5User->InterestRate,
            'mt5_commission_daily' => $mt5User->CommissionDaily,
            'mt5_commission_monthly' => $mt5User->CommissionMonthly,
            'mt5_balance_prev_day' => $mt5User->BalancePrevDay,
            'mt5_balance_prev_month' => $mt5User->BalancePrevMonth,
            'mt5_equity_prev_day' => $mt5User->EquityPrevDay,
            'mt5_equity_prev_month' => $mt5User->EquityPrevMonth,

            // MT5 Additional Information
            'mt5_name' => $mt5User->Name,
            'mt5_mqid' => $mt5User->MQID,
            'mt5_last_ip' => $mt5User->LastIP,
            'mt5_api_data' => $mt5User->ApiData,
            'mt5_currency' => $this->extractCurrencyFromGroup($mt5User->Group),
            'mt5_synced_at' => now()->toDateTimeString(),
            'updated_at' => now()->toDateTimeString(),
        ];
    }

    /**
     * Execute bulk updates efficiently
     */
    private function executeBulkUpdates($bulkUpdates)
    {
        if (empty($bulkUpdates)) {
            return;
        }

        // Group updates by chunks to avoid memory issues
        $chunks = array_chunk($bulkUpdates, 100);

        foreach ($chunks as $chunk) {
            foreach ($chunk as $updateData) {
                $userId = $updateData['id'];
                unset($updateData['id']);

                User::where('id', $userId)->update($updateData);
            }
        }
    }

    /**
     * FIXED: Issue 2 - Consolidate duplicate email accounts after sync
     * This method ensures only ONE entry per email is shown in the main list
     * while preserving all MT5 accounts in the database
     */
    private function consolidateDuplicateEmails()
    {
        $this->info("🔄 Starting duplicate email consolidation...");

        $consolidated = 0;
        $errors = 0;

        try {
            // Find all duplicate emails
            $duplicateEmails = User::select('email')
                ->whereNotNull('email')
                ->where('email', '!=', '')
                ->groupBy('email')
                ->havingRaw('COUNT(*) > 1')
                ->pluck('email');

            $this->info("📊 Found " . count($duplicateEmails) . " duplicate email groups to process");

            foreach ($duplicateEmails as $email) {
                try {
                    // Get all users with this email, ordered by most recent first
                    $users = User::where('email', $email)
                        ->orderBy('created_at', 'desc')
                        ->orderBy('id', 'desc')
                        ->get();

                    if ($users->count() <= 1) {
                        continue; // Skip if no duplicates
                    }

                    // Keep the most recent user (first in the ordered list)
                    $keepUser = $users->first();
                    $duplicateUsers = $users->skip(1);

                    $this->line("📧 Processing email: {$email} ({$users->count()} accounts)");
                    $this->line("   ✅ Keeping: User ID {$keepUser->id} (Created: {$keepUser->created_at})");

                    // Transfer all important data from duplicates to the kept user
                    foreach ($duplicateUsers as $duplicateUser) {
                        $this->line("   🔄 Processing duplicate: User ID {$duplicateUser->id}");

                        // If the duplicate has MT5 data that the kept user doesn't have, transfer it
                        if ($duplicateUser->mt5_login && !$keepUser->mt5_login) {
                            $this->transferMT5Data($duplicateUser, $keepUser);
                            $this->line("   📊 Transferred MT5 data from duplicate to kept user");
                        }

                        // Update any referral relationships
                        User::where('ref_by', $duplicateUser->id)->update(['ref_by' => $keepUser->id]);

                        // Update any IB relationships
                        User::where('ib_parent_id', $duplicateUser->id)->update(['ib_parent_id' => $keepUser->id]);

                        // Transfer IB status if duplicate is an IB but kept user is not
                        if ($duplicateUser->partner == 1 && $keepUser->partner != 1) {
                            $keepUser->update([
                                'partner' => 1,
                                'ib_status' => $duplicateUser->ib_status,
                                'ib_type' => $duplicateUser->ib_type,
                                'ib_group_id' => $duplicateUser->ib_group_id,
                                'ib_commission_rate' => $duplicateUser->ib_commission_rate,
                                'ib_max_levels' => $duplicateUser->ib_max_levels,
                                'ib_approved_at' => $duplicateUser->ib_approved_at,
                                'ib_approved_by' => $duplicateUser->ib_approved_by,
                                'referral_code' => $duplicateUser->referral_code,
                            ]);
                            $this->line("   🤝 Transferred IB status to kept user");
                        }

                        // Delete the duplicate user
                        $duplicateUser->delete();
                        $this->line("   🗑️ Deleted duplicate user ID {$duplicateUser->id}");
                    }

                    $consolidated++;

                } catch (\Exception $e) {
                    $errors++;
                    $this->error("❌ Error consolidating email {$email}: " . $e->getMessage());
                }
            }

        } catch (\Exception $e) {
            $this->error("💥 Consolidation failed: " . $e->getMessage());
            $errors++;
        }

        return [
            'consolidated' => $consolidated,
            'errors' => $errors
        ];
    }

    /**
     * Transfer MT5 data from one user to another
     */
    private function transferMT5Data($fromUser, $toUser)
    {
        $mt5Fields = [
            'mt5_login', 'mt5_timestamp', 'mt5_group', 'mt5_cert_serial_number', 'mt5_rights',
            'mt5_registration', 'mt5_last_access', 'mt5_last_pass_change', 'mt5_first_name',
            'mt5_last_name', 'mt5_middle_name', 'mt5_company', 'mt5_account', 'mt5_country',
            'mt5_language', 'mt5_client_id', 'mt5_city', 'mt5_state', 'mt5_zip_code',
            'mt5_address', 'mt5_phone', 'mt5_email', 'mt5_id', 'mt5_status', 'mt5_comment',
            'mt5_color', 'mt5_phone_password', 'mt5_leverage', 'mt5_agent', 'mt5_trade_accounts',
            'mt5_limit_positions', 'mt5_limit_orders', 'mt5_lead_campaign', 'mt5_lead_source',
            'mt5_timestamp_trade', 'mt5_balance', 'mt5_credit', 'mt5_equity', 'mt5_interest_rate',
            'mt5_commission_daily', 'mt5_commission_monthly', 'mt5_balance_prev_day',
            'mt5_balance_prev_month', 'mt5_equity_prev_day', 'mt5_equity_prev_month',
            'mt5_name', 'mt5_mqid', 'mt5_last_ip', 'mt5_api_data', 'mt5_currency', 'mt5_synced_at'
        ];

        $updateData = [];
        foreach ($mt5Fields as $field) {
            if ($fromUser->$field !== null) {
                $updateData[$field] = $fromUser->$field;
            }
        }

        if (!empty($updateData)) {
            $toUser->update($updateData);
        }
    }

    /**
     * ISSUE 4 PERMANENT FIX: Ensure we always have the latest data from MT5
     */
    private function ensureLatestMT5Data()
    {
        $this->info("📊 Verifying latest MT5 data synchronization...");

        try {
            // Get the most recent MT5 data timestamp
            $latestMT5Timestamp = DB::connection('mbf-dbmt5')
                ->table('mt5_users')
                ->whereNotNull('Email')
                ->where('Email', '!=', '')
                ->max('Registration');

            // Get the most recent local sync timestamp
            $latestLocalSync = User::whereNotNull('mt5_synced_at')
                ->max('mt5_synced_at');

            $this->info("📅 Latest MT5 timestamp: {$latestMT5Timestamp}");
            $this->info("📅 Latest local sync: {$latestLocalSync}");

            // Check if we need to sync more recent data
            if (!$latestLocalSync || Carbon::parse($latestMT5Timestamp)->gt(Carbon::parse($latestLocalSync))) {
                $this->warn("⚠️ Found newer MT5 data that needs synchronization");

                // Sync recent data with minimum 1000 batch size for performance
                $recentMT5Users = DB::connection('mbf-dbmt5')
                    ->table('mt5_users')
                    ->whereNotNull('Email')
                    ->where('Email', '!=', '')
                    ->where('Registration', '>', $latestLocalSync ?: '1900-01-01')
                    ->orderBy('Registration', 'desc')
                    ->limit(1000) // Increased to 1000 for better performance
                    ->get();

                $this->info("🔄 Auto-sync: Processing {$recentMT5Users->count()} recent MT5 records...");

                $syncedCount = 0;
                $newUsersCount = 0;

                foreach ($recentMT5Users as $mt5User) {
                    $result = $this->syncUser($mt5User, false, true, false);
                    if ($result === 'created') {
                        $newUsersCount++;
                    }
                    $syncedCount++;
                }

                $this->info("✅ Auto-sync completed: {$syncedCount} processed, {$newUsersCount} new users added");

                // Log for verification
                Log::info('MT5 Auto-sync completed', [
                    'processed' => $syncedCount,
                    'new_users' => $newUsersCount,
                    'latest_mt5_timestamp' => $latestMT5Timestamp,
                    'latest_local_sync' => $latestLocalSync
                ]);
            } else {
                $this->info("✅ Local data is up to date with MT5");
            }

        } catch (\Exception $e) {
            $this->error("❌ Error ensuring latest MT5 data: " . $e->getMessage());
        }
    }

    /**
     * ISSUE 1 PERMANENT FIX: Enhanced duplicate consolidation that persists across all sync operations
     */
    private function consolidateDuplicateEmailsPermanent()
    {
        $this->info("🔄 Starting PERMANENT duplicate email consolidation...");

        $consolidated = 0;
        $errors = 0;

        try {
            // Find ALL duplicate emails with comprehensive data
            $duplicateEmails = DB::table('users')
                ->select('email', DB::raw('COUNT(*) as count'), DB::raw('GROUP_CONCAT(id ORDER BY created_at DESC) as user_ids'))
                ->whereNotNull('email')
                ->where('email', '!=', '')
                ->groupBy('email')
                ->having('count', '>', 1)
                ->get();

            $this->info("📊 Found " . count($duplicateEmails) . " duplicate email groups to consolidate permanently");

            foreach ($duplicateEmails as $emailGroup) {
                try {
                    $userIds = explode(',', $emailGroup->user_ids);
                    $keepUserId = $userIds[0]; // Most recent user (first in DESC order)
                    $duplicateUserIds = array_slice($userIds, 1);

                    $this->line("📧 Consolidating email: {$emailGroup->email} ({$emailGroup->count} accounts)");

                    // Get the user to keep and all duplicates
                    $keepUser = User::find($keepUserId);
                    $duplicateUsers = User::whereIn('id', $duplicateUserIds)->get();

                    if (!$keepUser) {
                        $this->error("❌ Could not find user to keep for email: {$emailGroup->email}");
                        continue;
                    }

                    // Consolidate all MT5 data into the kept user
                    $allMT5Logins = [];
                    $latestMT5Data = null;
                    $latestMT5Timestamp = null;

                    // Collect all MT5 data from duplicates
                    foreach ($duplicateUsers as $duplicateUser) {
                        if ($duplicateUser->mt5_login) {
                            $allMT5Logins[] = $duplicateUser->mt5_login;

                            // Keep the most recent MT5 data
                            if (!$latestMT5Timestamp ||
                                ($duplicateUser->mt5_synced_at && $duplicateUser->mt5_synced_at > $latestMT5Timestamp)) {
                                $latestMT5Data = $duplicateUser;
                                $latestMT5Timestamp = $duplicateUser->mt5_synced_at;
                            }
                        }

                        // Update referral relationships
                        User::where('ref_by', $duplicateUser->id)->update(['ref_by' => $keepUser->id]);

                        // Update IB relationships
                        User::where('ib_parent_id', $duplicateUser->id)->update(['ib_parent_id' => $keepUser->id]);

                        // Transfer IB status if needed
                        if ($duplicateUser->partner == 1 && $keepUser->partner != 1) {
                            $keepUser->update([
                                'partner' => 1,
                                'ib_status' => $duplicateUser->ib_status,
                                'ib_type' => $duplicateUser->ib_type,
                                'ib_group_id' => $duplicateUser->ib_group_id,
                                'ib_commission_rate' => $duplicateUser->ib_commission_rate,
                                'ib_max_levels' => $duplicateUser->ib_max_levels,
                                'ib_approved_at' => $duplicateUser->ib_approved_at,
                                'ib_approved_by' => $duplicateUser->ib_approved_by,
                                'referral_code' => $duplicateUser->referral_code,
                            ]);
                        }
                    }

                    // Update the kept user with the latest MT5 data
                    if ($latestMT5Data && (!$keepUser->mt5_login || $latestMT5Timestamp > $keepUser->mt5_synced_at)) {
                        $this->transferMT5Data($latestMT5Data, $keepUser);
                        $this->line("   📊 Updated kept user with latest MT5 data");
                    }

                    // Store all MT5 logins for reference (for search functionality)
                    if (!empty($allMT5Logins)) {
                        $keepUser->update([
                            'all_mt5_accounts' => implode(',', array_unique($allMT5Logins))
                        ]);
                    }

                    // Delete duplicate users
                    User::whereIn('id', $duplicateUserIds)->delete();
                    $this->line("   🗑️ Deleted " . count($duplicateUserIds) . " duplicate accounts");

                    $consolidated++;

                } catch (\Exception $e) {
                    $errors++;
                    $this->error("❌ Error consolidating email {$emailGroup->email}: " . $e->getMessage());
                }
            }

        } catch (\Exception $e) {
            $this->error("💥 Permanent consolidation failed: " . $e->getMessage());
            $errors++;
        }

        return [
            'consolidated' => $consolidated,
            'errors' => $errors
        ];
    }

    /**
     * Update sync statistics for monitoring and dashboard
     */
    private function updateSyncStatistics($processed, $created, $updated, $errors)
    {
        $stats = [
            'last_sync' => now(),
            'processed' => $processed,
            'created' => $created,
            'updated' => $updated,
            'errors' => $errors,
            'total_users' => User::count(),
            'mt5_users' => User::whereNotNull('mt5_login')->count(),
            'sync_status' => $errors > 0 ? 'warning' : 'success'
        ];

        Cache::put('mt5_sync_stats', $stats, 3600); // Store for 1 hour

        $this->info("📊 Updated sync statistics: {$processed} processed, {$created} created, {$updated} updated, {$errors} errors");
    }

    /**
     * Group MT5 users by email for duplicate consolidation
     */
    private function groupMT5UsersByEmail($query)
    {
        $emailGroups = [];

        $query->orderBy('Email')->orderBy('Registration', 'desc')->chunk(1000, function ($mt5Users) use (&$emailGroups) {
            foreach ($mt5Users as $mt5User) {
                $email = strtolower(trim($mt5User->Email));
                if (!isset($emailGroups[$email])) {
                    $emailGroups[$email] = [];
                }
                $emailGroups[$email][] = $mt5User;
            }
        });

        return $emailGroups;
    }

    /**
     * Sync email group with intelligent primary account selection - CRITICAL FIX FOR ISSUE 2
     */
    private function syncEmailGroup($email, $mt5Accounts, $dryRun = false, $force = false, $replaceAll = false)
    {
        if (empty($mt5Accounts)) {
            return 'skipped';
        }

        // CRITICAL FIX: Intelligent primary account selection based on account type hierarchy
        $primaryAccount = $this->selectPrimaryAccountByHierarchy($mt5Accounts);
        $additionalAccounts = array_filter($mt5Accounts, function($account) use ($primaryAccount) {
            return $account->Login !== $primaryAccount->Login;
        });

        // Check if user exists
        $existingUser = User::where('email', $email)->first();

        if ($existingUser) {
            // Update existing user with primary account data
            $this->updateUserMt5Data($existingUser, $primaryAccount, $dryRun);

            // CRITICAL FIX: Store ALL accounts including primary in user_accounts table
            if (!$dryRun) {
                $this->storeAllMT5AccountsEnhanced($existingUser, $mt5Accounts);
            }

            return 'updated';
        } else {
            // Create new user with primary account
            $userData = $this->mapMt5UserToLocal($primaryAccount);

            if ($dryRun) {
                return 'created';
            }

            $newUser = User::create($userData);

            // CRITICAL FIX: Store ALL accounts including primary in user_accounts table
            $this->storeAllMT5AccountsEnhanced($newUser, $mt5Accounts);

            return 'created';
        }
    }

    /**
     * Store additional MT5 accounts in UserAccounts table
     */
    private function storeAdditionalMT5Accounts($user, $additionalAccounts)
    {
        foreach ($additionalAccounts as $mt5Account) {
            // Check if account already exists
            $existingAccount = \App\Models\UserAccounts::where('User_Id', $user->id)
                ->where('Account', $mt5Account->Login)
                ->first();

            if (!$existingAccount) {
                \App\Models\UserAccounts::create([
                    'User_Id' => $user->id,
                    'Account' => $mt5Account->Login,
                    'Master_Password' => 'synced_from_mt5',
                    'Investor_Password' => 'synced_from_mt5',
                    'Phone_Password' => $mt5Account->PhonePassword ?? '',
                    'Group_Name' => $mt5Account->Group,
                    'Leverage' => $mt5Account->Leverage,
                    'Balance' => $mt5Account->Balance,
                    'Currency' => $this->extractCurrencyFromGroup($mt5Account->Group),
                    'created_at' => $this->parseTimestamp($mt5Account->Registration) ?: now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }

    /**
     * Enhanced fast bulk sync with duplicate consolidation
     */
    private function enhancedFastBulkSync($query, $dryRun = false, $force = false, $replaceAll = false)
    {
        $processed = 0;
        $created = 0;
        $updated = 0;
        $errors = 0;
        $consolidated = 0;

        try {
            // Group by email first
            $emailGroups = $this->groupMT5UsersByEmail($query);
            $this->info("📧 Processing " . count($emailGroups) . " unique emails in fast mode");

            foreach ($emailGroups as $email => $mt5Accounts) {
                try {
                    $result = $this->syncEmailGroup($email, $mt5Accounts, $dryRun, $force, $replaceAll);

                    if ($result === 'created') {
                        $created++;
                    } elseif ($result === 'updated') {
                        $updated++;
                    }

                    $processed += count($mt5Accounts);

                    if (count($mt5Accounts) > 1) {
                        $consolidated++;
                    }

                    if ($processed % 500 === 0) {
                        $this->info("📈 Fast Progress: {$processed} MT5 accounts, {$created} users created, {$updated} updated, {$consolidated} consolidated");
                    }

                } catch (\Exception $e) {
                    $errors++;
                    Log::error('Enhanced Fast Bulk Sync Error', [
                        'email' => $email,
                        'accounts_count' => count($mt5Accounts),
                        'error' => $e->getMessage()
                    ]);
                }
            }

        } catch (\Exception $e) {
            $this->error("❌ Enhanced fast bulk sync failed: " . $e->getMessage());
            $errors++;
        }

        return [
            'processed' => $processed,
            'created' => $created,
            'updated' => $updated,
            'errors' => $errors,
            'consolidated' => $consolidated
        ];
    }

    /**
     * CRITICAL FIX FOR ISSUE 2: Intelligent primary account selection
     * Preserves account type hierarchy instead of using newest account
     */
    private function selectPrimaryAccountByHierarchy($mt5Accounts)
    {
        // Define account type hierarchy (highest priority first)
        $accountTypeHierarchy = [
            'ib' => 1,          // IB accounts have highest priority
            'affiliate' => 2,    // Affiliate accounts second priority
            'multi-ib' => 3,     // Multi-IB accounts third priority
            'real' => 4,         // Real trading accounts fourth priority
            'demo' => 5,         // Demo accounts lowest priority
        ];

        // Sort accounts by hierarchy priority, then by registration date
        usort($mt5Accounts, function($a, $b) use ($accountTypeHierarchy) {
            $typeA = $this->getAccountTypeFromGroup($a->Group);
            $typeB = $this->getAccountTypeFromGroup($b->Group);

            $priorityA = $accountTypeHierarchy[$typeA] ?? 999;
            $priorityB = $accountTypeHierarchy[$typeB] ?? 999;

            // If same priority, use registration date (older accounts preferred for IB)
            if ($priorityA === $priorityB) {
                if ($typeA === 'ib' || $typeA === 'affiliate') {
                    // For IB accounts, prefer older registration (original IB status)
                    return strtotime($a->Registration) - strtotime($b->Registration);
                } else {
                    // For trading accounts, prefer newer registration
                    return strtotime($b->Registration) - strtotime($a->Registration);
                }
            }

            return $priorityA - $priorityB;
        });

        return $mt5Accounts[0]; // Return highest priority account
    }

    /**
     * Extract account type from MT5 group name
     */
    private function getAccountTypeFromGroup($group)
    {
        $group = strtolower($group);

        if (strpos($group, 'ib') !== false) {
            return 'ib';
        } elseif (strpos($group, 'affiliate') !== false) {
            return 'affiliate';
        } elseif (strpos($group, 'multi-ib') !== false) {
            return 'multi-ib';
        } elseif (strpos($group, 'demo') !== false) {
            return 'demo';
        } else {
            return 'real';
        }
    }

    /**
     * CRITICAL FIX: Store ALL MT5 accounts including primary account
     */
    private function storeAllMT5AccountsEnhanced($user, $allAccounts)
    {
        foreach ($allAccounts as $mt5Account) {
            // Check if account already exists
            $existingAccount = \App\Models\UserAccounts::where('User_Id', $user->id)
                ->where('Account', $mt5Account->Login)
                ->first();

            $accountType = $this->getAccountTypeFromGroup($mt5Account->Group);

            if (!$existingAccount) {
                \App\Models\UserAccounts::create([
                    'User_Id' => $user->id,
                    'Account' => $mt5Account->Login,
                    'Master_Password' => 'synced_from_mt5',
                    'Investor_Password' => 'synced_from_mt5',
                    'Phone_Password' => $mt5Account->PhonePassword ?? '',
                    'Group_Name' => $mt5Account->Group,
                    'Account_Type' => $accountType,
                    'Leverage' => $mt5Account->Leverage,
                    'Balance' => $mt5Account->Balance,
                    'Currency' => $this->extractCurrencyFromGroup($mt5Account->Group),
                    'created_at' => $this->parseTimestamp($mt5Account->Registration) ?: now(),
                    'updated_at' => now(),
                ]);
            } else {
                // Update existing account with latest data
                $existingAccount->update([
                    'Group_Name' => $mt5Account->Group,
                    'Account_Type' => $accountType,
                    'Leverage' => $mt5Account->Leverage,
                    'Balance' => $mt5Account->Balance,
                    'Currency' => $this->extractCurrencyFromGroup($mt5Account->Group),
                    'updated_at' => now(),
                ]);
            }
        }
    }

    /**
     * Enhanced method to store additional MT5 accounts with proper account type tracking
     */
    private function storeAdditionalMT5AccountsEnhanced($user, $additionalAccounts)
    {
        foreach ($additionalAccounts as $mt5Account) {
            // Check if account already exists
            $existingAccount = \App\Models\UserAccounts::where('User_Id', $user->id)
                ->where('Account', $mt5Account->Login)
                ->first();

            $accountType = $this->getAccountTypeFromGroup($mt5Account->Group);

            if (!$existingAccount) {
                \App\Models\UserAccounts::create([
                    'User_Id' => $user->id,
                    'Account' => $mt5Account->Login,
                    'Master_Password' => 'synced_from_mt5',
                    'Investor_Password' => 'synced_from_mt5',
                    'Phone_Password' => $mt5Account->PhonePassword ?? '',
                    'Group_Name' => $mt5Account->Group,
                    'Account_Type' => $accountType, // Store account type for admin display
                    'Leverage' => $mt5Account->Leverage,
                    'Balance' => $mt5Account->Balance,
                    'Currency' => $this->extractCurrencyFromGroup($mt5Account->Group),
                    'created_at' => $this->parseTimestamp($mt5Account->Registration) ?: now(),
                    'updated_at' => now(),
                ]);
            } else {
                // Update existing account with latest data
                $existingAccount->update([
                    'Group_Name' => $mt5Account->Group,
                    'Account_Type' => $accountType,
                    'Leverage' => $mt5Account->Leverage,
                    'Balance' => $mt5Account->Balance,
                    'Currency' => $this->extractCurrencyFromGroup($mt5Account->Group),
                    'updated_at' => now(),
                ]);
            }
        }
    }
}
