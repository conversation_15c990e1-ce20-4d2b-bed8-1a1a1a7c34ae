<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\IbCommission;
use App\Models\User;
use App\Services\IbCommissionService;
use App\Services\IbHierarchyService;
use App\Services\IbManagementService;
use App\Services\IbCommissionIntegrationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class IbController extends Controller
{
    protected $ibCommissionService;
    protected $ibHierarchyService;
    protected $ibManagementService;
    protected $commissionIntegrationService;

    public function __construct(IbCommissionIntegrationService $commissionIntegrationService)
    {
        parent::__construct(); // Call parent constructor to set activeTemplate
        $this->ibCommissionService = new IbCommissionService();
        $this->ibHierarchyService = new IbHierarchyService();
        $this->ibManagementService = new IbManagementService();
        $this->commissionIntegrationService = $commissionIntegrationService;
    }

    /**
     * Enhanced IB Dashboard
     */
    public function dashboard()
    {
        $user = Auth::user();
        
        if (!$user->isIb()) {
            $notify[] = ['error', 'You are not an approved IB'];
            return redirect()->route('user.home')->withNotify($notify);
        }

        $pageTitle = 'IB Dashboard';
        
        // Get dashboard data
        $dashboardData = $this->ibManagementService->getIbDashboardData($user->id);

        // Get real-time commission statistics
        $commissionStats = $this->commissionIntegrationService->getIbCommissionStats($user->id, '30days');

        // Get recent activity with MT5 integration
        $recentCommissions = $user->ibCommissionsEarned()
            ->with(['fromUser:id,username,firstname,lastname'])
            ->select('id', 'from_user_id', 'commission_amount', 'symbol', 'volume', 'status', 'deal_time', 'created_at', 'mt5_deal_id', 'level')
            ->latest('deal_time')
            ->limit(10)
            ->get();

        // Get network commission flow
        $networkFlow = $this->commissionIntegrationService->getNetworkCommissionFlow($user->id, '30days');

        // Get performance metrics
        $performanceMetrics = $this->ibManagementService->getIbPerformanceMetrics($user->id);

        return view($this->activeTemplate . 'user.ib.dashboard', compact(
            'pageTitle',
            'user',
            'dashboardData',
            'commissionStats',
            'recentCommissions',
            'networkFlow',
            'performanceMetrics'
        ));
    }

    /**
     * IB Commissions page with real MT5 data
     */
    public function commissions(Request $request)
    {
        $user = Auth::user();

        if (!$user->isIb()) {
            $notify[] = ['error', 'You are not an approved IB'];
            return redirect()->route('user.home')->withNotify($notify);
        }

        $pageTitle = 'My Commissions';

        // Get commission filters
        $startDate = $request->start_date;
        $endDate = $request->end_date;
        $status = $request->status;
        $symbol = $request->symbol;

        // Get real MT5 commission data
        $mt5CommissionData = $this->getMT5CommissionData($user->mt5_login, $startDate, $endDate, $symbol);

        // Format commissions for the view
        $commissions = $mt5CommissionData['commissions'];

        // Create commission summary
        $commissionSummary = [
            'basic_stats' => [
                'total_commissions' => $mt5CommissionData['total_commission'],
                'paid_commissions' => $mt5CommissionData['total_commission'], // All MT5 commissions are paid
                'pending_commissions' => 0,
                'total_trades' => $mt5CommissionData['commission_count']
            ],
            'level_breakdown' => collect(),
            'symbol_breakdown' => $mt5CommissionData['symbol_breakdown']
        ];

        // Get available symbols for filter
        $symbols = $mt5CommissionData['symbols'];

        return view($this->activeTemplate . 'user.ib.commissions', compact(
            'pageTitle',
            'commissions',
            'commissionSummary',
            'symbols',
            'startDate',
            'endDate',
            'status',
            'symbol'
        ));
    }

    /**
     * Get real MT5 commission data
     */
    private function getMT5CommissionData($mt5Login, $startDate = null, $endDate = null, $symbol = null)
    {
        if (!$mt5Login) {
            return [
                'total_commission' => 0,
                'commission_count' => 0,
                'commissions' => collect(),
                'symbols' => collect(),
                'symbol_breakdown' => collect()
            ];
        }

        try {
            $query = \DB::connection('mbf-dbmt5')
                ->table('mt5_deals_2025')
                ->where('Login', $mt5Login)
                ->where('Action', 18); // Commission action

            // Apply date filters
            if ($startDate) {
                $query->where('Time', '>=', $startDate . ' 00:00:00');
            }
            if ($endDate) {
                $query->where('Time', '<=', $endDate . ' 23:59:59');
            }
            if ($symbol) {
                $query->where('Symbol', $symbol);
            }

            $commissionDeals = $query
                ->select('Deal', 'Login', 'Time', 'Symbol', 'Volume', 'Profit as Commission', 'Comment')
                ->orderBy('Time', 'desc')
                ->get();

            // Format for pagination
            $currentPage = request()->get('page', 1);
            $perPage = 20;
            $commissions = new \Illuminate\Pagination\LengthAwarePaginator(
                $commissionDeals->forPage($currentPage, $perPage),
                $commissionDeals->count(),
                $perPage,
                $currentPage,
                ['path' => request()->url(), 'pageName' => 'page']
            );

            // Add required properties for the view
            $commissions->getCollection()->transform(function ($deal) {
                $deal->trade_closed_at = $deal->Time;
                $deal->trade_id = $deal->Deal;
                $deal->fromUser = (object)['username' => 'MT5 Client'];
                $deal->symbol = $deal->Symbol;
                $deal->volume = $deal->Volume;
                $deal->level = 1;
                $deal->commission_rate = 0;
                $deal->commission_amount = $deal->Commission;
                $deal->status = 'paid';
                return $deal;
            });

            // Get symbols for filter
            $symbols = \DB::connection('mbf-dbmt5')
                ->table('mt5_deals_2025')
                ->where('Login', $mt5Login)
                ->where('Action', 18)
                ->distinct()
                ->pluck('Symbol')
                ->sort();

            // Symbol breakdown
            $symbolBreakdown = $commissionDeals->groupBy('Symbol')->map(function ($deals, $symbol) {
                return (object)[
                    'symbol' => $symbol,
                    'total_trades' => $deals->count(),
                    'total_volume' => $deals->sum('Volume'),
                    'total_amount' => $deals->sum('Commission')
                ];
            })->sortByDesc('total_amount');

            return [
                'total_commission' => $commissionDeals->sum('Commission'),
                'commission_count' => $commissionDeals->count(),
                'commissions' => $commissions,
                'symbols' => $symbols,
                'symbol_breakdown' => $symbolBreakdown
            ];

        } catch (\Exception $e) {
            \Log::error("Failed to get MT5 commission data", [
                'mt5_login' => $mt5Login,
                'error' => $e->getMessage()
            ]);

            return [
                'total_commission' => 0,
                'commission_count' => 0,
                'commissions' => collect(),
                'symbols' => collect(),
                'symbol_breakdown' => collect()
            ];
        }
    }

    /**
     * Sub IBs management (for Master IBs)
     */
    public function subIbs()
    {
        $user = Auth::user();
        
        if (!$user->isMasterIb()) {
            $notify[] = ['error', 'Only Master IBs can access this page'];
            return redirect()->route('user.ib.dashboard')->withNotify($notify);
        }

        $pageTitle = 'My Sub IBs';
        
        // Get Sub IBs
        $subIbs = $user->ibChildren()
            ->whereNotNull('ib_status')
            ->with([
                'ibGroup:id,name,commission_multiplier',
                'ibCommissionsEarned' => function($q) {
                    $q->select('id', 'to_ib_user_id', 'commission_amount', 'status', 'created_at');
                }
            ])
            ->select('id', 'username', 'firstname', 'lastname', 'email', 'ib_status', 'ib_type', 'ib_group_id', 'ib_approved_at', 'created_at')
            ->paginate(20);

        // Get Sub IB statistics
        $subIbStats = [];
        foreach ($subIbs as $subIb) {
            $stats = $subIb->getIbStats();
            $subIbStats[$subIb->id] = $stats;
        }

        return view($this->activeTemplate . 'user.ib.sub_ibs', compact(
            'pageTitle', 
            'subIbs', 
            'subIbStats'
        ));
    }

    /**
     * IB Hierarchy view
     */
    public function hierarchy()
    {
        $user = Auth::user();
        
        if (!$user->isIb()) {
            $notify[] = ['error', 'You are not an approved IB'];
            return redirect()->route('user.home')->withNotify($notify);
        }

        $pageTitle = 'IB Hierarchy';
        
        // Get hierarchy tree
        $hierarchyTree = $this->ibHierarchyService->getIbHierarchyTree($user->id);
        $hierarchyStats = $this->ibHierarchyService->getHierarchyStats($user->id);

        // Get upline hierarchy
        $uplineHierarchy = $user->getIbHierarchy();

        return view($this->activeTemplate . 'user.ib.hierarchy', compact(
            'pageTitle', 
            'hierarchyTree', 
            'hierarchyStats',
            'uplineHierarchy',
            'user'
        ));
    }

    /**
     * IB Reports
     */
    public function reports(Request $request)
    {
        $user = Auth::user();
        
        if (!$user->isIb()) {
            $notify[] = ['error', 'You are not an approved IB'];
            return redirect()->route('user.home')->withNotify($notify);
        }

        $pageTitle = 'IB Reports';
        
        $period = $request->get('period', '30days');
        
        // Get performance metrics
        $performanceMetrics = $this->ibManagementService->getIbPerformanceMetrics($user->id, $period);
        
        // Get commission breakdown
        $levelBreakdown = IbCommission::getLevelBreakdown($user->id);
        $symbolBreakdown = IbCommission::getSymbolBreakdown($user->id);

        // Get monthly commission data for chart
        $monthlyCommissions = IbCommission::forIb($user->id)
            ->selectRaw('YEAR(created_at) as year, MONTH(created_at) as month, SUM(commission_amount) as total')
            ->where('created_at', '>=', now()->subMonths(12))
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get();

        return view($this->activeTemplate . 'user.ib.reports', compact(
            'pageTitle', 
            'performanceMetrics',
            'levelBreakdown',
            'symbolBreakdown',
            'monthlyCommissions',
            'period'
        ));
    }

    /**
     * Get referral link
     */
    public function getReferralLink()
    {
        $user = Auth::user();
        
        if (!$user->isIb()) {
            return response()->json(['error' => 'You are not an approved IB'], 403);
        }

        $referralLink = route('register') . '?ref=' . $user->referral_code;
        
        return response()->json([
            'success' => true,
            'referral_code' => $user->referral_code,
            'referral_link' => $referralLink
        ]);
    }

    /**
     * Update IB settings
     */
    public function updateSettings(Request $request)
    {
        $user = Auth::user();
        
        if (!$user->isIb()) {
            $notify[] = ['error', 'You are not an approved IB'];
            return redirect()->route('user.home')->withNotify($notify);
        }

        $request->validate([
            'ib_commission_rate' => 'nullable|numeric|min:0|max:100',
            'ib_max_levels' => 'nullable|integer|min:1|max:10'
        ]);

        try {
            $this->ibManagementService->updateIbSettings($user->id, $request->only([
                'ib_commission_rate',
                'ib_max_levels'
            ]));

            $notify[] = ['success', 'IB settings updated successfully'];
            
        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to update settings: ' . $e->getMessage()];
        }

        return back()->withNotify($notify);
    }

    /**
     * Network Tree Visualization
     */
    public function network()
    {
        $user = Auth::user();

        if (!$user->isIb()) {
            $notify[] = ['error', 'You are not an approved IB'];
            return redirect()->route('user.home')->withNotify($notify);
        }

        $pageTitle = 'My Network Tree';

        // Build comprehensive network tree with real-time data
        $networkTree = $this->buildNetworkTree($user);

        // Get network statistics
        $networkStats = $this->getNetworkStatistics($user);

        // Get recent network activity
        $recentActivity = $this->getRecentNetworkActivity($user);

        return view($this->activeTemplate . 'user.ib.network', compact(
            'pageTitle',
            'networkTree',
            'networkStats',
            'recentActivity',
            'user'
        ));
    }

    /**
     * Build hierarchical network tree with real-time MT5 data
     */
    private function buildNetworkTree($rootUser, $level = 0, $maxDepth = 5)
    {
        if ($level >= $maxDepth) {
            return [];
        }

        $tree = [];

        // Get direct referrals
        $directReferrals = User::where('ref_by', $rootUser->id)
            ->with(['ibGroup', 'ibCommissionsEarned'])
            ->get();

        foreach ($directReferrals as $referral) {
            $nodeData = [
                'user' => $referral,
                'level' => $level + 1,
                'is_ib' => $referral->isIb(),
                'ib_type' => $referral->ib_type,
                'total_commission' => $this->getUserTotalCommission($referral->id),
                'mt5_balance' => $referral->mt5_balance ?? 0,
                'mt5_login' => $referral->mt5_login,
                'children' => []
            ];

            // Recursively build children if this user is an IB
            if ($referral->isIb()) {
                $nodeData['children'] = $this->buildNetworkTree($referral, $level + 1, $maxDepth);
            }

            $tree[] = $nodeData;
        }

        return $tree;
    }

    /**
     * Get network statistics
     */
    private function getNetworkStatistics($user)
    {
        $stats = [
            'total_referrals' => 0,
            'total_ibs' => 0,
            'total_commission' => 0,
            'total_volume' => 0,
            'active_traders' => 0,
            'levels' => []
        ];

        // Get all network users recursively
        $allNetworkUsers = $this->getAllNetworkUsers($user);

        $stats['total_referrals'] = count($allNetworkUsers);

        foreach ($allNetworkUsers as $networkUser) {
            if ($networkUser['user']->isIb()) {
                $stats['total_ibs']++;
            }

            // Get commission data for this user
            $commissionData = IbCommission::where('to_ib_user_id', $user->id)
                ->where('from_user_id', $networkUser['user']->id)
                ->selectRaw('SUM(commission_amount) as total_commission, SUM(volume) as total_volume, COUNT(DISTINCT from_user_id) as active_traders')
                ->first();

            if ($commissionData) {
                $stats['total_commission'] += $commissionData->total_commission ?? 0;
                $stats['total_volume'] += $commissionData->total_volume ?? 0;
                $stats['active_traders'] += $commissionData->active_traders ?? 0;
            }

            // Track by level
            $level = $networkUser['level'];
            if (!isset($stats['levels'][$level])) {
                $stats['levels'][$level] = ['count' => 0, 'ibs' => 0];
            }
            $stats['levels'][$level]['count']++;
            if ($networkUser['user']->isIb()) {
                $stats['levels'][$level]['ibs']++;
            }
        }

        return $stats;
    }

    /**
     * Get all network users recursively
     */
    private function getAllNetworkUsers($user, $level = 0, $maxDepth = 5)
    {
        if ($level >= $maxDepth) {
            return [];
        }

        $allUsers = [];

        $directReferrals = User::where('ref_by', $user->id)->get();

        foreach ($directReferrals as $referral) {
            $allUsers[] = [
                'user' => $referral,
                'level' => $level + 1
            ];

            // Get children recursively
            $children = $this->getAllNetworkUsers($referral, $level + 1, $maxDepth);
            $allUsers = array_merge($allUsers, $children);
        }

        return $allUsers;
    }

    /**
     * Get user total commission
     */
    private function getUserTotalCommission($userId)
    {
        return IbCommission::where('from_user_id', $userId)
            ->sum('commission_amount') ?? 0;
    }

    /**
     * Get recent network activity
     */
    private function getRecentNetworkActivity($user)
    {
        return IbCommission::where('to_ib_user_id', $user->id)
            ->with(['fromUser'])
            ->orderBy('deal_time', 'desc')
            ->limit(20)
            ->get();
    }
}
