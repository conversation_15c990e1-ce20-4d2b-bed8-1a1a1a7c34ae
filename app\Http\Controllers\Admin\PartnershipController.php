<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AccountLevel;
use App\Models\IbLevel;
use App\Models\Symbol;
use App\Models\SymbolGroup;
use App\Models\RebateRule;
use App\Models\IbGroup;
use App\Models\IbResource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class PartnershipController extends Controller
{
    /**
     * Manage Levels - Account Types Management
     */
    public function manageLevels()
    {
        $pageTitle = 'Manage Account Levels';
        $accountLevels = AccountLevel::orderBy('name')->paginate(20);
        
        return view('admin.partnership.manage_levels', compact('pageTitle', 'accountLevels'));
    }

    /**
     * Store new account level
     */
    public function storeAccountLevel(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:account_levels,name',
            'platform_group_default' => 'required|string|max:255',
            'trading_server_live' => 'required|string|max:255',
            'leverage_options' => 'required|array',
            'leverage_options.*' => 'integer|min:1',
            'country_restrictions' => 'nullable|array',
            'tags' => 'nullable|array',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        if ($validator->fails()) {
            $notify[] = ['error', 'Validation failed: ' . $validator->errors()->first()];
            return back()->withNotify($notify)->withInput();
        }

        try {
            $data = $request->only([
                'name', 'platform_group_default', 'trading_server_live',
                'leverage_options', 'country_restrictions', 'tags'
            ]);
            
            $data['enable_separate_swap_free'] = $request->has('enable_separate_swap_free');
            $data['platform_group_swap_free'] = $request->platform_group_swap_free;
            
            // Handle image upload using system's fileUploader helper
            if ($request->hasFile('image')) {
                try {
                    $path = 'assets/images/account_levels';
                    $size = '100x100'; // Standard size for account level images
                    $filename = fileUploader($request->image, $path, $size);
                    $data['image'] = $filename;
                } catch (\Exception $exp) {
                    $notify[] = ['error', 'Couldn\'t upload the image'];
                    return back()->withNotify($notify)->withInput();
                }
            }

            AccountLevel::create($data);

            $notify[] = ['success', 'Account level created successfully'];
            return back()->withNotify($notify);

        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to create account level: ' . $e->getMessage()];
            return back()->withNotify($notify)->withInput();
        }
    }

    /**
     * Multi IB Levels Management
     */
    public function multiIbLevels()
    {
        $pageTitle = 'Multi IB Levels';
        $ibLevels = IbLevel::orderBy('level')->paginate(20);
        
        return view('admin.partnership.multi_ib_levels', compact('pageTitle', 'ibLevels'));
    }

    /**
     * Store new IB level
     */
    public function storeIbLevel(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'level' => 'required|integer|min:1|unique:ib_levels,level',
            'commission_percent' => 'required|numeric|min:0|max:100',
            'max_commission_percent' => 'required|numeric|min:0|max:100',
            'description' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            $notify[] = ['error', 'Validation failed: ' . $validator->errors()->first()];
            return back()->withNotify($notify)->withInput();
        }

        try {
            IbLevel::create($request->all());

            $notify[] = ['success', 'IB level created successfully'];
            return back()->withNotify($notify);

        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to create IB level: ' . $e->getMessage()];
            return back()->withNotify($notify)->withInput();
        }
    }

    /**
     * Symbols Management
     */
    public function symbols(Request $request)
    {
        $pageTitle = 'Symbols Management';
        
        $query = Symbol::query();
        
        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }
        
        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'inactive') {
                $query->inactive();
            }
        }
        
        // Filter by path
        if ($request->filled('path')) {
            $query->byPath($request->path);
        }
        
        $symbols = $query->orderBy('symbol', 'ASC')->paginate(15);
        
        // Get unique paths for filter dropdown
        $paths = Symbol::distinct()->pluck('path')->sort();
        
        return view('admin.partnership.symbols', compact('pageTitle', 'symbols', 'paths'));
    }

    /**
     * Toggle symbol status
     */
    public function toggleSymbolStatus($id)
    {
        try {
            $symbol = Symbol::findOrFail($id);
            $symbol->toggleStatus();

            $status = $symbol->status ? 'enabled' : 'disabled';

            // Return JSON response for AJAX calls
            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'status' => 'success',
                    'message' => "Symbol {$symbol->symbol} has been {$status}"
                ]);
            }

            $notify[] = ['success', "Symbol {$symbol->symbol} has been {$status}"];
            return back()->withNotify($notify);

        } catch (\Exception $e) {
            // Return JSON response for AJAX calls
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'status' => 'error',
                    'message' => 'Failed to toggle symbol status: ' . $e->getMessage()
                ], 500);
            }

            $notify[] = ['error', 'Failed to toggle symbol status: ' . $e->getMessage()];
            return back()->withNotify($notify);
        }
    }

    /**
     * Symbol Groups Management
     */
    public function symbolGroups()
    {
        $pageTitle = 'Symbol Groups';
        $symbolGroups = SymbolGroup::orderBy('name')->paginate(20);
        $allSymbols = Symbol::active()->orderBy('symbol')->get();

        return view('admin.partnership.symbol_groups', compact('pageTitle', 'symbolGroups', 'allSymbols'));
    }

    /**
     * Store new symbol group
     */
    public function storeSymbolGroup(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:symbol_groups,name',
            'description' => 'nullable|string',
            'symbols' => 'required|array|min:1',
            'symbols.*' => 'exists:symbols,id'
        ]);

        if ($validator->fails()) {
            $notify[] = ['error', 'Validation failed: ' . $validator->errors()->first()];
            return back()->withNotify($notify)->withInput();
        }

        try {
            $symbolGroup = SymbolGroup::create([
                'name' => $request->name,
                'description' => $request->description,
                'symbols_json' => $request->symbols
            ]);

            $notify[] = ['success', 'Symbol group created successfully'];
            return back()->withNotify($notify);

        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to create symbol group: ' . $e->getMessage()];
            return back()->withNotify($notify)->withInput();
        }
    }

    /**
     * Rebate Rules Management
     */
    public function rebateRules()
    {
        $pageTitle = 'Rebate Rules';
        $rebateRules = RebateRule::with(['ibGroup', 'symbolGroup'])->orderBy('id', 'desc')->paginate(20);
        $symbolGroups = SymbolGroup::active()->get();
        $ibGroups = IbGroup::active()->get();

        return view('admin.partnership.rebate_rules', compact(
            'pageTitle', 'rebateRules', 'symbolGroups', 'ibGroups'
        ));
    }

    /**
     * Store new rebate rule
     */
    public function storeRebateRule(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ib_group_id' => 'nullable|exists:ib_groups,id',
            'symbol_group_id' => 'nullable|exists:symbol_groups,id',
            'symbol' => 'nullable|string|max:255',
            'rebate_per_lot' => 'required|numeric|min:0',
            'min_volume' => 'required|numeric|min:0',
            'max_volume' => 'nullable|numeric|min:0'
        ]);

        if ($validator->fails()) {
            $notify[] = ['error', 'Validation failed: ' . $validator->errors()->first()];
            return back()->withNotify($notify)->withInput();
        }

        try {
            RebateRule::create($request->all());

            $notify[] = ['success', 'Rebate rule created successfully'];
            return back()->withNotify($notify);

        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to create rebate rule: ' . $e->getMessage()];
            return back()->withNotify($notify)->withInput();
        }
    }

    /**
     * Update account level
     */
    public function updateAccountLevel(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:account_levels,name,' . $id,
            'platform_group_default' => 'required|string|max:255',
            'trading_server_live' => 'required|string|max:255',
            'leverage_options' => 'required|array',
            'leverage_options.*' => 'integer|min:1',
            'country_restrictions' => 'nullable|string',
            'tags' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        if ($validator->fails()) {
            $notify[] = ['error', 'Validation failed: ' . $validator->errors()->first()];
            return back()->withNotify($notify)->withInput();
        }

        try {
            $accountLevel = AccountLevel::findOrFail($id);

            $data = $request->only([
                'name', 'platform_group_default', 'trading_server_live'
            ]);

            $data['enable_separate_swap_free'] = $request->has('enable_separate_swap_free');
            $data['platform_group_swap_free'] = $request->platform_group_swap_free;
            $data['leverage_options'] = $request->leverage_options;

            // Handle country restrictions
            if ($request->country_restrictions) {
                $data['country_restrictions'] = array_map('trim', explode(',', $request->country_restrictions));
            } else {
                $data['country_restrictions'] = [];
            }

            // Handle tags
            if ($request->tags) {
                $data['tags'] = array_map('trim', explode(',', $request->tags));
            } else {
                $data['tags'] = [];
            }

            // Handle image upload using system's fileUploader helper
            if ($request->hasFile('image')) {
                try {
                    $path = 'assets/images/account_levels';
                    $size = '100x100'; // Standard size for account level images
                    $filename = fileUploader($request->image, $path, $size, $accountLevel->image);
                    $data['image'] = $filename;
                } catch (\Exception $exp) {
                    $notify[] = ['error', 'Couldn\'t upload the image'];
                    return back()->withNotify($notify)->withInput();
                }
            }

            $accountLevel->update($data);

            $notify[] = ['success', 'Account level updated successfully'];
            return back()->withNotify($notify);

        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to update account level: ' . $e->getMessage()];
            return back()->withNotify($notify)->withInput();
        }
    }

    /**
     * Update IB level
     */
    public function updateIbLevel(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'level' => 'required|integer|min:1|unique:ib_levels,level,' . $id,
            'commission_percent' => 'required|numeric|min:0|max:100',
            'max_commission_percent' => 'required|numeric|min:0|max:100',
            'description' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            $notify[] = ['error', 'Validation failed: ' . $validator->errors()->first()];
            return back()->withNotify($notify)->withInput();
        }

        try {
            $ibLevel = IbLevel::findOrFail($id);
            $ibLevel->update($request->all());

            $notify[] = ['success', 'IB level updated successfully'];
            return back()->withNotify($notify);

        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to update IB level: ' . $e->getMessage()];
            return back()->withNotify($notify)->withInput();
        }
    }

    /**
     * Delete IB level
     */
    public function deleteIbLevel($id)
    {
        try {
            $ibLevel = IbLevel::findOrFail($id);
            $ibLevel->delete();

            $notify[] = ['success', 'IB level deleted successfully'];
            return back()->withNotify($notify);

        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to delete IB level: ' . $e->getMessage()];
            return back()->withNotify($notify);
        }
    }

    /**
     * Update symbol group
     */
    public function updateSymbolGroup(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:symbol_groups,name,' . $id,
            'description' => 'nullable|string',
            'symbols' => 'required|array|min:1',
            'symbols.*' => 'exists:symbols,id'
        ]);

        if ($validator->fails()) {
            $notify[] = ['error', 'Validation failed: ' . $validator->errors()->first()];
            return back()->withNotify($notify)->withInput();
        }

        try {
            $symbolGroup = SymbolGroup::findOrFail($id);
            $symbolGroup->update([
                'name' => $request->name,
                'description' => $request->description,
                'symbols_json' => $request->symbols
            ]);

            $notify[] = ['success', 'Symbol group updated successfully'];
            return back()->withNotify($notify);

        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to update symbol group: ' . $e->getMessage()];
            return back()->withNotify($notify)->withInput();
        }
    }

    /**
     * Delete symbol group
     */
    public function deleteSymbolGroup($id)
    {
        try {
            $symbolGroup = SymbolGroup::findOrFail($id);
            $symbolGroup->delete();

            $notify[] = ['success', 'Symbol group deleted successfully'];
            return back()->withNotify($notify);

        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to delete symbol group: ' . $e->getMessage()];
            return back()->withNotify($notify);
        }
    }

    /**
     * Update rebate rule
     */
    public function updateRebateRule(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'ib_group_id' => 'nullable|exists:ib_groups,id',
            'symbol_group_id' => 'nullable|exists:symbol_groups,id',
            'symbol' => 'nullable|string|max:255',
            'rebate_per_lot' => 'required|numeric|min:0',
            'min_volume' => 'required|numeric|min:0',
            'max_volume' => 'nullable|numeric|min:0'
        ]);

        if ($validator->fails()) {
            $notify[] = ['error', 'Validation failed: ' . $validator->errors()->first()];
            return back()->withNotify($notify)->withInput();
        }

        try {
            $rebateRule = RebateRule::findOrFail($id);
            $rebateRule->update($request->all());

            $notify[] = ['success', 'Rebate rule updated successfully'];
            return back()->withNotify($notify);

        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to update rebate rule: ' . $e->getMessage()];
            return back()->withNotify($notify)->withInput();
        }
    }

    /**
     * Toggle rebate rule status
     */
    public function toggleRebateRule($id)
    {
        try {
            $rebateRule = RebateRule::findOrFail($id);
            $rebateRule->status = !$rebateRule->status;
            $rebateRule->save();

            $status = $rebateRule->status ? 'enabled' : 'disabled';
            $notify[] = ['success', "Rebate rule has been {$status}"];
            return back()->withNotify($notify);

        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to toggle rebate rule status: ' . $e->getMessage()];
            return back()->withNotify($notify);
        }
    }

    /**
     * Delete rebate rule
     */
    public function deleteRebateRule($id)
    {
        try {
            $rebateRule = RebateRule::findOrFail($id);
            $rebateRule->delete();

            $notify[] = ['success', 'Rebate rule deleted successfully'];
            return back()->withNotify($notify);

        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to delete rebate rule: ' . $e->getMessage()];
            return back()->withNotify($notify);
        }
    }

    /**
     * Store new IB group
     */
    public function storeIbGroup(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:ib_groups,name',
            'description' => 'nullable|string',
            'commission_multiplier' => 'required|numeric|min:0|max:10',
            'max_levels' => 'required|integer|min:1|max:10'
        ]);

        if ($validator->fails()) {
            $notify[] = ['error', 'Validation failed: ' . $validator->errors()->first()];
            return back()->withNotify($notify)->withInput();
        }

        try {
            IbGroup::create($request->all());

            $notify[] = ['success', 'IB group created successfully'];
            return back()->withNotify($notify);

        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to create IB group: ' . $e->getMessage()];
            return back()->withNotify($notify)->withInput();
        }
    }

    /**
     * Update IB group
     */
    public function updateIbGroup(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:ib_groups,name,' . $id,
            'description' => 'nullable|string',
            'commission_multiplier' => 'required|numeric|min:0|max:10',
            'max_levels' => 'required|integer|min:1|max:10'
        ]);

        if ($validator->fails()) {
            $notify[] = ['error', 'Validation failed: ' . $validator->errors()->first()];
            return back()->withNotify($notify)->withInput();
        }

        try {
            $ibGroup = IbGroup::findOrFail($id);
            $ibGroup->update($request->all());

            $notify[] = ['success', 'IB group updated successfully'];
            return back()->withNotify($notify);

        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to update IB group: ' . $e->getMessage()];
            return back()->withNotify($notify)->withInput();
        }
    }

    /**
     * Toggle account level status
     */
    public function toggleAccountLevelStatus($id)
    {
        try {
            $accountLevel = AccountLevel::findOrFail($id);
            $accountLevel->status = !$accountLevel->status;
            $accountLevel->save();

            $status = $accountLevel->status ? 'enabled' : 'disabled';
            $notify[] = ['success', "Account level has been {$status}"];
            return back()->withNotify($notify);

        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to toggle account level status: ' . $e->getMessage()];
            return back()->withNotify($notify);
        }
    }

    /**
     * Import symbols from MT5
     */
    public function importSymbols(Request $request)
    {
        try {
            // This would typically connect to MT5 to import symbols
            // For now, we'll create a placeholder implementation
            $notify[] = ['info', 'Symbol import functionality will be implemented with MT5 integration'];
            return back()->withNotify($notify);

        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to import symbols: ' . $e->getMessage()];
            return back()->withNotify($notify);
        }
    }

    /**
     * IB Groups Management
     */
    public function ibGroups()
    {
        $pageTitle = 'IB Groups';
        $ibGroups = IbGroup::with(['users', 'rebateRules'])->orderBy('name')->paginate(20);

        return view('admin.partnership.ib_groups', compact('pageTitle', 'ibGroups'));
    }
}
