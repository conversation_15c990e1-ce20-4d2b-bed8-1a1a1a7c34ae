@extends($activeTemplate . 'layouts.master')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title">
                <h4>@lang('IB Reports & Analytics')</h4>
            </div>
        </div>
    </div>

    <!-- Period Filter -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="{{ route('user.ib.reports') }}">
                        <div class="row align-items-end">
                            <div class="col-md-3">
                                <label>@lang('Report Period')</label>
                                <select name="period" class="form-control">
                                    <option value="7days" {{ $period == '7days' ? 'selected' : '' }}>@lang('Last 7 Days')</option>
                                    <option value="30days" {{ $period == '30days' ? 'selected' : '' }}>@lang('Last 30 Days')</option>
                                    <option value="90days" {{ $period == '90days' ? 'selected' : '' }}>@lang('Last 90 Days')</option>
                                    <option value="1year" {{ $period == '1year' ? 'selected' : '' }}>@lang('Last Year')</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn--primary">@lang('Update Report')</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Metrics -->
    <div class="row gy-4 mt-2">
        <div class="col-xl-3 col-lg-4 col-sm-6">
            <div class="dashboard-widget">
                <div class="dashboard-widget__icon">
                    <i class="las la-dollar-sign"></i>
                </div>
                <div class="dashboard-widget__content">
                    <h4 class="dashboard-widget__number">
                        {{ showAmount($performanceMetrics['total_commissions'] ?? 0) }}
                    </h4>
                    <span class="dashboard-widget__caption">@lang('Total Commissions')</span>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-4 col-sm-6">
            <div class="dashboard-widget">
                <div class="dashboard-widget__icon">
                    <i class="las la-chart-line"></i>
                </div>
                <div class="dashboard-widget__content">
                    <h4 class="dashboard-widget__number">{{ $performanceMetrics['total_trades'] ?? 0 }}</h4>
                    <span class="dashboard-widget__caption">@lang('Total Trades')</span>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-4 col-sm-6">
            <div class="dashboard-widget">
                <div class="dashboard-widget__icon">
                    <i class="las la-chart-area"></i>
                </div>
                <div class="dashboard-widget__content">
                    <h4 class="dashboard-widget__number">{{ showAmount($performanceMetrics['total_volume'] ?? 0) }}</h4>
                    <span class="dashboard-widget__caption">@lang('Total Volume')</span>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-4 col-sm-6">
            <div class="dashboard-widget">
                <div class="dashboard-widget__icon">
                    <i class="las la-percentage"></i>
                </div>
                <div class="dashboard-widget__content">
                    <h4 class="dashboard-widget__number">{{ number_format($performanceMetrics['conversion_rate'] ?? 0, 2) }}%</h4>
                    <span class="dashboard-widget__caption">@lang('Conversion Rate')</span>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <!-- Commission by Level -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">@lang('Commission by Level')</h5>
                </div>
                <div class="card-body">
                    @if($levelBreakdown->count() > 0)
                    @foreach($levelBreakdown as $breakdown)
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <h6 class="mb-0">Level {{ $breakdown->level }}</h6>
                            <small class="text-muted">{{ $breakdown->total_trades }} trades</small>
                        </div>
                        <div class="text-end">
                            <span class="fw-bold">{{ showAmount($breakdown->total_amount) }}</span>
                            <br>
                            <small class="text-muted">
                                {{ $levelBreakdown->sum('total_amount') > 0 ? number_format(($breakdown->total_amount / $levelBreakdown->sum('total_amount')) * 100, 1) : 0 }}%
                            </small>
                        </div>
                    </div>
                    @endforeach
                    @else
                    <div class="text-center py-4">
                        <i class="las la-chart-bar text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">@lang('No commission data available')</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Commission by Symbol -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">@lang('Top Symbols')</h5>
                </div>
                <div class="card-body">
                    @if($symbolBreakdown->count() > 0)
                    @foreach($symbolBreakdown->take(8) as $breakdown)
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <h6 class="mb-0">{{ $breakdown->symbol }}</h6>
                            <small class="text-muted">{{ $breakdown->total_trades }} trades | {{ $breakdown->total_volume }} lots</small>
                        </div>
                        <div class="text-end">
                            <span class="fw-bold">{{ showAmount($breakdown->total_amount) }}</span>
                        </div>
                    </div>
                    @endforeach
                    @else
                    <div class="text-center py-4">
                        <i class="las la-chart-pie text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">@lang('No symbol data available')</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Commission Chart -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">@lang('Monthly Commission Trend')</h5>
                </div>
                <div class="card-body">
                    @if($monthlyCommissions->count() > 0)
                    <canvas id="monthlyCommissionChart" height="100"></canvas>
                    @else
                    <div class="text-center py-5">
                        <i class="las la-chart-line text-muted" style="font-size: 4rem;"></i>
                        <h5 class="text-muted mt-3">@lang('No Monthly Data Available')</h5>
                        <p class="text-muted">@lang('Commission trends will appear here once you have historical data')</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Statistics -->
    <div class="row mt-4">
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">@lang('Performance Summary')</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-12 mb-3">
                            <h4 class="text--primary">{{ showAmount($performanceMetrics['paid_commissions'] ?? 0) }}</h4>
                            <span class="caption">@lang('Paid Commissions')</span>
                        </div>
                        <div class="col-12 mb-3">
                            <h4 class="text--warning">{{ showAmount($performanceMetrics['pending_commissions'] ?? 0) }}</h4>
                            <span class="caption">@lang('Pending Commissions')</span>
                        </div>
                        <div class="col-12">
                            <h4 class="text--info">
                                {{ $performanceMetrics['total_trades'] > 0 ? showAmount($performanceMetrics['total_commissions'] / $performanceMetrics['total_trades']) : '0.00' }}
                            </h4>
                            <span class="caption">@lang('Avg Commission per Trade')</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">@lang('Period Comparison')</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>@lang('Current Period')</h6>
                            <p class="mb-1">@lang('Period'): {{ ucfirst(str_replace('days', ' Days', $period)) }}</p>
                            <p class="mb-1">@lang('Start Date'): {{ $performanceMetrics['start_date']->format('d M Y') }}</p>
                            <p class="mb-1">@lang('End Date'): {{ $performanceMetrics['end_date']->format('d M Y') }}</p>
                            <p class="mb-0">@lang('Total Days'): {{ $performanceMetrics['start_date']->diffInDays($performanceMetrics['end_date']) + 1 }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6>@lang('Key Metrics')</h6>
                            <p class="mb-1">@lang('Daily Avg Commissions'): 
                                {{ showAmount(($performanceMetrics['total_commissions'] ?? 0) / max(1, $performanceMetrics['start_date']->diffInDays($performanceMetrics['end_date']) + 1)) }}
                            </p>
                            <p class="mb-1">@lang('Daily Avg Trades'): 
                                {{ number_format(($performanceMetrics['total_trades'] ?? 0) / max(1, $performanceMetrics['start_date']->diffInDays($performanceMetrics['end_date']) + 1), 1) }}
                            </p>
                            <p class="mb-0">@lang('Commission per Volume'): 
                                {{ $performanceMetrics['total_volume'] > 0 ? showAmount($performanceMetrics['total_commissions'] / $performanceMetrics['total_volume']) : '0.00' }} per lot
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('script')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
@if($monthlyCommissions->count() > 0)
// Monthly Commission Chart
const ctx = document.getElementById('monthlyCommissionChart').getContext('2d');
const monthlyData = @json($monthlyCommissions);

const labels = monthlyData.map(item => {
    const date = new Date(item.year, item.month - 1);
    return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
});

const data = monthlyData.map(item => parseFloat(item.total));

new Chart(ctx, {
    type: 'line',
    data: {
        labels: labels.reverse(),
        datasets: [{
            label: 'Monthly Commissions',
            data: data.reverse(),
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            tension: 0.1,
            fill: true
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '$' + value.toFixed(2);
                    }
                }
            }
        }
    }
});
@endif
</script>
@endpush
