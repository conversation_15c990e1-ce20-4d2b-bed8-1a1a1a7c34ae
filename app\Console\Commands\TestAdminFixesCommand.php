<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use App\Models\User;
use Carbon\Carbon;

class TestAdminFixesCommand extends Command
{
    protected $signature = 'test:admin-fixes';
    protected $description = 'Test all admin user list fixes comprehensively';

    public function handle()
    {
        $this->info('🧪 COMPREHENSIVE ADMIN USER LIST FIXES TEST');
        $this->info('==========================================');
        $this->newLine();

        // Test 1: Duplicate Email Consolidation
        $this->info('📋 TEST 1: DUPLICATE EMAIL CONSOLIDATION (CRITICAL)');
        $this->info('---------------------------------------------------');

        try {
            $duplicateEmails = DB::table('users')
                ->select('email', DB::raw('COUNT(*) as count'))
                ->whereNotNull('email')
                ->where('email', '!=', '')
                ->groupBy('email')
                ->having('count', '>', 1)
                ->get();

            $this->info("📊 Current duplicate emails: " . count($duplicateEmails));
            
            if (count($duplicateEmails) > 0) {
                $this->warn("⚠️ DUPLICATES FOUND - Need consolidation");
                foreach ($duplicateEmails->take(3) as $duplicate) {
                    $this->line("   - {$duplicate->email}: {$duplicate->count} accounts");
                }
                $this->error("❌ ISSUE 1 NOT RESOLVED: Duplicates still exist");
            } else {
                $this->info("✅ ISSUE 1 RESOLVED: No duplicate emails found");
            }
            
        } catch (\Exception $e) {
            $this->error("❌ Test 1 failed: " . $e->getMessage());
        }

        $this->newLine();

        // Test 2: MT5 Search Functionality
        $this->info('📋 TEST 2: MT5 SEARCH FUNCTIONALITY');
        $this->info('-----------------------------------');

        try {
            $sampleMT5Accounts = User::whereNotNull('mt5_login')
                ->select('mt5_login', 'all_mt5_accounts', 'email')
                ->limit(5)
                ->get();
            
            $this->info("🔍 Testing search with " . count($sampleMT5Accounts) . " sample MT5 accounts:");
            
            $searchSuccessCount = 0;
            $totalSearchTests = 0;
            
            foreach ($sampleMT5Accounts as $testUser) {
                $totalSearchTests++;
                
                // Test primary MT5 account search
                $searchResult = User::where('mt5_login', 'like', "%{$testUser->mt5_login}%")
                    ->orWhere('all_mt5_accounts', 'like', "%{$testUser->mt5_login}%")
                    ->count();
                
                if ($searchResult > 0) {
                    $searchSuccessCount++;
                    $this->line("   ✅ {$testUser->mt5_login}: Found");
                } else {
                    $this->line("   ❌ {$testUser->mt5_login}: NOT FOUND");
                }
            }
            
            $searchSuccessRate = $totalSearchTests > 0 ? ($searchSuccessCount / $totalSearchTests) * 100 : 0;
            $this->info("📊 Search success rate: {$searchSuccessRate}% ({$searchSuccessCount}/{$totalSearchTests})");
            
            if ($searchSuccessRate >= 95) {
                $this->info("✅ ISSUE 2 RESOLVED: MT5 search functionality working excellently");
            } elseif ($searchSuccessRate >= 80) {
                $this->warn("⚠️ ISSUE 2 PARTIAL: MT5 search working but needs improvement");
            } else {
                $this->error("❌ ISSUE 2 NOT RESOLVED: MT5 search functionality failing");
            }
            
        } catch (\Exception $e) {
            $this->error("❌ Test 2 failed: " . $e->getMessage());
        }

        $this->newLine();

        // Test 3: Performance Test
        $this->info('📋 TEST 3: PERFORMANCE TEST');
        $this->info('---------------------------');

        try {
            $startTime = microtime(true);
            
            // Simulate the admin user list query
            $users = User::whereIn('users.id', function($query) {
                    $query->select(DB::raw('MAX(id)'))
                          ->from('users')
                          ->whereNotNull('email')
                          ->where('email', '!=', '')
                          ->groupBy('email');
                })
                ->with([
                    'wallets:id,user_id,currency_id,balance',
                    'wallets.currency:id,symbol,sign',
                    'mt5Accounts:id,User_Id,Account,Master_Password',
                    'mt5Accounts.mt5Data:Login,Group,Balance,Credit,Rights,Leverage'
                ])
                ->orderBy('created_at', 'desc')
                ->limit(50)
                ->get();
            
            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);
            
            $this->info("⚡ Query performance: {$duration}ms for " . count($users) . " users");
            
            if ($duration < 3000) {
                $this->info("✅ Performance: EXCELLENT (under 3 seconds)");
            } else {
                $this->warn("⚠️ Performance: SLOW (over 3 seconds)");
            }
            
        } catch (\Exception $e) {
            $this->error("❌ Performance test failed: " . $e->getMessage());
        }

        $this->newLine();

        // Test 4: Database Structure
        $this->info('📋 TEST 4: DATABASE STRUCTURE');
        $this->info('------------------------------');

        try {
            // Check if all_mt5_accounts field exists
            $hasField = DB::getSchemaBuilder()->hasColumn('users', 'all_mt5_accounts');
            
            if ($hasField) {
                $this->info("✅ all_mt5_accounts field exists");
                
                // Check if field is being populated
                $populatedCount = User::whereNotNull('all_mt5_accounts')
                    ->where('all_mt5_accounts', '!=', '')
                    ->count();
                
                $this->info("📊 Users with populated all_mt5_accounts: {$populatedCount}");
                
                if ($populatedCount > 0) {
                    $this->info("✅ Field is being populated correctly");
                } else {
                    $this->warn("⚠️ Field exists but not populated");
                }
            } else {
                $this->error("❌ all_mt5_accounts field missing - run migration");
            }
            
        } catch (\Exception $e) {
            $this->error("❌ Database structure test failed: " . $e->getMessage());
        }

        $this->newLine();

        // Summary
        $this->info('🎉 COMPREHENSIVE TEST COMPLETED');
        $this->info('===============================');
        
        $this->info('💡 NEXT STEPS:');
        $this->line('1. Run consolidation: php artisan users:consolidate-duplicates');
        $this->line('2. Run sync: php artisan mt5:sync-users --fast --limit=1000');
        $this->line('3. Test admin user list in browser');
        $this->line('4. Verify search functionality');
        $this->line('5. Check duplicate consolidation');

        return 0;
    }
}
