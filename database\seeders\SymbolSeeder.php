<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Symbol;
use Illuminate\Support\Facades\File;

class SymbolSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Read the Symbols.txt file
        $symbolsFile = base_path('Symbols.txt');
        
        if (!File::exists($symbolsFile)) {
            $this->command->error('Symbols.txt file not found!');
            return;
        }
        
        $lines = File::lines($symbolsFile);
        $isFirstLine = true;
        
        foreach ($lines as $line) {
            // Skip header line
            if ($isFirstLine) {
                $isFirstLine = false;
                continue;
            }
            
            // Skip empty lines
            if (trim($line) === '') {
                continue;
            }
            
            // Parse the tab-separated values
            $columns = explode("\t", $line);
            
            if (count($columns) >= 6) {
                $symbolId = trim($columns[0]);
                $symbol = trim($columns[1]);
                $path = trim($columns[2]);
                $description = trim($columns[3]);
                $contractSize = trim($columns[4]);
                $status = trim($columns[5]);
                
                // Convert status to boolean
                $isEnabled = strtolower($status) === 'enabled';
                
                // Clean contract size (remove commas and convert to number)
                $contractSize = (float) str_replace(',', '', $contractSize);
                
                // Create or update symbol
                Symbol::updateOrCreate(
                    ['symbol' => $symbol],
                    [
                        'path' => $path,
                        'description' => $description,
                        'contract_size' => $contractSize,
                        'status' => $isEnabled
                    ]
                );
                
                $this->command->info("Processed symbol: {$symbol}");
            }
        }
        
        $this->command->info('Symbol seeding completed!');
    }
}
