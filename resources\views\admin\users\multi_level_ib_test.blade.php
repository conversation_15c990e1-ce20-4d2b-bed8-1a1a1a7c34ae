@extends('admin.layouts.app')

@section('panel')
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">
                    <i class="fas fa-sitemap"></i> Multi-Level IB System Testing Dashboard
                </h4>
                <div class="d-flex gap-2">
                    <button class="btn btn--success btn-sm" onclick="testMT5Connection()">
                        <i class="fas fa-database"></i> Test MT5 Connection
                    </button>
                    <button class="btn btn--info btn-sm" onclick="syncMT5Data()">
                        <i class="fas fa-sync"></i> Sync MT5 Data
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- PART 1: Hierarchy Display -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-crown"></i> Test Hierarchy Structure
                                </h5>
                            </div>
                            <div class="card-body">
                                @if($hierarchyData)
                                    <div id="hierarchy-tree">
                                        <div class="hierarchy-node level-0">
                                            <div class="node-content master-ib">
                                                <strong>{{ $hierarchyData['name'] }}</strong>
                                                <br><small>MT5: {{ $hierarchyData['mt5_login'] }}</small>
                                                <br><span class="badge badge--success">Master IB</span>
                                            </div>
                                            @if(count($hierarchyData['children']) > 0)
                                                <div class="children">
                                                    @foreach($hierarchyData['children'] as $child)
                                                        <div class="hierarchy-node level-1">
                                                            <div class="node-content {{ $child['ib_type'] == 'client' ? 'client' : 'sub-ib' }}">
                                                                <strong>{{ $child['name'] }}</strong>
                                                                <br><small>MT5: {{ $child['mt5_login'] }}</small>
                                                                <br><span class="badge badge--{{ $child['ib_type'] == 'client' ? 'info' : 'warning' }}">
                                                                    {{ ucfirst($child['ib_type']) }}
                                                                </span>
                                                            </div>
                                                            @if(count($child['children']) > 0)
                                                                <div class="children">
                                                                    @foreach($child['children'] as $grandchild)
                                                                        <div class="hierarchy-node level-2">
                                                                            <div class="node-content client">
                                                                                <strong>{{ $grandchild['name'] }}</strong>
                                                                                <br><small>MT5: {{ $grandchild['mt5_login'] }}</small>
                                                                                @if(isset($grandchild['mt5_balance']) && $grandchild['mt5_balance'] > 0)
                                                                                    <br><small class="text-success">Balance: ${{ number_format($grandchild['mt5_balance'], 2) }}</small>
                                                                                @endif
                                                                                @if(isset($grandchild['recent_trades']) && $grandchild['recent_trades'] > 0)
                                                                                    <br><small class="text-warning">{{ $grandchild['recent_trades'] }} recent trades</small>
                                                                                @endif
                                                                                <br><span class="badge badge--info">Client</span>
                                                                            </div>
                                                                        </div>
                                                                    @endforeach
                                                                </div>
                                                            @endif
                                                        </div>
                                                    @endforeach
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                @else
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        Test hierarchy not found. Please ensure users with MT5 logins 878046, 878010, 878023, and 878012 exist.
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-line"></i> Commission Summary
                                </h5>
                            </div>
                            <div class="card-body">
                                @if($commissionSummary)
                                    <div class="commission-stats">
                                        <div class="stat-item">
                                            <label>Master IB:</label>
                                            <strong>{{ $commissionSummary['master_ib'] }}</strong>
                                        </div>
                                        <div class="stat-item">
                                            <label>Direct Commissions:</label>
                                            <strong class="text-success">${{ number_format($commissionSummary['direct_commissions'], 2) }}</strong>
                                        </div>
                                        <div class="stat-item">
                                            <label>Multi-Level Commissions:</label>
                                            <strong class="text-info">${{ number_format($commissionSummary['multi_level_commissions'], 2) }}</strong>
                                        </div>
                                        <div class="stat-item">
                                            <label>Sub-IB Commissions:</label>
                                            <strong class="text-warning">${{ number_format($commissionSummary['sub_ib_commissions'], 2) }}</strong>
                                        </div>
                                        <hr>
                                        <div class="stat-item">
                                            <label>Total Hierarchy:</label>
                                            <strong class="text-primary">${{ number_format($commissionSummary['hierarchy_total'], 2) }}</strong>
                                        </div>
                                    </div>
                                @else
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        No commission data available yet. Create test trades to see commission distribution.
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- PART 2: Commission Testing -->
                <div class="row mb-4">
                    <div class="col-lg-12">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0">
                                    <i class="fas fa-vial"></i> Commission Testing Lab
                                </h5>
                            </div>
                            <div class="card-body">
                                <form id="test-trade-form">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label class="form-label">MT5 Login:</label>
                                            <select class="form-control" name="mt5_login" required>
                                                <option value="">Select User</option>
                                                <option value="878012" selected>878012 (Client under Sub-IB) - RECOMMENDED</option>
                                                @if($masterIB)
                                                    <option value="{{ $masterIB->mt5_login }}">{{ $masterIB->mt5_login }} (Master IB)</option>
                                                @endif
                                                @if($subIB)
                                                    <option value="{{ $subIB->mt5_login }}">{{ $subIB->mt5_login }} (Sub-IB)</option>
                                                @endif
                                                @if($client878023)
                                                    <option value="{{ $client878023->mt5_login }}">{{ $client878023->mt5_login }} (Client)</option>
                                                @endif
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Symbol:</label>
                                            <select class="form-control" name="symbol" required>
                                                <option value="EURUSD">EURUSD</option>
                                                <option value="GBPUSD">GBPUSD</option>
                                                <option value="USDJPY">USDJPY</option>
                                                <option value="XAUUSD">XAUUSD</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Volume (Lots):</label>
                                            <input type="number" class="form-control" name="volume" step="0.01" min="0.01" value="1.00" required>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Profit ($):</label>
                                            <input type="number" class="form-control" name="profit" step="0.01" value="100.00" required>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Commission ($):</label>
                                            <input type="number" class="form-control" name="commission" step="0.01" value="5.00">
                                        </div>
                                        <div class="col-md-1">
                                            <label class="form-label">&nbsp;</label>
                                            <button type="submit" class="btn btn--primary btn-block">
                                                <i class="fas fa-play"></i> Test
                                            </button>
                                        </div>
                                    </div>
                                </form>
                                
                                <div id="test-results" class="mt-3" style="display: none;">
                                    <div class="alert alert-info">
                                        <h6>Test Results:</h6>
                                        <div id="test-results-content"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- PART 3: Recent MT5 Deals -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="card border-secondary">
                            <div class="card-header bg-secondary text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-bar"></i> Recent MT5 Deals
                                </h5>
                                <button class="btn btn--light btn-sm" onclick="refreshMT5Deals()">
                                    <i class="fas fa-refresh"></i> Refresh
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Deal ID</th>
                                                <th>MT5 Login</th>
                                                <th>Symbol</th>
                                                <th>Volume</th>
                                                <th>Profit</th>
                                                <th>Commission</th>
                                                <th>Time</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody id="mt5-deals-table">
                                            @forelse($recentDeals as $deal)
                                                <tr>
                                                    <td>{{ $deal->Deal }}</td>
                                                    <td>{{ $deal->Login }}</td>
                                                    <td>{{ $deal->Symbol }}</td>
                                                    <td>{{ number_format($deal->Volume / 100, 2) }}</td>
                                                    <td class="{{ $deal->Profit >= 0 ? 'text-success' : 'text-danger' }}">
                                                        ${{ number_format($deal->Profit, 2) }}
                                                    </td>
                                                    <td>${{ number_format($deal->Commission, 2) }}</td>
                                                    <td>{{ \Carbon\Carbon::parse($deal->Time)->format('Y-m-d H:i:s') }}</td>
                                                    <td>
                                                        <button class="btn btn--primary btn-sm" onclick="processCommission('{{ $deal->Deal }}', '{{ $deal->Login }}')">
                                                            <i class="fas fa-calculator"></i> Process
                                                        </button>
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="8" class="text-center text-muted">
                                                        No recent deals found
                                                    </td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('style')
<style>
/* Hierarchy Tree Styling */
#hierarchy-tree {
    font-family: Arial, sans-serif;
}

.hierarchy-node {
    margin: 10px 0;
    position: relative;
}

.node-content {
    padding: 10px;
    border-radius: 8px;
    border: 2px solid #ddd;
    background: #f8f9fa;
    display: inline-block;
    min-width: 200px;
    text-align: center;
}

.node-content.master-ib {
    border-color: #dc3545;
    background: #fff5f5;
}

.node-content.sub-ib {
    border-color: #ffc107;
    background: #fffbf0;
}

.node-content.client {
    border-color: #17a2b8;
    background: #f0f9ff;
}

.children {
    margin-left: 30px;
    margin-top: 10px;
    border-left: 2px solid #ddd;
    padding-left: 20px;
}

.level-1 .children {
    margin-left: 20px;
}

/* Commission Stats Styling */
.commission-stats .stat-item {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
    border-bottom: 1px solid #eee;
}

.commission-stats .stat-item:last-child {
    border-bottom: none;
}

/* Test Results Styling */
#test-results {
    max-height: 300px;
    overflow-y: auto;
}

.test-result-item {
    padding: 8px;
    margin: 5px 0;
    border-radius: 4px;
    border-left: 4px solid #007bff;
    background: #f8f9fa;
}

.test-result-success {
    border-left-color: #28a745;
    background: #d4edda;
}

.test-result-error {
    border-left-color: #dc3545;
    background: #f8d7da;
}
</style>
@endpush

@push('script')
<script>
$(document).ready(function() {
    console.log('Multi-Level IB Testing Dashboard Ready');

    // Test trade form submission
    $('#test-trade-form').on('submit', function(e) {
        e.preventDefault();

        const formData = {
            mt5_login: $('select[name="mt5_login"]').val(),
            symbol: $('select[name="symbol"]').val(),
            volume: $('input[name="volume"]').val(),
            profit: $('input[name="profit"]').val(),
            commission: $('input[name="commission"]').val(),
            _token: '{{ csrf_token() }}'
        };

        console.log('Creating test trade:', formData);

        $.ajax({
            url: '{{ route("admin.ib.test.create-trade") }}',
            method: 'POST',
            data: formData,
            success: function(response) {
                console.log('Test trade response:', response);
                displayTestResults(response);

                if (response.success) {
                    // Refresh commission summary
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                }
            },
            error: function(xhr) {
                console.error('Test trade error:', xhr);
                displayTestResults({
                    success: false,
                    message: 'Test trade failed: ' + xhr.responseText
                });
            }
        });
    });
});

// Display test results
function displayTestResults(response) {
    const resultsDiv = $('#test-results');
    const contentDiv = $('#test-results-content');

    let html = `
        <div class="test-result-item ${response.success ? 'test-result-success' : 'test-result-error'}">
            <strong>${response.success ? '✅ Success' : '❌ Error'}:</strong> ${response.message}
        </div>
    `;

    if (response.trade_data) {
        html += `
            <div class="test-result-item">
                <strong>Trade Data:</strong><br>
                MT5 Login: ${response.trade_data.mt5_login}<br>
                Symbol: ${response.trade_data.symbol}<br>
                Volume: ${response.trade_data.volume} lots<br>
                Profit: $${response.trade_data.profit}
            </div>
        `;
    }

    if (response.commission_summary) {
        html += `
            <div class="test-result-item test-result-success">
                <strong>Updated Commission Summary:</strong><br>
                Direct: $${response.commission_summary.direct_commissions}<br>
                Multi-Level: $${response.commission_summary.multi_level_commissions}<br>
                Total: $${response.commission_summary.hierarchy_total}
            </div>
        `;
    }

    contentDiv.html(html);
    resultsDiv.show();
}

// Test MT5 connection
function testMT5Connection() {
    console.log('Testing MT5 connection...');

    $.ajax({
        url: '{{ route("admin.ib.test.mt5-status") }}',
        method: 'GET',
        success: function(response) {
            alert(response.success ?
                '✅ MT5 Connection Successful' :
                '❌ MT5 Connection Failed: ' + response.message
            );
        },
        error: function() {
            alert('❌ MT5 Connection Test Failed');
        }
    });
}

// Sync MT5 data
function syncMT5Data() {
    console.log('Syncing MT5 data...');

    $.ajax({
        url: '{{ route("admin.ib.test.sync-mt5") }}',
        method: 'POST',
        data: { _token: '{{ csrf_token() }}' },
        success: function(response) {
            alert(response.success ?
                '✅ MT5 Sync Completed: ' + response.data.processed + ' deals processed' :
                '❌ MT5 Sync Failed: ' + response.message
            );

            if (response.success) {
                refreshMT5Deals();
            }
        },
        error: function() {
            alert('❌ MT5 Sync Failed');
        }
    });
}

// Refresh MT5 deals table
function refreshMT5Deals() {
    console.log('Refreshing MT5 deals...');
    location.reload();
}

// Process commission for specific deal
function processCommission(dealId, mt5Login) {
    console.log('Processing commission for deal:', dealId, 'MT5:', mt5Login);

    $.ajax({
        url: '{{ route("admin.ib.test.real-time") }}',
        method: 'POST',
        data: {
            mt5_login: mt5Login,
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            alert(response.success ?
                '✅ Commission Processed Successfully' :
                '❌ Commission Processing Failed: ' + response.message
            );

            if (response.success) {
                location.reload();
            }
        },
        error: function() {
            alert('❌ Commission Processing Failed');
        }
    });
}
</script>
@endpush
