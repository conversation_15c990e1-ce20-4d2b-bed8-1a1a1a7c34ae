<?php

/**
 * Test MT5 balance deduction with a real MT5 user
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Testing Real MT5 User Balance Deduction ===\n\n";

// Find a user with real MT5 accounts and sufficient balance
$realMT5User = DB::connection('mbf-dbmt5')->table('mt5_users')
    ->where('Group', 'like', 'real%')
    ->where('Balance', '>', 1)
    ->whereNotNull('Email')
    ->where('Email', '!=', '')
    ->first();

if (!$realMT5User) {
    echo "❌ No real MT5 users with balance found\n";
    exit(1);
}

echo "Found real MT5 user:\n";
echo "Email: {$realMT5User->Email}\n";
echo "Login: {$realMT5User->Login}\n";
echo "Group: {$realMT5User->Group}\n";
echo "Balance: \${$realMT5User->Balance}\n\n";

// Check if this user exists in Laravel
$user = \App\Models\User::where('email', $realMT5User->Email)->first();

if (!$user) {
    echo "❌ User not found in Laravel users table\n";
    exit(1);
}

echo "✅ Found Laravel user: ID {$user->id}\n";

// Check if user has wallets
$wallets = $user->wallets;
echo "User has {$wallets->count()} wallets\n";

if ($wallets->count() == 0) {
    echo "⚠️  User has no Laravel wallets - creating a default USD wallet for testing\n";
    
    // Create a default wallet for testing
    $usdCurrency = \App\Models\Currency::where('symbol', 'USD')->first();
    if (!$usdCurrency) {
        echo "❌ USD currency not found\n";
        exit(1);
    }
    
    $wallet = new \App\Models\Wallet();
    $wallet->user_id = $user->id;
    $wallet->currency_id = $usdCurrency->id;
    $wallet->balance = 0;
    $wallet->wallet_type = 1; // Assuming 1 is spot wallet
    $wallet->save();
    
    echo "✅ Created USD wallet for user\n";
} else {
    $wallet = $wallets->first();
    echo "Using existing wallet: {$wallet->name} ({$wallet->currency->symbol})\n";
}

// Test MT5 account retrieval
echo "\nTesting MT5 account retrieval:\n";
$mt5Accounts = \App\Models\Mt5Users::getAccounts($user->email);
echo "Total MT5 accounts: {$mt5Accounts->count()}\n";

foreach ($mt5Accounts as $account) {
    echo "  {$account->Login} | {$account->Group} | \${$account->Balance}\n";
}

// Filter for real accounts
$realAccounts = $mt5Accounts->filter(function ($account) {
    return stripos($account->Group, 'real') !== false;
});

echo "\nReal accounts: {$realAccounts->count()}\n";
foreach ($realAccounts as $account) {
    echo "  {$account->Login} | {$account->Group} | \${$account->Balance}\n";
}

if ($realAccounts->isEmpty()) {
    echo "❌ No real accounts found for this user\n";
    exit(1);
}

// Test MT5Service balance deduction
echo "\nTesting MT5Service balance deduction:\n";

$testAmount = 0.01; // Test with 1 cent
echo "Testing deduction of \${$testAmount}\n";

try {
    $mt5Service = new \App\Services\MT5Service();
    $result = $mt5Service->deductBalanceFromUserAccounts($user, $testAmount, "Test deduction - Real user test");
    
    echo "\nMT5 Deduction Result:\n";
    echo "Success: " . ($result['success'] ? 'YES' : 'NO') . "\n";
    echo "Message: " . $result['message'] . "\n";
    echo "Accounts updated: " . $result['accounts_updated'] . "\n";
    echo "Total accounts: " . $result['total_accounts'] . "\n";
    
    if (isset($result['amount_deducted'])) {
        echo "Amount deducted: \${$result['amount_deducted']}\n";
    }
    
    if (isset($result['errors']) && !empty($result['errors'])) {
        echo "Errors:\n";
        foreach ($result['errors'] as $error) {
            echo "  - {$error}\n";
        }
    }
    
    if ($result['success']) {
        echo "✅ MT5 balance deduction test SUCCESSFUL\n";
        
        // Add the balance back
        echo "\nAdding balance back...\n";
        $addResult = $mt5Service->addBalanceToUserAccounts($user, $testAmount, "Test refund - Real user test");
        
        if ($addResult['success']) {
            echo "✅ Balance refunded successfully\n";
        } else {
            echo "❌ Balance refund failed: " . $addResult['message'] . "\n";
        }
    } else {
        echo "❌ MT5 balance deduction test FAILED\n";
        echo "This explains why withdrawals are not working!\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Exception during MT5 test: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== Test Complete ===\n";
