<div class="row mb--20">
  <div class="col-lg-12">
    <div class="card mt-30">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
          <i class="fas fa-chart-line text-primary me-2"></i><?php echo app('translator')->get('MT5 Accounts'); ?>
        </h5>
        <div class="d-flex gap-2">
          <span class="badge badge--info"><?php echo e($accounts->total()); ?> Total</span>
          <?php if($accounts->total() > 10): ?>
            <span class="badge badge--warning">Paginated</span>
          <?php endif; ?>
        </div>
      </div>

      <div class="card-body p-0">
        <div class="table-responsive--sm table-responsive">
          <table class="table table--light style--two custom-data-table">
            <thead>
              <tr>
                <th><?php echo app('translator')->get('Login'); ?></th>
                <th><?php echo app('translator')->get('Group'); ?></th>
                <th><?php echo app('translator')->get('Balance'); ?></th>
                <th><?php echo app('translator')->get('Equity'); ?></th>
                <th><?php echo app('translator')->get('Credit'); ?></th>
              </tr>
            </thead>
            <tbody>
              <?php $__empty_1 = true; $__currentLoopData = $accounts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $acc): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr>
                  <td><?php echo e($acc->Login); ?></td>
                  <td><?php echo e($acc->Group); ?></td>
                  <td><?php echo e($acc->Balance); ?></td>
                  <td><?php echo e($acc->EquityPrevDay); ?></td>
                  <td><?php echo e($acc->Credit); ?></td>
                </tr>
              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                  <td colspan="5" class="text-center"><?php echo app('translator')->get('Data not found'); ?></td>
                </tr>
              <?php endif; ?>
            </tbody>
          </table>
        </div>
      </div>

      <?php if($accounts->hasPages()): ?>
        <div class="card-footer py-4">
          
          <div class="d-flex justify-content-between align-items-center">
            <div class="pagination-info">
              <span class="text-muted">
                Showing <?php echo e($accounts->firstItem() ?? 0); ?> to <?php echo e($accounts->lastItem() ?? 0); ?>

                of <?php echo e($accounts->total()); ?> MT5 accounts
              </span>
            </div>
            <div class="pagination-controls">
              <nav aria-label="MT5 accounts pagination">
                <ul class="pagination pagination-sm mb-0">
                  
                  <?php if($accounts->onFirstPage()): ?>
                    <li class="page-item disabled"><span class="page-link">‹</span></li>
                  <?php else: ?>
                    <li class="page-item">
                      <a class="page-link" href="<?php echo e($accounts->previousPageUrl()); ?>">‹</a>
                    </li>
                  <?php endif; ?>

                  
                  <?php $__currentLoopData = $accounts->getUrlRange(1, $accounts->lastPage()); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $page => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($page == $accounts->currentPage()): ?>
                      <li class="page-item active"><span class="page-link"><?php echo e($page); ?></span></li>
                    <?php else: ?>
                      <li class="page-item">
                        <a class="page-link" href="<?php echo e($url); ?>"><?php echo e($page); ?></a>
                      </li>
                    <?php endif; ?>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                  
                  <?php if($accounts->hasMorePages()): ?>
                    <li class="page-item">
                      <a class="page-link" href="<?php echo e($accounts->nextPageUrl()); ?>">›</a>
                    </li>
                  <?php else: ?>
                    <li class="page-item disabled"><span class="page-link">›</span></li>
                  <?php endif; ?>
                </ul>
              </nav>
            </div>
          </div>
        </div>
      <?php endif; ?>
    </div>
  </div>
</div>

<?php if (isset($component)) { $__componentOriginalbd5922df145d522b37bf664b524be380 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbd5922df145d522b37bf664b524be380 = $attributes; } ?>
<?php $component = App\View\Components\ConfirmationModal::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('confirmation-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\ConfirmationModal::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbd5922df145d522b37bf664b524be380)): ?>
<?php $attributes = $__attributesOriginalbd5922df145d522b37bf664b524be380; ?>
<?php unset($__attributesOriginalbd5922df145d522b37bf664b524be380); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbd5922df145d522b37bf664b524be380)): ?>
<?php $component = $__componentOriginalbd5922df145d522b37bf664b524be380; ?>
<?php unset($__componentOriginalbd5922df145d522b37bf664b524be380); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\mbf.mybrokerforex.com-********\resources\views/components/user-detail/account.blade.php ENDPATH**/ ?>