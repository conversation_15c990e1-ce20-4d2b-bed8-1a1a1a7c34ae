<?php
/**
 * WINDOWS/PLESK PRODUCTION SERVER CONFIGURATION GUIDE
 * 
 * This guide provides all the necessary configurations for your live Windows/Plesk server.
 * DO NOT apply these changes to localhost - only for production server!
 */

echo "<h1>Windows/Plesk Production Server Configuration</h1>";
echo "<style>
body{font-family:Arial;margin:20px;} 
.code{background:#f5f5f5;padding:15px;border:1px solid #ddd;margin:10px 0;overflow-x:auto;} 
.warning{color:red;font-weight:bold;background:#ffe6e6;padding:10px;border:1px solid red;margin:10px 0;} 
.success{color:green;font-weight:bold;background:#e6ffe6;padding:10px;border:1px solid green;margin:10px 0;}
.step{background:#e6f3ff;padding:15px;border:1px solid #0066cc;margin:15px 0;}
</style>";

echo "<div class='warning'>⚠️ CRITICAL: Apply these changes ONLY to your live Windows/Plesk server, NOT localhost!</div>";

echo "<div class='step'>";
echo "<h2>STEP 1: Update Production .env File</h2>";
echo "<p>Replace these lines in your live server's .env file:</p>";
echo "<div class='code'>";
echo htmlspecialchars('
# Windows/Plesk Production Paths
PYTHON_EXE="C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python.exe"
PYTHON_SCRIPT="C:\\Inetpub\\vhosts\\mybrokerforex.com\\mbf.mybrokerforex.com\\python\\mt5manager.py"
');
echo "</div>";
echo "<p><strong>Alternative paths if above doesn't work:</strong></p>";
echo "<div class='code'>";
echo htmlspecialchars('
# Option 2: If using httpdocs
PYTHON_SCRIPT="C:\\Inetpub\\vhosts\\mybrokerforex.com\\httpdocs\\python\\mt5manager.py"

# Option 3: If using subdomain structure
PYTHON_SCRIPT="C:\\Inetpub\\vhosts\\mybrokerforex.com\\subdomains\\mbf\\httpdocs\\python\\mt5manager.py"
');
echo "</div>";
echo "</div>";

echo "<div class='step'>";
echo "<h2>STEP 2: Update MT5Service.php Constructor</h2>";
echo "<p>In your live server's <code>app/Services/MT5Service.php</code>, replace the constructor (lines 10-22):</p>";
echo "<div class='code'>";
echo htmlspecialchars('
private $pythonScript;
private $pythonExe;
private $mt5Server;
private $mt5Port;
private $managerLogin;
private $managerPassword;
private $build;
private $agent;

public function __construct()
{
    $this->pythonExe = env(\'PYTHON_EXE\', \'python\');
    $this->pythonScript = env(\'PYTHON_SCRIPT\', base_path(\'python/mt5manager.py\'));

    // MT5 Web API Configuration
    $this->mt5Server = env(\'MT5_SERVER\', \'**************\');
    $this->mt5Port = env(\'MT5_PORT\', 443);
    $this->managerLogin = env(\'MT5_MANAGER_LOGIN\', \'877966\');
    $this->managerPassword = env(\'MT5_MANAGER_PASSWORD\', \'Mbf@2024\');
    $this->build = env(\'MT5_BUILD\', \'484\');
    $this->agent = env(\'MT5_AGENT\', \'WebAPI\');
}
');
echo "</div>";
echo "</div>";

echo "<div class='step'>";
echo "<h2>STEP 3: Fix Password Change Method</h2>";
echo "<p>Replace the command building in <code>changeAccountPasswordWebAPI</code> method (around line 51-53):</p>";
echo "<div class='code'>";
echo htmlspecialchars('
// Build Python command using environment variables
$command = escapeshellarg($this->pythonExe) . " " . escapeshellarg($this->pythonScript) . " change_password --login {$accountLogin} --new_password " . escapeshellarg($newPassword) . " --password_type {$passwordType}";
');
echo "</div>";
echo "</div>";

echo "<div class='step'>";
echo "<h2>STEP 4: Fix Leverage Change Method</h2>";
echo "<p>Replace the command building in <code>changeAccountLeverageWebAPI</code> method (around line 165-166):</p>";
echo "<div class='code'>";
echo htmlspecialchars('
// Build Python command using environment variables
$command = escapeshellarg($this->pythonExe) . " " . escapeshellarg($this->pythonScript) . " change_leverage --login {$accountLogin} --leverage {$newLeverage}";
');
echo "</div>";
echo "</div>";

echo "<div class='step'>";
echo "<h2>STEP 5: Fix Balance Methods</h2>";
echo "<p><strong>A. addBalanceToAccount method (around line 303):</strong></p>";
echo "<div class='code'>";
echo htmlspecialchars('
$command = sprintf(
    \'%s %s add_balance --login %d --amount %.2f --comment %s\',
    escapeshellarg($this->pythonExe),
    escapeshellarg($this->pythonScript),
    $login,
    $amount,
    escapeshellarg($comment)
);
');
echo "</div>";

echo "<p><strong>B. deductBalanceFromAccount method (around line 490):</strong></p>";
echo "<div class='code'>";
echo htmlspecialchars('
$command = sprintf(
    \'%s %s add_balance --login %d --amount %.2f --comment %s\',
    escapeshellarg($this->pythonExe),
    escapeshellarg($this->pythonScript),
    $login,
    -$amount, // Negative amount for deduction
    escapeshellarg($comment)
);
');
echo "</div>";

echo "<p><strong>C. getAccountBalance method (around line 531):</strong></p>";
echo "<div class='code'>";
echo htmlspecialchars('
$command = sprintf(
    \'%s %s get_balance --login %d\',
    escapeshellarg($this->pythonExe),
    escapeshellarg($this->pythonScript),
    $login
);
');
echo "</div>";
echo "</div>";

echo "<div class='step'>";
echo "<h2>STEP 6: Fix Legacy Methods</h2>";
echo "<p><strong>A. changeLeverage method (around line 570):</strong></p>";
echo "<div class='code'>";
echo htmlspecialchars('
$command = sprintf(
    \'%s %s change_leverage --login %d --leverage %d\',
    escapeshellarg($this->pythonExe),
    escapeshellarg($this->pythonScript),
    $login,
    $leverage
);
');
echo "</div>";

echo "<p><strong>B. changePassword method (around line 609):</strong></p>";
echo "<div class='code'>";
echo htmlspecialchars('
$command = sprintf(
    \'%s %s change_password --login %d --new_password %s --password_type %s --password %s\',
    escapeshellarg($this->pythonExe),
    escapeshellarg($this->pythonScript),
    $login,
    escapeshellarg($newPassword),
    $passwordType,
    escapeshellarg($newPassword)
);
');
echo "</div>";

echo "<p><strong>C. createAccount method (around line 650):</strong></p>";
echo "<div class='code'>";
echo htmlspecialchars('
$command = sprintf(
    \'%s %s create_account --first_name %s --last_name %s --password %s --group %s --leverage %d --email %s --country %s --city %s --state %s --address %s --zipcode %s --phone %s --initial_balance %.2f\',
    escapeshellarg($this->pythonExe),
    escapeshellarg($this->pythonScript),
    escapeshellarg($userData[\'first_name\']),
    escapeshellarg($userData[\'last_name\']),
    escapeshellarg($userData[\'password\']),
    escapeshellarg($userData[\'group\']),
    $userData[\'leverage\'],
    escapeshellarg($userData[\'email\']),
    escapeshellarg($userData[\'country\']),
    escapeshellarg($userData[\'city\']),
    escapeshellarg($userData[\'state\']),
    escapeshellarg($userData[\'address\']),
    escapeshellarg($userData[\'zipcode\']),
    escapeshellarg($userData[\'phone\']),
    $userData[\'initial_balance\'] ?? 0.0
);
');
echo "</div>";
echo "</div>";

echo "<div class='step'>";
echo "<h2>STEP 7: Clear Caches and Test</h2>";
echo "<p>After making all changes, run these commands on your live server:</p>";
echo "<div class='code'>";
echo htmlspecialchars('
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
');
echo "</div>";
echo "</div>";

echo "<div class='step'>";
echo "<h2>STEP 8: Verification Commands</h2>";
echo "<p>Test these commands directly on your Windows server to verify setup:</p>";
echo "<div class='code'>";
echo htmlspecialchars('
# Test Python installation
"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" --version

# Test MetaTrader5 package
"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" -c "import MetaTrader5; print(MetaTrader5.__version__)"

# Test MT5 connection
"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" "C:\Inetpub\vhosts\mybrokerforex.com\mbf.mybrokerforex.com\python\mt5manager.py" test_connection

# Test leverage change (replace YOUR_LOGIN with actual MT5 login)
"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" "C:\Inetpub\vhosts\mybrokerforex.com\mbf.mybrokerforex.com\python\mt5manager.py" change_leverage --login YOUR_LOGIN --leverage 300
');
echo "</div>";
echo "</div>";

echo "<div class='success'>";
echo "<h2>✅ Why These Changes Fix the Issues</h2>";
echo "<ul>";
echo "<li><strong>Consistent Environment Variables:</strong> All methods now use PYTHON_EXE and PYTHON_SCRIPT from .env</li>";
echo "<li><strong>Proper Windows Paths:</strong> Double backslashes for Windows/Plesk compatibility</li>";
echo "<li><strong>Shell Escaping:</strong> escapeshellarg() handles paths with spaces and special characters</li>";
echo "<li><strong>No Hardcoded Paths:</strong> Easy to change paths without modifying code</li>";
echo "</ul>";
echo "</div>";

echo "<div class='warning'>";
echo "<h3>🚨 FINAL REMINDERS:</h3>";
echo "<ul>";
echo "<li>BACKUP your live server files before making changes</li>";
echo "<li>Apply changes ONLY to live server, NOT localhost</li>";
echo "<li>Upload python/mt5manager.py to the correct path on live server</li>";
echo "<li>Ensure MetaTrader5 Python package is installed on live server</li>";
echo "<li>Test thoroughly after applying changes</li>";
echo "<li>Delete this file after use for security</li>";
echo "</ul>";
echo "</div>";

echo "<p><strong>Next Steps:</strong> Upload this file to your live server, follow the steps, then test the MT5 functionality through your admin panel.</p>";
?>
