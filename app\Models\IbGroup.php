<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class IbGroup extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'commission_multiplier',
        'max_levels',
        'rules_json',
        'status'
    ];

    protected $casts = [
        'commission_multiplier' => 'decimal:2',
        'rules_json' => 'array',
        'status' => 'boolean'
    ];

    /**
     * Get users in this IB group
     */
    public function users()
    {
        return $this->hasMany(User::class, 'ib_group_id');
    }

    /**
     * Get rebate rules for this group
     */
    public function rebateRules()
    {
        return $this->hasMany(RebateRule::class);
    }

    /**
     * Scope for active groups
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Get all active groups
     */
    public static function getActiveGroups()
    {
        return self::active()->orderBy('name')->get();
    }

    /**
     * Get default group (first active group)
     */
    public static function getDefaultGroup()
    {
        return self::active()->first();
    }

    /**
     * Get group statistics (optimized to avoid N+1 queries)
     */
    public function getStats()
    {
        // Use loaded relationships if available, otherwise query
        if ($this->relationLoaded('users')) {
            $users = $this->users->whereNotNull('ib_status');
            $totalIbs = $users->count();
            $activeIbs = $users->where('ib_status', 'approved')->count();
            $pendingIbs = $users->where('ib_status', 'pending')->count();
        } else {
            $totalIbs = $this->users()->whereNotNull('ib_status')->count();
            $activeIbs = $this->users()->where('ib_status', 'approved')->count();
            $pendingIbs = $this->users()->where('ib_status', 'pending')->count();
        }

        if ($this->relationLoaded('rebateRules')) {
            $totalRules = $this->rebateRules->count();
            $activeRules = $this->rebateRules->where('status', true)->count();
        } else {
            $totalRules = $this->rebateRules()->count();
            $activeRules = $this->rebateRules()->where('status', true)->count();
        }

        return [
            'total_ibs' => $totalIbs,
            'active_ibs' => $activeIbs,
            'pending_ibs' => $pendingIbs,
            'total_rules' => $totalRules,
            'active_rules' => $activeRules
        ];
    }
}
