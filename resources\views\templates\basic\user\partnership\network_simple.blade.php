@extends($activeTemplate . 'layouts.master')
@section('content')

<!-- Include OrgChart.js CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/orgchart@4.0.1/dist/css/jquery.orgchart.min.css" />

<div class="container-fluid">
    <div class="row justify-content-center mt-4">
        <div class="col-xl-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">@lang('Partnership Network - Simple Working Version')</h5>
                </div>
                <div class="card-body">
                    <!-- Debug Info -->
                    <div class="alert alert-info mb-3">
                        <h6>🔧 Debug Information:</h6>
                        <div id="debugInfo">Loading debug info...</div>
                    </div>
                    
                    <!-- Network Container -->
                    <div id="networkContainer" style="min-height: 400px; border: 2px dashed #ddd; padding: 20px;">
                        <div class="text-center py-5">
                            <h4>Network Tree Loading...</h4>
                            <div class="spinner-border text-primary" role="status"></div>
                        </div>
                    </div>
                    
                    <!-- Test Buttons -->
                    <div class="mt-3">
                        <button class="btn btn-primary" onclick="testOrgChart()">Test OrgChart</button>
                        <button class="btn btn-secondary" onclick="testFallback()">Test Fallback</button>
                        <button class="btn btn-info" onclick="showDebugData()">Show Debug Data</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{{-- Pass data to JavaScript safely --}}
<script>
window.networkDataJson = @json($networkData ?? []);
window.userDataJson = {
    id: {{ $user->id ?? 0 }},
    firstname: @json($user->firstname ?? ''),
    lastname: @json($user->lastname ?? ''),
    ib_type: @json($user->ib_type ?? 'master'),
    mt5_login: @json($user->mt5_login ?? 'N/A'),
    mt5_balance: {{ $user->mt5_balance ?? 0 }}
};
window.routesJson = {
    getReferrals: @json(route('user.partnership.get-referrals')),
    network: @json(route('user.partnership.network'))
};
console.log('✅ SIMPLE: PHP Data passed successfully');
</script>

@push('script')
<script src="https://cdn.jsdelivr.net/npm/orgchart@4.0.1/dist/js/jquery.orgchart.min.js"></script>
<script>
$(document).ready(function() {
    console.log('✅ SIMPLE: Document ready started');
    
    // Create BACKEND_DATA
    window.BACKEND_DATA = {
        networkData: window.networkDataJson || [],
        user: {
            id: window.userDataJson.id || 0,
            name: (window.userDataJson.firstname || '') + ' ' + (window.userDataJson.lastname || ''),
            title: (window.userDataJson.ib_type || 'master') + ' IB',
            mt5_login: window.userDataJson.mt5_login || 'N/A',
            mt5_balance: (window.userDataJson.mt5_balance || 0).toFixed(2),
            node_type: 'master'
        },
        routes: window.routesJson
    };
    
    console.log('✅ SIMPLE: BACKEND_DATA created:', window.BACKEND_DATA);
    
    // Update debug info
    updateDebugInfo();
    
    // Test OrgChart.js
    if (typeof $.fn.orgchart !== 'undefined') {
        console.log('✅ SIMPLE: OrgChart.js available');
        initializeSimpleTree();
    } else {
        console.log('⚠️ SIMPLE: OrgChart.js not available, showing fallback');
        showSimpleFallback();
    }
});

function updateDebugInfo() {
    const debugHtml = `
        <strong>✅ JavaScript Status:</strong><br>
        • PHP Data: ${typeof window.networkDataJson !== 'undefined' ? '✅ Loaded' : '❌ Missing'}<br>
        • BACKEND_DATA: ${typeof window.BACKEND_DATA !== 'undefined' ? '✅ Created' : '❌ Missing'}<br>
        • jQuery: ${typeof $ !== 'undefined' ? '✅ Available' : '❌ Missing'}<br>
        • OrgChart.js: ${typeof $.fn.orgchart !== 'undefined' ? '✅ Available' : '❌ Missing'}<br>
        • User: ${window.BACKEND_DATA?.user?.name || 'Unknown'}<br>
        • Direct Referrals: ${window.BACKEND_DATA?.networkData?.direct_referrals || 'Unknown'}
    `;
    $('#debugInfo').html(debugHtml);
}

function initializeSimpleTree() {
    console.log('✅ SIMPLE: Initializing tree...');
    
    const treeData = {
        id: window.BACKEND_DATA.user.id || 1,
        name: window.BACKEND_DATA.user.name || 'Test User',
        title: 'Network Root',
        children: [
            { id: 2, name: 'Test Child 1', title: 'Sub IB' },
            { id: 3, name: 'Test Child 2', title: 'Client' }
        ]
    };
    
    try {
        $('#networkContainer').empty().orgchart({
            data: treeData,
            nodeContent: 'name',
            direction: 't2b'
        });
        
        console.log('✅ SIMPLE: Tree initialized successfully');
        $('#networkContainer').prepend('<div class="alert alert-success">✅ OrgChart.js tree loaded!</div>');
        
    } catch (error) {
        console.error('❌ SIMPLE: OrgChart error:', error);
        showSimpleFallback();
    }
}

function showSimpleFallback() {
    console.log('✅ SIMPLE: Showing fallback tree...');
    
    const fallbackHtml = `
        <div class="alert alert-warning">⚠️ OrgChart.js fallback mode</div>
        <div class="text-center">
            <h4>Network Tree (Fallback Mode)</h4>
            <div class="row justify-content-center">
                <div class="col-md-4">
                    <div class="card border-primary">
                        <div class="card-body text-center">
                            <h6>${window.BACKEND_DATA.user.name || 'Test User'}</h6>
                            <small>MT5: ${window.BACKEND_DATA.user.mt5_login || 'N/A'}</small><br>
                            <span class="badge bg-primary">Master IB</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    $('#networkContainer').html(fallbackHtml);
    console.log('✅ SIMPLE: Fallback displayed');
}

// Test functions
function testOrgChart() {
    console.log('✅ SIMPLE: Testing OrgChart manually...');
    initializeSimpleTree();
}

function testFallback() {
    console.log('✅ SIMPLE: Testing fallback manually...');
    showSimpleFallback();
}

function showDebugData() {
    console.log('=== SIMPLE DEBUG DATA ===');
    console.log('window.BACKEND_DATA:', window.BACKEND_DATA);
    console.log('jQuery available:', typeof $ !== 'undefined');
    console.log('OrgChart available:', typeof $.fn.orgchart !== 'undefined');
    alert('Debug data logged to console. Check F12 → Console tab');
}
</script>
@endpush

@endsection
