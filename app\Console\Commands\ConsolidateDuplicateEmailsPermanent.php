<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use Carbon\Carbon;

class ConsolidateDuplicateEmailsPermanent extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:consolidate-duplicates 
                            {--dry-run : Show what would be done without making changes}
                            {--limit=100 : Maximum number of duplicate groups to process}
                            {--email= : Process specific email only}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'PERMANENT FIX: Consolidate duplicate email accounts ensuring one email = one display account';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔧 PERMANENT DUPLICATE EMAIL CONSOLIDATION');
        $this->info('==========================================');

        $dryRun = $this->option('dry-run');
        $limit = $this->option('limit');
        $specificEmail = $this->option('email');

        if ($dryRun) {
            $this->warn('🧪 DRY RUN MODE - No changes will be made');
        }

        try {
            $startTime = microtime(true);
            
            // Find duplicate emails
            $query = DB::table('users')
                ->select('email', DB::raw('COUNT(*) as count'), DB::raw('GROUP_CONCAT(id ORDER BY created_at DESC) as user_ids'))
                ->whereNotNull('email')
                ->where('email', '!=', '');

            if ($specificEmail) {
                $query->where('email', $specificEmail);
                $this->info("🎯 Processing specific email: {$specificEmail}");
            } else {
                $query->groupBy('email')
                      ->having('count', '>', 1)
                      ->limit($limit);
                $this->info("📊 Processing up to {$limit} duplicate email groups");
            }

            $duplicateEmails = $query->get();
            
            if (count($duplicateEmails) == 0) {
                $this->info("✅ No duplicate emails found!");
                return 0;
            }

            $this->info("📊 Found " . count($duplicateEmails) . " duplicate email groups");
            
            $consolidated = 0;
            $errors = 0;
            $totalAccountsProcessed = 0;

            foreach ($duplicateEmails as $emailGroup) {
                try {
                    $result = $this->consolidateEmailGroup($emailGroup, $dryRun);
                    
                    if ($result['success']) {
                        $consolidated++;
                        $totalAccountsProcessed += $result['accounts_processed'];
                        
                        $this->line("✅ {$emailGroup->email}: Consolidated {$result['accounts_processed']} accounts");
                    } else {
                        $errors++;
                        $this->error("❌ {$emailGroup->email}: {$result['error']}");
                    }
                    
                } catch (\Exception $e) {
                    $errors++;
                    $this->error("❌ Error processing {$emailGroup->email}: " . $e->getMessage());
                }
            }

            $endTime = microtime(true);
            $duration = round($endTime - $startTime, 2);

            // Summary
            $this->info("\n🎉 CONSOLIDATION SUMMARY");
            $this->table(['Metric', 'Count'], [
                ['Email Groups Processed', count($duplicateEmails)],
                ['Successfully Consolidated', $consolidated],
                ['Total Accounts Processed', $totalAccountsProcessed],
                ['Errors', $errors],
                ['Duration', "{$duration} seconds"]
            ]);

            if (!$dryRun && $consolidated > 0) {
                $this->info('✅ Changes have been permanently saved to the database');
                $this->info('🔄 The admin user list will now show only one account per email');
            }

            return 0;

        } catch (\Exception $e) {
            $this->error('💥 Consolidation failed: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * Consolidate a single email group
     */
    private function consolidateEmailGroup($emailGroup, $dryRun = false)
    {
        $userIds = explode(',', $emailGroup->user_ids);
        $keepUserId = $userIds[0]; // Most recent user (first in DESC order)
        $duplicateUserIds = array_slice($userIds, 1);

        if (empty($duplicateUserIds)) {
            return ['success' => true, 'accounts_processed' => 1];
        }

        // Get the user to keep and all duplicates
        $keepUser = User::find($keepUserId);
        $duplicateUsers = User::whereIn('id', $duplicateUserIds)->get();

        if (!$keepUser) {
            return ['success' => false, 'error' => 'Could not find user to keep'];
        }

        if ($dryRun) {
            $this->line("🧪 Would consolidate {$emailGroup->email}:");
            $this->line("   Keep: User ID {$keepUser->id} ({$keepUser->username})");
            foreach ($duplicateUsers as $dup) {
                $this->line("   Delete: User ID {$dup->id} ({$dup->username})");
            }
            return ['success' => true, 'accounts_processed' => count($userIds)];
        }

        // Consolidate all MT5 data
        $allMT5Logins = [];
        $latestMT5Data = null;
        $latestMT5Timestamp = null;

        // Add current user's MT5 login if exists
        if ($keepUser->mt5_login) {
            $allMT5Logins[] = $keepUser->mt5_login;
            $latestMT5Data = $keepUser;
            $latestMT5Timestamp = $keepUser->mt5_synced_at;
        }

        // Process duplicates
        foreach ($duplicateUsers as $duplicateUser) {
            // Collect MT5 data
            if ($duplicateUser->mt5_login) {
                $allMT5Logins[] = $duplicateUser->mt5_login;
                
                // Keep the most recent MT5 data
                if (!$latestMT5Timestamp || 
                    ($duplicateUser->mt5_synced_at && $duplicateUser->mt5_synced_at > $latestMT5Timestamp)) {
                    $latestMT5Data = $duplicateUser;
                    $latestMT5Timestamp = $duplicateUser->mt5_synced_at;
                }
            }
            
            // Update referral relationships
            User::where('ref_by', $duplicateUser->id)->update(['ref_by' => $keepUser->id]);
            
            // Update IB relationships
            User::where('ib_parent_id', $duplicateUser->id)->update(['ib_parent_id' => $keepUser->id]);
            
            // Transfer IB status if needed
            if ($duplicateUser->partner == 1 && $keepUser->partner != 1) {
                $keepUser->update([
                    'partner' => 1,
                    'ib_status' => $duplicateUser->ib_status,
                    'ib_type' => $duplicateUser->ib_type,
                    'ib_group_id' => $duplicateUser->ib_group_id,
                    'ib_commission_rate' => $duplicateUser->ib_commission_rate,
                    'ib_max_levels' => $duplicateUser->ib_max_levels,
                    'ib_approved_at' => $duplicateUser->ib_approved_at,
                    'ib_approved_by' => $duplicateUser->ib_approved_by,
                    'referral_code' => $duplicateUser->referral_code,
                ]);
            }
        }

        // Update the kept user with consolidated data
        $updateData = [];

        // Update with latest MT5 data if different from current
        if ($latestMT5Data && $latestMT5Data->id != $keepUser->id) {
            // Check if the MT5 login already exists in another user to avoid constraint violation
            $existingUser = User::where('mt5_login', $latestMT5Data->mt5_login)
                ->where('id', '!=', $keepUser->id)
                ->first();

            if (!$existingUser) {
                $this->transferMT5Data($latestMT5Data, $updateData);
            } else {
                // If MT5 login already exists, just update the all_mt5_accounts field
                $this->line("   ⚠️ MT5 login {$latestMT5Data->mt5_login} already exists, skipping transfer");
            }
        }

        // Store all MT5 accounts for comprehensive search
        if (!empty($allMT5Logins)) {
            $updateData['all_mt5_accounts'] = implode(',', array_unique($allMT5Logins));
        }

        if (!empty($updateData)) {
            try {
                $keepUser->update($updateData);
            } catch (\Exception $e) {
                // If update fails due to constraint, try without MT5 data
                if (strpos($e->getMessage(), 'users_mt5_login_unique') !== false) {
                    unset($updateData['mt5_login']);
                    $keepUser->update($updateData);
                    $this->line("   ⚠️ Updated without MT5 login due to constraint violation");
                } else {
                    throw $e;
                }
            }
        }

        // Delete duplicate users
        User::whereIn('id', $duplicateUserIds)->delete();

        return [
            'success' => true, 
            'accounts_processed' => count($userIds),
            'mt5_accounts_consolidated' => count($allMT5Logins)
        ];
    }

    /**
     * Transfer MT5 data from one user to update array
     */
    private function transferMT5Data($fromUser, &$updateData)
    {
        $mt5Fields = [
            'mt5_login', 'mt5_timestamp', 'mt5_group', 'mt5_cert_serial_number', 'mt5_rights',
            'mt5_registration', 'mt5_last_access', 'mt5_last_pass_change', 'mt5_first_name',
            'mt5_last_name', 'mt5_middle_name', 'mt5_company', 'mt5_account', 'mt5_country',
            'mt5_language', 'mt5_client_id', 'mt5_city', 'mt5_state', 'mt5_zip_code',
            'mt5_address', 'mt5_phone', 'mt5_email', 'mt5_id', 'mt5_status', 'mt5_comment',
            'mt5_color', 'mt5_phone_password', 'mt5_leverage', 'mt5_agent', 'mt5_trade_accounts',
            'mt5_limit_positions', 'mt5_limit_orders', 'mt5_lead_campaign', 'mt5_lead_source',
            'mt5_timestamp_trade', 'mt5_balance', 'mt5_credit', 'mt5_equity', 'mt5_interest_rate',
            'mt5_commission_daily', 'mt5_commission_monthly', 'mt5_balance_prev_day',
            'mt5_balance_prev_month', 'mt5_equity_prev_day', 'mt5_equity_prev_month',
            'mt5_name', 'mt5_mqid', 'mt5_last_ip', 'mt5_api_data', 'mt5_currency', 'mt5_synced_at'
        ];
        
        foreach ($mt5Fields as $field) {
            if ($fromUser->$field !== null) {
                $updateData[$field] = $fromUser->$field;
            }
        }
    }
}
