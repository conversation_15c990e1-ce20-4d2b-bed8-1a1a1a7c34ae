<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <!-- Enhanced Static Content MIME Types for Live Server -->
        <staticContent>
            <!-- CSS Files -->
            <remove fileExtension=".css" />
            <mimeMap fileExtension=".css" mimeType="text/css" />
            
            <!-- JavaScript Files -->
            <remove fileExtension=".js" />
            <mimeMap fileExtension=".js" mimeType="application/javascript" />
            
            <!-- JSON Files -->
            <remove fileExtension=".json" />
            <mimeMap fileExtension=".json" mimeType="application/json" />
            
            <!-- Font Files -->
            <remove fileExtension=".woff" />
            <mimeMap fileExtension=".woff" mimeType="font/woff" />
            <remove fileExtension=".woff2" />
            <mimeMap fileExtension=".woff2" mimeType="font/woff2" />
            <remove fileExtension=".ttf" />
            <mimeMap fileExtension=".ttf" mimeType="font/ttf" />
            <remove fileExtension=".eot" />
            <mimeMap fileExtension=".eot" mimeType="application/vnd.ms-fontobject" />
            
            <!-- Image Files -->
            <remove fileExtension=".svg" />
            <mimeMap fileExtension=".svg" mimeType="image/svg+xml" />
            <remove fileExtension=".webp" />
            <mimeMap fileExtension=".webp" mimeType="image/webp" />
            
            <!-- Source Map Files -->
            <remove fileExtension=".map" />
            <mimeMap fileExtension=".map" mimeType="application/json" />
            
            <!-- Python Files (for API system) -->
            <remove fileExtension=".py" />
            <mimeMap fileExtension=".py" mimeType="text/x-python" />
        </staticContent>

        <!-- Enhanced URL Rewrite Rules for Live Server -->
        <rewrite>
            <rules>
                <!-- Rule 1: Python Scripts (Highest Priority) -->
                <rule name="Python Scripts" stopProcessing="true">
                    <match url="^(.*)\.(py)$" />
                    <conditions>
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" />
                    </conditions>
                    <action type="None" />
                </rule>

                <!-- Rule 2: Static Assets Directory (High Priority) -->
                <rule name="Static Assets Directory" stopProcessing="true">
                    <match url="^(assets|public/assets)/(.*)$" />
                    <conditions>
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" />
                    </conditions>
                    <action type="None" />
                </rule>

                <!-- Rule 3: Common Static Directories -->
                <rule name="Static Directories" stopProcessing="true">
                    <match url="^(css|js|images|img|fonts|uploads|storage|vendor)/(.*)$" />
                    <conditions>
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" />
                    </conditions>
                    <action type="None" />
                </rule>

                <!-- Rule 4: Static File Extensions (Critical for Email Editor) -->
                <rule name="Static File Extensions" stopProcessing="true">
                    <match url="\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map|json|xml|txt|pdf|zip|rar|doc|docx|xls|xlsx|ppt|pptx)$" />
                    <conditions>
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" />
                    </conditions>
                    <action type="None" />
                </rule>

                <!-- Rule 5: Laravel Public Directory -->
                <rule name="Laravel Public" stopProcessing="true">
                    <match url="^public/(.*)$" />
                    <conditions>
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" />
                    </conditions>
                    <action type="None" />
                </rule>

                <!-- Rule 6: Laravel Routes (Lowest Priority) -->
                <rule name="Laravel Routes">
                    <match url="^(.*)$" ignoreCase="false" />
                    <conditions>
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" ignoreCase="false" negate="true" />
                        <add input="{REQUEST_FILENAME}" matchType="IsDirectory" ignoreCase="false" negate="true" />
                        <add input="{URL}" pattern="^/favicon.ico$" ignoreCase="true" negate="true" />
                    </conditions>
                    <action type="Rewrite" url="index.php" appendQueryString="true" />
                </rule>
            </rules>
        </rewrite>

        <!-- Handlers for PHP 8.4 and Python -->
        <handlers>
            <!-- Python Handler (for API system) -->
            <add name="Python" path="*.py" verb="*" modules="FastCgiModule" scriptProcessor="C:\Python312\python.exe|C:\Python312\Lib\site-packages\wfastcgi.py" resourceType="File" requireAccess="Script" />
            
            <!-- PHP 8.4 Handler -->
            <add name="PHP84_via_FastCGI" path="*.php" verb="GET,HEAD,POST,PUT,DELETE,OPTIONS" modules="FastCgiModule" scriptProcessor="C:\php84\php-cgi.exe" resourceType="Either" requireAccess="Script" />
        </handlers>

        <!-- Security Settings -->
        <security>
            <requestFiltering>
                <!-- Increased limits for file uploads and large requests -->
                <requestLimits maxAllowedContentLength="52428800" maxUrl="4096" maxQueryString="2048" />
                
                <!-- Allow common file extensions -->
                <fileExtensions>
                    <add fileExtension=".php" allowed="true" />
                    <add fileExtension=".py" allowed="true" />
                </fileExtensions>
            </requestFiltering>
        </security>

        <!-- Enhanced Caching for Static Assets -->
        <caching>
            <profiles>
                <!-- CSS Files - Cache for 1 hour -->
                <add extension=".css" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="1.00:00:00" />
                
                <!-- JavaScript Files - Cache for 1 hour -->
                <add extension=".js" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="1.00:00:00" />
                
                <!-- Image Files - Cache for 1 day -->
                <add extension=".png" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="1.00:00:00" />
                <add extension=".jpg" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="1.00:00:00" />
                <add extension=".jpeg" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="1.00:00:00" />
                <add extension=".gif" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="1.00:00:00" />
                <add extension=".svg" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="1.00:00:00" />
                
                <!-- Font Files - Cache for 1 week -->
                <add extension=".woff" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="7.00:00:00" />
                <add extension=".woff2" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="7.00:00:00" />
                <add extension=".ttf" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="7.00:00:00" />
                <add extension=".eot" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="7.00:00:00" />
            </profiles>
        </caching>

        <!-- HTTP Response Headers for Better Asset Loading -->
        <httpProtocol>
            <customHeaders>
                <!-- Security Headers -->
                <add name="X-Content-Type-Options" value="nosniff" />
                <add name="X-Frame-Options" value="SAMEORIGIN" />
                
                <!-- CORS Headers for Assets (if needed) -->
                <add name="Access-Control-Allow-Origin" value="*" />
                <add name="Access-Control-Allow-Methods" value="GET, POST, PUT, DELETE, OPTIONS" />
                <add name="Access-Control-Allow-Headers" value="Content-Type, Authorization, X-Requested-With" />
            </customHeaders>
        </httpProtocol>

        <!-- Detailed HTTP Errors for Debugging -->
        <httpErrors errorMode="Detailed">
            <!-- Custom error pages -->
            <remove statusCode="400" />
            <error statusCode="400" path="/errors/400.html" responseMode="File" />
            
            <remove statusCode="401" />
            <error statusCode="401" path="/errors/401.html" responseMode="File" />
            
            <remove statusCode="403" />
            <error statusCode="403" path="/errors/403.html" responseMode="File" />
            
            <remove statusCode="404" />
            <error statusCode="404" path="/errors/404.html" responseMode="File" />
            
            <remove statusCode="405" />
            <error statusCode="405" path="/errors/405.html" responseMode="File" />
            
            <remove statusCode="406" />
            <error statusCode="406" path="/errors/406.html" responseMode="File" />
            
            <remove statusCode="412" />
            <error statusCode="412" path="/errors/412.html" responseMode="File" />
            
            <remove statusCode="500" />
            <error statusCode="500" path="/errors/500.html" responseMode="File" />
            
            <remove statusCode="501" />
            <error statusCode="501" path="/errors/501.html" responseMode="File" />
            
            <remove statusCode="502" />
            <error statusCode="502" path="/errors/502.html" responseMode="File" />
        </httpErrors>

        <!-- Default Document Settings -->
        <defaultDocument>
            <files>
                <clear />
                <add value="index.php" />
                <add value="index.html" />
                <add value="index.htm" />
            </files>
        </defaultDocument>

        <!-- Directory Browsing -->
        <directoryBrowse enabled="false" />

        <!-- Compression for Better Performance -->
        <urlCompression doStaticCompression="true" doDynamicCompression="true" />
        
        <httpCompression>
            <scheme name="gzip" dll="%Windir%\system32\inetsrv\gzip.dll" />
            <dynamicTypes>
                <add mimeType="text/*" enabled="true" />
                <add mimeType="message/*" enabled="true" />
                <add mimeType="application/javascript" enabled="true" />
                <add mimeType="application/json" enabled="true" />
                <add mimeType="*/*" enabled="false" />
            </dynamicTypes>
            <staticTypes>
                <add mimeType="text/*" enabled="true" />
                <add mimeType="message/*" enabled="true" />
                <add mimeType="application/javascript" enabled="true" />
                <add mimeType="application/json" enabled="true" />
                <add mimeType="*/*" enabled="false" />
            </staticTypes>
        </httpCompression>
    </system.webServer>

    <!-- PHP Configuration -->
    <system.web>
        <compilation debug="false" targetFramework="4.0" />
        <httpRuntime maxRequestLength="51200" executionTimeout="300" />
    </system.web>
</configuration>