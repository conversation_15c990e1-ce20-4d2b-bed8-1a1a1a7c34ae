<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ResolveDuplicateEmails extends Command
{
    protected $signature = 'mt5:resolve-duplicates
                            {--dry-run : Show what would be done without making changes}
                            {--auto-resolve : Automatically resolve duplicates without confirmation}
                            {--limit=100 : Number of duplicate emails to process}
                            {--email= : Resolve specific email only}';

    protected $description = 'Resolve duplicate email addresses by identifying primary accounts and marking others as secondary';

    public function handle()
    {
        $dryRun = $this->option('dry-run');
        $autoResolve = $this->option('auto-resolve');
        $limit = (int) $this->option('limit');
        $specificEmail = $this->option('email');

        $this->info('🔄 Starting Duplicate Email Resolution...');
        $this->info('==========================================');

        if ($dryRun) {
            $this->warn('🧪 DRY RUN MODE - No changes will be made');
        }

        try {
            // Get duplicate emails
            $duplicateEmailsQuery = DB::table('users')
                ->select('email', DB::raw('COUNT(*) as account_count'))
                ->whereNotNull('mt5_login')
                ->groupBy('email')
                ->having('account_count', '>', 1)
                ->orderBy('account_count', 'desc');

            if ($specificEmail) {
                $duplicateEmailsQuery->where('email', $specificEmail);
                $this->info("🎯 Processing specific email: {$specificEmail}");
            } else {
                $duplicateEmailsQuery->limit($limit);
                $this->info("📊 Processing top {$limit} duplicate emails");
            }

            $duplicateEmails = $duplicateEmailsQuery->get();

            if ($duplicateEmails->isEmpty()) {
                $this->info('✅ No duplicate emails found to process');
                return 0;
            }

            $this->info("Found {$duplicateEmails->count()} emails with multiple accounts");

            $processed = 0;
            $resolved = 0;
            $errors = 0;

            foreach ($duplicateEmails as $emailGroup) {
                try {
                    $result = $this->resolveDuplicateEmail($emailGroup->email, $dryRun, $autoResolve);
                    
                    if ($result['resolved']) {
                        $resolved++;
                    }
                    
                    $processed++;

                    if ($processed % 10 === 0) {
                        $this->info("📈 Processed: {$processed} | Resolved: {$resolved} | Errors: {$errors}");
                    }

                } catch (\Exception $e) {
                    $errors++;
                    $this->error("❌ Error processing {$emailGroup->email}: " . $e->getMessage());
                    Log::error('Duplicate Resolution Error', [
                        'email' => $emailGroup->email,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // Final summary
            $this->info('');
            $this->info('🎉 Duplicate Resolution Completed!');
            $this->table(['Metric', 'Count'], [
                ['Emails Processed', $processed],
                ['Duplicates Resolved', $resolved],
                ['Errors', $errors]
            ]);

            if (!$dryRun) {
                $this->info('✅ All changes have been saved to the database');
            } else {
                $this->warn('🧪 DRY RUN - No actual changes were made');
            }

        } catch (\Exception $e) {
            $this->error('💥 Duplicate resolution failed: ' . $e->getMessage());
            Log::error('Duplicate Resolution Command Failed', ['error' => $e->getMessage()]);
            return 1;
        }

        return 0;
    }

    /**
     * Resolve duplicates for a specific email
     */
    private function resolveDuplicateEmail($email, $dryRun = false, $autoResolve = false)
    {
        $this->line("\n📧 Processing email: {$email}");

        // Get all accounts for this email
        $accounts = User::where('email', $email)
            ->whereNotNull('mt5_login')
            ->orderBy('created_at', 'asc')
            ->get();

        if ($accounts->count() <= 1) {
            return ['resolved' => false, 'reason' => 'No duplicates found'];
        }

        $this->line("   Found {$accounts->count()} accounts");

        // Analyze accounts and determine primary
        $analysis = $this->analyzeAccounts($accounts);
        $primaryAccount = $analysis['primary'];
        $secondaryAccounts = $analysis['secondary'];

        $this->line("   📋 Analysis Results:");
        $this->line("      Primary: ID {$primaryAccount->id} | MT5: {$primaryAccount->mt5_login} | Reason: {$analysis['reason']}");
        
        foreach ($secondaryAccounts as $account) {
            $this->line("      Secondary: ID {$account->id} | MT5: {$account->mt5_login}");
        }

        // Confirm resolution unless auto-resolve is enabled
        if (!$autoResolve && !$dryRun) {
            if (!$this->confirm("Proceed with resolution for {$email}?")) {
                return ['resolved' => false, 'reason' => 'User cancelled'];
            }
        }

        if ($dryRun) {
            $this->line("   🧪 Would mark {$secondaryAccounts->count()} accounts as secondary");
            return ['resolved' => true, 'reason' => 'Dry run completed'];
        }

        // Mark secondary accounts
        foreach ($secondaryAccounts as $account) {
            $account->update([
                'is_primary_account' => false,
                'primary_account_id' => $primaryAccount->id,
                'account_status' => 'secondary',
                'duplicate_resolved_at' => now()
            ]);
        }

        // Mark primary account
        $primaryAccount->update([
            'is_primary_account' => true,
            'primary_account_id' => null,
            'account_status' => 'primary',
            'duplicate_resolved_at' => now()
        ]);

        $this->line("   ✅ Resolved: 1 primary + {$secondaryAccounts->count()} secondary accounts");

        return ['resolved' => true, 'reason' => 'Successfully resolved'];
    }

    /**
     * Analyze accounts to determine primary account
     */
    private function analyzeAccounts($accounts)
    {
        $ibAccount = null;
        $highestBalanceAccount = null;
        $oldestAccount = $accounts->first();
        $accountWithCommissions = null;

        foreach ($accounts as $account) {
            // Check if account is IB based on group
            $isIB = $this->isIBAccount($account);
            
            if ($isIB && !$ibAccount) {
                $ibAccount = $account;
            }

            // Check for highest balance
            if (!$highestBalanceAccount || $account->mt5_balance > $highestBalanceAccount->mt5_balance) {
                $highestBalanceAccount = $account;
            }

            // Check for commission earnings
            if ($this->hasCommissionEarnings($account->mt5_login) && !$accountWithCommissions) {
                $accountWithCommissions = $account;
            }
        }

        // Determine primary account based on priority
        if ($ibAccount) {
            $primary = $ibAccount;
            $reason = 'IB Account (Group: ' . $ibAccount->mt5_group . ')';
        } elseif ($accountWithCommissions) {
            $primary = $accountWithCommissions;
            $reason = 'Has Commission Earnings';
        } elseif ($highestBalanceAccount && $highestBalanceAccount->mt5_balance > 0) {
            $primary = $highestBalanceAccount;
            $reason = 'Highest Balance ($' . number_format($highestBalanceAccount->mt5_balance, 2) . ')';
        } else {
            $primary = $oldestAccount;
            $reason = 'Oldest Account';
        }

        $secondary = $accounts->reject(function ($account) use ($primary) {
            return $account->id === $primary->id;
        });

        return [
            'primary' => $primary,
            'secondary' => $secondary,
            'reason' => $reason
        ];
    }

    /**
     * Check if account is IB based on MT5 group
     */
    private function isIBAccount($account)
    {
        $ibGroups = [
            'real\\Affiliates',
            'real\\Multi-IB\\',
            'real\\IB\\',
            'real\\Partner',
            '1:1'
        ];

        foreach ($ibGroups as $ibGroup) {
            if (strpos($account->mt5_group, $ibGroup) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if account has commission earnings
     */
    private function hasCommissionEarnings($mt5Login)
    {
        try {
            $commissions = DB::connection('mbf-dbmt5')
                ->table('mt5_deals_2025')
                ->where('Login', $mt5Login)
                ->where('Action', 18) // Commission action
                ->count();

            return $commissions > 0;
        } catch (\Exception $e) {
            return false;
        }
    }
}
