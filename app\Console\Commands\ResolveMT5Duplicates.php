<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ResolveMT5Duplicates extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'mt5:resolve-duplicates 
                            {--dry-run : Show what would be done without making changes}
                            {--auto-resolve : Automatically resolve duplicates using smart logic}
                            {--limit=100 : Number of duplicate groups to process}';

    /**
     * The console command description.
     */
    protected $description = 'Resolve duplicate email addresses in MT5 user data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 MT5 DUPLICATE RESOLUTION STARTING');
        $this->info('====================================');

        $dryRun = $this->option('dry-run');
        $autoResolve = $this->option('auto-resolve');
        $limit = $this->option('limit');

        if ($dryRun) {
            $this->warn('🧪 DRY RUN MODE - No changes will be made');
        }

        // Find duplicate emails
        $duplicateEmails = $this->findDuplicateEmails($limit);
        
        if (empty($duplicateEmails)) {
            $this->info('✅ No duplicate emails found');
            return 0;
        }

        $this->info("📊 Found " . count($duplicateEmails) . " email addresses with duplicates");

        $resolved = 0;
        $skipped = 0;
        $errors = 0;

        foreach ($duplicateEmails as $email => $users) {
            $this->info("\n📧 Processing email: {$email} ({$users->count()} duplicates)");

            try {
                if ($autoResolve) {
                    $result = $this->autoResolveDuplicates($email, $users, $dryRun);
                } else {
                    $result = $this->manualResolveDuplicates($email, $users, $dryRun);
                }

                if ($result === 'resolved') {
                    $resolved++;
                } elseif ($result === 'skipped') {
                    $skipped++;
                }

            } catch (\Exception $e) {
                $errors++;
                $this->error("❌ Error processing {$email}: " . $e->getMessage());
                Log::error('Duplicate resolution error', [
                    'email' => $email,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // Summary
        $this->info("\n🎉 DUPLICATE RESOLUTION SUMMARY");
        $this->table(['Metric', 'Count'], [
            ['Duplicate Groups Processed', count($duplicateEmails)],
            ['Successfully Resolved', $resolved],
            ['Skipped', $skipped],
            ['Errors', $errors]
        ]);

        if (!$dryRun && $resolved > 0) {
            $this->info('✅ Changes have been saved to the database');
        }

        return 0;
    }

    /**
     * Find duplicate email addresses
     */
    private function findDuplicateEmails($limit)
    {
        $duplicateEmails = User::select('email')
            ->groupBy('email')
            ->havingRaw('COUNT(*) > 1')
            ->limit($limit)
            ->pluck('email');

        $duplicates = [];
        
        foreach ($duplicateEmails as $email) {
            $users = User::where('email', $email)
                ->orderBy('created_at', 'asc')
                ->get();
            
            $duplicates[$email] = $users;
        }

        return $duplicates;
    }

    /**
     * Auto-resolve duplicates using smart logic
     */
    private function autoResolveDuplicates($email, $users, $dryRun)
    {
        $this->line("🤖 Auto-resolving duplicates for: {$email}");

        // Strategy 1: Keep the user with MT5 data, remove others without MT5 data
        $usersWithMT5 = $users->whereNotNull('mt5_login');
        $usersWithoutMT5 = $users->whereNull('mt5_login');

        if ($usersWithMT5->count() === 1 && $usersWithoutMT5->count() > 0) {
            $keepUser = $usersWithMT5->first();
            $removeUsers = $usersWithoutMT5;
            
            $this->line("  → Strategy: Keep user with MT5 data (ID: {$keepUser->id})");
            return $this->executeResolution($keepUser, $removeUsers, $dryRun);
        }

        // Strategy 2: Keep the oldest user, remove newer duplicates
        if ($usersWithMT5->count() > 1) {
            $keepUser = $users->sortBy('created_at')->first();
            $removeUsers = $users->except($keepUser->id);
            
            $this->line("  → Strategy: Keep oldest user (ID: {$keepUser->id})");
            return $this->executeResolution($keepUser, $removeUsers, $dryRun);
        }

        // Strategy 3: Keep user with most complete profile
        $bestUser = $this->findMostCompleteUser($users);
        if ($bestUser) {
            $removeUsers = $users->except($bestUser->id);
            
            $this->line("  → Strategy: Keep most complete profile (ID: {$bestUser->id})");
            return $this->executeResolution($bestUser, $removeUsers, $dryRun);
        }

        $this->warn("  → Cannot auto-resolve: Complex case requiring manual review");
        return 'skipped';
    }

    /**
     * Manual resolution with user interaction
     */
    private function manualResolveDuplicates($email, $users, $dryRun)
    {
        $this->line("👤 Manual resolution required for: {$email}");

        // Display user options
        $this->table(['ID', 'Name', 'MT5 Login', 'Created', 'Status'], 
            $users->map(function($user) {
                return [
                    $user->id,
                    $user->firstname . ' ' . $user->lastname,
                    $user->mt5_login ?: 'None',
                    $user->created_at->format('Y-m-d'),
                    $user->status ? 'Active' : 'Inactive'
                ];
            })->toArray()
        );

        $keepUserId = $this->ask('Which user ID should be kept? (Enter ID or "skip")');

        if ($keepUserId === 'skip') {
            $this->line("  → Skipped manual resolution");
            return 'skipped';
        }

        $keepUser = $users->where('id', $keepUserId)->first();
        if (!$keepUser) {
            $this->error("  → Invalid user ID: {$keepUserId}");
            return 'skipped';
        }

        $removeUsers = $users->except($keepUser->id);
        
        $this->line("  → Manual choice: Keep user ID {$keepUser->id}");
        return $this->executeResolution($keepUser, $removeUsers, $dryRun);
    }

    /**
     * Execute the resolution by keeping one user and removing others
     */
    private function executeResolution($keepUser, $removeUsers, $dryRun)
    {
        $this->line("  ✅ Keeping: {$keepUser->firstname} {$keepUser->lastname} (ID: {$keepUser->id})");

        foreach ($removeUsers as $removeUser) {
            $this->line("  🗑️  Removing: {$removeUser->firstname} {$removeUser->lastname} (ID: {$removeUser->id})");
            
            if (!$dryRun) {
                // Before deleting, merge any important data
                $this->mergeUserData($keepUser, $removeUser);
                
                // Delete the duplicate user
                $removeUser->delete();
            }
        }

        return 'resolved';
    }

    /**
     * Find the user with the most complete profile
     */
    private function findMostCompleteUser($users)
    {
        $scores = [];

        foreach ($users as $user) {
            $score = 0;
            
            // Score based on data completeness
            if ($user->mt5_login) $score += 10;
            if ($user->firstname && $user->firstname !== 'Unknown') $score += 5;
            if ($user->lastname && $user->lastname !== 'User') $score += 5;
            if ($user->mobile) $score += 3;
            if ($user->address) $score += 2;
            if ($user->country_code) $score += 2;
            if ($user->status) $score += 5; // Active user
            if ($user->ev) $score += 3; // Email verified
            if ($user->kv) $score += 3; // KYC verified
            
            $scores[$user->id] = $score;
        }

        $bestUserId = array_keys($scores, max($scores))[0];
        return $users->where('id', $bestUserId)->first();
    }

    /**
     * Merge important data from duplicate user to keep user
     */
    private function mergeUserData($keepUser, $removeUser)
    {
        $updateData = [];

        // Merge missing data from removeUser to keepUser
        if (!$keepUser->mobile && $removeUser->mobile) {
            $updateData['mobile'] = $removeUser->mobile;
        }
        
        if (!$keepUser->address && $removeUser->address) {
            $updateData['address'] = $removeUser->address;
        }
        
        if (!$keepUser->country_code && $removeUser->country_code) {
            $updateData['country_code'] = $removeUser->country_code;
        }

        // Merge verification status (keep the best status)
        if (!$keepUser->ev && $removeUser->ev) {
            $updateData['ev'] = 1;
        }
        
        if (!$keepUser->sv && $removeUser->sv) {
            $updateData['sv'] = 1;
        }
        
        if (!$keepUser->kv && $removeUser->kv) {
            $updateData['kv'] = 1;
        }

        // Update if there's data to merge
        if (!empty($updateData)) {
            $keepUser->update($updateData);
            $this->line("    📝 Merged data from duplicate user");
        }
    }
}
