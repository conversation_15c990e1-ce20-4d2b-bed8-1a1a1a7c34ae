<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔄 CREATING TEST COMMISSION DATA\n";
echo "================================\n";

try {
    // Get real IBs
    $realIBs = [
        '<EMAIL>' => 865607,
        '<EMAIL>' => 866207,
        '<EMAIL>' => 866426
    ];

    // Get some random users to act as clients
    $randomUsers = \App\Models\User::whereNotNull('mt5_login')
        ->where('partner', 0) // Non-IB users
        ->limit(10)
        ->get();

    echo "📊 Found {$randomUsers->count()} potential client users\n";

    $commissionId = 1;
    
    foreach ($realIBs as $email => $ibMt5Login) {
        $ibUser = \App\Models\User::where('email', $email)->first();
        if (!$ibUser) continue;

        echo "\n🔍 Creating test commissions for: {$email}\n";

        // Create 5 test commissions for each IB
        for ($i = 1; $i <= 5; $i++) {
            $randomClient = $randomUsers->random();
            
            // Generate realistic commission data
            $brokerCommission = rand(500, 5000) / 100; // $5-50
            $ibCommissionRate = 50.00; // 50%
            $ibCommission = ($brokerCommission * $ibCommissionRate) / 100;
            
            $symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCAD'];
            $symbol = $symbols[array_rand($symbols)];
            
            $volume = rand(10, 500) / 100; // 0.1 to 5.0 lots
            $profit = rand(1000, 10000) / 100; // $10-100 profit
            
            // Create commission record
            DB::table('ib_commissions')->insert([
                'to_ib_user_id' => $ibUser->id,
                'from_user_id' => $randomClient->id,
                'mt5_login' => $randomClient->mt5_login,
                'mt5_deal_id' => time() + $commissionId,
                'commission_amount' => $ibCommission,
                'symbol' => $symbol,
                'volume' => $volume,
                'deal_profit' => $profit,
                'deal_time' => now()->subDays(rand(1, 30)),
                'status' => rand(0, 1) ? 'pending' : 'paid',
                'commission_rate' => $ibCommissionRate,
                'deal_commission' => $brokerCommission,
                'level' => 1,
                'created_at' => now()->subDays(rand(1, 30)),
                'updated_at' => now()
            ]);
            
            echo "   ✅ Commission {$i}: $" . number_format($ibCommission, 2) . 
                 " from {$randomClient->email} ({$symbol})\n";
            
            $commissionId++;
        }
    }

    // Create additional random commissions for testing
    echo "\n🔍 Creating additional test commissions...\n";
    
    for ($i = 1; $i <= 20; $i++) {
        $randomIB = \App\Models\User::where('partner', 1)->inRandomOrder()->first();
        $randomClient = $randomUsers->random();
        
        if ($randomIB) {
            $brokerCommission = rand(200, 2000) / 100; // $2-20
            $ibCommissionRate = rand(30, 60); // 30-60%
            $ibCommission = ($brokerCommission * $ibCommissionRate) / 100;
            
            $symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCAD', 'XAUUSD', 'BTCUSD'];
            $symbol = $symbols[array_rand($symbols)];
            
            DB::table('ib_commissions')->insert([
                'to_ib_user_id' => $randomIB->id,
                'from_user_id' => $randomClient->id,
                'mt5_login' => $randomClient->mt5_login,
                'mt5_deal_id' => time() + $commissionId,
                'commission_amount' => $ibCommission,
                'symbol' => $symbol,
                'volume' => rand(10, 300) / 100,
                'deal_profit' => rand(500, 5000) / 100,
                'deal_time' => now()->subDays(rand(1, 60)),
                'status' => ['pending', 'paid', 'cancelled'][rand(0, 2)],
                'commission_rate' => $ibCommissionRate,
                'deal_commission' => $brokerCommission,
                'level' => rand(1, 3),
                'created_at' => now()->subDays(rand(1, 60)),
                'updated_at' => now()
            ]);
            
            $commissionId++;
        }
    }

    // Summary
    echo "\n📊 COMMISSION DATA SUMMARY\n";
    echo "==========================\n";
    
    $totalCommissions = DB::table('ib_commissions')->count();
    $pendingCommissions = DB::table('ib_commissions')->where('status', 'pending')->count();
    $paidCommissions = DB::table('ib_commissions')->where('status', 'paid')->count();
    $cancelledCommissions = DB::table('ib_commissions')->where('status', 'cancelled')->count();
    
    $totalAmount = DB::table('ib_commissions')->sum('commission_amount');
    $pendingAmount = DB::table('ib_commissions')->where('status', 'pending')->sum('commission_amount');
    $paidAmount = DB::table('ib_commissions')->where('status', 'paid')->sum('commission_amount');
    
    echo "✅ Total Commission Records: {$totalCommissions}\n";
    echo "⏳ Pending Commissions: {$pendingCommissions} ($" . number_format($pendingAmount, 2) . ")\n";
    echo "💰 Paid Commissions: {$paidCommissions} ($" . number_format($paidAmount, 2) . ")\n";
    echo "❌ Cancelled Commissions: {$cancelledCommissions}\n";
    echo "💵 Total Amount: $" . number_format($totalAmount, 2) . "\n";

    // Update IB earnings
    echo "\n📊 UPDATING IB EARNINGS\n";
    echo "=======================\n";
    
    $allIBs = \App\Models\User::where('partner', 1)->get();
    foreach ($allIBs as $ib) {
        $totalEarned = DB::table('ib_commissions')
            ->where('to_ib_user_id', $ib->id)
            ->where('status', 'paid')
            ->sum('commission_amount');
        
        $pendingEarnings = DB::table('ib_commissions')
            ->where('to_ib_user_id', $ib->id)
            ->where('status', 'pending')
            ->sum('commission_amount');
        
        if ($totalEarned > 0 || $pendingEarnings > 0) {
            $ib->update([
                'commission_earnings' => $totalEarned,
                'pending_commission' => $pendingEarnings,
                'last_commission_sync' => now()
            ]);
            
            echo "✅ {$ib->email}: Earned $" . number_format($totalEarned, 2) . 
                 ", Pending $" . number_format($pendingEarnings, 2) . "\n";
        }
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n✅ Test commission data creation completed!\n";
