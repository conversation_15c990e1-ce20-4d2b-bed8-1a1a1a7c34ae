<?php
/**
 * EMERGENCY TEMPLATE RECOVERY SCRIPT
 * This script can restore templates if the enhancement command caused data loss
 */

// Include Laravel bootstrap
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🚨 EMERGENCY TEMPLATE RECOVERY SCRIPT\n";
echo "=====================================\n\n";

// Check current state
$templates = \App\Models\NotificationTemplate::all();
$emptyTemplates = $templates->where('email_body', '')->count();

echo "📊 CURRENT STATE:\n";
echo "Total templates: {$templates->count()}\n";
echo "Empty templates: {$emptyTemplates}\n\n";

if ($emptyTemplates === 0) {
    echo "✅ No empty templates found. Recovery not needed.\n";
    exit(0);
}

echo "🚨 CRITICAL: {$emptyTemplates} templates have empty content!\n\n";

// Ask for confirmation
echo "Do you want to restore basic content to empty templates? (y/N): ";
$handle = fopen("php://stdin", "r");
$line = fgets($handle);
fclose($handle);

if (trim(strtolower($line)) !== 'y') {
    echo "❌ Recovery cancelled.\n";
    exit(0);
}

echo "\n🔄 STARTING RECOVERY...\n";
echo "======================\n";

$recovered = 0;
$errors = 0;

foreach ($templates as $template) {
    if (empty($template->email_body)) {
        try {
            // Create basic professional template
            $basicContent = createBasicTemplate($template->act, $template->name);
            
            $template->email_body = $basicContent;
            
            if ($template->save()) {
                $recovered++;
                echo "✅ Recovered: {$template->name} (ID: {$template->id})\n";
            } else {
                $errors++;
                echo "❌ Failed to save: {$template->name}\n";
            }
            
        } catch (\Exception $e) {
            $errors++;
            echo "❌ Error recovering {$template->name}: " . $e->getMessage() . "\n";
        }
    }
}

echo "\n📊 RECOVERY RESULTS:\n";
echo "====================\n";
echo "✅ Recovered: {$recovered} templates\n";
echo "❌ Errors: {$errors} templates\n";

if ($recovered > 0) {
    echo "\n🎉 Recovery completed! Templates now have basic professional content.\n";
    echo "💡 You can now use the Visual Builder to customize them further.\n";
}

function createBasicTemplate($templateType, $templateName)
{
    $title = getBasicTitle($templateType, $templateName);
    $content = getBasicContent($templateType);
    
    return '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . $title . '</title>
</head>
<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4; line-height: 1.6;">
    <table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color: #f4f4f4;">
        <tr>
            <td align="center">
                <table width="600" cellpadding="0" cellspacing="0" border="0" style="background-color: #ffffff; box-shadow: 0 0 10px rgba(0,0,0,0.1);">
                    
                    <!-- Header Section -->
                    <tr>
                        <td style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); padding: 30px 40px; text-align: center;">
                            <img src="https://mbf.mybrokerforex.com/assets/images/logoIcon/logo.png" alt="MBFX Logo" style="max-width: 150px; height: auto;">
                        </td>
                    </tr>

                    <!-- Title Section -->
                    <tr>
                        <td style="background-color: #ffffff; padding: 30px 40px 20px; text-align: center;">
                            <h2 style="margin: 0; color: #dc3545; font-size: 28px; font-weight: bold;">' . $title . '</h2>
                        </td>
                    </tr>

                    <!-- Main Content Section -->
                    <tr>
                        <td style="background-color: #ffffff; padding: 20px 40px; color: #333333;">
                            ' . $content . '
                        </td>
                    </tr>

                    <!-- Regards Section -->
                    <tr>
                        <td style="background-color: #ffffff; padding: 20px 40px 30px; color: #333333;">
                            <p style="margin: 15px 0 0 0; font-size: 16px;">Best regards,<br><strong>MBFX Team</strong></p>
                        </td>
                    </tr>

                    <!-- Footer Section -->
                    <tr>
                        <td style="background-color: #000000; padding: 30px 40px; text-align: center; color: #ffffff;">
                            <p style="margin: 0 0 10px 0; font-size: 18px; font-weight: bold;">MBFX - Professional Trading Platform</p>
                            <p style="margin: 0 0 15px 0; font-size: 14px; color: #cccccc;">Account Settings | Contact Support | Privacy Policy</p>
                            <p style="margin: 0; font-size: 12px; color: #999999;">© 2025 MBFX. All rights reserved.</p>
                        </td>
                    </tr>

                </table>
            </td>
        </tr>
    </table>
</body>
</html>';
}

function getBasicTitle($templateType, $templateName)
{
    $titles = [
        'BAL_ADD' => 'Balance Added Successfully',
        'BAL_SUB' => 'Balance Deducted',
        'DEPOSIT_COMPLETE' => 'Deposit Completed',
        'DEPOSIT_APPROVE' => 'Deposit Approved',
        'DEPOSIT_REJECT' => 'Deposit Rejected',
        'WITHDRAW_APPROVE' => 'Withdrawal Approved',
        'WITHDRAW_REJECT' => 'Withdrawal Rejected',
        'USER_REGISTRATION' => 'Welcome to MBFX',
        'KYC_APPROVE' => 'KYC Approved',
        'KYC_REJECT' => 'KYC Update Required',
        'IB_APPLICATION_APPROVED' => 'IB Application Approved',
        'IB_APPLICATION_REJECTED' => 'IB Application Update'
    ];
    
    return $titles[$templateType] ?? $templateName;
}

function getBasicContent($templateType)
{
    return '<p>Dear {{fullname}},</p>
<p>This is an important notification regarding your account.</p>
<p>{{message}}</p>
<p>If you have any questions, please contact our support team.</p>';
}

?>
