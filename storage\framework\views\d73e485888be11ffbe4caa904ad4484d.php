
<?php $__env->startSection('panel'); ?>
  <div class="row">

    <div class="col-12">
    <div class="row gy-4">
      <div class="col-xxl-3 col-sm-6">
      <div class="widget-two style--two box--shadow2 b-radius--5 bg--19">
        <div class="widget-two__icon b-radius--5 bg--primary">
        <i class="fas fa-chart-line"></i>
        </div>
        <div class="widget-two__content">
        <h3 class="text-white">$<?php echo e(number_format($widget['mt5_real_balance'] ?? 0, 2)); ?></h3>
        <p class="text-white"><?php echo app('translator')->get('Real Account Balance'); ?></p>
        </div>
        <a href="#account-tab-pane" onclick="switchToMT5Tab()"
        class="widget-two__btn"><?php echo app('translator')->get('View MT5'); ?></a>
      </div>
      </div>
      <div class="col-xxl-3 col-sm-6">
      <div class="widget-two style--two box--shadow2 b-radius--5 bg--primary">
        <div class="widget-two__icon b-radius--5 bg--primary">
        <i class="fas fa-chart-bar"></i>
        </div>
        <div class="widget-two__content">
        <h3 class="text-white">$<?php echo e(number_format($widget['mt5_demo_balance'] ?? 0, 2)); ?></h3>
        <p class="text-white"><?php echo app('translator')->get('Demo Account Balance'); ?></p>
        </div>
        <a href="#account-tab-pane" onclick="switchToMT5Tab()"
        class="widget-two__btn"><?php echo app('translator')->get('View MT5'); ?></a>
      </div>
      </div>
      <div class="col-xxl-3 col-sm-6">
      <div class="widget-two style--two box--shadow2 b-radius--5 bg--1">
        <div class="widget-two__icon b-radius--5 bg--primary">
        <i class="fas fa-wallet"></i>
        </div>
        <div class="widget-two__content">
        <h3 class="text-white"><?php echo e(getAmount($widget['total_deposit'])); ?></h3>
        <p class="text-white"><?php echo app('translator')->get('Total Deposit'); ?></p>
        </div>
        <a href="<?php echo e(route('admin.deposit.list')); ?>?search=<?php echo e($user->username); ?>"
        class="widget-two__btn"><?php echo app('translator')->get('View All'); ?></a>
      </div>
      </div>
      <div class="col-xxl-3 col-sm-6">
      <div class="widget-two style--two box--shadow2 b-radius--5 bg--17">
        <div class="widget-two__icon b-radius--5 bg--primary">
        <i class="las la-exchange-alt"></i>
        </div>
        <div class="widget-two__content">
        <h3 class="text-white"><?php echo e(getAmount($widget['total_transaction'])); ?></h3>
        <p class="text-white"><?php echo app('translator')->get('Transactions'); ?></p>
        </div>
        <a href="<?php echo e(route('admin.report.transaction')); ?>?search=<?php echo e($user->username); ?>"
        class="widget-two__btn"><?php echo app('translator')->get('View All'); ?></a>
      </div>
      </div>
    </div>

    <div class="d-flex flex-wrap gap-3 mt-4">
      <div class="flex-fill">
      <button data-bs-toggle="modal" data-bs-target="#addSubModal"
        class="btn btn--success btn--shadow w-100 btn-lg bal-btn" data-act="add">
        <i class="las la-plus-circle"></i> <?php echo app('translator')->get('Balance'); ?>
      </button>
      </div>

      <div class="flex-fill">
      <button data-bs-toggle="modal" data-bs-target="#addSubModal"
        class="btn btn--danger btn--shadow w-100 btn-lg bal-btn" data-act="sub">
        <i class="las la-minus-circle"></i> <?php echo app('translator')->get('Balance'); ?>
      </button>
      </div>

      <div class="flex-fill">
      <button data-bs-toggle="modal" data-bs-target="#adminTransferModal"
        class="btn btn--primary btn--shadow w-100 btn-lg" id="adminTransferBtn">
        <i class="las la-exchange-alt"></i> <?php echo app('translator')->get('Transfer'); ?>
      </button>
      </div>

      <div class="flex-fill">
      <a href="<?php echo e(route('admin.report.login.history')); ?>?search=<?php echo e($user->username); ?>"
        class="btn btn--primary btn--shadow w-100 btn-lg">
        <i class="las la-list-alt"></i><?php echo app('translator')->get('Logins'); ?>
      </a>
      </div>

      <div class="flex-fill">
      <a href="<?php echo e(route('admin.users.notification.log', $user->id)); ?>"
        class="btn btn--secondary btn--shadow w-100 btn-lg">
        <i class="las la-bell"></i><?php echo app('translator')->get('Notifications'); ?>
      </a>
      </div>

      <div class="flex-fill">
      <a href="<?php echo e(route('admin.users.login.as.user', $user->id)); ?>" target="_blank"
        class="btn btn--primary btn--gradi btn--shadow w-100 btn-lg">
        <i class="las la-sign-in-alt"></i><?php echo app('translator')->get('Login as User'); ?>
      </a>
      </div>


      <?php if($user->kyc_data): ?>
      <div class="flex-fill">
      <a href="<?php echo e(route('admin.users.kyc.details', $user->id)); ?>" target="_blank"
      class="btn btn--dark btn--shadow w-100 btn-lg">
      <i class="las la-user-check"></i><?php echo app('translator')->get('KYC Data'); ?>
      </a>
      </div>
    <?php endif; ?>

      <div class="flex-fill">
      <?php if($user->status == Status::USER_ACTIVE): ?>
      <button type="button" class="btn btn--warning btn--gradi btn--shadow w-100 btn-lg userStatus"
      data-bs-toggle="modal" data-bs-target="#userStatusModal">
      <i class="las la-ban"></i><?php echo app('translator')->get('Ban User'); ?>
      </button>
    <?php else: ?>
      <button type="button" class="btn btn--success btn--gradi btn--shadow w-100 btn-lg userStatus"
      data-bs-toggle="modal" data-bs-target="#userStatusModal">
      <i class="las la-undo"></i><?php echo app('translator')->get('Unban User'); ?>
      </button>
    <?php endif; ?>
      </div>
    </div>

    <div class="user-detail-tabs-container">
      <div class="card">
        <div class="card-header" style="background: #f8f9fa; border-bottom: 1px solid #e5e5e5;">
          <div class="row align-items-center">
            <div class="col-md-8">
              <h5 class="card-title mb-0" style="color: #333; font-weight: 600;"><?php echo app('translator')->get('User Management Tabs'); ?></h5>
            </div>
            <div class="col-md-4 text-end">
              <small class="text-muted"><?php echo app('translator')->get('Navigate between sections'); ?></small>
            </div>
          </div>
        </div>
        <div class="card-body p-0">
          <ul class="nav nav-tabs user-detail-nav-tabs" style="border-bottom: none; background: white;">
            <li class="nav-item">
              <a href="#overview-tab-pane" class="nav-link active user-detail-tab-link" data-bs-toggle="tab" role="tab"
              aria-controls="overview-tab-pane" aria-selected="true">
              <i class="fas fa-chart-pie me-2"></i>
              <span class="menu-title"><?php echo app('translator')->get('Overview'); ?></span>
              </a>
            </li>
            <li class="nav-item">
              <a href="#account-tab-pane" class="nav-link user-detail-tab-link" data-bs-toggle="tab" role="tab" aria-controls="account-tab-pane"
              aria-selected="false">
              <i class="fas fa-chart-line me-2"></i>
              <span class="menu-title"><?php echo app('translator')->get('MT5'); ?></span>
              </a>
            </li>
            <li class="nav-item">
              <a href="#transaction-tab-pane" class="nav-link user-detail-tab-link" data-bs-toggle="tab" role="tab"
              aria-controls="transaction-tab-pane" aria-selected="false">
              <i class="fas fa-exchange-alt me-2"></i>
              <span class="menu-title"><?php echo app('translator')->get('Transaction'); ?></span>
              </a>
            </li>
            <li class="nav-item">
              <a href="#partner-tab-pane" class="nav-link user-detail-tab-link" data-bs-toggle="tab" role="tab" aria-controls="partner-tab-pane"
              aria-selected="false">
              <i class="fas fa-handshake me-2"></i>
              <span class="menu-title"><?php echo app('translator')->get('Partner'); ?></span>
              </a>
            </li>
            <li class="nav-item">
              <a href="#referals-tab-pane" class="nav-link user-detail-tab-link" data-bs-toggle="tab" role="tab"
              aria-controls="referals-tab-pane" aria-selected="false">
              <i class="fas fa-users me-2"></i>
              <span class="menu-title"><?php echo app('translator')->get('Direct Referrals'); ?></span>
              </a>
            </li>
            <li class="nav-item">
              <a href="#network-tab-pane" class="nav-link user-detail-tab-link" data-bs-toggle="tab" role="tab" aria-controls="network-tab-pane"
              aria-selected="false">
              <i class="fas fa-sitemap me-2"></i>
              <span class="menu-title"><?php echo app('translator')->get('Network'); ?></span>
              </a>
            </li>
            <li class="nav-item">
              <a href="#tickets-tab-pane" class="nav-link user-detail-tab-link" data-bs-toggle="tab" role="tab" aria-controls="tickets-tab-pane"
              aria-selected="false">
              <i class="fas fa-ticket-alt me-2"></i>
              <span class="menu-title"><?php echo app('translator')->get('Tickets'); ?></span>
              </a>
            </li>
            <li class="nav-item">
              <a href="#note-tab-pane" class="nav-link user-detail-tab-link" data-bs-toggle="tab" role="tab" aria-controls="note-tab-pane"
              aria-selected="false">
              <i class="fas fa-sticky-note me-2"></i>
              <span class="menu-title"><?php echo app('translator')->get('Add Note'); ?></span>
              </a>
            </li>
            <li class="nav-item">
              <a href="#security-tab-pane" class="nav-link user-detail-tab-link" data-bs-toggle="tab" role="tab"
              aria-controls="security-tab-pane" aria-selected="false">
              <i class="fas fa-shield-alt me-2"></i>
              <span class="menu-title"><?php echo app('translator')->get('Security'); ?></span>
              </a>
            </li>
          </ul>
        </div>
      </div>

      <div class="tab-content mt-3">
      <div class="tab-pane fade show active" id="overview-tab-pane" role="tabpanel" aria-labelledby="overview-tab">
        <?php if (isset($component)) { $__componentOriginala0ef5316ac0fdfe890982606f1fcf72e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala0ef5316ac0fdfe890982606f1fcf72e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.user-detail.detail','data' => ['user' => $user,'countries' => $countries]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('user-detail.detail'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['user' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($user),'countries' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($countries)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala0ef5316ac0fdfe890982606f1fcf72e)): ?>
<?php $attributes = $__attributesOriginala0ef5316ac0fdfe890982606f1fcf72e; ?>
<?php unset($__attributesOriginala0ef5316ac0fdfe890982606f1fcf72e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala0ef5316ac0fdfe890982606f1fcf72e)): ?>
<?php $component = $__componentOriginala0ef5316ac0fdfe890982606f1fcf72e; ?>
<?php unset($__componentOriginala0ef5316ac0fdfe890982606f1fcf72e); ?>
<?php endif; ?>
      </div>
      <div class="tab-pane fade" id="account-tab-pane" role="tabpanel" aria-labelledby="account-tab">
        <?php if (isset($component)) { $__componentOriginal16f465b8ff3fc8e0b2f7af4aa5becfcd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal16f465b8ff3fc8e0b2f7af4aa5becfcd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.user-detail.account','data' => ['user' => $user,'accounts' => $accounts,'countries' => $countries]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('user-detail.account'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['user' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($user),'accounts' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($accounts),'countries' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($countries)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal16f465b8ff3fc8e0b2f7af4aa5becfcd)): ?>
<?php $attributes = $__attributesOriginal16f465b8ff3fc8e0b2f7af4aa5becfcd; ?>
<?php unset($__attributesOriginal16f465b8ff3fc8e0b2f7af4aa5becfcd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal16f465b8ff3fc8e0b2f7af4aa5becfcd)): ?>
<?php $component = $__componentOriginal16f465b8ff3fc8e0b2f7af4aa5becfcd; ?>
<?php unset($__componentOriginal16f465b8ff3fc8e0b2f7af4aa5becfcd); ?>
<?php endif; ?>
      </div>
      <div class="tab-pane fade" id="transaction-tab-pane" role="tabpanel" aria-labelledby="transaction-tab">
        <?php if (isset($component)) { $__componentOriginal88462fb48556cb5c4addcf7594e0b2bd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal88462fb48556cb5c4addcf7594e0b2bd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.user-detail.transaction','data' => ['user' => $user,'transactions' => $transactions]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('user-detail.transaction'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['user' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($user),'transactions' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($transactions)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal88462fb48556cb5c4addcf7594e0b2bd)): ?>
<?php $attributes = $__attributesOriginal88462fb48556cb5c4addcf7594e0b2bd; ?>
<?php unset($__attributesOriginal88462fb48556cb5c4addcf7594e0b2bd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal88462fb48556cb5c4addcf7594e0b2bd)): ?>
<?php $component = $__componentOriginal88462fb48556cb5c4addcf7594e0b2bd; ?>
<?php unset($__componentOriginal88462fb48556cb5c4addcf7594e0b2bd); ?>
<?php endif; ?>
      </div>
      <div class="tab-pane fade" id="partner-tab-pane" role="tabpanel" aria-labelledby="partner-tab">
        <?php if($user->isIb()): ?>
          <?php if (isset($component)) { $__componentOriginalb3ddc39ba38568d97d173db0ea2e5a30 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb3ddc39ba38568d97d173db0ea2e5a30 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.user-detail.partner','data' => ['user' => $user,'ibaccount' => $ib_accounts,'ibData' => $ibData,'mt5CommissionData' => $mt5CommissionData]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('user-detail.partner'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['user' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($user),'ibaccount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($ib_accounts),'ibData' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($ibData),'mt5CommissionData' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($mt5CommissionData)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb3ddc39ba38568d97d173db0ea2e5a30)): ?>
<?php $attributes = $__attributesOriginalb3ddc39ba38568d97d173db0ea2e5a30; ?>
<?php unset($__attributesOriginalb3ddc39ba38568d97d173db0ea2e5a30); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb3ddc39ba38568d97d173db0ea2e5a30)): ?>
<?php $component = $__componentOriginalb3ddc39ba38568d97d173db0ea2e5a30; ?>
<?php unset($__componentOriginalb3ddc39ba38568d97d173db0ea2e5a30); ?>
<?php endif; ?>
        <?php else: ?>
          <div class="card">
            <div class="card-body text-center">
              <i class="fas fa-handshake fa-3x text-muted mb-3"></i>
              <h5 class="text-muted"><?php echo app('translator')->get('Not a Partner'); ?></h5>
              <p class="text-muted"><?php echo app('translator')->get('This user is not currently enrolled in the partnership program.'); ?></p>
              <?php if($user->partner == 2): ?>
                <div class="alert alert-info">
                  <i class="fas fa-clock me-2"></i>
                  <?php echo app('translator')->get('Partnership application is pending approval.'); ?>
                </div>
              <?php elseif($user->partner == 3): ?>
                <div class="alert alert-warning">
                  <i class="fas fa-times-circle me-2"></i>
                  <?php echo app('translator')->get('Partnership application was rejected.'); ?>
                </div>
              <?php else: ?>
                <div class="alert alert-light">
                  <i class="fas fa-info-circle me-2"></i>
                  <?php echo app('translator')->get('User has not applied for partnership yet.'); ?>
                </div>
              <?php endif; ?>
            </div>
          </div>
        <?php endif; ?>
      </div>
      <div class="tab-pane fade" id="referals-tab-pane" role="tabpanel" aria-labelledby="referals-tab">
        <?php if (isset($component)) { $__componentOriginal3d45e574616271730a5849e41371306a = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3d45e574616271730a5849e41371306a = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.user-detail.referral','data' => ['user' => $user,'allUsers' => $allUsers,'referrer' => $referrer,'directReferralsPaginated' => $directReferralsPaginated]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('user-detail.referral'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['user' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($user),'allUsers' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($allUsers),'referrer' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($referrer),'directReferralsPaginated' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($directReferralsPaginated)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3d45e574616271730a5849e41371306a)): ?>
<?php $attributes = $__attributesOriginal3d45e574616271730a5849e41371306a; ?>
<?php unset($__attributesOriginal3d45e574616271730a5849e41371306a); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3d45e574616271730a5849e41371306a)): ?>
<?php $component = $__componentOriginal3d45e574616271730a5849e41371306a; ?>
<?php unset($__componentOriginal3d45e574616271730a5849e41371306a); ?>
<?php endif; ?>
      </div>
      <div class="tab-pane fade" id="network-tab-pane" role="tabpanel" aria-labelledby="network-tab">
        <?php if (isset($component)) { $__componentOriginal753ce4fede55bfa6b75294fa8234f891 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal753ce4fede55bfa6b75294fa8234f891 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.user-detail.network','data' => ['user' => $user,'networkData' => $networkData]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('user-detail.network'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['user' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($user),'networkData' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($networkData)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal753ce4fede55bfa6b75294fa8234f891)): ?>
<?php $attributes = $__attributesOriginal753ce4fede55bfa6b75294fa8234f891; ?>
<?php unset($__attributesOriginal753ce4fede55bfa6b75294fa8234f891); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal753ce4fede55bfa6b75294fa8234f891)): ?>
<?php $component = $__componentOriginal753ce4fede55bfa6b75294fa8234f891; ?>
<?php unset($__componentOriginal753ce4fede55bfa6b75294fa8234f891); ?>
<?php endif; ?>
      </div>
      <div class="tab-pane fade" id="tickets-tab-pane" role="tabpanel" aria-labelledby="tickets-tab">
        <?php if (isset($component)) { $__componentOriginala0b348b7db00eda42b0ca0cd8a4f9bda = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala0b348b7db00eda42b0ca0cd8a4f9bda = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.user-detail.tickets','data' => ['user' => $user]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('user-detail.tickets'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['user' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($user)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala0b348b7db00eda42b0ca0cd8a4f9bda)): ?>
<?php $attributes = $__attributesOriginala0b348b7db00eda42b0ca0cd8a4f9bda; ?>
<?php unset($__attributesOriginala0b348b7db00eda42b0ca0cd8a4f9bda); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala0b348b7db00eda42b0ca0cd8a4f9bda)): ?>
<?php $component = $__componentOriginala0b348b7db00eda42b0ca0cd8a4f9bda; ?>
<?php unset($__componentOriginala0b348b7db00eda42b0ca0cd8a4f9bda); ?>
<?php endif; ?>
      </div>
      <div class="tab-pane fade" id="note-tab-pane" role="tabpanel" aria-labelledby="note-tab">
        <?php if (isset($component)) { $__componentOriginal347b1ff0216db3f61e17873627890e32 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal347b1ff0216db3f61e17873627890e32 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.user-detail.note','data' => ['user' => $user]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('user-detail.note'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['user' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($user)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal347b1ff0216db3f61e17873627890e32)): ?>
<?php $attributes = $__attributesOriginal347b1ff0216db3f61e17873627890e32; ?>
<?php unset($__attributesOriginal347b1ff0216db3f61e17873627890e32); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal347b1ff0216db3f61e17873627890e32)): ?>
<?php $component = $__componentOriginal347b1ff0216db3f61e17873627890e32; ?>
<?php unset($__componentOriginal347b1ff0216db3f61e17873627890e32); ?>
<?php endif; ?>
      </div>
      <div class="tab-pane fade" id="security-tab-pane" role="tabpanel" aria-labelledby="security-tab">
        <?php if (isset($component)) { $__componentOriginalf8ce50e44824d0fe7bcde3b8aff17c53 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8ce50e44824d0fe7bcde3b8aff17c53 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.user-detail.security','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('user-detail.security'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8ce50e44824d0fe7bcde3b8aff17c53)): ?>
<?php $attributes = $__attributesOriginalf8ce50e44824d0fe7bcde3b8aff17c53; ?>
<?php unset($__attributesOriginalf8ce50e44824d0fe7bcde3b8aff17c53); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8ce50e44824d0fe7bcde3b8aff17c53)): ?>
<?php $component = $__componentOriginalf8ce50e44824d0fe7bcde3b8aff17c53; ?>
<?php unset($__componentOriginalf8ce50e44824d0fe7bcde3b8aff17c53); ?>
<?php endif; ?>
      </div>
      </div>
    </div>
    </div>
    <div class="row mt-3 gy-3">
    <div class="col-12">
      <h6><?php echo app('translator')->get('User Wallet Balance'); ?></h6>
    </div>
    <?php $__empty_1 = true; $__currentLoopData = $user->wallets->where('balance', ">", 0)->sortByDesc('balance'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $wallet): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
    <div class="col-xxl-3 col-sm-6">
      <div class="widget-two box--shadow2 b-radius--5 bg--white">
      <div class="widget-two__icon b-radius--5">
      <img src="<?php echo e(@$wallet->currency->image_url); ?>">
      </div>
      <div class="widget-two__content">
      <h3><?php echo e(showAmount($wallet->balance)); ?> <?php echo e(@$wallet->currency->symbol); ?></h3>
      <p><?php echo app('translator')->get('Total Balance'); ?></p>
      <?php if(Status::WALLET_TYPE_SPOT == $wallet->wallet_type): ?>
      <span class="badge badge--primary">
      <?php echo app('translator')->get('SPOT'); ?>
      </span>
    <?php else: ?>
      <span class="badge badge--success">
      <?php echo app('translator')->get('FUNDING'); ?>
      </span>
    <?php endif; ?>
      </div>
      </div>
    </div>
  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
  <div class="col-12 text-center">
    <div class="card">
    <div class="card-body">
    <h6><?php echo app('translator')->get("This user haven't any wallet balance"); ?></h6>
    </div>
    </div>
  </div>
<?php endif; ?>
    </div>
  </div>
  </div>

  
  <div id="addSubModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered" role="document" style="max-width: 500px;">
    <div class="modal-content" style="border-radius: 8px; border: none; box-shadow: 0 4px 20px rgba(0,0,0,0.15);">
      <div class="modal-header" style="background: #f8f9fa; border-bottom: 1px solid #e5e5e5; border-radius: 8px 8px 0 0;">
      <h5 class="modal-title" style="color: #333; font-weight: normal; font-size: 16px;">
        <span class="type"></span> <span><?php echo app('translator')->get('MT5 Account Balance'); ?></span>
      </h5>
      <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form action="<?php echo e(route('admin.users.mt5.balance.update', $user->id)); ?>" method="POST" id="balanceForm">
      <?php echo csrf_field(); ?>
      <input type="hidden" name="act">
      <div class="modal-body" style="padding: 20px;">
        <div class="form-group mb-3">
        <label style="font-weight: normal; color: #333; margin-bottom: 6px; font-size: 14px; text-align: left;"><?php echo app('translator')->get('Currency'); ?></label>
        <select name="currency" class="form-control" style="height: 40px; border: 1px solid #ddd; font-size: 14px;" required>
          <option value="" disabled selected><?php echo app('translator')->get('Select Currency'); ?></option>
          <?php $__currentLoopData = $currencies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $currency): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
          <option value="<?php echo e($currency->id); ?>"><?php echo e($currency->name); ?> (<?php echo e($currency->symbol); ?>)</option>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
        </div>

        <div class="form-group mb-3">
        <label style="font-weight: normal; color: #333; margin-bottom: 6px; font-size: 14px; text-align: left;"><?php echo app('translator')->get('MT5 Account'); ?></label>
        <select name="mt5_account" class="form-control" style="height: 40px; border: 1px solid #ddd; font-size: 14px;" required id="mt5AccountSelect">
          <option value="" disabled selected><?php echo app('translator')->get('Select MT5 Account'); ?></option>
          
        </select>
        <small class="text-muted" style="font-size: 12px; text-align: left;"><?php echo app('translator')->get('Select the MT5 account to update balance'); ?></small>
        </div>

        <div class="form-group mb-3">
        <label style="font-weight: normal; color: #333; margin-bottom: 6px; font-size: 14px; text-align: left;"><?php echo app('translator')->get('Amount'); ?></label>
        <div class="input-group">
          <input type="number" step="0.01" name="amount" class="form-control"
                 style="height: 40px; border: 1px solid #ddd; font-size: 14px;"
                 placeholder="<?php echo app('translator')->get('Enter amount'); ?>" required>
          <div class="input-group-text" style="background: #f8f9fa; border: 1px solid #ddd; font-size: 14px;">USD</div>
        </div>
        <small class="text-muted" style="font-size: 12px; text-align: left;"><?php echo app('translator')->get('Enter positive amount for add, will be processed accordingly'); ?></small>
        </div>

        <div class="form-group mb-3">
        <label style="font-weight: normal; color: #333; margin-bottom: 6px; font-size: 14px; text-align: left;"><?php echo app('translator')->get('Remark'); ?></label>
        <textarea class="form-control" style="border: 1px solid #ddd; min-height: 70px; font-size: 14px;"
                  placeholder="<?php echo app('translator')->get('Enter transaction remark'); ?>" name="remark" rows="3" required></textarea>
        </div>

        <div class="alert alert-info" style="background: #e3f2fd; border: 1px solid #bbdefb; color: #1976d2; padding: 12px; border-radius: 6px;">
          <i class="fas fa-info-circle me-2"></i>
          <span style="font-size: 12px; font-weight: normal; text-align: left;"><?php echo app('translator')->get('Note:'); ?></span> <span style="font-size: 12px; font-weight: normal; text-align: left;"><?php echo app('translator')->get('This will update the balance directly in the MT5 server and reflect immediately in the trading platform.'); ?></span>
        </div>
      </div>
      <div class="modal-footer" style="background: #f8f9fa; border-top: 1px solid #e5e5e5; padding: 12px 20px; border-radius: 0 0 8px 8px;">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" style="height: 40px; min-width: 90px; font-size: 14px;">
          <?php echo app('translator')->get('Cancel'); ?>
        </button>
        <button type="submit" class="btn btn--primary" style="height: 40px; min-width: 110px; font-size: 14px;" id="submitBalanceBtn">
          <i class="fas fa-check me-2"></i><?php echo app('translator')->get('Update Balance'); ?>
        </button>
      </div>
      </form>
    </div>
    </div>
  </div>

  
  <div id="adminTransferModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered" role="document" style="max-width: 600px;">
      <div class="modal-content" style="border-radius: 8px; border: none; box-shadow: 0 4px 20px rgba(0,0,0,0.15);">
        <div class="modal-header" style="background: #f8f9fa; border-bottom: 1px solid #e5e5e5; border-radius: 8px 8px 0 0;">
          <h5 class="modal-title" style="color: #333; font-weight: normal; font-size: 16px;">
            <i class="las la-exchange-alt me-2"></i><?php echo app('translator')->get('Internal Transfer'); ?>
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <form id="adminTransferForm">
          <?php echo csrf_field(); ?>
          <div class="modal-body" style="padding: 20px;">
            <div class="row">
              <div class="col-md-6">
                <div class="form-group mb-3">
                  <label style="font-weight: normal; color: #333; margin-bottom: 6px; font-size: 14px;"><?php echo app('translator')->get('From Account'); ?></label>
                  <select name="from_account" class="form-control" style="height: 40px; border: 1px solid #ddd; font-size: 14px;" required id="fromAccountSelect">
                    <option value="" disabled selected><?php echo app('translator')->get('Select Source Account'); ?></option>
                  </select>
                  <small class="text-muted" style="font-size: 12px;"><?php echo app('translator')->get('Available Balance'); ?>: <span id="availableBalance">$0.00</span></small>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group mb-3">
                  <label style="font-weight: normal; color: #333; margin-bottom: 6px; font-size: 14px;"><?php echo app('translator')->get('To Account'); ?></label>
                  <select name="to_account" class="form-control" style="height: 40px; border: 1px solid #ddd; font-size: 14px;" required id="toAccountSelect">
                    <option value="" disabled selected><?php echo app('translator')->get('Select Destination Account'); ?></option>
                  </select>
                </div>
              </div>
            </div>

            <div class="form-group mb-3">
              <label style="font-weight: normal; color: #333; margin-bottom: 6px; font-size: 14px;"><?php echo app('translator')->get('Transfer Amount'); ?></label>
              <div class="input-group">
                <input type="number" step="0.01" min="1" name="amount" class="form-control"
                       style="height: 40px; border: 1px solid #ddd; font-size: 14px;"
                       placeholder="0.00" required id="transferAmount">
                <span class="input-group-text">USD</span>
              </div>
            </div>

            <div class="alert alert-info" style="background: #e3f2fd; border: 1px solid #bbdefb; color: #1976d2; padding: 12px; border-radius: 6px;">
              <i class="fas fa-info-circle me-2"></i>
              <span style="font-size: 12px;"><?php echo app('translator')->get('This will transfer funds between the user\'s MT5 accounts instantly with no fees.'); ?></span>
            </div>
          </div>
          <div class="modal-footer" style="background: #f8f9fa; border-top: 1px solid #e5e5e5; padding: 12px 20px; border-radius: 0 0 8px 8px;">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" style="height: 40px; min-width: 90px; font-size: 14px;">
              <?php echo app('translator')->get('Cancel'); ?>
            </button>
            <button type="submit" class="btn btn--primary" style="height: 40px; min-width: 110px; font-size: 14px;" id="submitTransferBtn">
              <i class="las la-exchange-alt me-2"></i><?php echo app('translator')->get('Transfer Now'); ?>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <div id="userStatusModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
      <h5 class="modal-title">
        <?php if($user->status == Status::USER_ACTIVE): ?>
      <span><?php echo app('translator')->get('Ban User'); ?></span>
    <?php else: ?>
    <span><?php echo app('translator')->get('Unban User'); ?></span>
  <?php endif; ?>
      </h5>
      <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
        <i class="las la-times"></i>
      </button>
      </div>
      <form action="<?php echo e(route('admin.users.status', $user->id)); ?>" method="POST">
      <?php echo csrf_field(); ?>
      <div class="modal-body">
        <?php if($user->status == Status::USER_ACTIVE): ?>
      <h6 class="mb-2"><?php echo app('translator')->get('If you ban this user he/she won\'t able to access his/her dashboard.'); ?></h6>
      <div class="form-group">
      <label><?php echo app('translator')->get('Reason'); ?></label>
      <textarea class="form-control" name="reason" rows="4" required></textarea>
      </div>
    <?php else: ?>
    <p><span><?php echo app('translator')->get('Ban reason was'); ?>:</span></p>
    <p><?php echo e($user->ban_reason); ?></p>
    <h4 class="text-center mt-3"><?php echo app('translator')->get('Are you sure to unban this user?'); ?></h4>
  <?php endif; ?>
      </div>
      <div class="modal-footer">
        <?php if($user->status == Status::USER_ACTIVE): ?>
      <button type="submit" class="btn btn--primary h-45 w-100"><?php echo app('translator')->get('Submit'); ?></button>
    <?php else: ?>
    <button type="button" class="btn btn--dark" data-bs-dismiss="modal"><?php echo app('translator')->get('No'); ?></button>
    <button type="submit" class="btn btn--primary"><?php echo app('translator')->get('Yes'); ?></button>
  <?php endif; ?>
      </div>
      </form>
    </div>
    </div>
  </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('style'); ?>
<style>
/* Professional User Detail Tabs Styling */
.user-detail-tabs-container {
  margin-top: 20px;
}

.user-detail-nav-tabs {
  padding: 0;
  margin: 0;
  flex-wrap: wrap;
}

.user-detail-nav-tabs .nav-item {
  margin-bottom: 0;
}

.user-detail-tab-link {
  border: none !important;
  border-radius: 0 !important;
  padding: 15px 20px !important;
  color: #666 !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  transition: all 0.3s ease !important;
  background: transparent !important;
  border-bottom: 3px solid transparent !important;
  position: relative;
  display: flex;
  align-items: center;
  min-height: 60px;
}

.user-detail-tab-link:hover {
  color: #dc3545 !important;
  background: #f8f9fa !important;
  border-bottom-color: #dc3545 !important;
}

.user-detail-tab-link.active {
  color: #dc3545 !important;
  background: white !important;
  border-bottom-color: #dc3545 !important;
  font-weight: 600 !important;
}

.user-detail-tab-link i {
  font-size: 16px;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.user-detail-tab-link:hover i,
.user-detail-tab-link.active i {
  opacity: 1;
}

.user-detail-tab-link .menu-title {
  white-space: nowrap;
}

/* Responsive design for tabs */
@media (max-width: 768px) {
  .user-detail-nav-tabs {
    flex-direction: column;
  }

  .user-detail-tab-link {
    padding: 12px 15px !important;
    min-height: 50px;
    justify-content: flex-start;
  }

  .user-detail-tab-link .menu-title {
    font-size: 13px;
  }
}

@media (max-width: 576px) {
  .user-detail-tab-link {
    padding: 10px 12px !important;
    min-height: 45px;
  }

  .user-detail-tab-link i {
    font-size: 14px;
  }

  .user-detail-tab-link .menu-title {
    font-size: 12px;
  }
}

/* Tab content styling */
.tab-content {
  background: white;
  border-radius: 0 0 8px 8px;
  min-height: 400px;
}

.tab-pane {
  padding: 20px;
}

/* Card header improvements */
.user-detail-tabs-container .card {
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.user-detail-tabs-container .card-header {
  border-radius: 8px 8px 0 0;
}

/* Animation for tab switching */
.tab-pane {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.tab-pane.active {
  opacity: 1;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script'); ?>
  <script>
    (function ($) {
    "use strict"

    // MT5 accounts data from server - Fixed for paginated data
    const userMT5AccountsRaw = <?php echo json_encode($accounts ?? [], 15, 512) ?>;

    // Extract accounts data from paginated object
    let userMT5Accounts = [];
    if (userMT5AccountsRaw && typeof userMT5AccountsRaw === 'object') {
      if (Array.isArray(userMT5AccountsRaw)) {
        // Already an array
        userMT5Accounts = userMT5AccountsRaw;
      } else if (userMT5AccountsRaw.data && Array.isArray(userMT5AccountsRaw.data)) {
        // Paginated object with data array
        userMT5Accounts = userMT5AccountsRaw.data;
      } else if (userMT5AccountsRaw.items && Array.isArray(userMT5AccountsRaw.items)) {
        // Alternative pagination structure
        userMT5Accounts = userMT5AccountsRaw.items;
      }
    }

    // Debug MT5 accounts data
    console.log('🔍 MT5 Accounts Debug:', {
      rawData: userMT5AccountsRaw,
      extractedAccounts: userMT5Accounts,
      accountsCount: userMT5Accounts ? userMT5Accounts.length : 0,
      dataType: typeof userMT5AccountsRaw,
      isArray: Array.isArray(userMT5Accounts)
    });

    $('.bal-btn').click(function () {
      var act = $(this).data('act');
      $('#addSubModal').find('input[name=act]').val(act);
      if (act == 'add') {
        $('.type').text('Add');
      } else {
        $('.type').text('Subtract');
      }

      // Load MT5 accounts when modal opens with enhanced debugging
      console.log('🔍 Modal opened, loading MT5 accounts...');
      loadMT5Accounts();
    });

    function loadMT5Accounts() {
      console.log('🔍 loadMT5Accounts() called');
      const mt5Select = $('#mt5AccountSelect');

      if (!mt5Select.length) {
        console.error('❌ MT5 Account select element not found!');
        return;
      }

      console.log('✅ MT5 Select element found:', mt5Select);
      mt5Select.empty();
      mt5Select.append('<option value="" disabled selected><?php echo app('translator')->get("Select MT5 Account"); ?></option>');

      console.log('🔍 Processing accounts:', userMT5Accounts);

      if (userMT5Accounts && Array.isArray(userMT5Accounts) && userMT5Accounts.length > 0) {
        console.log('✅ Found', userMT5Accounts.length, 'MT5 accounts');

        userMT5Accounts.forEach(function(account, index) {
          console.log(`🔍 Processing account ${index + 1}:`, account);

          const accountType = account.Group && account.Group.toLowerCase().includes('demo') ? 'Demo' : 'Live';
          const balance = account.Balance ? parseFloat(account.Balance).toFixed(2) : '0.00';
          const leverage = account.Leverage ? account.Leverage : 'N/A';
          const login = account.Login || account.login || 'Unknown';

          const optionHtml = `
            <option value="${login}" data-balance="${balance}" data-group="${account.Group || ''}">
              ${login} (${accountType}) - Balance: $${balance} - Leverage: 1:${leverage}
            </option>
          `;

          console.log('✅ Adding option:', optionHtml);
          mt5Select.append(optionHtml);
        });

        console.log('✅ All accounts loaded successfully');
      } else {
        console.log('⚠️ No MT5 accounts found or invalid data');
        mt5Select.append('<option value="" disabled><?php echo app('translator')->get("No MT5 accounts found"); ?></option>');
      }

      // Trigger change event to update any dependent elements
      mt5Select.trigger('change');
    }

    // Handle form submission
    $('#balanceForm').on('submit', function(e) {
      e.preventDefault();

      const submitBtn = $('#submitBalanceBtn');
      const originalText = submitBtn.html();

      // Show loading state
      submitBtn.prop('disabled', true);
      submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i><?php echo app('translator')->get("Processing..."); ?>');

      // Get form data
      const formData = new FormData(this);

      // Make AJAX request
      fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
          'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          // Show success message
          showNotification('success', data.message || '<?php echo app('translator')->get("Balance updated successfully!"); ?>');

          // Close modal and reload page after delay
          setTimeout(() => {
            $('#addSubModal').modal('hide');
            window.location.reload();
          }, 1500);
        } else {
          showNotification('error', data.message || '<?php echo app('translator')->get("Failed to update balance. Please try again."); ?>');

          // Reset button
          submitBtn.prop('disabled', false);
          submitBtn.html(originalText);
        }
      })
      .catch(error => {
        console.error('Balance update error:', error);
        showNotification('error', '<?php echo app('translator')->get("Network error. Please check your connection and try again."); ?>');

        // Reset button
        submitBtn.prop('disabled', false);
        submitBtn.html(originalText);
      });
    });

    function showNotification(type, message) {
      // Use Laravel-style notifications
      const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
      const iconClass = type === 'success' ? 'las la-check-circle' : 'las la-exclamation-triangle';

      // Create notification element with Laravel styling
      const notification = document.createElement('div');
      notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
      notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 320px; max-width: 400px; border-radius: 6px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);';
      notification.innerHTML = `
        <div class="d-flex align-items-center">
          <i class="${iconClass} me-2" style="font-size: 18px;"></i>
          <div style="font-size: 14px; line-height: 1.4;">${message}</div>
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
      `;

      // Add to page
      document.body.appendChild(notification);

      // Auto remove after 4 seconds
      setTimeout(() => {
        if (notification.parentNode) {
          notification.classList.remove('show');
          setTimeout(() => {
            if (notification.parentNode) {
              notification.remove();
            }
          }, 300);
        }
      }, 4000);
    }

    // Function to switch to MT5 tab
    function switchToMT5Tab() {
      // Remove active class from all tabs
      $('.user-detail-tab-link').removeClass('active');
      $('.tab-pane').removeClass('show active');

      // Activate MT5 tab
      $('a[href="#account-tab-pane"]').addClass('active');
      $('#account-tab-pane').addClass('show active');
    }

    // Function to update MT5 balance widgets dynamically
    function updateMT5BalanceWidgets() {
      // Use the extracted accounts array
      if (userMT5Accounts && Array.isArray(userMT5Accounts) && userMT5Accounts.length > 0) {
        let realBalance = 0;
        let demoBalance = 0;

        userMT5Accounts.forEach(function(account) {
          const balance = parseFloat(account.Balance || 0);
          const group = (account.Group || '').toLowerCase();

          if (group.includes('demo')) {
            demoBalance += balance;
          } else {
            realBalance += balance;
          }
        });

        // Update widget values with proper formatting
        $('.widget-two__content h3').eq(0).text('$' + realBalance.toLocaleString('en-US', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        }));

        $('.widget-two__content h3').eq(1).text('$' + demoBalance.toLocaleString('en-US', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        }));
      } else {
        console.log('⚠️ No MT5 accounts available for widget update');
      }
    }

    // Update widgets on page load
    updateMT5BalanceWidgets();

    // Make switchToMT5Tab globally available
    window.switchToMT5Tab = switchToMT5Tab;

    // Admin Transfer Modal Functions
    $('#adminTransferBtn').on('click', function() {
      console.log('🔄 Admin Transfer button clicked');
      loadTransferAccounts();
    });

    $('#adminTransferModal').on('show.bs.modal', function() {
      console.log('🔄 Admin Transfer modal opening');
      loadTransferAccounts();
    });

    function loadTransferAccounts() {
      console.log('🔄 Loading transfer accounts...');
      const fromSelect = $('#fromAccountSelect');
      const toSelect = $('#toAccountSelect');

      // Clear existing options
      fromSelect.empty().append('<option value="" disabled selected><?php echo app('translator')->get("Select Source Account"); ?></option>');
      toSelect.empty().append('<option value="" disabled selected><?php echo app('translator')->get("Select Destination Account"); ?></option>');

      console.log('🔍 Available MT5 Accounts:', userMT5Accounts);

      if (userMT5Accounts && Array.isArray(userMT5Accounts) && userMT5Accounts.length > 0) {
        console.log('✅ Found', userMT5Accounts.length, 'MT5 accounts for transfer');

        userMT5Accounts.forEach(function(account, index) {
          console.log(`🔍 Processing account ${index + 1}:`, account);

          const accountType = account.Group && account.Group.toLowerCase().includes('demo') ? 'Demo' : 'Live';
          const balance = account.Balance ? parseFloat(account.Balance).toFixed(2) : '0.00';
          const login = account.Login || account.login || 'Unknown';

          const optionHtml = `
            <option value="${login}" data-balance="${balance}" data-group="${account.Group || ''}">
              ${login} (${accountType}) - Balance: $${balance}
            </option>
          `;

          fromSelect.append(optionHtml);
          toSelect.append(optionHtml);
        });

        console.log('✅ Transfer accounts loaded successfully');
      } else {
        console.warn('⚠️ No MT5 accounts found for transfer');
        fromSelect.append('<option value="" disabled><?php echo app('translator')->get("No MT5 accounts found"); ?></option>');
        toSelect.append('<option value="" disabled><?php echo app('translator')->get("No MT5 accounts found"); ?></option>');
      }
    }

    // Update available balance when source account changes
    $('#fromAccountSelect').on('change', function() {
      const selectedOption = $(this).find('option:selected');
      const balance = selectedOption.data('balance') || 0;
      $('#availableBalance').text('$' + parseFloat(balance).toFixed(2));

      // Update destination options (exclude selected source)
      const selectedValue = $(this).val();
      $('#toAccountSelect option').each(function() {
        if ($(this).val() === selectedValue) {
          $(this).hide();
        } else {
          $(this).show();
        }
      });
      $('#toAccountSelect').val(''); // Reset destination selection
    });

    // Handle admin transfer form submission
    $('#adminTransferForm').on('submit', function(e) {
      e.preventDefault();
      console.log('🔄 Admin transfer form submitted');

      const submitBtn = $('#submitTransferBtn');
      const originalText = submitBtn.html();
      const fromAccount = $('#fromAccountSelect').val();
      const toAccount = $('#toAccountSelect').val();
      const amount = $('#transferAmount').val();

      console.log('📊 Transfer Details:', {
        from: fromAccount,
        to: toAccount,
        amount: amount
      });

      // Validation
      if (!fromAccount || !toAccount || !amount) {
        showNotification('error', '<?php echo app('translator')->get("Please fill in all required fields"); ?>');
        return;
      }

      if (fromAccount === toAccount) {
        showNotification('error', '<?php echo app('translator')->get("Source and destination accounts must be different"); ?>');
        return;
      }

      if (parseFloat(amount) <= 0) {
        showNotification('error', '<?php echo app('translator')->get("Transfer amount must be greater than 0"); ?>');
        return;
      }

      // Show loading state
      submitBtn.prop('disabled', true);
      submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i><?php echo app('translator')->get("Processing..."); ?>');

      // Get form data
      const formData = new FormData(this);

      console.log('🚀 Sending transfer request to:', '<?php echo e(route("admin.users.admin.transfer", $user->id)); ?>');

      // Make AJAX request
      fetch('<?php echo e(route("admin.users.admin.transfer", $user->id)); ?>', {
        method: 'POST',
        body: formData,
        headers: {
          'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
          'Accept': 'application/json'
        }
      })
      .then(response => {
        console.log('📥 Response status:', response.status);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        console.log('📥 Response data:', data);

        if (data.success) {
          showNotification('success', data.message);

          // Reset form
          $('#adminTransferForm')[0].reset();
          $('#availableBalance').text('$0.00');

          // Close modal and reload page after delay
          setTimeout(() => {
            $('#adminTransferModal').modal('hide');
            window.location.reload();
          }, 2000);
        } else {
          showNotification('error', data.message || '<?php echo app('translator')->get("Transfer failed. Please try again."); ?>');

          // Reset button
          submitBtn.prop('disabled', false);
          submitBtn.html(originalText);
        }
      })
      .catch(error => {
        console.error('❌ Transfer error:', error);
        showNotification('error', '<?php echo app('translator')->get("Network error. Please check your connection and try again."); ?>');

        // Reset button
        submitBtn.prop('disabled', false);
        submitBtn.html(originalText);
      });
    });

    // Test MT5 accounts loading on page load
    $(document).ready(function() {
      console.log('🔍 Document ready - Testing MT5 accounts loading...');

      // Test if modal elements exist
      const modal = $('#addSubModal');
      const select = $('#mt5AccountSelect');

      console.log('🔍 Modal exists:', modal.length > 0);
      console.log('🔍 Select exists:', select.length > 0);

      // Pre-load accounts for testing
      if (select.length > 0) {
        console.log('✅ Pre-loading MT5 accounts for testing...');
        loadMT5Accounts();
      }
    });

    let mobileElement = $('.mobile-code');

    $('select[name=country]').change(function () {
      mobileElement.text(`+${$('select[name=country] :selected').data('mobile_code')}`);
    });

    // ISSUE 1 FIX: Properly set country value by finding the correct country code
    <?php
      $userCountryCode = '';
      foreach ($countries as $key => $country) {
          if ($user->country_code == $key ||
              strtolower($user->country_code) == strtolower($country->country) ||
              $user->country_code == $country->country) {
              $userCountryCode = $key;
              break;
          }
      }
    ?>
    $('select[name=country]').val('<?php echo e($userCountryCode); ?>');

    let dialCode = $('select[name=country] :selected').data('mobile_code');
    let mobileNumber = `<?php echo e($user->mobile); ?>`;

    mobileNumber = mobileNumber.replace(dialCode, '');
    $('input[name=mobile]').val(mobileNumber);
    mobileElement.text(`+${dialCode}`);

    })(jQuery);
  </script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\mbf.mybrokerforex.com-31052025\resources\views/admin/users/detail.blade.php ENDPATH**/ ?>