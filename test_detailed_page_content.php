<?php
/**
 * Detailed Page Content Test
 * This script tests what's actually being served by the web server
 */

echo "🔧 DETAILED PAGE CONTENT TEST\n";
echo str_repeat('=', 50) . "\n\n";

// Test template ID 44 (Account Verification Required)
$templateId = 44;
$url = "https://localhost/mbf.mybrokerforex.com-********/admin/notification/template/edit/{$templateId}";

echo "📧 TESTING TEMPLATE ID: {$templateId}\n";
echo str_repeat('-', 40) . "\n";

$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => [
            'User-Agent: Mozilla/5.0',
            'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language: en-US,en;q=0.5',
            'Accept-Encoding: gzip, deflate',
            'Connection: keep-alive',
            'Cache-Control: no-cache',
            'Pragma: no-cache'
        ],
        'timeout' => 30
    ],
    'ssl' => [
        'verify_peer' => false,
        'verify_peer_name' => false
    ]
]);

$content = file_get_contents($url, false, $context);

if ($content) {
    echo "✅ Page loaded successfully (" . strlen($content) . " chars)\n\n";
    
    // Check for specific Visual Builder elements
    $checks = [
        'visual-builder-container' => 'Visual Builder container',
        'visual-editor-btn' => 'Visual Editor button',
        'html-editor-btn' => 'HTML Editor button',
        'visual-email-builder' => 'Visual email builder panel',
        'html-editor-panel' => 'HTML editor panel',
        'visual-builder-email-editor.js' => 'Visual Builder JavaScript',
        'templateData' => 'Template data initialization',
        'initEnhancedVisualBuilder' => 'Enhanced Visual Builder function',
        'Account Verification Required' => 'Template content in HTML',
        'Dear {{fullname}}' => 'Template shortcodes'
    ];
    
    echo "🔍 ELEMENT CHECKS:\n";
    echo str_repeat('-', 30) . "\n";
    
    foreach ($checks as $search => $description) {
        if (strpos($content, $search) !== false) {
            echo "✅ {$description} found\n";
        } else {
            echo "❌ {$description} missing\n";
        }
    }
    
    echo "\n📄 CONTENT ANALYSIS:\n";
    echo str_repeat('-', 30) . "\n";
    
    // Extract template data if possible
    if (preg_match('/templateData\s*=\s*\{([^}]+)\}/', $content, $matches)) {
        echo "✅ Template data found: " . substr($matches[0], 0, 100) . "...\n";
    } else {
        echo "❌ Template data not found\n";
    }
    
    // Check for JavaScript errors
    if (strpos($content, 'SyntaxError') !== false || strpos($content, 'ReferenceError') !== false) {
        echo "❌ JavaScript errors detected\n";
    } else {
        echo "✅ No JavaScript errors detected\n";
    }
    
    // Check for PHP errors
    if (strpos($content, 'Fatal error') !== false || strpos($content, 'Parse error') !== false) {
        echo "❌ PHP errors detected\n";
    } else {
        echo "✅ No PHP errors detected\n";
    }
    
    // Extract a sample of the actual content to see what's being served
    echo "\n📋 CONTENT SAMPLE (first 500 chars):\n";
    echo str_repeat('-', 40) . "\n";
    echo substr($content, 0, 500) . "...\n";
    
    // Look for the visual builder container specifically
    if (preg_match('/<div[^>]*visual-builder-container[^>]*>.*?<\/div>/s', $content, $matches)) {
        echo "\n🎯 VISUAL BUILDER CONTAINER FOUND:\n";
        echo str_repeat('-', 40) . "\n";
        echo substr($matches[0], 0, 200) . "...\n";
    } else {
        echo "\n❌ Visual Builder container not found in HTML\n";
    }
    
} else {
    echo "❌ Failed to load page\n";
    echo "Error: " . error_get_last()['message'] . "\n";
}

echo "\n" . str_repeat('=', 50) . "\n";
echo "🎯 NEXT STEPS:\n";
echo "1. Check browser developer tools for JavaScript errors\n";
echo "2. Verify Visual Builder JavaScript is loading\n";
echo "3. Check if template data is being passed correctly\n";
echo "4. Test Visual Builder initialization manually\n";
