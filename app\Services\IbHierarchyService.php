<?php

namespace App\Services;

use App\Models\User;
use App\Models\IbGroup;
use App\Models\IbLevel;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class IbHierarchyService
{
    /**
     * Create IB hierarchy for a user
     */
    public function createIbHierarchy($userId, $ibType, $parentId = null, $groupId = null, $approvedBy = null)
    {
        try {
            DB::beginTransaction();

            $user = User::findOrFail($userId);
            
            // Validate parent if provided
            if ($parentId) {
                $parent = User::findOrFail($parentId);
                if (!$parent->isIb()) {
                    throw new \Exception('Parent must be an approved IB');
                }
                
                // Check if parent can have more sub-IBs
                $maxLevels = $parent->ibGroup ? $parent->ibGroup->max_levels : 3;
                $currentLevel = $parent->getIbLevelInHierarchy();
                
                if ($currentLevel >= $maxLevels) {
                    throw new \Exception('Parent IB has reached maximum hierarchy levels');
                }
            }

            // Set default group if not provided
            if (!$groupId) {
                $defaultGroup = IbGroup::getDefaultGroup();
                $groupId = $defaultGroup ? $defaultGroup->id : null;
            }

            // Approve the IB
            $user->approveIbApplication($approvedBy, $ibType, $groupId, $parentId);

            DB::commit();
            
            Log::info("IB hierarchy created for user {$userId}", [
                'user_id' => $userId,
                'ib_type' => $ibType,
                'parent_id' => $parentId,
                'group_id' => $groupId
            ]);

            return $user;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to create IB hierarchy: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get IB hierarchy tree for a user
     */
    public function getIbHierarchyTree($userId, $maxDepth = 5)
    {
        $user = User::with(['ibChildren', 'ibParent'])->findOrFail($userId);
        
        if (!$user->isIb()) {
            return null;
        }

        return $this->buildHierarchyTree($user, 0, $maxDepth);
    }

    /**
     * Build hierarchy tree recursively
     */
    private function buildHierarchyTree($user, $currentDepth, $maxDepth)
    {
        if ($currentDepth >= $maxDepth) {
            return null;
        }

        $tree = [
            'id' => $user->id,
            'username' => $user->username,
            'fullname' => $user->fullname,
            'ib_type' => $user->ib_type,
            'ib_status' => $user->ib_status,
            'level' => $user->getIbLevelInHierarchy(),
            'total_clients' => $user->getTotalReferredClients(),
            'active_clients' => $user->getActiveReferredClients(),
            'children' => []
        ];

        foreach ($user->ibChildren as $child) {
            if ($child->isIb()) {
                $childTree = $this->buildHierarchyTree($child, $currentDepth + 1, $maxDepth);
                if ($childTree) {
                    $tree['children'][] = $childTree;
                }
            }
        }

        return $tree;
    }

    /**
     * Move IB to different parent
     */
    public function moveIbToParent($ibId, $newParentId)
    {
        try {
            DB::beginTransaction();

            $ib = User::findOrFail($ibId);
            $newParent = User::findOrFail($newParentId);

            if (!$ib->isIb()) {
                throw new \Exception('User is not an approved IB');
            }

            if (!$newParent->isIb()) {
                throw new \Exception('New parent is not an approved IB');
            }

            // Check for circular reference
            if ($this->wouldCreateCircularReference($ib, $newParent)) {
                throw new \Exception('Moving IB would create circular reference');
            }

            // Check hierarchy depth limits
            $maxLevels = $newParent->ibGroup ? $newParent->ibGroup->max_levels : 3;
            $newParentLevel = $newParent->getIbLevelInHierarchy();
            
            if ($newParentLevel >= $maxLevels) {
                throw new \Exception('New parent has reached maximum hierarchy levels');
            }

            $ib->ib_parent_id = $newParentId;
            $ib->save();

            DB::commit();

            Log::info("IB moved to new parent", [
                'ib_id' => $ibId,
                'old_parent_id' => $ib->ib_parent_id,
                'new_parent_id' => $newParentId
            ]);

            return $ib;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to move IB: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Check if moving IB would create circular reference
     */
    private function wouldCreateCircularReference($ib, $newParent)
    {
        $current = $newParent;
        
        while ($current && $current->ibParent) {
            if ($current->ibParent->id === $ib->id) {
                return true;
            }
            $current = $current->ibParent;
        }
        
        return false;
    }

    /**
     * Get IB statistics for hierarchy
     */
    public function getHierarchyStats($ibId)
    {
        $ib = User::findOrFail($ibId);
        
        if (!$ib->isIb()) {
            return null;
        }

        $descendants = $ib->getAllIbDescendants();
        
        return [
            'total_sub_ibs' => $descendants->count(),
            'direct_sub_ibs' => $ib->ibChildren()->whereNotNull('ib_status')->count(),
            'total_clients' => $descendants->sum(function($descendant) {
                return $descendant->getTotalReferredClients();
            }) + $ib->getTotalReferredClients(),
            'hierarchy_depth' => $this->getMaxHierarchyDepth($ib),
            'total_commissions' => $ib->ibCommissionsEarned()->sum('commission_amount')
        ];
    }

    /**
     * Get maximum depth of hierarchy under an IB
     */
    private function getMaxHierarchyDepth($ib, $currentDepth = 0)
    {
        $maxDepth = $currentDepth;
        
        foreach ($ib->ibChildren as $child) {
            if ($child->isIb()) {
                $childDepth = $this->getMaxHierarchyDepth($child, $currentDepth + 1);
                $maxDepth = max($maxDepth, $childDepth);
            }
        }
        
        return $maxDepth;
    }

    /**
     * Validate IB hierarchy constraints
     */
    public function validateHierarchyConstraints($ibId)
    {
        $ib = User::findOrFail($ibId);
        $errors = [];

        if (!$ib->isIb()) {
            $errors[] = 'User is not an approved IB';
            return $errors;
        }

        // Check group constraints
        if ($ib->ibGroup) {
            $maxLevels = $ib->ibGroup->max_levels;
            $currentLevel = $ib->getIbLevelInHierarchy();
            
            if ($currentLevel > $maxLevels) {
                $errors[] = "IB exceeds maximum levels ({$maxLevels}) for group {$ib->ibGroup->name}";
            }
        }

        // Check parent relationship
        if ($ib->ibParent && !$ib->ibParent->isIb()) {
            $errors[] = 'Parent is not an approved IB';
        }

        return $errors;
    }
}
