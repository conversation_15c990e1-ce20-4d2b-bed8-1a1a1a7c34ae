
<?php $__env->startSection('panel'); ?>
  <div class="row">
    <div class="col-lg-12">
    <div class="card b-radius--10" style="background: white; border: 1px solid #e5e5e5; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
      <div class="card-header" style="background: #f8f9fa; border-bottom: 1px solid #e5e5e5; padding: 20px;">
        
        <div class="search-filter-container">
          
          <div class="row mb-3">
            <div class="col-12">
              <?php if (isset($component)) { $__componentOriginale48b4598ffc2f41a085f001458a956d1 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale48b4598ffc2f41a085f001458a956d1 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.search-form','data' => ['placeholder' => 'Search by name, email, username, phone or MT5 account...']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('search-form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['placeholder' => 'Search by name, email, username, phone or MT5 account...']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale48b4598ffc2f41a085f001458a956d1)): ?>
<?php $attributes = $__attributesOriginale48b4598ffc2f41a085f001458a956d1; ?>
<?php unset($__attributesOriginale48b4598ffc2f41a085f001458a956d1); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale48b4598ffc2f41a085f001458a956d1)): ?>
<?php $component = $__componentOriginale48b4598ffc2f41a085f001458a956d1; ?>
<?php unset($__componentOriginale48b4598ffc2f41a085f001458a956d1); ?>
<?php endif; ?>
            </div>
          </div>

          
          <div class="row">
            <div class="col-12">
              <div class="filter-buttons-container d-flex flex-wrap gap-2">
                
                <div class="dropdown">
                  <button class="btn btn--primary dropdown-toggle filter-btn" type="button" id="account-type-filter" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-users me-1"></i>
                    <?php if(request()->account_type == 'ib_only'): ?>
                      IB Accounts
                    <?php elseif(request()->account_type == 'regular_only'): ?>
                      Regular Accounts
                    <?php else: ?>
                      Account Type
                    <?php endif; ?>
                  </button>
                  <ul class="dropdown-menu" aria-labelledby="account-type-filter">
                    <li><a class="dropdown-item" href="<?php echo e(route('admin.users.all')); ?>?search=<?php echo e(request()->search); ?>"><?php echo app('translator')->get('All Accounts'); ?></a></li>
                    <li><a class="dropdown-item" href="<?php echo e(route('admin.users.all')); ?>?search=<?php echo e(request()->search); ?>&account_type=ib_only"><?php echo app('translator')->get('IB Accounts Only'); ?></a></li>
                    <li><a class="dropdown-item" href="<?php echo e(route('admin.users.all')); ?>?search=<?php echo e(request()->search); ?>&account_type=regular_only"><?php echo app('translator')->get('Regular Accounts Only'); ?></a></li>
                  </ul>
                </div>

                
                <div class="dropdown">
                  <button class="btn btn--primary dropdown-toggle filter-btn" type="button" id="status-filter" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-filter me-1"></i> Filter
                  </button>
                  <ul class="dropdown-menu" aria-labelledby="status-filter">
                    <li><a class="dropdown-item" href="<?php echo e(route('admin.users.active')); ?>"><?php echo app('translator')->get('Active'); ?></a></li>
                    <li><a class="dropdown-item" href="<?php echo e(route('admin.users.banned')); ?>"><?php echo app('translator')->get('Banned'); ?></a></li>
                    <li><a class="dropdown-item" href="<?php echo e(route('admin.users.email.unverified')); ?>"><?php echo app('translator')->get('Email Unverified'); ?></a></li>
                    <li><a class="dropdown-item" href="<?php echo e(route('admin.users.mobile.unverified')); ?>"><?php echo app('translator')->get('Mobile Unverified'); ?></a></li>
                  </ul>
                </div>

                
                <div class="dropdown">
                  <button class="btn btn--primary dropdown-toggle filter-btn" type="button" id="kyc-filter" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-user-check me-1"></i> KYC
                  </button>
                  <ul class="dropdown-menu" aria-labelledby="kyc-filter">
                    <li><a class="dropdown-item" href="<?php echo e(route('admin.users.kyc.pending')); ?>"><?php echo app('translator')->get('KYC Pending'); ?></a></li>
                    <li><a class="dropdown-item" href="<?php echo e(route('admin.users.kyc.unverified')); ?>"><?php echo app('translator')->get('KYC Unverified'); ?></a></li>
                  </ul>
                </div>

                
                <div class="dropdown">
                  <button class="btn btn--primary dropdown-toggle filter-btn" type="button" id="balance-filter" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-dollar-sign me-1"></i> Balance
                  </button>
                  <ul class="dropdown-menu" aria-labelledby="balance-filter">
                    <li><a class="dropdown-item" href="<?php echo e(route('admin.users.all')); ?>?balance=0"><?php echo app('translator')->get('$0'); ?></a></li>
                    <li><a class="dropdown-item" href="<?php echo e(route('admin.users.all')); ?>?balance=100"><?php echo app('translator')->get('$100'); ?></a></li>
                    <li><a class="dropdown-item" href="<?php echo e(route('admin.users.all')); ?>?balance=1000"><?php echo app('translator')->get('$1,000'); ?></a></li>
                    <li><a class="dropdown-item" href="<?php echo e(route('admin.users.all')); ?>?balance=5000"><?php echo app('translator')->get('$5,000'); ?></a></li>
                    <li><a class="dropdown-item" href="<?php echo e(route('admin.users.all')); ?>?balance=10000"><?php echo app('translator')->get('$10,000'); ?></a></li>
                    <li><a class="dropdown-item" href="<?php echo e(route('admin.users.all')); ?>"><?php echo app('translator')->get('All Balances'); ?></a></li>
                  </ul>
                </div>

                
                <div class="dropdown">
                  <button class="btn btn--primary dropdown-toggle filter-btn" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-download me-1"></i> Export
                  </button>
                  <ul class="dropdown-menu" aria-labelledby="exportDropdown">
                    <li><a class="dropdown-item" href="<?php echo e(url('users/export?type=active')); ?>">Active Users</a></li>
                    <li><a class="dropdown-item" href="<?php echo e(url('users/export?type=banned')); ?>">Banned Users</a></li>
                    <li><a class="dropdown-item" href="<?php echo e(url('users/export?type=email_unverified')); ?>">Email Unverified</a></li>
                    <li><a class="dropdown-item" href="<?php echo e(url('users/export?type=mobile_unverified')); ?>">Mobile Unverified</a></li>
                    <li><a class="dropdown-item" href="<?php echo e(url('users/export?type=kyc_unverified')); ?>">KYC Unverified</a></li>
                    <li><a class="dropdown-item" href="<?php echo e(url('users/export?type=kyc_pending')); ?>">KYC Pending</a></li>
                    <li><a class="dropdown-item" href="<?php echo e(url('users/export?type=all')); ?>">All Users</a></li>
                  </ul>
                </div>

                
                <div class="action-buttons ms-auto d-flex gap-2">
                  
                  <button type="button" class="btn btn--primary filter-btn" onclick="syncMT5Data('all')" id="syncBtn">
                    <i class="fas fa-sync-alt me-1"></i> <?php echo app('translator')->get('Sync'); ?>
                  </button>

                  
                  <a href="<?php echo e(route('admin.users.all')); ?>" class="btn btn-outline-secondary filter-btn">
                    <i class="fas fa-undo me-1"></i> <?php echo app('translator')->get('Reset'); ?>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      
      <div class="card-body p-3" style="background: #f8f9fa; border-bottom: 1px solid #e5e5e5;">
        <div id="mt5-sync-status" class="row g-3">
          <div class="col-md-3">
            <div class="d-flex align-items-center">
              <div class="flex-shrink-0">
                <div class="sync-status-icon" id="sync-status-icon">
                  <i class="fas fa-sync-alt text-primary"></i>
                </div>
              </div>
              <div class="flex-grow-1 ms-3">
                <div class="sync-status-text">
                  <small class="text-muted d-block">Last Sync</small>
                  <span class="fw-bold" id="last-sync-time">Loading...</span>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-2">
            <div class="text-center">
              <small class="text-muted d-block">Total Users</small>
              <span class="fw-bold text-primary" id="total-users"><?php echo e(number_format($users->total())); ?></span>
            </div>
          </div>
          <div class="col-md-2">
            <div class="text-center">
              <small class="text-muted d-block">MT5 Users</small>
              <span class="fw-bold text-success" id="mt5-users">Loading...</span>
            </div>
          </div>
          <div class="col-md-2">
            <div class="text-center">
              <small class="text-muted d-block">Last Sync</small>
              <span class="fw-bold" id="sync-stats">Loading...</span>
            </div>
          </div>
          <div class="col-md-3">
            <div class="d-flex align-items-center justify-content-end">
              <button type="button" class="btn btn-sm btn--primary me-2" onclick="refreshSyncStatus()">
                <i class="fas fa-refresh"></i> Refresh
              </button>
              <button type="button" class="btn btn-sm btn--success" onclick="syncMT5Data('all')" id="quickSyncBtn">
                <i class="fas fa-sync-alt"></i> Quick Sync
              </button>
            </div>
          </div>
        </div>
      </div>

    <div class="card-body p-0">
    <div class="table-responsive--md table-responsive">
      <table class="table table-bordered table--light style--two highlighted-table" style="background: white;">
      <thead>
        <tr>
        <th><?php echo app('translator')->get('Profile'); ?></th>
        <th><?php echo app('translator')->get('User'); ?></th>
        <th><?php echo app('translator')->get('Email-Phone'); ?></th>
        <th><?php echo app('translator')->get('Country'); ?></th>
        <th><?php echo app('translator')->get('MT5'); ?></th>
        <th><?php echo app('translator')->get('Balance'); ?></th>
        <th><?php echo app('translator')->get('Credit'); ?></th>
        <th><?php echo app('translator')->get('Joined At'); ?></th>
        <th><?php echo app('translator')->get('Action'); ?></th>
        </tr>
      </thead>
      <tbody>
        <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
      <tr style="border-bottom: 1px solid #f0f0f0;">
      <td style="padding: 8px; vertical-align: middle; text-align: center;">
        <div class="avatar avatar--sm">
          <img src="<?php echo e(getImage(getFilePath('userProfile') . '/' . $user->image, getFileSize('userProfile'), true)); ?>"
               alt="<?php echo app('translator')->get('Profile Picture'); ?>"
               style="width: 40px; height: 40px; border-radius: 50%; object-fit: cover; border: 2px solid #e5e5e5;">
        </div>
      </td>
      <td style="padding: 12px; vertical-align: middle;">
        <span style="font-size: 12px; font-weight: 600; color: #000;"><?php echo e($user->fullname); ?></span>
        <br>
        <span class="small">
        <a href="<?php echo e(route('admin.users.detail', $user->id)); ?>" style="font-size: 11px; color: #6c757d;">
          <?php
            // FIXED: Issue 3 - Improved username display logic
            $displayUsername = $user->username;

            // Remove any weird hash suffixes like _8e2d24, _94245f, _2fce19, etc.
            $displayUsername = preg_replace('/_[a-f0-9]{4,8}$/', '', $displayUsername);

            // Remove any numeric suffixes that look like random IDs
            $displayUsername = preg_replace('/_\d{4,}$/', '', $displayUsername);

            // Ensure proper @ prefix for MT5 synced users
            if ($user->mt5_synced_at || $user->mt5_login) {
              if (!str_starts_with($displayUsername, '@')) {
                $displayUsername = '@' . $displayUsername;
              }
            }

            // Remove double @ if it exists
            if (str_starts_with($displayUsername, '@@')) {
              $displayUsername = substr($displayUsername, 1);
            }

            // If username is still empty or too short, use full name
            if (empty($displayUsername) || strlen($displayUsername) < 3) {
              $displayUsername = '@' . strtolower($user->firstname . $user->lastname);
              $displayUsername = preg_replace('/[^a-z0-9@]/', '', $displayUsername);
            }

            // Final fallback - use email prefix
            if (empty($displayUsername) || strlen($displayUsername) < 3) {
              $emailPrefix = explode('@', $user->email)[0];
              $displayUsername = '@' . strtolower($emailPrefix);
            }
          ?>
          <?php echo e($displayUsername); ?>

        </a>
        </span>
      </td>
      <td style="padding: 12px; vertical-align: middle;">
        <span class="d-block" style="font-size: 12px; color: #000;"><?php echo e($user->email); ?></span>
        <?php if($user->mobile): ?>
          <small class="text-muted" style="font-size: 11px; color: #6c757d;"><?php echo e($user->mobile); ?></small>
        <?php endif; ?>
      </td>
      <td style="padding: 12px; vertical-align: middle;">
        <span style="font-size: 12px; font-weight: 600; color: #000;" title="<?php echo e(@$user->address->country); ?>"><?php echo e($user->country_code); ?></span>
      </td>
      <td style="padding: 12px; vertical-align: middle;">
        <?php
          // CRITICAL FIX FOR ISSUE 3: Enhanced MT5 accounts parsing with account type hierarchy
          $primaryMT5 = $user->mt5_login;
          $allMT5AccountsDetailed = [];

          if ($user->all_mt5_accounts_detailed) {
              // Parse enhanced format: login:group:type:balance:leverage:currency
              $accountsData = explode('|', $user->all_mt5_accounts_detailed);
              foreach ($accountsData as $accountData) {
                  $parts = explode(':', $accountData);
                  if (count($parts) >= 6) {
                      $allMT5AccountsDetailed[] = [
                          'login' => $parts[0],
                          'group' => $parts[1],
                          'type' => $parts[2],
                          'balance' => floatval($parts[3]),
                          'leverage' => intval($parts[4]),
                          'currency' => $parts[5],
                          'credit' => 0 // Will be fetched from MT5 if needed
                      ];
                  }
              }

              // CRITICAL FIX FOR ISSUE 2: Sort by account type hierarchy (IB first, then real, then demo)
              usort($allMT5AccountsDetailed, function($a, $b) {
                  $hierarchy = ['ib' => 1, 'affiliate' => 2, 'multi-ib' => 3, 'real' => 4, 'demo' => 5];
                  $priorityA = $hierarchy[$a['type']] ?? 999;
                  $priorityB = $hierarchy[$b['type']] ?? 999;
                  return $priorityA - $priorityB;
              });
          }

          // Fallback to simple list if detailed data not available
          $allMT5Accounts = !empty($allMT5AccountsDetailed) ?
              array_column($allMT5AccountsDetailed, 'login') :
              ($user->all_mt5_accounts ? explode(',', $user->all_mt5_accounts) : []);

          $totalMT5Accounts = count($allMT5Accounts);
          $mt5Group = $user->mt5_group ?? 'Unknown';
          $mt5Balance = $user->mt5_balance ?? 0;
          $mt5Credit = $user->mt5_credit ?? 0;
        ?>

        <?php if($primaryMT5): ?>
          <div class="mt5-accounts-container" style="position: relative;"
               onmouseenter="showMT5Tooltip(this)"
               onmouseleave="hideMT5Tooltip(this)">
            <span class="fw-bold text-primary cursor-pointer mt5-account-number"
                  onclick="copyToClipboard('<?php echo e($primaryMT5); ?>')"
                  style="font-size: 12px;">
              <?php echo e($primaryMT5); ?>

            </span>

            <?php if($totalMT5Accounts > 1): ?>
              <span class="badge badge--info ms-1" style="font-size: 10px;">+<?php echo e($totalMT5Accounts - 1); ?></span>
            <?php endif; ?>

            <!-- ISSUE 4 & 5 FIXED: Enhanced hover tooltip with MT5 Group information and no conflicts -->
            <div class="mt5-accounts-tooltip" style="display: none; position: absolute; top: 100%; left: 0; z-index: 9999; background: white; border: 1px solid #ddd; border-radius: 6px; padding: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); min-width: 280px; max-width: 350px; max-height: 250px; overflow-y: auto; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
              <div class="fw-bold mb-2" style="font-size: 10px; color: #333; border-bottom: 1px solid #eee; padding-bottom: 6px;">
                MT5 Accounts for <?php echo e($user->email); ?>

              </div>

              <!-- Primary Account Details -->
              <div class="mb-3 p-2" style="background: #f8f9fa; border-radius: 4px; border-left: 3px solid #dc3545;">
                <div class="d-flex justify-content-between align-items-center mb-1">
                  <span class="fw-bold cursor-pointer" onclick="copyToClipboard('<?php echo e($primaryMT5); ?>')" style="font-size: 11px; color: #dc3545;">
                    <?php echo e($primaryMT5); ?> <small class="text-success">(Primary)</small>
                  </span>
                </div>
                <div style="font-size: 10px; color: #666;">
                  <div><strong>Group:</strong> <?php echo e($mt5Group); ?></div>
                  <div><strong>Type:</strong>
                    <?php if(stripos($mt5Group, 'affiliate') !== false || stripos($mt5Group, 'ib') !== false): ?>
                      <span class="text-warning">IB Account</span>
                    <?php elseif(stripos($mt5Group, 'demo') !== false): ?>
                      <span class="text-info">Demo Account</span>
                    <?php else: ?>
                      <span class="text-primary">Trading Account</span>
                    <?php endif; ?>
                  </div>
                  <?php if($mt5Balance > 0 || $mt5Credit > 0): ?>
                    <div><strong>Balance:</strong> $<?php echo e(number_format($mt5Balance, 2)); ?></div>
                    <?php if($mt5Credit > 0): ?>
                      <div><strong>Credit:</strong> $<?php echo e(number_format($mt5Credit, 2)); ?></div>
                    <?php endif; ?>
                  <?php endif; ?>
                </div>
              </div>

              <!-- Additional Accounts with detailed information -->
              <?php if(count($allMT5AccountsDetailed) > 1): ?>
                <div style="font-size: 11px; color: #666; margin-bottom: 8px;">Additional Accounts (<?php echo e(count($allMT5AccountsDetailed) - 1); ?>):</div>
                <?php $__currentLoopData = $allMT5AccountsDetailed; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $accountDetail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <?php if($accountDetail['login'] != $primaryMT5): ?>
                    <div class="mb-2 p-2" style="background: #f8f9fa; border-radius: 4px; border-left: 2px solid #6c757d;">
                      <div class="d-flex justify-content-between align-items-center mb-1">
                        <span class="fw-bold cursor-pointer" onclick="copyToClipboard('<?php echo e($accountDetail['login']); ?>')" style="font-size: 10px; color: #333;">
                          <?php echo e($accountDetail['login']); ?>

                        </span>
                        <small class="text-muted" style="font-size: 8px;">Click to copy</small>
                      </div>
                      <div style="font-size: 9px; color: #666;">
                        <div><strong>Group:</strong> <?php echo e($accountDetail['group']); ?></div>
                        <div><strong>Type:</strong>
                          <?php if($accountDetail['type'] === 'ib'): ?>
                            <span class="text-warning fw-bold">IB Account</span>
                          <?php elseif($accountDetail['type'] === 'affiliate'): ?>
                            <span class="text-warning">Affiliate Account</span>
                          <?php elseif($accountDetail['type'] === 'demo'): ?>
                            <span class="text-info">Demo Account</span>
                          <?php else: ?>
                            <span class="text-primary">Trading Account</span>
                          <?php endif; ?>
                        </div>
                        <div><strong>Leverage:</strong> 1:<?php echo e(number_format($accountDetail['leverage'])); ?></div>
                        <div><strong>Currency:</strong> <?php echo e($accountDetail['currency']); ?></div>
                        <?php if($accountDetail['balance'] > 0): ?>
                          <div><strong>Balance:</strong> <?php echo e($accountDetail['currency']); ?><?php echo e(number_format($accountDetail['balance'], 2)); ?></div>
                        <?php endif; ?>
                      </div>
                    </div>
                  <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              <?php elseif(count($allMT5Accounts) > 1): ?>
                
                <div style="font-size: 11px; color: #666; margin-bottom: 8px;">Additional Accounts (<?php echo e(count($allMT5Accounts) - 1); ?>):</div>
                <?php $__currentLoopData = $allMT5Accounts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $account): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <?php if($account != $primaryMT5): ?>
                    <div class="d-flex justify-content-between align-items-center mb-1 p-1" style="background: #f8f9fa; border-radius: 3px;">
                      <span class="cursor-pointer" onclick="copyToClipboard('<?php echo e($account); ?>')" style="font-size: 10px; color: #333;">
                        <?php echo e($account); ?>

                      </span>
                      <small class="text-muted" style="font-size: 9px;">Click to copy</small>
                    </div>
                  <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              <?php endif; ?>

              <div class="mt-2 pt-2 border-top text-center">
                <small class="text-muted" style="font-size: 9px;">Click any account number to copy</small>
              </div>
            </div>
          </div>
        <?php else: ?>
          <span class="text-muted" style="font-size: 12px;">No MT5</span>
        <?php endif; ?>
      </td>
      <td style="padding: 12px; vertical-align: middle;">
        <span style="font-size: 12px; font-weight: 600; color: #000;">
          $<?php echo e(number_format($user->mt5_summary['balance'] ?? 0, 2)); ?>

        </span>
      </td>
      <td style="padding: 12px; vertical-align: middle;">
        <span style="font-size: 12px; font-weight: 600; color: #000;">
          $<?php echo e(number_format($user->mt5_summary['credit'] ?? 0, 2)); ?>

        </span>
      </td>
      <td style="padding: 12px; vertical-align: middle;">
        <span class="d-block" style="font-size: 12px; color: #000;"><?php echo e(showDateTime($user->created_at)); ?></span>
        <small class="text-muted" style="font-size: 11px; color: #6c757d;"><?php echo e(diffForHumans($user->created_at)); ?></small>
      </td>
      <td style="padding: 12px; vertical-align: middle;">
        <div class="dropdown">
          <button class="btn btn-sm btn-outline--primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
            <i class="las la-ellipsis-v"></i>
          </button>
          <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="<?php echo e(route('admin.users.detail', $user->id)); ?>">
              <i class="las la-eye"></i> <?php echo app('translator')->get('View Details'); ?>
            </a></li>
            <li><a class="dropdown-item" href="<?php echo e(route('admin.users.login.as.user', $user->id)); ?>" target="_blank">
              <i class="las la-sign-in-alt"></i> <?php echo app('translator')->get('Login as User'); ?>
            </a></li>
            <?php if($user->status == 1): ?>
              <li><a class="dropdown-item text-warning" href="#" onclick="suspendUser(<?php echo e($user->id); ?>)">
                <i class="las la-pause"></i> <?php echo app('translator')->get('Suspend'); ?>
              </a></li>
            <?php else: ?>
              <li><a class="dropdown-item text-success" href="#" onclick="activateUser(<?php echo e($user->id); ?>)">
                <i class="las la-play"></i> <?php echo app('translator')->get('Activate'); ?>
              </a></li>
            <?php endif; ?>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item text-danger" href="#" onclick="banUser(<?php echo e($user->id); ?>)">
              <i class="las la-ban"></i> <?php echo app('translator')->get('Ban User'); ?>
            </a></li>
            <?php if(request()->routeIs('admin.users.kyc.pending')): ?>
              <li><a class="dropdown-item" href="<?php echo e(route('admin.users.kyc.details', $user->id)); ?>" target="_blank">
                <i class="las la-user-check"></i> <?php echo app('translator')->get('KYC Data'); ?>
              </a></li>
            <?php endif; ?>
            <?php if(request()->routeIs('admin.users.request.pending')): ?>
              <li><a class="dropdown-item" href="<?php echo e(route('admin.users.request.data', $user->id)); ?>" target="_blank">
                <i class="las la-user-check"></i> <?php echo app('translator')->get('Show Request'); ?>
              </a></li>
            <?php endif; ?>
          </ul>
        </div>
      </td>
      </tr>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
    <tr>
    <td class="text-muted text-center" colspan="9">
      <div class="py-4">
        <i class="las la-frown f-size--40 text-muted"></i>
        <br>
        <?php echo app('translator')->get('No users found'); ?>
      </div>
    </td>
    </tr>
  <?php endif; ?>
      </tbody>
      </table><!-- table end -->
    </div>
    </div>
    <?php if($users->hasPages()): ?>
    <div class="card-footer py-4">
    <?php echo e(paginateLinks($users)); ?>

    </div>
  <?php endif; ?>
  </div>
  </div>
  </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('breadcrumb-plugins'); ?>
  <div class="d-flex flex-wrap align-items-center gap-2 justify-content-between">

  </div>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script'); ?>
<script>
// Auto-refresh sync status every 30 seconds
let syncStatusInterval;

document.addEventListener('DOMContentLoaded', function() {
    // Load initial sync status
    refreshSyncStatus();

    // Set up auto-refresh
    syncStatusInterval = setInterval(refreshSyncStatus, 30000); // 30 seconds
});

function refreshSyncStatus() {
    fetch('<?php echo e(route("admin.mt5.sync.status")); ?>', {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
        }
    })
    .then(response => response.json())
    .then(data => {
        updateSyncStatusDisplay(data);
    })
    .catch(error => {
        console.error('Sync status error:', error);
        // Show fallback status
        updateSyncStatusDisplay({
            last_sync: 'Unknown',
            total_users: '<?php echo e(number_format($users->total())); ?>',
            mt5_users: 'Unknown',
            sync_status: 'error',
            processed: 0,
            created: 0,
            updated: 0,
            errors: 0
        });
    });
}

function updateSyncStatusDisplay(data) {
    // Update last sync time
    const lastSyncElement = document.getElementById('last-sync-time');
    if (data.last_sync && data.last_sync !== 'Unknown') {
        const syncDate = new Date(data.last_sync);
        const now = new Date();
        const diffMinutes = Math.floor((now - syncDate) / (1000 * 60));

        if (diffMinutes < 1) {
            lastSyncElement.textContent = 'Just now';
            lastSyncElement.className = 'fw-bold text-success';
        } else if (diffMinutes < 60) {
            lastSyncElement.textContent = `${diffMinutes}m ago`;
            lastSyncElement.className = 'fw-bold text-success';
        } else {
            const diffHours = Math.floor(diffMinutes / 60);
            lastSyncElement.textContent = `${diffHours}h ago`;
            lastSyncElement.className = 'fw-bold text-warning';
        }
    } else {
        lastSyncElement.textContent = 'Unknown';
        lastSyncElement.className = 'fw-bold text-muted';
    }

    // Update user counts
    document.getElementById('total-users').textContent = data.total_users ? data.total_users.toLocaleString() : '<?php echo e(number_format($users->total())); ?>';
    document.getElementById('mt5-users').textContent = data.mt5_users ? data.mt5_users.toLocaleString() : 'Loading...';

    // Update sync stats
    const syncStatsElement = document.getElementById('sync-stats');
    if (data.processed > 0) {
        syncStatsElement.innerHTML = `
            <small class="text-success">+${data.created || 0}</small> /
            <small class="text-primary">~${data.updated || 0}</small>
            ${data.errors > 0 ? `/ <small class="text-danger">${data.errors} err</small>` : ''}
        `;
    } else {
        syncStatsElement.textContent = 'No recent sync';
    }

    // Update sync status icon
    const syncIcon = document.getElementById('sync-status-icon');
    const iconElement = syncIcon.querySelector('i');

    switch(data.sync_status) {
        case 'success':
            iconElement.className = 'fas fa-check-circle text-success';
            break;
        case 'warning':
            iconElement.className = 'fas fa-exclamation-triangle text-warning';
            break;
        case 'error':
            iconElement.className = 'fas fa-times-circle text-danger';
            break;
        default:
            iconElement.className = 'fas fa-sync-alt text-primary';
    }
}

function syncMT5Data(type) {
    const syncBtn = document.getElementById('syncBtn');
    const originalText = syncBtn.innerHTML;

    // Show loading state
    syncBtn.disabled = true;
    syncBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Syncing...';

    // Make AJAX request to sync endpoint
    fetch('<?php echo e(route("admin.mt5.sync")); ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
        },
        body: JSON.stringify({
            type: type,
            filter: null // All users
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            showNotification('success', data.message || 'MT5 data synchronized successfully!');

            // Refresh sync status immediately
            refreshSyncStatus();

            // Reload page after short delay to show new users
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            showNotification('error', data.message || 'Synchronization failed. Please try again.');

            // Reset button
            syncBtn.disabled = false;
            syncBtn.innerHTML = originalText;
        }
    })
    .catch(error => {
        console.error('Sync error:', error);
        showNotification('error', 'Network error. Please check your connection and try again.');

        // Reset button
        syncBtn.disabled = false;
        syncBtn.innerHTML = originalText;
    });
}

function showNotification(type, message) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}
</script>
<?php $__env->stopPush(); ?>

<style>
  /* Search and Filter Component Styling */
  .search-filter-container {
    background: white;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e5e5e5;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  }

  .filter-buttons-container {
    min-height: 45px;
  }

  .filter-btn {
    height: 45px !important;
    padding: 0 16px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    border-radius: 6px !important;
    display: flex !important;
    align-items: center !important;
    white-space: nowrap !important;
    min-width: auto !important;
  }

  .filter-btn i {
    font-size: 14px;
  }

  .action-buttons {
    flex-shrink: 0;
  }

  /* Responsive design */
  @media (max-width: 768px) {
    .filter-buttons-container {
      justify-content: center !important;
    }

    .action-buttons {
      margin-left: 0 !important;
      margin-top: 10px;
      width: 100%;
      justify-content: center;
    }

    .filter-btn {
      font-size: 13px !important;
      padding: 0 12px !important;
    }
  }

  @media (max-width: 576px) {
    .filter-buttons-container {
      flex-direction: column;
      align-items: stretch !important;
      gap: 8px !important;
    }

    .filter-btn {
      width: 100%;
      justify-content: center !important;
    }

    .action-buttons {
      flex-direction: column;
      gap: 8px !important;
    }
  }

  .icon {
    width: 14px;
    height: 14px;
    fill: currentColor;
  }

  .align-items-center {
    align-items: baseline !important;
  }

  .card-footer2 {
    margin-bottom: 0 !important;
  }

  .cursor-pointer {
    cursor: pointer;
  }

  /* Remove CSS hover to prevent conflicts with JavaScript */

  .mt5-accounts-tooltip {
    z-index: 99999 !important;
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    background: white !important;
    border: 1px solid #ddd !important;
    border-radius: 4px !important;
    padding: 8px !important;
    box-shadow: 0 8px 24px rgba(0,0,0,0.25) !important;
    min-width: 250px !important;
    max-width: 300px !important;
    max-height: 200px !important;
    overflow-y: auto !important;
    transform: translateZ(0) !important;
  }

  form.d-flex.flex-wrap.gap-2 {
    margin-bottom: 0px !important;
  }

  .mt5-accounts-tooltip::-webkit-scrollbar {
    width: 4px;
  }

  .mt5-accounts-tooltip::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
  }

  .mt5-accounts-tooltip::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
  }

  .mt5-accounts-tooltip::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  /* Ensure tooltip appears above pagination and other elements */
  .table-responsive {
    overflow: visible !important;
  }

  .card {
    overflow: visible !important;
  }

  /* Fix for pagination z-index */
  .pagination {
    z-index: 1 !important;
  }

  /* Ensure table cells don't clip the tooltip */
  td {
    overflow: visible !important;
  }

  .dropdown-menu {
    min-width: 160px;
  }

  .dropdown-item {
    padding: 0.5rem 1rem;
  }

  .dropdown-item i {
    width: 16px;
    margin-right: 8px;
  }

  /* White background styling */
  body {
    background-color: #f8f9fa !important;
  }

  .card {
    background: white !important;
    border: 1px solid #e5e5e5 !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
  }

  .table {
    background: white !important;
  }

  .table-hover tbody tr:hover {
    background-color: #f8f9fa !important;
  }
</style>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'alert alert-success position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; padding: 10px 15px;';
        toast.innerHTML = `<i class="las la-check"></i> Copied: ${text}`;
        document.body.appendChild(toast);

        setTimeout(() => {
            document.body.removeChild(toast);
        }, 2000);
    });
}

function suspendUser(userId) {
    if (confirm('Are you sure you want to suspend this user?')) {
        // Add your suspend user logic here
        window.location.href = `/admin/users/status/${userId}`;
    }
}

function activateUser(userId) {
    if (confirm('Are you sure you want to activate this user?')) {
        // Add your activate user logic here
        window.location.href = `/admin/users/status/${userId}`;
    }
}

function banUser(userId) {
    if (confirm('Are you sure you want to ban this user? This action cannot be undone.')) {
        // Add your ban user logic here
        window.location.href = `/admin/users/ban/${userId}`;
    }
}

// ISSUE 2 FIXED: Enhanced MT5 tooltip functions with better reliability
let tooltipTimeout;
let activeTooltip = null;

function showMT5Tooltip(container) {
    // Clear any existing timeout
    clearTimeout(tooltipTimeout);

    // Hide any currently active tooltip
    if (activeTooltip && activeTooltip !== container) {
        hideMT5Tooltip(activeTooltip);
    }

    // Small delay to prevent flickering
    tooltipTimeout = setTimeout(() => {
        const tooltip = container.querySelector('.mt5-accounts-tooltip');
        if (tooltip) {
            // Reset any previous styles
            tooltip.style.display = 'block';
            tooltip.style.opacity = '0';
            tooltip.style.transform = 'translateY(-5px)';
            tooltip.style.transition = 'none';

            // Force reflow
            tooltip.offsetHeight;

            // Animate in
            setTimeout(() => {
                tooltip.style.transition = 'all 0.2s ease-in-out';
                tooltip.style.opacity = '1';
                tooltip.style.transform = 'translateY(0)';
                activeTooltip = container;
            }, 10);
        }
    }, 150); // Slightly longer delay for better UX
}

function hideMT5Tooltip(container) {
    // Clear any existing timeout
    clearTimeout(tooltipTimeout);

    const tooltip = container.querySelector('.mt5-accounts-tooltip');
    if (tooltip && tooltip.style.display !== 'none') {
        tooltip.style.transition = 'all 0.15s ease-in-out';
        tooltip.style.opacity = '0';
        tooltip.style.transform = 'translateY(-5px)';

        setTimeout(() => {
            if (tooltip.style.opacity === '0') { // Only hide if still faded out
                tooltip.style.display = 'none';
                if (activeTooltip === container) {
                    activeTooltip = null;
                }
            }
        }, 150);
    }
}

// Add global click handler to hide tooltips when clicking outside
document.addEventListener('click', function(event) {
    if (activeTooltip && !activeTooltip.contains(event.target)) {
        hideMT5Tooltip(activeTooltip);
    }
});
</script>
<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\mbf.mybrokerforex.com-********\resources\views/admin/users/list.blade.php ENDPATH**/ ?>