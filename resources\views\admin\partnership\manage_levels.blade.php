@extends('admin.layouts.app')

@section('panel')
<div class="row">
    <div class="col-lg-12">
        <div class="card b-radius--10">
            <div class="card-header">
                <div class="row g-3 align-items-center">
                    <div class="col-md-8">
                        <h5 class="card-title">@lang('Manage Account Levels')</h5>
                        <p class="text-muted mb-0">@lang('Configure trading account types and platform settings')</p>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn--primary btn-sm" data-bs-toggle="modal" data-bs-target="#addAccountLevelModal">
                                <i class="las la-plus"></i> @lang('Add New Account Level')
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive--md table-responsive">
                    <table class="table--light style--two table">
                        <thead>
                            <tr>
                                <th>@lang('Image')</th>
                                <th>@lang('Account Level')</th>
                                <th>@lang('Platform Group')</th>
                                <th>@lang('Trading Server')</th>
                                <th>@lang('Swap Free')</th>
                                <th>@lang('Leverage Options')</th>
                                <th>@lang('Status')</th>
                                <th>@lang('Action')</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($accountLevels as $level)
                            <tr>
                                {{-- PART 3.1: Enhanced Image Display Column --}}
                                <td class="text-center">
                                    <div class="account-level-image">
                                        @if($level->image)
                                            <img src="{{ $level->image_url }}" alt="{{ $level->name }}"
                                                 class="account-level-img"
                                                 style="width: 40px; height: 40px; object-fit: cover; border-radius: 8px; border: 2px solid #e9ecef;">
                                        @else
                                            <div class="no-image-placeholder"
                                                 style="width: 40px; height: 40px; background: #f8f9fa; border: 2px dashed #dee2e6; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                                                <i class="las la-image text-muted"></i>
                                            </div>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <div class="account-level-info">
                                        <strong class="d-block">{{ $level->name }}</strong>
                                        @if($level->tags)
                                            <small class="text-muted">{{ $level->formatted_tags }}</small>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <span class="fw-bold">{{ $level->platform_group_default }}</span>
                                    @if($level->enable_separate_swap_free && $level->platform_group_swap_free)
                                        <br><small class="text-info">Swap Free: {{ $level->platform_group_swap_free }}</small>
                                    @endif
                                </td>
                                <td>{{ $level->trading_server_live }}</td>
                                <td>
                                    @if($level->enable_separate_swap_free)
                                        <span class="badge badge--success">@lang('Enabled')</span>
                                    @else
                                        <span class="badge badge--secondary">@lang('Disabled')</span>
                                    @endif
                                </td>
                                <td>{{ $level->formatted_leverage_options }}</td>
                                <td>
                                    @if($level->status)
                                        <span class="badge badge--success">@lang('Active')</span>
                                    @else
                                        <span class="badge badge--warning">@lang('Inactive')</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="dropdown">
                                        <button type="button" class="btn btn-sm btn-outline--primary dropdown-toggle" data-bs-toggle="dropdown">
                                            <i class="las la-ellipsis-v"></i>
                                        </button>
                                        <div class="dropdown-menu">
                                            <button type="button" class="dropdown-item editBtn"
                                                    data-id="{{ $level->id }}"
                                                    data-name="{{ $level->name }}"
                                                    data-platform_group_default="{{ $level->platform_group_default }}"
                                                    data-trading_server_live="{{ $level->trading_server_live }}"
                                                    data-enable_separate_swap_free="{{ $level->enable_separate_swap_free }}"
                                                    data-platform_group_swap_free="{{ $level->platform_group_swap_free }}"
                                                    data-leverage_options="{{ json_encode($level->leverage_options) }}"
                                                    data-country_restrictions="{{ json_encode($level->country_restrictions) }}"
                                                    data-tags="{{ json_encode($level->tags) }}">
                                                <i class="las la-pencil"></i> @lang('Edit')
                                            </button>

                                            <button type="button" class="dropdown-item toggleStatusBtn"
                                                    data-id="{{ $level->id }}"
                                                    data-status="{{ $level->status }}">
                                                @if($level->status)
                                                    <i class="las la-eye-slash"></i> @lang('Disable')
                                                @else
                                                    <i class="las la-eye"></i> @lang('Enable')
                                                @endif
                                            </button>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="8" class="text-muted text-center">{{ __($emptyMessage ?? 'No account levels found') }}</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if($accountLevels->hasPages())
            <div class="card-footer py-4">
                {{ paginateLinks($accountLevels) }}
            </div>
            @endif
        </div>
    </div>
</div>



<!-- Add Account Level Modal -->
<div class="modal fade" id="addAccountLevelModal" tabindex="-1" role="dialog" aria-labelledby="addAccountLevelModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addAccountLevelModalLabel">@lang('Add New Account Level')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('admin.partnership.store_account_level') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Account Level Name') <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Image')</label>
                                <input type="file" class="form-control" name="image" accept="image/*">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Platform Group (Default)') <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="platform_group_default" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Trading Server (Live)') <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="trading_server_live" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="enable_separate_swap_free" id="enableSwapFree">
                                    <label class="form-check-label" for="enableSwapFree">
                                        @lang('Enable Separate Swap Free')
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group" id="swapFreeGroup" style="display: none;">
                                <label>@lang('Platform Group (Swap Free)')</label>
                                <input type="text" class="form-control" name="platform_group_swap_free">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>@lang('Leverage Options') <span class="text-danger">*</span></label>
                        <select class="form-control select2-multi" name="leverage_options[]" multiple required>
                            <option value="50">1:50</option>
                            <option value="100">1:100</option>
                            <option value="200">1:200</option>
                            <option value="300">1:300</option>
                            <option value="400">1:400</option>
                            <option value="500">1:500</option>
                            <option value="1000">1:1000</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>@lang('Country Restrictions')</label>
                        <input type="text" class="form-control" name="country_restrictions" placeholder="@lang('Enter countries separated by commas')">
                        <small class="text-muted">@lang('Leave empty to allow all countries')</small>
                    </div>
                    
                    <div class="form-group">
                        <label>@lang('Tags')</label>
                        <input type="text" class="form-control" name="tags" placeholder="@lang('Enter tags separated by commas')">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn--dark" data-bs-dismiss="modal">@lang('Close')</button>
                    <button type="submit" class="btn btn--primary">@lang('Save')</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Account Level Modal -->
<div class="modal fade" id="editAccountLevelModal" tabindex="-1" role="dialog" aria-labelledby="editAccountLevelModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editAccountLevelModalLabel">@lang('Edit Account Level')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editAccountLevelForm" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Account Level Name') <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="name" id="editName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Image')</label>
                                <input type="file" class="form-control" name="image" accept="image/*">
                                <small class="text-muted">@lang('Leave empty to keep current image')</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Platform Group (Default)') <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="platform_group_default" id="editPlatformGroupDefault" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Trading Server (Live)') <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="trading_server_live" id="editTradingServerLive" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="enable_separate_swap_free" id="editEnableSwapFree">
                                    <label class="form-check-label" for="editEnableSwapFree">
                                        @lang('Enable Separate Swap Free')
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group" id="editSwapFreeGroup" style="display: none;">
                                <label>@lang('Platform Group (Swap Free)')</label>
                                <input type="text" class="form-control" name="platform_group_swap_free" id="editPlatformGroupSwapFree">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>@lang('Leverage Options') <span class="text-danger">*</span></label>
                        <select class="form-control select2-multi" name="leverage_options[]" id="editLeverageOptions" multiple required>
                            <option value="50">1:50</option>
                            <option value="100">1:100</option>
                            <option value="200">1:200</option>
                            <option value="300">1:300</option>
                            <option value="400">1:400</option>
                            <option value="500">1:500</option>
                            <option value="1000">1:1000</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>@lang('Country Restrictions')</label>
                        <input type="text" class="form-control" name="country_restrictions" id="editCountryRestrictions" placeholder="@lang('Enter countries separated by commas')">
                        <small class="text-muted">@lang('Leave empty to allow all countries')</small>
                    </div>

                    <div class="form-group">
                        <label>@lang('Tags')</label>
                        <input type="text" class="form-control" name="tags" id="editTags" placeholder="@lang('Enter tags separated by commas')">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn--dark" data-bs-dismiss="modal">@lang('Close')</button>
                    <button type="submit" class="btn btn--primary">@lang('Update')</button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

{{-- PART 3.1: Enhanced Image Display Styling --}}
@push('style')
<style>
.account-level-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.account-level-img {
    transition: all 0.3s ease;
    cursor: pointer;
}

.account-level-img:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.no-image-placeholder {
    transition: all 0.3s ease;
}

.no-image-placeholder:hover {
    background: #e9ecef !important;
    border-color: #adb5bd !important;
}

.account-level-info strong {
    color: #333;
    font-size: 14px;
}

.account-level-info small {
    font-size: 12px;
    color: #6c757d;
}

/* Image preview in modal */
.image-preview {
    max-width: 100px;
    max-height: 100px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid #e9ecef;
    margin-top: 10px;
}

/* Enhanced form styling */
.form-group label {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.form-control:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.select2-container--default .select2-selection--multiple:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}
</style>
@endpush

@push('script')
<script>
    (function ($) {
        'use strict';
        
        // Toggle swap free group visibility
        $('#enableSwapFree').change(function() {
            if ($(this).is(':checked')) {
                $('#swapFreeGroup').show();
            } else {
                $('#swapFreeGroup').hide();
            }
        });
        
        // Initialize Select2 for multiple select
        $('.select2-multi').select2({
            placeholder: '@lang("Select leverage options")',
            allowClear: true
        });

        // Edit button click
        $('.editBtn').on('click', function() {
            const id = $(this).data('id');
            const name = $(this).data('name');
            const platformGroupDefault = $(this).data('platform_group_default');
            const tradingServerLive = $(this).data('trading_server_live');
            const enableSwapFree = $(this).data('enable_separate_swap_free');
            const platformGroupSwapFree = $(this).data('platform_group_swap_free');
            const leverageOptions = $(this).data('leverage_options');
            const countryRestrictions = $(this).data('country_restrictions');
            const tags = $(this).data('tags');

            // Fill form fields
            $('#editName').val(name);
            $('#editPlatformGroupDefault').val(platformGroupDefault);
            $('#editTradingServerLive').val(tradingServerLive);
            $('#editEnableSwapFree').prop('checked', enableSwapFree);
            $('#editPlatformGroupSwapFree').val(platformGroupSwapFree);

            // Handle leverage options
            if (leverageOptions && Array.isArray(leverageOptions)) {
                $('#editLeverageOptions').val(leverageOptions).trigger('change');
            }

            // Handle country restrictions
            if (countryRestrictions && Array.isArray(countryRestrictions)) {
                $('#editCountryRestrictions').val(countryRestrictions.join(', '));
            }

            // Handle tags
            if (tags && Array.isArray(tags)) {
                $('#editTags').val(tags.join(', '));
            }

            // Show/hide swap free group
            if (enableSwapFree) {
                $('#editSwapFreeGroup').show();
            } else {
                $('#editSwapFreeGroup').hide();
            }

            // Set form action
            const updateUrl = `{{ route('admin.partnership.update_account_level', ':id') }}`.replace(':id', id);
            $('#editAccountLevelForm').attr('action', updateUrl);

            $('#editAccountLevelModal').modal('show');
        });

        // Toggle edit swap free group visibility
        $('#editEnableSwapFree').change(function() {
            if ($(this).is(':checked')) {
                $('#editSwapFreeGroup').show();
            } else {
                $('#editSwapFreeGroup').hide();
            }
        });

    })(jQuery);
</script>
@endpush
