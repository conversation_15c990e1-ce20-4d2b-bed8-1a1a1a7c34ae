<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\MT5CompleteMigrationService;

class MT5CompleteMigration extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'mt5:complete-migration {--clear : Clear existing data before migration}';

    /**
     * The console command description.
     */
    protected $description = 'Complete MT5 migration - All accounts as separate entries with primary user per email';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Starting Complete MT5 Migration...');
        $this->info('Strategy: All MT5 accounts as separate entries, primary user per email');
        
        $clearData = $this->option('clear');
        
        if ($clearData) {
            if (!$this->confirm('⚠️  This will DELETE ALL existing users and related data. Continue?')) {
                $this->info('Migration cancelled.');
                return;
            }
        }

        $migrationService = new MT5CompleteMigrationService();
        
        $this->info('📊 Starting migration process...');
        $startTime = microtime(true);
        
        $result = $migrationService->migrateAllMT5AccountsComplete($clearData);
        
        $endTime = microtime(true);
        $duration = round($endTime - $startTime, 2);
        
        if ($result['success']) {
            $this->info('✅ Migration completed successfully!');
            $this->table(
                ['Metric', 'Count'],
                [
                    ['Total MT5 Accounts Processed', $result['total_processed']],
                    ['Primary Users Created', $result['users_created']],
                    ['MT5 Account Records Created', $result['accounts_created']],
                    ['Errors', $result['errors']],
                    ['Duration', $duration . ' seconds']
                ]
            );
            
            $this->info('📋 Migration Summary:');
            $this->line("• Each email has ONE primary user in the main users table");
            $this->line("• ALL MT5 accounts are stored in user_accounts table");
            $this->line("• Admin list shows primary user per email");
            $this->line("• User detail page shows all MT5 accounts in Accounts tab");
            $this->line("• IB status determined from best available MT5 group");
            $this->line("• Contact info uses best data from all accounts");
            
        } else {
            $this->error('❌ Migration failed: ' . $result['error']);
        }
    }
}
