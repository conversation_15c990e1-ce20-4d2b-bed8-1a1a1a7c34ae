<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Editor Test - Fixed Version</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="assets/admin/css/simple-email-editor.css">
    <link rel="stylesheet" href="assets/global/css/iziToast.min.css">
    <style>
        body { padding: 20px; background: #f8f9fa; }
        .test-container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .status-indicator { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .status-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Email Editor Test - Fixed Version</h1>
        <p class="text-muted">Testing the consolidated email editor with proper Laravel notifications</p>
        
        <div id="test-results"></div>
        
        <!-- Email Editor Interface -->
        <div class="card mt-4">
            <div class="card-header">
                <h5>Email Template Editor</h5>
            </div>
            <div class="card-body">
                <!-- Tab Navigation -->
                <ul class="nav nav-tabs" id="editorTabs">
                    <li class="nav-item">
                        <button class="nav-link active" id="visual-tab" data-bs-toggle="tab" data-bs-target="#visual-editor-panel">Visual Editor</button>
                    </li>
                    <li class="nav-item">
                        <button class="nav-link" id="html-tab" data-bs-toggle="tab" data-bs-target="#html-editor-panel">HTML Editor</button>
                    </li>
                </ul>
                
                <!-- Tab Content -->
                <div class="tab-content mt-3">
                    <div class="tab-pane fade show active" id="visual-editor-panel">
                        <div id="visual-editor" class="border p-3" style="min-height: 300px; background: white;">
                            <p>Welcome to our platform! Your account has been successfully created.</p>
                            <p>You can now start trading with confidence.</p>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="html-editor-panel">
                        <textarea id="html-editor-textarea" class="form-control" rows="15" placeholder="Enter HTML content here...">
&lt;p&gt;Welcome to our platform! Your account has been successfully created.&lt;/p&gt;
&lt;p&gt;You can now start trading with confidence.&lt;/p&gt;
                        </textarea>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="mt-3">
                    <button type="button" class="btn btn-primary" onclick="testSave()">💾 Test Save</button>
                    <button type="button" class="btn btn-info" onclick="testPreview()">👁️ Test Preview</button>
                    <button type="button" class="btn btn-success" onclick="testNotification()">🔔 Test Notification</button>
                    <button type="button" class="btn btn-warning" onclick="testValidation()">✅ Test Validation</button>
                </div>
            </div>
        </div>
        
        <!-- Hidden Form Fields (simulating Laravel form) -->
        <form id="template-form" style="display: none;">
            <input type="hidden" name="email_body" value="">
            <input type="hidden" name="email_body_final" value="">
            <input type="hidden" name="subject" value="Test Subject">
            <input type="hidden" name="sms_body" value="Test SMS">
        </form>
    </div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/global/js/jquery-3.7.1.min.js"></script>
    <script src="assets/global/js/iziToast.min.js"></script>
    
    <!-- Simulate Laravel's notify function -->
    <script>
        function notify(status, message) {
            if (typeof iziToast !== 'undefined') {
                iziToast[status]({
                    message: message,
                    position: "topRight"
                });
            } else {
                console.log(`[${status.toUpperCase()}] ${message}`);
            }
        }
        
        // Simulate template data
        window.templateData = {
            content: '<p>Welcome to our platform! Your account has been successfully created.</p><p>You can now start trading with confidence.</p>',
            templateId: 1,
            testEmailRoute: '/test-email',
            previewRoute: '/preview/1',
            csrfToken: 'test-token',
            debug: {
                templateName: 'Test Template',
                templateSubject: 'Test Subject',
                contentLength: 100,
                serverTime: new Date().toISOString(),
                laravelVersion: '10.x',
                phpVersion: '8.1'
            }
        };
    </script>
    
    <!-- Load our consolidated email editor -->
    <script src="assets/admin/js/simple-email-editor.js"></script>
    
    <!-- Test Functions -->
    <script>
        function addTestResult(message, type = 'info') {
            const results = document.getElementById('test-results');
            const div = document.createElement('div');
            div.className = `status-indicator status-${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            results.appendChild(div);
        }
        
        function testSave() {
            addTestResult('Testing save functionality...', 'info');
            // Simulate save
            setTimeout(() => {
                notify('success', 'Template saved successfully!');
                addTestResult('✅ Save test completed', 'success');
            }, 1000);
        }
        
        function testPreview() {
            addTestResult('Testing preview functionality...', 'info');
            // Simulate preview
            setTimeout(() => {
                notify('success', 'Preview opened successfully!');
                addTestResult('✅ Preview test completed', 'success');
            }, 500);
        }
        
        function testNotification() {
            addTestResult('Testing notification system...', 'info');
            notify('success', 'This is a success notification!');
            setTimeout(() => notify('error', 'This is an error notification!'), 1000);
            setTimeout(() => notify('info', 'This is an info notification!'), 2000);
            addTestResult('✅ Notification test completed', 'success');
        }
        
        function testValidation() {
            addTestResult('Testing validation...', 'info');
            // Test validation
            setTimeout(() => {
                if (typeof window.validateForm === 'function') {
                    notify('success', 'Validation functions are working!');
                    addTestResult('✅ Validation test completed', 'success');
                } else {
                    notify('error', 'Validation functions not found!');
                    addTestResult('❌ Validation test failed', 'error');
                }
            }, 500);
        }
        
        // Initial test
        document.addEventListener('DOMContentLoaded', function() {
            addTestResult('🚀 Email Editor Test Started', 'info');
            
            // Check if editor loaded
            setTimeout(() => {
                if (typeof window.SERVER_CONFIG !== 'undefined') {
                    addTestResult('✅ Email editor loaded successfully', 'success');
                } else {
                    addTestResult('⚠️ Email editor may not have loaded properly', 'error');
                }
                
                // Check notification system
                if (typeof notify === 'function') {
                    addTestResult('✅ Notification system available', 'success');
                } else {
                    addTestResult('❌ Notification system not available', 'error');
                }
                
                // Check iziToast
                if (typeof iziToast !== 'undefined') {
                    addTestResult('✅ iziToast library loaded', 'success');
                } else {
                    addTestResult('❌ iziToast library not loaded', 'error');
                }
            }, 2000);
        });
    </script>
</body>
</html>