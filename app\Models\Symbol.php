<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Symbol extends Model
{
    use HasFactory;

    protected $fillable = [
        'symbol',
        'path',
        'description',
        'contract_size',
        'status'
    ];

    protected $casts = [
        'contract_size' => 'decimal:2',
        'status' => 'boolean'
    ];

    /**
     * Scope for active symbols
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Scope for inactive symbols
     */
    public function scopeInactive($query)
    {
        return $query->where('status', false);
    }

    /**
     * Get symbols by path category
     */
    public function scopeByPath($query, $path)
    {
        return $query->where('path', 'like', '%' . $path . '%');
    }

    /**
     * Get symbol groups that contain this symbol
     */
    public function symbolGroups()
    {
        return $this->belongsToMany(SymbolGroup::class, 'symbol_group_symbols', 'symbol_id', 'symbol_group_id');
    }

    /**
     * Toggle symbol status
     */
    public function toggleStatus()
    {
        $this->status = !$this->status;
        $this->save();
        return $this;
    }

    /**
     * Get path category (e.g., "Forex", "Commodities", etc.)
     */
    public function getPathCategoryAttribute()
    {
        $pathParts = explode('\\', $this->path);
        return $pathParts[0] ?? 'Unknown';
    }

    /**
     * Get path subcategory
     */
    public function getPathSubcategoryAttribute()
    {
        $pathParts = explode('\\', $this->path);
        return $pathParts[1] ?? '';
    }

    /**
     * Format contract size for display
     */
    public function getFormattedContractSizeAttribute()
    {
        return number_format($this->contract_size, 0);
    }

    /**
     * Get all symbols grouped by category
     */
    public static function getGroupedByCategory()
    {
        return self::active()
            ->get()
            ->groupBy('path_category')
            ->map(function ($symbols) {
                return $symbols->groupBy('path_subcategory');
            });
    }

    /**
     * Search symbols
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('symbol', 'like', '%' . $search . '%')
              ->orWhere('description', 'like', '%' . $search . '%')
              ->orWhere('path', 'like', '%' . $search . '%');
        });
    }
}
