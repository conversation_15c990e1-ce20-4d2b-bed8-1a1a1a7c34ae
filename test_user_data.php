<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;

echo "=== PHASE 1 TESTING: User Data Display ===\n";

// Get a user with MT5 data that has actual address info
$user = User::whereNotNull('mt5_login')
    ->whereNotNull('mt5_address')
    ->first();

if (!$user) {
    // Fallback to any MT5 user
    $user = User::whereNotNull('mt5_login')->first();
}

if ($user) {
    echo "Testing User: {$user->fullname} (ID: {$user->id})\n";
    echo "Email: {$user->email}\n";
    echo "MT5 Login: {$user->mt5_login}\n\n";
    
    echo "=== Address Data Analysis ===\n";
    echo "Main Address Object: " . json_encode($user->address) . "\n";
    echo "MT5 Address: " . ($user->mt5_address ?: 'NULL') . "\n";
    echo "MT5 City: " . ($user->mt5_city ?: 'NULL') . "\n";
    echo "MT5 State: " . ($user->mt5_state ?: 'NULL') . "\n";
    echo "MT5 Zip: " . ($user->mt5_zip_code ?: 'NULL') . "\n";
    echo "MT5 Phone: " . ($user->mt5_phone ?: 'NULL') . "\n\n";
    
    echo "=== Fallback Logic Test ===\n";
    $addressValue = @$user->address->address ?: $user->mt5_address ?: '';
    $cityValue = @$user->address->city ?: $user->mt5_city ?: '';
    $stateValue = @$user->address->state ?: $user->mt5_state ?: '';
    $zipValue = @$user->address->zip ?: $user->mt5_zip_code ?: '';
    $mobileValue = $user->mobile ?: $user->mt5_phone ?: '';
    
    echo "Final Address: '{$addressValue}'\n";
    echo "Final City: '{$cityValue}'\n";
    echo "Final State: '{$stateValue}'\n";
    echo "Final Zip: '{$zipValue}'\n";
    echo "Final Mobile: '{$mobileValue}'\n\n";
    
    echo "=== IB Status Check ===\n";
    echo "IB Status: " . ($user->ib_status ?: 'NULL') . "\n";
    echo "IB Type: " . ($user->ib_type ?: 'NULL') . "\n";
    echo "Is IB: " . ($user->isIb() ? 'YES' : 'NO') . "\n";
    echo "Partner Status: " . ($user->partner ?: 'NULL') . "\n\n";
    
    echo "=== Referral Data ===\n";
    echo "Referred By: " . ($user->ref_by ?: 'NULL') . "\n";
    $referralCount = User::where('ref_by', $user->id)->count();
    echo "Direct Referrals Count: {$referralCount}\n";
    
} else {
    echo "No users found with MT5 data!\n";
}

echo "\n=== PHASE 3 TESTING: Direct Referral System ===\n";

// Test referral functionality
$usersWithReferrals = User::whereNotNull('ref_by')->take(5)->get();
echo "Users with referrals: " . $usersWithReferrals->count() . "\n";

foreach ($usersWithReferrals as $user) {
    $referrer = User::find($user->ref_by);
    echo "- {$user->fullname} referred by " . ($referrer ? $referrer->fullname : 'UNKNOWN') . "\n";
}

echo "\n=== Database Schema Check ===\n";

// Check if MT5 fields exist in database
$columns = \DB::select("SHOW COLUMNS FROM users LIKE 'mt5_%'");
echo "MT5 columns found: " . count($columns) . "\n";

// Check for users with any MT5 address data
$usersWithMT5Address = User::whereNotNull('mt5_address')->count();
$usersWithMT5City = User::whereNotNull('mt5_city')->count();
$usersWithMT5Phone = User::whereNotNull('mt5_phone')->count();

echo "Users with MT5 address: {$usersWithMT5Address}\n";
echo "Users with MT5 city: {$usersWithMT5City}\n";
echo "Users with MT5 phone: {$usersWithMT5Phone}\n";

// Check a few sample users for any populated MT5 fields
echo "\n=== Sample MT5 Data Check ===\n";
$sampleUsers = User::whereNotNull('mt5_login')->take(3)->get();
foreach ($sampleUsers as $user) {
    echo "User {$user->id}: MT5 Login: {$user->mt5_login}\n";
    echo "  - MT5 Name: " . ($user->mt5_name ?: 'NULL') . "\n";
    echo "  - MT5 Email: " . ($user->mt5_email ?: 'NULL') . "\n";
    echo "  - MT5 Group: " . ($user->mt5_group ?: 'NULL') . "\n";
    echo "  - MT5 Address: " . ($user->mt5_address ?: 'NULL') . "\n";
}

echo "\n=== PHASE 3 TESTING: Referral System Verification ===\n";

// Test adding a referral relationship
$user1 = User::find(1); // Main user
$user2 = User::where('id', '!=', 1)->whereNull('ref_by')->first(); // User to be referred

if ($user1 && $user2) {
    echo "Testing referral assignment:\n";
    echo "Main User: {$user1->fullname} (ID: {$user1->id})\n";
    echo "User to refer: {$user2->fullname} (ID: {$user2->id})\n";

    // Test the referral assignment
    $user2->ref_by = $user1->id;
    $user2->save();

    echo "✅ Referral assigned successfully\n";

    // Verify the relationship
    $referralCount = User::where('ref_by', $user1->id)->count();
    echo "Total referrals for {$user1->fullname}: {$referralCount}\n";

    // Test IB assignment
    if (!$user1->isIb()) {
        $user1->ib_status = 1; // Approved
        $user1->ib_type = 'master';
        $user1->partner = 1;
        $user1->save();
        echo "✅ {$user1->fullname} set as Master IB\n";
    }

    // Test Sub-IB assignment
    $user2->ib_status = 1; // Approved
    $user2->ib_type = 'sub';
    $user2->ib_parent_id = $user1->id;
    $user2->save();
    echo "✅ {$user2->fullname} set as Sub-IB under {$user1->fullname}\n";

} else {
    echo "❌ Cannot find suitable users for referral testing\n";
}

echo "\n=== PHASE 4 TESTING: Search Functionality ===\n";

// Test MT5 search
$mt5Users = User::whereNotNull('mt5_login')->take(3)->get();
echo "Sample MT5 users for search testing:\n";
foreach ($mt5Users as $user) {
    echo "- {$user->fullname}: MT5 {$user->mt5_login}, Email: {$user->email}\n";
}

echo "\n=== Test Complete ===\n";
