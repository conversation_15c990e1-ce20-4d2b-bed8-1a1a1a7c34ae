<?php

/**
 * Test the fixed MT5 account selection logic
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Testing Fixed MT5 Account Selection ===\n\n";

// Clear logs
file_put_contents('storage/logs/laravel.log', '');
echo "✅ Cleared Laravel logs\n";

// Use the specific user
$user = \App\Models\User::find(10860); // <EMAIL>
if (!$user) {
    echo "❌ User 10860 not found\n";
    exit(1);
}

echo "Testing with user: {$user->email} (ID: {$user->id})\n\n";

// Test accounts
$testAccounts = [
    875070, // Previously failing account
    873517, // Previously failing account
    873612, // Previously failing account
];

echo "Testing Specific Account Deduction:\n";
echo "==================================\n";

foreach ($testAccounts as $accountLogin) {
    echo "\n--- Testing Account {$accountLogin} ---\n";
    
    // Get account balance before
    $accountBefore = \App\Models\Mt5Users::where('Login', $accountLogin)->first();
    if (!$accountBefore) {
        echo "❌ Account {$accountLogin} not found\n";
        continue;
    }
    
    echo "Balance Before: \${$accountBefore->Balance}\n";
    
    if ($accountBefore->Balance < 15) {
        echo "⚠️ Insufficient balance for $15 test\n";
        continue;
    }
    
    // Test specific account deduction
    $testAmount = 5.00;
    echo "Testing deduction of \${$testAmount} from specific account {$accountLogin}...\n";
    
    try {
        // Use the controller method with specific account
        $controller = new \App\Http\Controllers\User\WithdrawController();
        $reflection = new ReflectionClass($controller);
        $method = $reflection->getMethod('deductBalanceFromMT5Accounts');
        $method->setAccessible(true);
        
        $comment = "Test specific account deduction: {$accountLogin}";
        $result = $method->invoke(null, $user, $testAmount, $comment, $accountLogin);
        
        // Check balance after
        $accountAfter = \App\Models\Mt5Users::where('Login', $accountLogin)->first();
        $balanceChange = $accountAfter->Balance - $accountBefore->Balance;
        
        echo "Result: " . ($result ? 'SUCCESS' : 'FAILED') . "\n";
        echo "Balance After: \${$accountAfter->Balance}\n";
        echo "Balance Change: \${$balanceChange}\n";
        
        if ($result && abs($balanceChange + $testAmount) < 0.01) {
            echo "✅ Account {$accountLogin}: WORKING CORRECTLY (specific account deduction)\n";
            
            // Refund the test amount
            echo "Refunding test amount...\n";
            $mt5Service = new \App\Services\MT5Service();
            $refundResult = $mt5Service->addBalanceToUserAccounts($user, $testAmount, "Test refund for account {$accountLogin}");
            
            if ($refundResult['success']) {
                echo "✅ Refund successful\n";
            } else {
                echo "❌ Refund failed: " . $refundResult['message'] . "\n";
            }
        } else {
            echo "❌ Account {$accountLogin}: STILL FAILING\n";
            echo "Expected change: -\${$testAmount}, Actual change: \${$balanceChange}\n";
        }
        
    } catch (\Exception $e) {
        echo "❌ Exception for account {$accountLogin}: " . $e->getMessage() . "\n";
    }
}

echo "\n=== Testing Web Interface Withdrawal with Specific Account ===\n";
echo "=============================================================\n";

// Test the complete web interface flow with specific account
$testAccount = 875070; // Previously failing account
$testAmount = 12.00; // Above minimum

echo "Testing complete withdrawal flow with account {$testAccount}...\n";

// Get account balance before
$accountBefore = \App\Models\Mt5Users::where('Login', $testAccount)->first();
echo "Account {$testAccount} Balance Before: \${$accountBefore->Balance}\n";

// Get USDT method
$usdtMethod = \App\Models\WithdrawMethod::where('name', 'LIKE', '%USDT%')->where('status', 1)->first();

// Create AJAX request data
$requestData = [
    'method_code' => $usdtMethod->id,
    'amount' => $testAmount,
    'mt5_account' => $testAccount,
    'currency' => 'USDT',
    'wallet_type' => 'spot',
    'wallet_address' => 'TQn9Y2khEsLJW1ChVWFMSMeRDow5oNDMHh',
    'network' => 'TRC20'
];

// Create mock AJAX request
$request = new \Illuminate\Http\Request();
$request->merge($requestData);
$request->setMethod('POST');
$request->headers->set('X-Requested-With', 'XMLHttpRequest');

// Set authenticated user
auth()->login($user);

try {
    $controller = new \App\Http\Controllers\User\WithdrawController();
    
    echo "Calling withdrawStore with specific account {$testAccount}...\n";
    $response = $controller->withdrawStore($request);
    
    if ($response instanceof \Illuminate\Http\JsonResponse) {
        $responseData = $response->getData(true);
        
        if ($responseData['success']) {
            echo "✅ withdrawStore SUCCESS\n";
            $trx = $responseData['trx'];
            echo "Transaction ID: {$trx}\n";
            
            // Check account balance after
            $accountAfter = \App\Models\Mt5Users::where('Login', $testAccount)->first();
            $balanceChange = $accountAfter->Balance - $accountBefore->Balance;
            
            echo "Account {$testAccount} Balance After: \${$accountAfter->Balance}\n";
            echo "Balance Change: \${$balanceChange}\n";
            
            if (abs($balanceChange + $testAmount) < 0.01) {
                echo "✅ SPECIFIC ACCOUNT DEDUCTION WORKING!\n";
                echo "The fix is successful - account {$testAccount} was deducted correctly.\n";
            } else {
                echo "❌ SPECIFIC ACCOUNT DEDUCTION STILL FAILING\n";
                echo "Expected change: -\${$testAmount}, Actual change: \${$balanceChange}\n";
            }
            
        } else {
            echo "❌ withdrawStore FAILED: " . $responseData['message'] . "\n";
        }
    } else {
        echo "❌ Unexpected response type\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "\n";
}

echo "\n=== CHECKING LOGS FOR SPECIFIC ACCOUNT USAGE ===\n";
echo "================================================\n";

$logs = file_get_contents('storage/logs/laravel.log');
if (!empty(trim($logs))) {
    $logLines = explode("\n", $logs);
    
    echo "Key log entries:\n";
    foreach ($logLines as $line) {
        if (!empty(trim($line)) && (
            strpos($line, 'specificAccount') !== false || 
            strpos($line, 'Deducting from specific') !== false ||
            strpos($line, 'Successfully deducted balance from MT5 account') !== false
        )) {
            echo "  " . trim($line) . "\n";
        }
    }
} else {
    echo "No relevant log entries found\n";
}

echo "\n=== Fixed MT5 Account Test Complete ===\n";
