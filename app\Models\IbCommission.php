<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\Searchable;

class IbCommission extends Model
{
    use HasFactory, Searchable;

    protected $fillable = [
        'from_user_id',
        'to_ib_user_id',
        'trade_id',
        'mt5_deal_id',
        'mt5_login',
        'symbol',
        'volume',
        'commission_amount',
        'commission_rate',
        'level',
        'status',
        'trade_closed_at',
        'deal_time',
        'deal_profit',
        'deal_commission',
        'paid_at',
        'processed_at',
        'parent_commission_id',
        'notes'
    ];

    protected $casts = [
        'volume' => 'decimal:2',
        'commission_amount' => 'decimal:2',
        'commission_rate' => 'decimal:2',
        'deal_profit' => 'decimal:2',
        'deal_commission' => 'decimal:2',
        'trade_closed_at' => 'datetime',
        'deal_time' => 'datetime',
        'paid_at' => 'datetime',
        'processed_at' => 'datetime'
    ];

    /**
     * Get the user who generated the trade
     */
    public function fromUser()
    {
        return $this->belongsTo(User::class, 'from_user_id');
    }

    /**
     * Get the IB who receives the commission
     */
    public function toIbUser()
    {
        return $this->belongsTo(User::class, 'to_ib_user_id');
    }

    /**
     * Get the parent commission (for multi-level commissions)
     */
    public function parentCommission()
    {
        return $this->belongsTo(IbCommission::class, 'parent_commission_id');
    }

    /**
     * Get child commissions (for multi-level commissions)
     */
    public function childCommissions()
    {
        return $this->hasMany(IbCommission::class, 'parent_commission_id');
    }

    /**
     * Scope for pending commissions
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for paid commissions
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    /**
     * Scope for cancelled commissions
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    /**
     * Scope for specific IB
     */
    public function scopeForIb($query, $ibUserId)
    {
        return $query->where('to_ib_user_id', $ibUserId);
    }

    /**
     * Scope for specific symbol
     */
    public function scopeForSymbol($query, $symbol)
    {
        return $query->where('symbol', $symbol);
    }

    /**
     * Scope for date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('trade_closed_at', [$startDate, $endDate]);
    }

    /**
     * Mark commission as paid
     */
    public function markAsPaid()
    {
        $this->status = 'paid';
        $this->paid_at = now();
        $this->save();
        return $this;
    }

    /**
     * Mark commission as cancelled
     */
    public function markAsCancelled($reason = null)
    {
        $this->status = 'cancelled';
        if ($reason) {
            $this->notes = $reason;
        }
        $this->save();
        return $this;
    }

    /**
     * Get commission statistics for IB
     */
    public static function getIbStats($ibUserId, $startDate = null, $endDate = null)
    {
        $query = self::forIb($ibUserId);

        if ($startDate && $endDate) {
            $query->dateRange($startDate, $endDate);
        }

        return [
            'total_commissions' => $query->sum('commission_amount'),
            'paid_commissions' => $query->paid()->sum('commission_amount'),
            'pending_commissions' => $query->pending()->sum('commission_amount'),
            'total_trades' => $query->count(),
            'total_volume' => $query->sum('volume')
        ];
    }

    /**
     * Get commission breakdown by level
     */
    public static function getLevelBreakdown($ibUserId, $startDate = null, $endDate = null)
    {
        $query = self::forIb($ibUserId);

        if ($startDate && $endDate) {
            $query->dateRange($startDate, $endDate);
        }

        return $query->selectRaw('level, SUM(commission_amount) as total_amount, COUNT(*) as total_trades')
            ->groupBy('level')
            ->orderBy('level')
            ->get();
    }

    /**
     * Get commission breakdown by symbol
     */
    public static function getSymbolBreakdown($ibUserId, $startDate = null, $endDate = null)
    {
        $query = self::forIb($ibUserId);

        if ($startDate && $endDate) {
            $query->dateRange($startDate, $endDate);
        }

        return $query->selectRaw('symbol, SUM(commission_amount) as total_amount, SUM(volume) as total_volume, COUNT(*) as total_trades')
            ->groupBy('symbol')
            ->orderBy('total_amount', 'desc')
            ->get();
    }

    /**
     * Scope for processed commissions
     */
    public function scopeProcessed($query)
    {
        return $query->where('status', 'processed');
    }

    /**
     * Scope for specific MT5 deal
     */
    public function scopeForMT5Deal($query, $dealId)
    {
        return $query->where('mt5_deal_id', $dealId);
    }

    /**
     * Scope for specific level
     */
    public function scopeForLevel($query, $level)
    {
        return $query->where('level', $level);
    }

    /**
     * Mark commission as processed
     */
    public function markAsProcessed($notes = null)
    {
        $this->update([
            'status' => 'processed',
            'processed_at' => now(),
            'notes' => $notes
        ]);
    }

    /**
     * Check if commission is from direct referral
     */
    public function isDirectCommission()
    {
        return $this->level === 1;
    }

    /**
     * Check if commission is from multi-level structure
     */
    public function isMultiLevelCommission()
    {
        return $this->level > 1;
    }

    /**
     * Get formatted commission amount
     */
    public function getFormattedCommissionAttribute()
    {
        return '$' . number_format($this->commission_amount, 2);
    }

    /**
     * Get level display name
     */
    public function getLevelDisplayAttribute()
    {
        return match($this->level) {
            1 => 'Direct',
            2 => 'Level 2',
            3 => 'Level 3',
            4 => 'Level 4',
            5 => 'Level 5',
            default => 'Level ' . $this->level
        };
    }
}
