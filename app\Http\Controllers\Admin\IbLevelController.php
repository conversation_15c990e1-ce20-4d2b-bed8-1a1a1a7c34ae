<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\IbLevel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class IbLevelController extends Controller
{
    /**
     * Display IB levels management page
     */
    public function index()
    {
        $pageTitle = 'IB Levels Management';
        $ibLevels = IbLevel::ordered()->get();
        
        return view('admin.ib_system.levels.index', compact('pageTitle', 'ibLevels'));
    }

    /**
     * Store or update IB levels
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'levels' => 'required|array|min:1',
            'levels.*.level' => 'required|integer|min:1',
            'levels.*.name' => 'required|string|max:255',
            'levels.*.commission_percent' => 'required|numeric|min:0|max:100',
            'levels.*.max_commission_percent' => 'required|numeric|min:0|max:100',
            'levels.*.description' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            $notify[] = ['error', 'Validation failed: ' . $validator->errors()->first()];
            return back()->withNotify($notify)->withInput();
        }

        try {
            // Clear existing levels
            IbLevel::truncate();

            // Create new levels
            foreach ($request->levels as $levelData) {
                IbLevel::create([
                    'level' => $levelData['level'],
                    'name' => $levelData['name'],
                    'commission_percent' => $levelData['commission_percent'],
                    'max_commission_percent' => $levelData['max_commission_percent'],
                    'description' => $levelData['description'] ?? null,
                    'status' => true
                ]);
            }

            $notify[] = ['success', 'IB levels updated successfully'];
            return back()->withNotify($notify);

        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to update IB levels: ' . $e->getMessage()];
            return back()->withNotify($notify);
        }
    }

    /**
     * Toggle level status
     */
    public function toggleStatus($id)
    {
        try {
            $level = IbLevel::findOrFail($id);
            $level->status = !$level->status;
            $level->save();

            $status = $level->status ? 'activated' : 'deactivated';
            $notify[] = ['success', "IB level {$status} successfully"];
            
        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to update level status'];
        }

        return back()->withNotify($notify);
    }

    /**
     * Delete level
     */
    public function destroy($id)
    {
        try {
            $level = IbLevel::findOrFail($id);
            
            // Check if level is being used
            $usageCount = \App\Models\IbCommission::where('level', $level->level)->count();
            if ($usageCount > 0) {
                $notify[] = ['error', 'Cannot delete level that has commission records'];
                return back()->withNotify($notify);
            }

            $level->delete();
            $notify[] = ['success', 'IB level deleted successfully'];
            
        } catch (\Exception $e) {
            $notify[] = ['error', 'Failed to delete level'];
        }

        return back()->withNotify($notify);
    }

    /**
     * Get level statistics
     */
    public function statistics()
    {
        $pageTitle = 'IB Level Statistics';
        
        $stats = [];
        $levels = IbLevel::active()->ordered()->get();
        
        foreach ($levels as $level) {
            $commissions = \App\Models\IbCommission::where('level', $level->level);
            
            $stats[] = [
                'level' => $level,
                'total_commissions' => $commissions->sum('commission_amount'),
                'total_trades' => $commissions->count(),
                'active_ibs' => $commissions->distinct('to_ib_user_id')->count(),
                'avg_commission' => $commissions->avg('commission_amount') ?? 0
            ];
        }
        
        return view('admin.ib_system.levels.statistics', compact('pageTitle', 'stats'));
    }
}
