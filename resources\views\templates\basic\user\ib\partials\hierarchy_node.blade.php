@if(isset($node['children']) && count($node['children']) > 0)
    @foreach($node['children'] as $child)
    <div class="hierarchy-item mb-2" style="margin-left: {{ $level * 20 }}px;">
        <div class="d-flex align-items-center">
            <div class="hierarchy-level">
                <span class="badge badge--info">L{{ $child['level'] }}</span>
            </div>
            <div class="hierarchy-info ms-3">
                <h6 class="mb-0">{{ $child['fullname'] }}</h6>
                <small class="text-muted">{{ $child['username'] }}</small>
            </div>
            <div class="hierarchy-stats ms-auto">
                <small class="text-muted">
                    {{ $child['total_clients'] }} clients | {{ $child['active_clients'] }} active
                </small>
            </div>
            <div class="hierarchy-type ms-2">
                <span class="badge badge--{{ $child['ib_type'] == 'master' ? 'success' : 'primary' }}">
                    {{ ucfirst($child['ib_type']) }}
                </span>
            </div>
        </div>
        
        @if(isset($child['children']) && count($child['children']) > 0)
            @include('templates.basic.user.ib.partials.hierarchy_node', ['node' => $child, 'level' => $level + 1])
        @endif
    </div>
    @endforeach
@endif
