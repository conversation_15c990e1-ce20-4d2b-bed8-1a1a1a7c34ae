<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 CHECKING TABLE COLUMNS\n";
echo "=========================\n";

$tables = ['deposits', 'withdrawals', 'transactions', 'support_tickets'];

foreach ($tables as $table) {
    echo "\n📋 {$table} TABLE COLUMNS:\n";
    try {
        $columns = \DB::select("SHOW COLUMNS FROM {$table}");
        foreach ($columns as $col) {
            echo "   - {$col->Field} ({$col->Type})\n";
        }
    } catch (Exception $e) {
        echo "   ❌ Error: {$e->getMessage()}\n";
    }
}

echo "\n✅ TABLE STRUCTURE CHECK COMPLETED!\n";
