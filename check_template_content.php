<?php

require_once 'vendor/autoload.php';
require_once 'bootstrap/app.php';

use App\Models\NotificationTemplate;

$template = NotificationTemplate::find(44);

echo "Template: " . $template->name . "\n";
echo "Content Length: " . strlen($template->email_body) . " characters\n";
echo "First 300 characters:\n";
echo substr($template->email_body, 0, 300) . "\n";
echo "...\n";

// Check for duplicate content indicators
$content = $template->email_body;
$duplicateIndicators = [
    'Best regards,',
    'MBFX Team',
    'MBFX Team',
    'footer-section',
    'header-banner'
];

echo "\nDuplicate Content Check:\n";
foreach ($duplicateIndicators as $indicator) {
    $count = substr_count($content, $indicator);
    echo "- '$indicator': appears $count times\n";
}

// Check if content has proper structure
echo "\nStructure Check:\n";
echo "- Has DOCTYPE: " . (strpos($content, '<!DOCTYPE html') !== false ? 'YES' : 'NO') . "\n";
echo "- Has header-banner: " . (strpos($content, 'header-banner') !== false ? 'YES' : 'NO') . "\n";
echo "- Has logo-section: " . (strpos($content, 'logo-section') !== false ? 'YES' : 'NO') . "\n";
echo "- Has footer-section: " . (strpos($content, 'footer-section') !== false ? 'YES' : 'NO') . "\n";
